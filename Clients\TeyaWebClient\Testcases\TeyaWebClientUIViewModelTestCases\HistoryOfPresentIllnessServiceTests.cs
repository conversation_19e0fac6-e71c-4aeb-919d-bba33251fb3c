using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using Moq;
using Moq.Protected;
using NUnit.Framework;
using TeyaUIModels.Model;
using TeyaUIViewModels.ViewModel;
using TeyaUIViewModels.TeyaUIViewModelResources;
using System.Net.Http.Json;
using System.Text;

namespace TeyaWebClientUIViewModelTestCases
{
    [TestFixture]
    public class HistoryOfPresentIllnessServiceTests
    {
        private Mock<HttpMessageHandler> _mockHttpMessageHandler;
        private HttpClient _httpClient;
        private Mock<IConfiguration> _mockConfiguration;
        private Mock<IStringLocalizer<TeyaUIViewModelsStrings>> _mockLocalizer;
        private Mock<ITokenService> _mockTokenService;
        private HistoryOfPresentIllnessService _historyOfPresentIllnessService;
        private readonly string _baseUrl = "http://test-api.com";
        private readonly string _accessToken = "test-token";

        [SetUp]
        public void Setup()
        {
            // Setup environment variable
            Environment.SetEnvironmentVariable("EncounterNotesURL", _baseUrl);

            // Setup mock HTTP handler
            _mockHttpMessageHandler = new Mock<HttpMessageHandler>();

            // Setup HttpClient with mock handler
            _httpClient = new HttpClient(_mockHttpMessageHandler.Object)
            {
                BaseAddress = new Uri(_baseUrl)
            };

            // Setup mock configuration
            _mockConfiguration = new Mock<IConfiguration>();

            // Setup mock localizer
            _mockLocalizer = new Mock<IStringLocalizer<TeyaUIViewModelsStrings>>();
            _mockLocalizer.Setup(l => l["AddressRetrievalFailure"])
                .Returns(new LocalizedString("AddressRetrievalFailure", "Failed to retrieve address"));
            _mockLocalizer.Setup(l => l["DatabaseError"])
                .Returns(new LocalizedString("DatabaseError", "Database error"));

            // Setup mock token service
            _mockTokenService = new Mock<ITokenService>();
            _mockTokenService.Setup(t => t.AccessToken).Returns(_accessToken);

            // Create HistoryOfPresentIllnessService with mocked dependencies
            _historyOfPresentIllnessService = new HistoryOfPresentIllnessService(_httpClient, _mockConfiguration.Object, _mockLocalizer.Object, _mockTokenService.Object);
        }

        [TearDown]
        public void TearDown()
        {
            _httpClient.Dispose();
            Environment.SetEnvironmentVariable("EncounterNotesURL", null);
        }

        [Test]
        public async Task GetHpiByPatientIdAsync_WhenSuccessful_ReturnsHpiRecords()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var expectedHpiRecords = new List<HistoryOfPresentIllness>
            {
                new HistoryOfPresentIllness
                {
                    Id = Guid.NewGuid(),
                    PatientId = patientId,
                    Symptoms = "Headache",
                    Notes = "Severe headache for 3 days",
                    IsActive = true
                },
                new HistoryOfPresentIllness
                {
                    Id = Guid.NewGuid(),
                    PatientId = patientId,
                    Symptoms = "Fever",
                    Notes = "Low-grade fever for 2 days",
                    IsActive = false
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedHpiRecords)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _historyOfPresentIllnessService.GetHpiByPatientIdAsync(patientId, orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(2));
            Assert.That(result[0].Symptoms, Is.EqualTo("Headache"));
            Assert.That(result[1].Symptoms, Is.EqualTo("Fever"));
        }

        [Test]
        public async Task GetHpiByPatientIdAsync_WhenHttpRequestFails_ReturnsEmptyList()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _historyOfPresentIllnessService.GetHpiByPatientIdAsync(patientId, orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(0));
        }

        [Test]
        public async Task GetActiveHpiByPatientIdAsync_WhenSuccessful_ReturnsActiveHpiRecords()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var expectedHpiRecords = new List<HistoryOfPresentIllness>
            {
                new HistoryOfPresentIllness
                {
                    Id = Guid.NewGuid(),
                    PatientId = patientId,
                    Symptoms = "Headache",
                    Notes = "Severe headache for 3 days",
                    IsActive = true
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedHpiRecords)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _historyOfPresentIllnessService.GetActiveHpiByPatientIdAsync(patientId, orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(1));
            Assert.That(result[0].Symptoms, Is.EqualTo("Headache"));
            Assert.That(result[0].IsActive, Is.True);
        }

        [Test]
        public async Task GetActiveHpiByPatientIdAsync_WhenHttpRequestFails_ReturnsEmptyList()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _historyOfPresentIllnessService.GetActiveHpiByPatientIdAsync(patientId, orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(0));
        }

        [Test]
        public async Task AddHpiAsync_WhenSuccessful_CompletesWithoutException()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var hpiRecords = new List<HistoryOfPresentIllness>
            {
                new HistoryOfPresentIllness
                {
                    Id = Guid.NewGuid(),
                    PatientId = patientId,
                    Symptoms = "Headache",
                    Notes = "Severe headache for 3 days",
                    IsActive = true
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            // No exception should be thrown
            await _historyOfPresentIllnessService.AddHpiAsync(hpiRecords, orgId, subscription);
        }

        [Test]
        public async Task AddHpiAsync_WhenHttpRequestFails_CompletesWithoutException()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var hpiRecords = new List<HistoryOfPresentIllness>
            {
                new HistoryOfPresentIllness
                {
                    Id = Guid.NewGuid(),
                    PatientId = patientId,
                    Symptoms = "Headache",
                    Notes = "Severe headache for 3 days",
                    IsActive = true
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            // Service handles errors gracefully, no exception should be thrown
            await _historyOfPresentIllnessService.AddHpiAsync(hpiRecords, orgId, subscription);
        }

        [Test]
        public async Task UpdateHpiListAsync_WhenSuccessful_CompletesWithoutException()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var hpiRecords = new List<HistoryOfPresentIllness>
            {
                new HistoryOfPresentIllness
                {
                    Id = Guid.NewGuid(),
                    PatientId = patientId,
                    Symptoms = "Headache",
                    Notes = "Updated: Severe headache for 5 days with nausea",
                    IsActive = true
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            // No exception should be thrown
            await _historyOfPresentIllnessService.UpdateHpiListAsync(hpiRecords, orgId, subscription);
        }

        [Test]
        public async Task UpdateHpiListAsync_WhenHttpRequestFails_CompletesWithoutException()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var hpiRecords = new List<HistoryOfPresentIllness>
            {
                new HistoryOfPresentIllness
                {
                    Id = Guid.NewGuid(),
                    PatientId = patientId,
                    Symptoms = "Headache",
                    Notes = "Updated: Severe headache for 5 days with nausea",
                    IsActive = true
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            // Service handles errors gracefully, no exception should be thrown
            await _historyOfPresentIllnessService.UpdateHpiListAsync(hpiRecords, orgId, subscription);
        }
    }
}



