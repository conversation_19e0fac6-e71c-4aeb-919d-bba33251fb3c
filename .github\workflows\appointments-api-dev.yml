name: Teya-ContainerApp-Appointment-API-Dev
on:
  workflow_dispatch:
  schedule:
    # Runs at 00:00 UTC (midnight) every day
    - cron: '0 0 * * *'

env:
  CONTAINER_APP_CONTAINER_NAME: appointments-api-dev
  CONTAINER_APP_NAME: appointments-api-dev
  CONTAINER_APP_RESOURCE_GROUP_NAME: teyahealth-rg-dev-eastus-001
  CONTAINER_REGISTRY_LOGIN_SERVER: teyahealthdev.azurecr.io
  DOCKER_FILE_PATH: Services/AppointmentsApi/Dockerfile
  PROJECT_NAME_FOR_DOCKER: appointments
jobs:
  build:
    runs-on: ubuntu-latest
    steps:
    - name: Checkout to the branch
      uses: actions/checkout@v4
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
    - name: Log in to container registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.CONTAINER_REGISTRY_LOGIN_SERVER }}
        username: TeyaHealthDev
        password: ****************************************************
    - name: Build and push container image to registry
      uses: docker/build-push-action@v5
      with:
        push: true
        tags: ${{ env.CONTAINER_REGISTRY_LOGIN_SERVER }}/${{ env.PROJECT_NAME_FOR_DOCKER }}:${{ github.sha }}
        file: ${{ env.DOCKER_FILE_PATH }}
  deploy:
    runs-on: ubuntu-latest
    needs: build
    steps:
    - name: Azure Login
      uses: azure/login@v1
      with:
        creds: ${{ secrets.DEVREPOSECRET }}
    - name: Deploy to containerapp
      uses: azure/CLI@v1
      with:
        inlineScript: >
          az config set extension.use_dynamic_install=yes_without_prompt

          az containerapp registry set --name ${{ env.CONTAINER_APP_NAME }} --resource-group ${{ env.CONTAINER_APP_RESOURCE_GROUP_NAME }} --server ${{ env.CONTAINER_REGISTRY_LOGIN_SERVER }} --username TeyaHealthDev --password ****************************************************

          az containerapp secret set --name ${{ env.CONTAINER_APP_NAME }} --resource-group ${{ env.CONTAINER_APP_RESOURCE_GROUP_NAME }} --secrets \
            keyvault-urls=https://commonvault-dev.vault.azure.net/,https://appointmentsapivault-dev.vault.azure.net/ \
            keyvault-tenantid=b9bdac0b-583f-4611-815a-9a83d5813d87 \
            keyvault-clientid=ba018615-fe19-42a3-97c6-e2a6f46d4092 \
            keyvault-clientsecret=**************************************** \
            environment-key=Development

          az containerapp update --name ${{ env.CONTAINER_APP_NAME }} --container-name ${{ env.CONTAINER_APP_CONTAINER_NAME }} --resource-group ${{ env.CONTAINER_APP_RESOURCE_GROUP_NAME }} --image ${{ env.CONTAINER_REGISTRY_LOGIN_SERVER }}/${{ env.PROJECT_NAME_FOR_DOCKER }}:${{ github.sha }} --set-env-vars \
            KEYVAULT__URLS=secretref:keyvault-urls \
            KEYVAULT__TENANTID=secretref:keyvault-tenantid \
            KEYVAULT__CLIENTID=secretref:keyvault-clientid \
            KEYVAULT__CLIENTSECRET=secretref:keyvault-clientsecret \
            ENVIRONMENT_KEY=secretref:environment-key

    - name: logout
      run: >
        az logout
