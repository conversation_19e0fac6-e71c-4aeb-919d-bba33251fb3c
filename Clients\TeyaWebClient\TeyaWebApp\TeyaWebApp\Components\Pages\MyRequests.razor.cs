using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using MudBlazor;
using TeyaUIModels.Model;
using TeyaUIViewModels.ViewModel;
using TeyaWebApp.TeyaAIScribeResource;
using Unity;

namespace TeyaWebApp.Components.Pages
{
    /// <summary>
    /// My Requests page for viewing outgoing co-signing requests
    /// </summary>
    public partial class Myrequests : ComponentBase
    {
        /// <summary>
        /// Current active user for authentication context
        /// </summary>
        [Inject] private ActiveUser CurrentUser { get; set; }
        [Inject] private UserContext UserContext { get; set; }
        [Inject] private ICosigningRequestService CosigningRequestService { get; set; }
        [Inject] private ILogger<Myrequests> Logger { get; set; }
        [Inject] private ISnackbar Snackbar { get; set; }
        [Inject] private NavigationManager Navigation { get; set; }
        [Inject] private IDialogService DialogService { get; set; }
        [Inject] private IStringLocalizer<TeyaWebApp.TeyaAIScribeResource.TeyaAIScribeResource> Localizer { get; set; }


        /// <summary>
        /// List of outgoing co-signing requests
        /// </summary>
        private List<CosigningRequest> _myRequests = new List<CosigningRequest>();

        /// <summary>
        /// Loading state indicator
        /// </summary>
        private bool _isLoading = true;

        /// <summary>
        /// Request details dialog state
        /// </summary>
        private bool _showRequestDetailsDialog = false;
        private CosigningRequest? _selectedRequest = null;
        private DialogOptions _requestDetailsDialogOptions = new()
        {
            MaxWidth = MaxWidth.Medium,
            FullWidth = true,
            CloseButton = true,
           
        };

        /// <summary>
        /// Component initialization
        /// </summary>
        protected override async Task OnInitializedAsync()
        {
            try
            {
                await LoadMyRequests();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex ,"Error initializing MyRequests page");
                Snackbar.Add(Localizer["ErrorLoadingRequests"], Severity.Error);
            }
            finally
            {
                _isLoading = false;
                StateHasChanged();
            }
        }

        /// <summary>
        /// Load outgoing co-signing requests for the current user
        /// </summary>
        private async Task LoadMyRequests()
        {
            try
            {
                if (!Guid.TryParse(CurrentUser.id, out var userId))
                {
                    Logger.LogError("Invalid user ID: {UserId}", CurrentUser.id);
                    return;
                }

                var requests = await CosigningRequestService.GetByRequesterIdAsync(
                    userId,
                    UserContext.ActiveUserOrganizationID,
                    UserContext.ActiveUserSubscription
                );
                _myRequests = requests.ToList();

                Logger.LogInformation("Loaded {Count} outgoing requests for user {UserId}", _myRequests.Count, userId);
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error loading outgoing requests for user {UserId}", CurrentUser.id);
                Snackbar.Add(Localizer["ErrorLoadingRequests"], Severity.Error);
            }
        }

        /// <summary>
        /// Get status color for display
        /// </summary>
        private Color GetStatusColor(CosigningRequestStatus status)
        {
            return status switch
            {
                CosigningRequestStatus.Pending => Color.Warning,
                CosigningRequestStatus.Approved => Color.Success,
                CosigningRequestStatus.ChangesRequested => Color.Error,
                _ => Color.Default
            };
        }

        /// <summary>
        /// Get status icon for display
        /// </summary>
        private string GetStatusIcon(CosigningRequestStatus status)
        {
            return status switch
            {
                CosigningRequestStatus.Pending => Icons.Material.Filled.Schedule,
                CosigningRequestStatus.Approved => Icons.Material.Filled.CheckCircle,
                CosigningRequestStatus.ChangesRequested => Icons.Material.Filled.Comment,
                _ => Icons.Material.Filled.Help
            };
        }

        /// <summary>
        /// Get status text for display
        /// </summary>
        private string GetStatusText(CosigningRequestStatus status)
        {
            return status switch
            {
                CosigningRequestStatus.Pending => Localizer["Pending"],
                CosigningRequestStatus.Approved => Localizer["Approved"],
                CosigningRequestStatus.ChangesRequested => Localizer["ChangesRequested"],
                _ => status.ToString()
            };
        }

        /// <summary>
        /// Get CSS class for status indicator
        /// </summary>
        private string GetStatusClass(CosigningRequestStatus status)
        {
            return status switch
            {
                CosigningRequestStatus.Pending => "pending",
                CosigningRequestStatus.Approved => "approved",
                CosigningRequestStatus.ChangesRequested => "changes-requested",
                _ => "pending"
            };
        }

        /// <summary>
        /// Get request card style based on status
        /// </summary>
        private string GetRequestCardStyle(CosigningRequest request)
        {
            var baseStyle = "border-radius: 12px; transition: all 0.2s ease; cursor: pointer;";

            return request.Status switch
            {
                CosigningRequestStatus.Pending => $"{baseStyle} border-left: 4px solid #ff9800;",
                CosigningRequestStatus.Approved => $"{baseStyle} border-left: 4px solid #4caf50;",
                CosigningRequestStatus.ChangesRequested => $"{baseStyle} border-left: 4px solid #f44336;",
                _ => baseStyle
            };
        }

        /// <summary>
        /// Get action icon based on status
        /// </summary>
        private string GetActionIcon(CosigningRequestStatus status)
        {
            return status switch
            {
                CosigningRequestStatus.Approved => Icons.Material.Filled.CheckCircle,
                CosigningRequestStatus.ChangesRequested => Icons.Material.Filled.Comment,
                _ => Icons.Material.Filled.Schedule
            };
        }

        /// <summary>
        /// Get action text based on status
        /// </summary>
        private string GetActionText(CosigningRequestStatus status)
        {
            return status switch
            {
                CosigningRequestStatus.Approved => Localizer["ApprovedOn"],
                CosigningRequestStatus.ChangesRequested => Localizer["ChangesRequestedOn"],
                _ => Localizer["PendingSince"]
            };
        }

        /// <summary>
        /// Get comments preview text
        /// </summary>
        private string GetCommentsPreview(string commentsJson)
        {
            try
            {
                if (string.IsNullOrEmpty(commentsJson) || commentsJson == "[]")
                    return string.Empty;

                var comments = System.Text.Json.JsonSerializer.Deserialize<List<CosigningComment>>(commentsJson);
                if (comments?.Any() == true)
                {
                    var latestComment = comments.OrderByDescending(c => c.CommentDate).First();
                    var preview = latestComment.Comment.Length > 60
                        ? latestComment.Comment.Substring(0, 60) + "..."
                        : latestComment.Comment;
                    return $"{latestComment.CommenterName}: {preview}";
                }
            }
            catch (Exception ex)
            {
                Logger.LogWarning(ex, "Error parsing comments JSON: {CommentsJson}", commentsJson);
            }

            return string.Empty;
        }

        /// <summary>
        /// Get request comments from JSON
        /// </summary>
        private List<CosigningComment> GetRequestComments(string commentsJson)
        {
            try
            {
                if (string.IsNullOrEmpty(commentsJson) || commentsJson == "[]")
                    return new List<CosigningComment>();

                return System.Text.Json.JsonSerializer.Deserialize<List<CosigningComment>>(commentsJson)
                       ?? new List<CosigningComment>();
            }
            catch (Exception ex)
            {
                Logger.LogWarning(ex, "Error parsing comments JSON: {CommentsJson}", commentsJson);
                return new List<CosigningComment>();
            }
        }

        /// <summary>
        /// View request details in dialog
        /// </summary>
        private void ViewRequestDetails(CosigningRequest request)
        {
            _selectedRequest = request;
            _showRequestDetailsDialog = true;
        }

        /// <summary>
        /// Close request details dialog
        /// </summary>
        private void CloseRequestDetailsDialog()
        {
            _showRequestDetailsDialog = false;
            _selectedRequest = null;
        }

        /// <summary>
        /// Navigate to notes page for the specified record
        /// </summary>
        private void ViewNotes(Guid recordId)
        {
            Navigation.NavigateTo($"/Chart?recordId={recordId}");
        }

        /// <summary>
        /// View comments for a request with changes requested
        /// </summary>
        private void ViewComments(CosigningRequest request)
        {
            ViewRequestDetails(request);
        }

        /// <summary>
        /// Cancel a pending request
        /// </summary>
        private async Task CancelRequest(CosigningRequest request)
        {
            try
            {
                var result = await DialogService.ShowMessageBox(
                    Localizer["ConfirmCancellation"],
                    Localizer["CancelRequestConfirmMessage"],
                    yesText: Localizer["Yes"],
                    cancelText: Localizer["No"]
                );

                if (result == true)
                {
                    await CosigningRequestService.CancelRequestAsync(
                        request.Id,
                        UserContext.ActiveUserOrganizationID,
                        UserContext.ActiveUserSubscription
                    );

                    Snackbar.Add(Localizer["RequestCancelledSuccessfully"], Severity.Success);
                    CloseRequestDetailsDialog(); // Close dialog if open
                    await LoadMyRequests(); // Refresh the list
                    StateHasChanged();
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error cancelling request {RequestId}", request.Id);
                Snackbar.Add(Localizer["ErrorCancellingRequest"], Severity.Error);
            }
        }
    }
}