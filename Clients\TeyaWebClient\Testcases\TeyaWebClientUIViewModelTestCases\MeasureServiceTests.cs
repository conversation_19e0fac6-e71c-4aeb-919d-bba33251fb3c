using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using Moq;
using Moq.Protected;
using NUnit.Framework;
using TeyaUIModels.Model;
using TeyaUIViewModels.ViewModel;
using TeyaUIViewModels.TeyaUIViewModelResources;

namespace TeyaWebClientUIViewModelTestCases
{
    [TestFixture]
    public class MeasureServiceTests
    {
        private Mock<HttpMessageHandler> _mockHttpMessageHandler;
        private Mock<IConfiguration> _mockConfiguration;
        private Mock<IStringLocalizer<TeyaUIViewModelsStrings>> _mockLocalizer;
        private HttpClient _httpClient;
        private MeasureService _measureService;

        private const string TestMeasuresUrl = "https://test-measures.com";

        [SetUp]
        public void SetUp()
        {
            _mockHttpMessageHandler = new Mock<HttpMessageHandler>();
            _mockConfiguration = new Mock<IConfiguration>();
            _mockLocalizer = new Mock<IStringLocalizer<TeyaUIViewModelsStrings>>();

            _httpClient = new HttpClient(_mockHttpMessageHandler.Object);

            // Set up environment variable
            Environment.SetEnvironmentVariable("AlertsServiceURL", TestMeasuresUrl);

            _measureService = new MeasureService(_httpClient, _mockConfiguration.Object, _mockLocalizer.Object);
        }

        [TearDown]
        public void TearDown()
        {
            _httpClient?.Dispose();
            Environment.SetEnvironmentVariable("AlertsServiceURL", null);
        }

        private Measure CreateTestMeasure()
        {
            return new Measure
            {
                Id = Guid.NewGuid(),
                MeasureTitle = "Blood Pressure Control",
                MeasureNumerator = "Patients with controlled BP",
                MeasureDenominator = "All hypertensive patients",
                MeasureDescription = "Percentage of patients with controlled blood pressure",
                CDSS = "Clinical Decision Support System",
                MeasureType = "Process",
                OrderSetLinked = "Hypertension Order Set",
                IsDeleted = false
            };
        }

        [Test]
        public async Task GetAllMeasuresAsync_WhenSuccessful_ReturnsMeasuresList()
        {
            // Arrange
            var expectedMeasures = new List<Measure>
            {
                CreateTestMeasure(),
                CreateTestMeasure()
            };

            var responseContent = JsonSerializer.Serialize(expectedMeasures);
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(responseContent, Encoding.UTF8, "application/json")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _measureService.GetAllMeasuresAsync();

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(2));
            Assert.That(result[0].MeasureTitle, Is.EqualTo("Blood Pressure Control"));
        }

        [Test]
        public async Task GetAllMeasuresAsync_WhenHttpRequestFails_ThrowsHttpRequestException()
        {
            // Arrange
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.InternalServerError,
                Content = new StringContent("Internal Server Error")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _measureService.GetAllMeasuresAsync());
        }

        [Test]
        public async Task AddMeasuresAsync_WhenSuccessful_CompletesWithoutException()
        {
            // Arrange
            var measures = new List<Measure>
            {
                CreateTestMeasure(),
                CreateTestMeasure()
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.Created
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            // No exception should be thrown
            await _measureService.AddMeasuresAsync(measures);
        }

        [Test]
        public async Task AddMeasuresAsync_WhenHttpRequestFails_ThrowsHttpRequestException()
        {
            // Arrange
            var measures = new List<Measure> { CreateTestMeasure() };
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad Request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _measureService.AddMeasuresAsync(measures));
        }

        [Test]
        public async Task UpdateMeasuresListAsync_WhenSuccessful_CompletesWithoutException()
        {
            // Arrange
            var measures = new List<Measure>
            {
                CreateTestMeasure(),
                CreateTestMeasure()
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            // No exception should be thrown
            await _measureService.UpdateMeasuresListAsync(measures);
        }

        [Test]
        public async Task UpdateMeasuresListAsync_WhenHttpRequestFails_ThrowsHttpRequestException()
        {
            // Arrange
            var measures = new List<Measure> { CreateTestMeasure() };
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad Request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _measureService.UpdateMeasuresListAsync(measures));
        }

        [Test]
        public async Task AskGptAsync_WhenSuccessful_ReturnsResponse()
        {
            // Arrange
            var systemMessage = "You are a healthcare assistant";
            var userPrompt = "What are the symptoms of diabetes?";
            var expectedResponse = "Common symptoms include increased thirst, frequent urination, and fatigue.";

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(expectedResponse, Encoding.UTF8, "text/plain")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.Is<HttpRequestMessage>(req =>
                        req.RequestUri.ToString().Contains("/api/QualityMeasure/ask-gpt") &&
                        req.RequestUri.ToString().Contains("systemMessage=") &&
                        req.RequestUri.ToString().Contains("userPrompt=")),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _measureService.AskGptAsync(systemMessage, userPrompt);

            // Assert
            Assert.That(result, Is.EqualTo(expectedResponse));
        }

        [Test]
        public async Task AskGptAsync_WhenHttpRequestFails_ThrowsHttpRequestException()
        {
            // Arrange
            var systemMessage = "Test system message";
            var userPrompt = "Test user prompt";
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.InternalServerError,
                Content = new StringContent("Internal Server Error")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _measureService.AskGptAsync(systemMessage, userPrompt));
        }

        [Test]
        public async Task GenerateAlertsAsync_WhenSuccessful_ReturnsAlertsList()
        {
            // Arrange
            var patientInfo = "Patient: John Doe, Age: 65, Diabetic, Hypertensive";
            var expectedAlerts = new List<string>
            {
                "Blood pressure check due",
                "HbA1c test recommended",
                "Annual eye exam needed"
            };

            var responseContent = JsonSerializer.Serialize(expectedAlerts);
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(responseContent, Encoding.UTF8, "application/json")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _measureService.GenerateAlertsAsync(patientInfo);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(3));
            Assert.That(result[0], Is.EqualTo("Blood pressure check due"));
        }

        [Test]
        public async Task GenerateAlertsAsync_WhenHttpRequestFails_ThrowsHttpRequestException()
        {
            // Arrange
            var patientInfo = "Test patient info";
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad Request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _measureService.GenerateAlertsAsync(patientInfo));
        }

        [Test]
        public async Task GenerateAlertsAsync_WhenEmptyResponse_ReturnsEmptyList()
        {
            // Arrange
            var patientInfo = "Patient with no alerts";
            var expectedAlerts = new List<string>();

            var responseContent = JsonSerializer.Serialize(expectedAlerts);
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(responseContent, Encoding.UTF8, "application/json")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _measureService.GenerateAlertsAsync(patientInfo);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(0));
        }
    }
}



