using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using Moq;
using Moq.Protected;
using NUnit.Framework;
using TeyaUIModels.Model;
using TeyaUIViewModels.ViewModel;
using TeyaUIViewModels.TeyaUIViewModelResources;
using System.Net.Http.Json;
using System.Text;

namespace TeyaWebClientUIViewModelTestCases
{
    [TestFixture]
    public class ExaminationServiceTests
    {
        private Mock<HttpMessageHandler> _mockHttpMessageHandler;
        private HttpClient _httpClient;

        private Mock<IStringLocalizer<TeyaUIViewModelsStrings>> _mockLocalizer;
        private Mock<ITokenService> _mockTokenService;
        private ExaminationService _examinationService;
        private readonly string _baseUrl = "http://test-api.com";
        private readonly string _accessToken = "test-token";

        [SetUp]
        public void Setup()
        {
            // Setup environment variable
            Environment.SetEnvironmentVariable("EncounterNotesURL", _baseUrl);

            // Setup mock HTTP handler
            _mockHttpMessageHandler = new Mock<HttpMessageHandler>();

            // Setup HttpClient with mock handler
            _httpClient = new HttpClient(_mockHttpMessageHandler.Object)
            {
                BaseAddress = new Uri(_baseUrl)
            };

            // Setup mock localizer
            _mockLocalizer = new Mock<IStringLocalizer<TeyaUIViewModelsStrings>>();
            _mockLocalizer.Setup(l => l["AddressRetrievalFailure"])
                .Returns(new LocalizedString("AddressRetrievalFailure", "Failed to retrieve address"));
            _mockLocalizer.Setup(l => l["DatabaseError"])
                .Returns(new LocalizedString("DatabaseError", "Database error"));

            // Setup mock token service
            _mockTokenService = new Mock<ITokenService>();
            _mockTokenService.Setup(t => t.AccessToken).Returns(_accessToken);

            // Create ExaminationService with mocked dependencies
            _examinationService = new ExaminationService(_httpClient, _mockLocalizer.Object, _mockTokenService.Object);
        }

        [TearDown]
        public void TearDown()
        {
            _httpClient.Dispose();
            Environment.SetEnvironmentVariable("EncounterNotesURL", null);
        }

        [Test]
        public async Task GetExaminationByPatientId_WhenSuccessful_ReturnsExaminations()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var expectedExaminations = new List<Examination>
            {
                new Examination
                {
                    ExaminationId = Guid.NewGuid(),
                    PatientId = patientId,
                    OrganizationId = orgId,
                    PCPId = Guid.NewGuid(),
                    CreatedDate = DateTime.Now.AddDays(-30),
                    UpdateDate = DateTime.Now.AddDays(-30),
                    GeneralDescription = "Regular checkup",
                    IsActive = true,
                    Subscription = subscription
                },
                new Examination
                {
                    ExaminationId = Guid.NewGuid(),
                    PatientId = patientId,
                    OrganizationId = orgId,
                    PCPId = Guid.NewGuid(),
                    CreatedDate = DateTime.Now.AddDays(-15),
                    UpdateDate = DateTime.Now.AddDays(-15),
                    GeneralDescription = "Follow-up test",
                    IsActive = false,
                    Subscription = subscription
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedExaminations)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _examinationService.GetExaminationByPatientId(patientId, orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(2));
            Assert.That(result[0].GeneralDescription, Is.EqualTo("Regular checkup"));
            Assert.That(result[1].GeneralDescription, Is.EqualTo("Follow-up test"));
        }

        [Test]
        public void GetExaminationByPatientId_WhenHttpRequestFails_ThrowsException()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _examinationService.GetExaminationByPatientId(patientId, orgId, subscription));

            Assert.That(exception.Message, Is.EqualTo("Failed to retrieve address"));
        }

        [Test]
        public async Task GetExaminationByPatientIdAsyncAndIsActive_WhenSuccessful_ReturnsActiveExaminations()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var expectedExaminations = new List<Examination>
            {
                new Examination
                {
                    ExaminationId = Guid.NewGuid(),
                    PatientId = patientId,
                    OrganizationId = orgId,
                    PCPId = Guid.NewGuid(),
                    CreatedDate = DateTime.Now.AddDays(-30),
                    UpdateDate = DateTime.Now.AddDays(-30),
                    GeneralDescription = "Regular checkup",
                    IsActive = true,
                    Subscription = subscription
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedExaminations)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _examinationService.GetExaminationByPatientIdAsyncAndIsActive(patientId, orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(1));
            Assert.That(result[0].GeneralDescription, Is.EqualTo("Regular checkup"));
            Assert.That(result[0].IsActive, Is.True);
        }

        [Test]
        public void GetExaminationByPatientIdAsyncAndIsActive_WhenHttpRequestFails_ThrowsException()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _examinationService.GetExaminationByPatientIdAsyncAndIsActive(patientId, orgId, subscription));

            Assert.That(exception.Message, Is.EqualTo("Failed to retrieve address"));
        }

        [Test]
        public async Task AddExaminationAsync_WhenSuccessful_CompletesWithoutException()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var examinations = new List<Examination>
            {
                new Examination
                {
                    ExaminationId = Guid.NewGuid(),
                    PatientId = patientId,
                    OrganizationId = orgId,
                    PCPId = Guid.NewGuid(),
                    CreatedDate = DateTime.Now.AddDays(-30),
                    UpdateDate = DateTime.Now.AddDays(-30),
                    GeneralDescription = "Regular checkup",
                    IsActive = true,
                    Subscription = subscription
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            // No exception should be thrown
            await _examinationService.AddExaminationAsync(examinations, orgId, subscription);
        }

        [Test]
        public void AddExaminationAsync_WhenHttpRequestFails_ThrowsException()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var examinations = new List<Examination>
            {
                new Examination
                {
                    ExaminationId = Guid.NewGuid(),
                    PatientId = patientId,
                    OrganizationId = orgId,
                    PCPId = Guid.NewGuid(),
                    CreatedDate = DateTime.Now.AddDays(-30),
                    UpdateDate = DateTime.Now.AddDays(-30),
                    GeneralDescription = "Regular checkup",
                    IsActive = true,
                    Subscription = subscription
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _examinationService.AddExaminationAsync(examinations, orgId, subscription));
        }

        [Test]
        public async Task UpdateExaminationListAsync_WhenSuccessful_CompletesWithoutException()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var examinations = new List<Examination>
            {
                new Examination
                {
                    ExaminationId = Guid.NewGuid(),
                    PatientId = patientId,
                    OrganizationId = orgId,
                    PCPId = Guid.NewGuid(),
                    CreatedDate = DateTime.Now.AddDays(-30),
                    UpdateDate = DateTime.Now.AddDays(-30),
                    GeneralDescription = "Updated notes",
                    IsActive = true,
                    Subscription = subscription
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            // No exception should be thrown
            await _examinationService.UpdateExaminationListAsync(examinations, orgId, subscription);
        }

        [Test]
        public void UpdateExaminationListAsync_WhenHttpRequestFails_ThrowsException()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var examinations = new List<Examination>
            {
                new Examination
                {
                    ExaminationId = Guid.NewGuid(),
                    PatientId = patientId,
                    OrganizationId = orgId,
                    PCPId = Guid.NewGuid(),
                    CreatedDate = DateTime.Now.AddDays(-30),
                    UpdateDate = DateTime.Now.AddDays(-30),
                    GeneralDescription = "Updated notes",
                    IsActive = true,
                    Subscription = subscription
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _examinationService.UpdateExaminationListAsync(examinations, orgId, subscription));
        }
    }
}



