using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Moq;
using Moq.Protected;
using NUnit.Framework;
using TeyaUIModels.Model;
using TeyaUIViewModels.ViewModel;
using TeyaUIModels.ViewModel;
using TeyaUIViewModels.TeyaUIViewModelResources;
using System.Net.Http.Json;
using System.Text;

namespace TeyaWebClientUIViewModelTestCases
{
    [TestFixture]
    public class DiagnosticImagingServiceTests
    {
        private Mock<HttpMessageHandler> _mockHttpMessageHandler;
        private HttpClient _httpClient;
        private Mock<IConfiguration> _mockConfiguration;
        private Mock<IStringLocalizer<DiagnosticImagingService>> _mockLocalizer;
        private Mock<ILogger<DiagnosticImagingService>> _mockLogger;
        private Mock<ITokenService> _mockTokenService;
        private DiagnosticImagingService _diagnosticImagingService;
        private readonly string _baseUrl = "http://test-api.com";
        private readonly string _accessToken = "test-token";

        [SetUp]
        public void Setup()
        {
            // Setup environment variable
            Environment.SetEnvironmentVariable("EncounterNotesURL", _baseUrl);

            // Setup mock HTTP handler
            _mockHttpMessageHandler = new Mock<HttpMessageHandler>();

            // Setup HttpClient with mock handler
            _httpClient = new HttpClient(_mockHttpMessageHandler.Object)
            {
                BaseAddress = new Uri(_baseUrl)
            };

            // Setup mock configuration
            _mockConfiguration = new Mock<IConfiguration>();

            // Setup mock localizer
            _mockLocalizer = new Mock<IStringLocalizer<DiagnosticImagingService>>();
            _mockLocalizer.Setup(l => l["AddressRetrievalFailure"])
                .Returns(new LocalizedString("AddressRetrievalFailure", "Failed to retrieve address"));
            _mockLocalizer.Setup(l => l["DatabaseError"])
                .Returns(new LocalizedString("DatabaseError", "Database error"));

            // Setup mock logger
            _mockLogger = new Mock<ILogger<DiagnosticImagingService>>();

            // Setup mock token service
            _mockTokenService = new Mock<ITokenService>();
            _mockTokenService.Setup(t => t.AccessToken).Returns(_accessToken);

            // Create DiagnosticImagingService with mocked dependencies
            _diagnosticImagingService = new DiagnosticImagingService(_httpClient, _mockConfiguration.Object, _mockLocalizer.Object, _mockLogger.Object, _mockTokenService.Object);
        }

        [TearDown]
        public void TearDown()
        {
            _httpClient.Dispose();
            Environment.SetEnvironmentVariable("EncounterNotesURL", null);
        }

        [Test]
        public async Task GetDiagnosticImagingAsync_WhenSuccessful_ReturnsDiagnosticImages()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var expectedImages = new List<DiagnosticImage>
            {
                new DiagnosticImage
                {
                    RecordID = Guid.NewGuid(),
                    PatientId = patientId,
                    DiCompany = "Test Company",
                    Type = "X-Ray",
                    Lookup = "Chest",
                    OrderName = "Chest X-Ray",
                    StartsWith = "C",
                    IsActive = true
                },
                new DiagnosticImage
                {
                    RecordID = Guid.NewGuid(),
                    PatientId = patientId,
                    DiCompany = "Test Company",
                    Type = "MRI",
                    Lookup = "Brain",
                    OrderName = "Brain MRI",
                    StartsWith = "B",
                    IsActive = false
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedImages)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _diagnosticImagingService.GetDiagnosticImagingAsync(patientId, orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(2));
            Assert.That(result[0].DiCompany, Is.EqualTo("Test Company"));
            Assert.That(result[0].Type, Is.EqualTo("X-Ray"));
            Assert.That(result[1].Type, Is.EqualTo("MRI"));
        }

        [Test]
        public async Task GetDiagnosticImagingAsync_WhenHttpRequestFails_ReturnsEmptyList()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _diagnosticImagingService.GetDiagnosticImagingAsync(patientId, orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(0));
        }

        [Test]
        public async Task GetDiagnosticImagingByIdAsyncAndIsActive_WhenSuccessful_ReturnsActiveDiagnosticImages()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var expectedImages = new List<DiagnosticImage>
            {
                new DiagnosticImage
                {
                    RecordID = Guid.NewGuid(),
                    PatientId = patientId,
                    DiCompany = "Test Company",
                    Type = "X-Ray",
                    Lookup = "Chest",
                    OrderName = "Chest X-Ray",
                    StartsWith = "C",
                    IsActive = true
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedImages)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _diagnosticImagingService.GetDiagnosticImagingByIdAsyncAndIsActive(patientId, orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(1));
            Assert.That(result[0].DiCompany, Is.EqualTo("Test Company"));
            Assert.That(result[0].Type, Is.EqualTo("X-Ray"));
            Assert.That(result[0].IsActive, Is.True);
        }

        [Test]
        public async Task GetDiagnosticImagingByIdAsyncAndIsActive_WhenHttpRequestFails_ReturnsEmptyList()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _diagnosticImagingService.GetDiagnosticImagingByIdAsyncAndIsActive(patientId, orgId, subscription);

            // Assert
            Assert.Multiple(() =>
            {
                Assert.That(result, Is.Not.Null);
                Assert.That(result, Has.Count.EqualTo(0));
            });
        }

        [Test]
        public async Task CreateDiagnosticImagingAsync_WhenSuccessful_CompletesWithoutException()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var diagnosticImages = new List<DiagnosticImage>
            {
                new()
                {
                    RecordID = Guid.NewGuid(),
                    PatientId = patientId,
                    DiCompany = "Test Company",
                    Type = "X-Ray",
                    Lookup = "Chest",
                    OrderName = "Chest X-Ray",
                    StartsWith = "C",
                    IsActive = true
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            // No exception should be thrown
            await _diagnosticImagingService.CreateDiagnosticImagingAsync(diagnosticImages, orgId, subscription);
        }

        [Test]
        public async Task CreateDiagnosticImagingAsync_WhenHttpRequestFails_CompletesWithoutException()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var diagnosticImages = new List<DiagnosticImage>
            {
                new DiagnosticImage
                {
                    RecordID = Guid.NewGuid(),
                    PatientId = patientId,
                    DiCompany = "Test Company",
                    Type = "X-Ray",
                    Lookup = "Chest",
                    OrderName = "Chest X-Ray",
                    StartsWith = "C",
                    IsActive = true
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            // Service handles errors gracefully, no exception should be thrown
            await _diagnosticImagingService.CreateDiagnosticImagingAsync(diagnosticImages, orgId, subscription);
        }

        [Test]
        public async Task UpdateDiagnosticImagingAsync_WhenSuccessful_CompletesWithoutException()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var recordId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var diagnosticImage = new DiagnosticImage
            {
                RecordID = recordId,
                PatientId = patientId,
                DiCompany = "Updated Company",
                Type = "X-Ray",
                Lookup = "Chest",
                OrderName = "Chest X-Ray",
                StartsWith = "C",
                IsActive = true
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            // No exception should be thrown
            await _diagnosticImagingService.UpdateDiagnosticImagingAsync(diagnosticImage, orgId, subscription);
        }

        [Test]
        public async Task UpdateDiagnosticImagingAsync_WhenHttpRequestFails_CompletesWithoutException()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var recordId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var diagnosticImage = new DiagnosticImage
            {
                RecordID = recordId,
                PatientId = patientId,
                DiCompany = "Updated Company",
                Type = "X-Ray",
                Lookup = "Chest",
                OrderName = "Chest X-Ray",
                StartsWith = "C",
                IsActive = true
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            // Service handles errors gracefully, no exception should be thrown
            await _diagnosticImagingService.UpdateDiagnosticImagingAsync(diagnosticImage, orgId, subscription);
        }

        [Test]
        public async Task UpdateDiagnosticImagingList_WhenSuccessful_CompletesWithoutException()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var diagnosticImages = new List<DiagnosticImage>
            {
                new DiagnosticImage
                {
                    RecordID = Guid.NewGuid(),
                    PatientId = patientId,
                    DiCompany = "Updated Company",
                    Type = "X-Ray",
                    Lookup = "Chest",
                    OrderName = "Chest X-Ray",
                    StartsWith = "C",
                    IsActive = true
                },
                new DiagnosticImage
                {
                    RecordID = Guid.NewGuid(),
                    PatientId = patientId,
                    DiCompany = "Updated Company",
                    Type = "MRI",
                    Lookup = "Brain",
                    OrderName = "Brain MRI",
                    StartsWith = "B",
                    IsActive = true
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            // No exception should be thrown
            await _diagnosticImagingService.UpdateDiagnosticImagingList(diagnosticImages, orgId, subscription);
        }

        [Test]
        public async Task UpdateDiagnosticImagingList_WhenHttpRequestFails_CompletesWithoutException()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var diagnosticImages = new List<DiagnosticImage>
            {
                new DiagnosticImage
                {
                    RecordID = Guid.NewGuid(),
                    PatientId = patientId,
                    DiCompany = "Updated Company",
                    Type = "X-Ray",
                    Lookup = "Chest",
                    OrderName = "Chest X-Ray",
                    StartsWith = "C",
                    IsActive = true
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            // Service handles errors gracefully, no exception should be thrown
            await _diagnosticImagingService.UpdateDiagnosticImagingList(diagnosticImages, orgId, subscription);
        }

        [Test]
        public async Task DeleteDiagnosticImagingAsync_WhenSuccessful_CompletesWithoutException()
        {
            // Arrange
            var recordId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            // No exception should be thrown
            await _diagnosticImagingService.DeleteDiagnosticImagingAsync(recordId, orgId, subscription);
        }

        [Test]
        public async Task DeleteDiagnosticImagingAsync_WhenHttpRequestFails_CompletesWithoutException()
        {
            // Arrange
            var recordId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            // Service handles errors gracefully, no exception should be thrown
            await _diagnosticImagingService.DeleteDiagnosticImagingAsync(recordId, orgId, subscription);
        }
    }
}



