using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using Moq;
using Moq.Protected;
using NUnit.Framework;
using TeyaUIModels.Model;
using TeyaUIViewModels.ViewModel;
using TeyaUIViewModels.TeyaUIViewModelResources;
using System.Net.Http.Json;
using System.Text.Json;

namespace TeyaWebClientUIViewModelTestCases
{
    [TestFixture]
    public class SurgicalServiceTests
    {
        private Mock<HttpMessageHandler> _mockHttpMessageHandler;
        private HttpClient _httpClient;
        private Mock<IConfiguration> _mockConfiguration;
        private Mock<IStringLocalizer<TeyaUIViewModelsStrings>> _mockLocalizer;
        private Mock<ITokenService> _mockTokenService;
        private SurgicalService _surgicalService;
        private readonly string _baseUrl = "http://test-api.com";
        private readonly string _accessToken = "test-access-token";

        [SetUp]
        public void Setup()
        {
            // Setup environment variable
            Environment.SetEnvironmentVariable("EncounterNotesURL", _baseUrl);

            // Setup mock HTTP handler
            _mockHttpMessageHandler = new Mock<HttpMessageHandler>();

            // Setup HttpClient with mock handler
            _httpClient = new HttpClient(_mockHttpMessageHandler.Object)
            {
                BaseAddress = new Uri(_baseUrl)
            };

            // Setup mock configuration
            _mockConfiguration = new Mock<IConfiguration>();

            // Setup mock localizer
            _mockLocalizer = new Mock<IStringLocalizer<TeyaUIViewModelsStrings>>();
            _mockLocalizer.Setup(l => l["AddressRetrievalFailure"])
                .Returns(new LocalizedString("AddressRetrievalFailure", "Address retrieval failure"));
            _mockLocalizer.Setup(l => l["TasksRetrievalFailure"])
                .Returns(new LocalizedString("TasksRetrievalFailure", "Tasks retrieval failure"));

            // Setup mock token service
            _mockTokenService = new Mock<ITokenService>();
            _mockTokenService.Setup(t => t.AccessToken).Returns(_accessToken);

            // Create SurgicalService with mocked dependencies
            _surgicalService = new SurgicalService(_httpClient, _mockConfiguration.Object, _mockLocalizer.Object, _mockTokenService.Object);
        }

        [TearDown]
        public void TearDown()
        {
            _httpClient.Dispose();
            Environment.SetEnvironmentVariable("EncounterNotesURL", null);
        }

        [Test]
        public async Task GetSurgeriesByIdAsync_WhenSuccessful_ReturnsSurgeries()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var expectedSurgeries = new List<Surgical>
            {
                new Surgical
                {
                    SurgeryId = Guid.NewGuid(),
                    PatientId = patientId,
                    Surgery = "Appendectomy - Dr. Smith at General Hospital, no complications, routine appendectomy",
                    IsActive = true,
                    CreatedDate = DateTime.Now.AddDays(-30),
                    OrganizationId = Guid.NewGuid(),
                    PCPId = Guid.NewGuid(),
                    CreatedBy = Guid.NewGuid(),
                    UpdatedBy = Guid.NewGuid()
                },
                new Surgical
                {
                    SurgeryId = Guid.NewGuid(),
                    PatientId = patientId,
                    Surgery = "Gallbladder removal - Dr. Johnson at City Medical Center, minor bleeding, laparoscopic cholecystectomy",
                    IsActive = true,
                    CreatedDate = DateTime.Now.AddDays(-15),
                    OrganizationId = Guid.NewGuid(),
                    PCPId = Guid.NewGuid(),
                    CreatedBy = Guid.NewGuid(),
                    UpdatedBy = Guid.NewGuid()
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedSurgeries)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _surgicalService.GetSurgeriesByIdAsync(patientId, orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(expectedSurgeries.Count));
            Assert.That(result[0].SurgeryId, Is.EqualTo(expectedSurgeries[0].SurgeryId));
            Assert.That(result[0].PatientId, Is.EqualTo(expectedSurgeries[0].PatientId));
            Assert.That(result[0].Surgery, Is.EqualTo(expectedSurgeries[0].Surgery));
            Assert.That(result[0].IsActive, Is.EqualTo(expectedSurgeries[0].IsActive));
        }

        [Test]
        public void GetSurgeriesByIdAsync_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.NotFound,
                Content = new StringContent("Not found")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _surgicalService.GetSurgeriesByIdAsync(patientId, orgId, subscription));

            Assert.That(exception.Message, Is.EqualTo("Address retrieval failure"));
        }

        [Test]
        public async Task GetSurgeryByIdAsyncAndIsActive_WhenSuccessful_ReturnsActiveSurgeries()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var expectedSurgeries = new List<Surgical>
            {
                new Surgical
                {
                    SurgeryId = Guid.NewGuid(),
                    PatientId = patientId,
                    Surgery = "Active Surgery - Dr. Active at Active Hospital, no complications, active surgical procedure",
                    IsActive = true,
                    CreatedDate = DateTime.Now.AddDays(-10),
                    OrganizationId = Guid.NewGuid(),
                    PCPId = Guid.NewGuid(),
                    CreatedBy = Guid.NewGuid(),
                    UpdatedBy = Guid.NewGuid()
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedSurgeries)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _surgicalService.GetSurgeryByIdAsyncAndIsActive(patientId, orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(expectedSurgeries.Count));
            Assert.That(result[0].IsActive, Is.True);
            Assert.That(result[0].Surgery, Does.Contain("Active Surgery"));
        }

        [Test]
        public void GetSurgeryByIdAsyncAndIsActive_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _surgicalService.GetSurgeryByIdAsyncAndIsActive(patientId, orgId, subscription));

            Assert.That(exception.Message, Is.EqualTo("Address retrieval failure"));
        }

        [Test]
        public async Task AddSurgeryAsync_WhenSuccessful_CompletesSuccessfully()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;
            var surgeries = new List<Surgical>
            {
                new Surgical
                {
                    SurgeryId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    Surgery = "New Surgery - Dr. New at New Hospital, no complications, new surgical procedure",
                    IsActive = true,
                    CreatedDate = DateTime.Now,
                    OrganizationId = Guid.NewGuid(),
                    PCPId = Guid.NewGuid(),
                    CreatedBy = Guid.NewGuid(),
                    UpdatedBy = Guid.NewGuid()
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.Created,
                Content = new StringContent("Success")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.DoesNotThrowAsync(async () => await _surgicalService.AddSurgeryAsync(surgeries, orgId, subscription));
        }

        [Test]
        public void AddSurgeryAsync_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;
            var surgeries = new List<Surgical>
            {
                new Surgical
                {
                    SurgeryId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    Surgery = "Failed Surgery - Dr. Failed at Failed Hospital, no complications, failed surgical procedure",
                    IsActive = true,
                    CreatedDate = DateTime.Now,
                    OrganizationId = Guid.NewGuid(),
                    PCPId = Guid.NewGuid(),
                    CreatedBy = Guid.NewGuid(),
                    UpdatedBy = Guid.NewGuid()
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _surgicalService.AddSurgeryAsync(surgeries, orgId, subscription));

            Assert.That(exception.Message, Is.EqualTo("Tasks retrieval failure"));
        }

        [Test]
        public async Task UpdateSurgeryAsync_WhenSuccessful_CompletesSuccessfully()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;
            var surgery = new Surgical
            {
                SurgeryId = Guid.NewGuid(),
                PatientId = Guid.NewGuid(),
                Surgery = "Updated Surgery - Dr. Updated at Updated Hospital, minor complications, updated surgical procedure",
                IsActive = true,
                CreatedDate = DateTime.Now.AddDays(-30),
                OrganizationId = Guid.NewGuid(),
                PCPId = Guid.NewGuid(),
                CreatedBy = Guid.NewGuid(),
                UpdatedBy = Guid.NewGuid()
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent("Success")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.DoesNotThrowAsync(async () => await _surgicalService.UpdateSurgeryAsync(surgery, orgId, subscription));
        }

        [Test]
        public void UpdateSurgeryAsync_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;
            var surgery = new Surgical
            {
                SurgeryId = Guid.NewGuid(),
                PatientId = Guid.NewGuid(),
                Surgery = "Failed Update Surgery - Dr. Failed Update at Failed Update Hospital, major complications, failed update surgical procedure",
                IsActive = true,
                CreatedDate = DateTime.Now.AddDays(-30),
                OrganizationId = Guid.NewGuid(),
                PCPId = Guid.NewGuid(),
                CreatedBy = Guid.NewGuid(),
                UpdatedBy = Guid.NewGuid()
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _surgicalService.UpdateSurgeryAsync(surgery, orgId, subscription));

            Assert.That(exception, Is.Not.Null);
        }

        [Test]
        public async Task DeletesurgeryAsync_WhenSuccessful_CompletesSuccessfully()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;
            var surgery = new Surgical
            {
                SurgeryId = Guid.NewGuid(),
                PatientId = Guid.NewGuid(),
                Surgery = "Surgery to Delete - Dr. Delete at Delete Hospital, no complications, surgery to be deleted",
                IsActive = false,
                CreatedDate = DateTime.Now.AddDays(-100),
                OrganizationId = Guid.NewGuid(),
                PCPId = Guid.NewGuid(),
                CreatedBy = Guid.NewGuid(),
                UpdatedBy = Guid.NewGuid()
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent("Success")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.DoesNotThrowAsync(async () => await _surgicalService.DeletesurgeryAsync(surgery, orgId, subscription));
        }

        [Test]
        public void DeletesurgeryAsync_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;
            var surgery = new Surgical
            {
                SurgeryId = Guid.NewGuid(),
                PatientId = Guid.NewGuid(),
                Surgery = "Failed Delete Surgery - Dr. Failed Delete at Failed Delete Hospital, no complications, failed delete surgery",
                IsActive = false,
                CreatedDate = DateTime.Now.AddDays(-100),
                OrganizationId = Guid.NewGuid(),
                PCPId = Guid.NewGuid(),
                CreatedBy = Guid.NewGuid(),
                UpdatedBy = Guid.NewGuid()
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _surgicalService.DeletesurgeryAsync(surgery, orgId, subscription));

            Assert.That(exception.Message, Is.EqualTo("Tasks retrieval failure"));
        }

        [Test]
        public async Task UpdateSurgeryListAsync_WhenSuccessful_CompletesSuccessfully()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;
            var surgeries = new List<Surgical>
            {
                new Surgical
                {
                    SurgeryId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    Surgery = "Batch Update Surgery 1 - Dr. Batch 1 at Batch Hospital 1, no complications, batch update surgery 1",
                    IsActive = true,
                    CreatedDate = DateTime.Now.AddDays(-20),
                    OrganizationId = Guid.NewGuid(),
                    PCPId = Guid.NewGuid(),
                    CreatedBy = Guid.NewGuid(),
                    UpdatedBy = Guid.NewGuid()
                },
                new Surgical
                {
                    SurgeryId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    Surgery = "Batch Update Surgery 2 - Dr. Batch 2 at Batch Hospital 2, minor complications, batch update surgery 2",
                    IsActive = true,
                    CreatedDate = DateTime.Now.AddDays(-10),
                    OrganizationId = Guid.NewGuid(),
                    PCPId = Guid.NewGuid(),
                    CreatedBy = Guid.NewGuid(),
                    UpdatedBy = Guid.NewGuid()
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent("Success")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.DoesNotThrowAsync(async () => await _surgicalService.UpdateSurgeryListAsync(surgeries, orgId, subscription));
        }

        [Test]
        public void UpdateSurgeryListAsync_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;
            var surgeries = new List<Surgical>
            {
                new Surgical
                {
                    SurgeryId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    Surgery = "Failed Batch Update Surgery - Dr. Failed Batch at Failed Batch Hospital, major complications, failed batch update surgery",
                    IsActive = true,
                    CreatedDate = DateTime.Now.AddDays(-20),
                    OrganizationId = Guid.NewGuid(),
                    PCPId = Guid.NewGuid(),
                    CreatedBy = Guid.NewGuid(),
                    UpdatedBy = Guid.NewGuid()
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _surgicalService.UpdateSurgeryListAsync(surgeries, orgId, subscription));

            Assert.That(exception.Message, Is.EqualTo("Tasks retrieval failure"));
        }

        [Test]
        public async Task GetSurgeriesByIdAsync_WhenEmptyResponse_ReturnsEmptyList()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var expectedSurgeries = new List<Surgical>();

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedSurgeries)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _surgicalService.GetSurgeriesByIdAsync(patientId, orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(0));
        }

        [Test]
        public void GetSurgeriesByIdAsync_WhenExceptionThrown_PropagatesException()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var expectedException = new HttpRequestException("Test exception");

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ThrowsAsync(expectedException);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _surgicalService.GetSurgeriesByIdAsync(patientId, orgId, subscription));

            Assert.That(exception.Message, Is.EqualTo("Test exception"));
        }

        [Test]
        public async Task GetSurgeryByIdAsyncAndIsActive_WhenEmptyResponse_ReturnsEmptyList()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var expectedSurgeries = new List<Surgical>();

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedSurgeries)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _surgicalService.GetSurgeryByIdAsyncAndIsActive(patientId, orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(0));
        }
    }
}



