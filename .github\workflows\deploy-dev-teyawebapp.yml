name: <PERSON><PERSON>-<PERSON>ppser<PERSON>-<PERSON><PERSON><PERSON>-Dev

on:
  workflow_dispatch:
  schedule:
    # Runs at 00:00 UTC (midnight) every day
    - cron: '0 0 * * *'

env:
  APP_SERVICE_NAME: Teya-Webapp-Dev
  RESOURCE_GROUP: teyahealth-rg-dev-eastus-001
  CONTAINER_REGISTRY_LOGIN_SERVER: teyahealthdev.azurecr.io
  DOCKER_FILE_PATH: Clients/TeyaWebClient/TeyaWebApp/TeyaWebApp/Dockerfile
  PROJECT_NAME_FOR_DOCKER: teyawebapp

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest

    steps:
      # ✅ Step 1: Checkout source code
      - name: Checkout code
        uses: actions/checkout@v4

      # ✅ Step 2: Set up Docker Buildx
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      # ✅ Step 3: Log in to container registry
      - name: Log in to container registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.CONTAINER_REGISTRY_LOGIN_SERVER }}
          username: <PERSON><PERSON><PERSON><PERSON><PERSON>Dev
          password: ****************************************************

      # ✅ Step 4: Build and push Docker image to registry
      - name: Build and push container image
        uses: docker/build-push-action@v5
        with:
          push: true
          tags: ${{ env.CONTAINER_REGISTRY_LOGIN_SERVER }}/${{ env.PROJECT_NAME_FOR_DOCKER }}:${{ github.sha }}
          file: ${{ env.DOCKER_FILE_PATH }}

      # ✅ Step 5: Azure Login using SP credentials
      - name: Azure Login
        uses: azure/login@v1
        with:
          creds: ${{ secrets.DEVREPOSECRET }}

      # ✅ Step 6: Set App Settings with Key Vault References
      - name: Set App Settings with Key Vault References
        run: |
          az webapp config appsettings set \
            --name ${{ env.APP_SERVICE_NAME }} \
            --resource-group ${{ env.RESOURCE_GROUP }} \
            --settings \
              KEYVAULT__URLS='@Microsoft.KeyVault(SecretUri=https://teyawebappvault-dev.vault.azure.net/secrets/keyvault-urls/bcfedda1d6d14293816c00807aefc2c6)' \
              KEYVAULT__TENANTID='@Microsoft.KeyVault(SecretUri=https://commonvault-dev.vault.azure.net/secrets/keyvault-tenantid/fb43f0f90b5941a28988a5d1d2f973bb)' \
              KEYVAULT__CLIENTID='@Microsoft.KeyVault(SecretUri=https://commonvault-dev.vault.azure.net/secrets/keyvault-clientid/903514adfd5f42b69e0080384ecf5aff)' \
              KEYVAULT__CLIENTSECRET='@Microsoft.KeyVault(SecretUri=https://commonvault-dev.vault.azure.net/secrets/keyvault-clientsecret/913c55db16a94b1981db54965e0096ca)' \
              ENVIRONMENT_KEY='@Microsoft.KeyVault(SecretUri=https://teyawebappvault-dev.vault.azure.net/secrets/environment-key/03447a630df641f5afaab9ece607840e)'

      # ✅ Step 7: Deploy Docker image to Azure App Service (Web App for Containers)
      - name: Deploy Docker image to Azure App Service
        run: |
          az webapp config container set \
            --name ${{ env.APP_SERVICE_NAME }} \
            --resource-group ${{ env.RESOURCE_GROUP }} \
            --docker-custom-image-name ${{ env.CONTAINER_REGISTRY_LOGIN_SERVER }}/${{ env.PROJECT_NAME_FOR_DOCKER }}:${{ github.sha }} \
            --docker-registry-server-url https://${{ env.CONTAINER_REGISTRY_LOGIN_SERVER }} \
            --docker-registry-server-user TeyaHealthDev \
            --docker-registry-server-password ****************************************************

      # ✅ Step 8: Azure Logout
      - name: Logout
        run: az logout
