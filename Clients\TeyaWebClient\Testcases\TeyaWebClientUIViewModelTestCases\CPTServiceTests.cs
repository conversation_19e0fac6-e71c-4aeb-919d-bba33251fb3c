using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using Moq;
using Moq.Protected;
using NUnit.Framework;
using TeyaUIModels.Model;
using TeyaUIViewModels.ViewModel;
using TeyaUIViewModels.TeyaUIViewModelResources;
using System.Net.Http.Json;

namespace TeyaWebClientUIViewModelTestCases
{
    [TestFixture]
    public class CPTServiceTests
    {
        private Mock<HttpMessageHandler> _mockHttpMessageHandler;
        private HttpClient _httpClient;
        private Mock<IConfiguration> _mockConfiguration;
        private Mock<IStringLocalizer<TeyaUIViewModelsStrings>> _mockLocalizer;
        private Mock<ITokenService> _mockTokenService;
        private CPTService _cptService;
        private readonly string _baseUrl = "http://test-api.com";
        private readonly string _accessToken = "test-token";

        [SetUp]
        public void Setup()
        {
            // Setup environment variable
            Environment.SetEnvironmentVariable("EncounterNotesURL", _baseUrl);

            // Setup mock HTTP handler
            _mockHttpMessageHandler = new Mock<HttpMessageHandler>();

            // Setup HttpClient with mock handler
            _httpClient = new HttpClient(_mockHttpMessageHandler.Object)
            {
                BaseAddress = new Uri(_baseUrl)
            };

            // Setup mock configuration
            _mockConfiguration = new Mock<IConfiguration>();

            // Setup mock localizer
            _mockLocalizer = new Mock<IStringLocalizer<TeyaUIViewModelsStrings>>();
            _mockLocalizer.Setup(l => l["CPTCodeRetrievalFailure"])
                .Returns(new LocalizedString("CPTCodeRetrievalFailure", "Failed to retrieve CPT codes"));

            // Setup mock token service
            _mockTokenService = new Mock<ITokenService>();
            _mockTokenService.Setup(t => t.AccessToken).Returns(_accessToken);

            // Create CPTService with mocked dependencies
            _cptService = new CPTService(_httpClient, _mockConfiguration.Object, _mockLocalizer.Object, _mockTokenService.Object);
        }

        [TearDown]
        public void TearDown()
        {
            _httpClient.Dispose();
            Environment.SetEnvironmentVariable("EncounterNotesURL", null);
        }

        [Test]
        public async Task GetAllCPTCodesAsync_WhenSuccessful_ReturnsCPTCodes()
        {
            // Arrange
            var expectedCPTCodes = new List<CPT>
            {
                new CPT
                {
                    CPTCode = "99201",
                    Description = "Office or other outpatient visit for the evaluation and management of a new patient"
                },
                new CPT
                {
                    CPTCode = "99202",
                    Description = "Office or other outpatient visit for the evaluation and management of a new patient"
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedCPTCodes)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _cptService.GetAllCPTCodesAsync();

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(2));
            Assert.That(result[0].CPTCode, Is.EqualTo("99201"));
            Assert.That(result[1].CPTCode, Is.EqualTo("99202"));
        }

        [Test]
        public void GetAllCPTCodesAsync_WhenHttpRequestFails_ThrowsException()
        {
            // Arrange
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _cptService.GetAllCPTCodesAsync());

            Assert.That(exception.Message, Is.EqualTo("Failed to retrieve CPT codes"));
        }

        [Test]
        public void GetAllCPTCodesAsync_WhenExceptionOccurs_ThrowsException()
        {
            // Arrange
            var expectedException = new HttpRequestException("Test exception");

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ThrowsAsync(expectedException);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _cptService.GetAllCPTCodesAsync());

            Assert.That(exception.Message, Is.EqualTo("Test exception"));
        }
    }
}



