using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using Moq;
using Moq.Protected;
using NUnit.Framework;
using TeyaUIModels.Model;
using TeyaWebApp.Services;
using System.Net.Http.Json;
using System.Text.Json;
using TeyaUIViewModels.ViewModel;
using System.Linq;

namespace TeyaWebClientUIViewModelTestCases
{
    [TestFixture]
    public class PageRoleMappingServiceTests
    {
        private Mock<HttpMessageHandler> _mockHttpMessageHandler;
        private HttpClient _httpClient;
        private Mock<ILogger<PageRoleMappingService>> _mockLogger;
        private Mock<IStringLocalizer<PageRoleMappingService>> _mockLocalizer;
        private Mock<ITokenService> _mockTokenService;
        private PageRoleMappingService _pageRoleMappingService;
        private readonly string _baseUrl = "http://test-api.com";
        private readonly string _accessToken = "test-access-token";

        [SetUp]
        public void Setup()
        {
            // Setup environment variable
            Environment.SetEnvironmentVariable("MemberServiceURL", _baseUrl);

            // Setup mock HTTP handler
            _mockHttpMessageHandler = new Mock<HttpMessageHandler>();

            // Setup HttpClient with mock handler
            _httpClient = new HttpClient(_mockHttpMessageHandler.Object)
            {
                BaseAddress = new Uri(_baseUrl)
            };

            // Setup mock logger
            _mockLogger = new Mock<ILogger<PageRoleMappingService>>();

            // Setup mock localizer
            _mockLocalizer = new Mock<IStringLocalizer<PageRoleMappingService>>();
            _mockLocalizer.Setup(l => l["ErrorFetchingAllPageRoleMappings"])
                .Returns(new LocalizedString("ErrorFetchingAllPageRoleMappings", "Error fetching all page role mappings"));
            _mockLocalizer.Setup(l => l["ErrorFetchingPageRoleMappingById"])
                .Returns(new LocalizedString("ErrorFetchingPageRoleMappingById", "Error fetching page role mapping by ID"));
            _mockLocalizer.Setup(l => l["GetByIdFailed"])
                .Returns(new LocalizedString("GetByIdFailed", "Get by ID failed"));
            _mockLocalizer.Setup(l => l["ErrorAddingPageRoleMapping"])
                .Returns(new LocalizedString("ErrorAddingPageRoleMapping", "Error adding page role mapping"));
            _mockLocalizer.Setup(l => l["AddPageRoleMappingFailed"])
                .Returns(new LocalizedString("AddPageRoleMappingFailed", "Add page role mapping failed"));
            _mockLocalizer.Setup(l => l["ErrorUpdatingPageRoleMapping"])
                .Returns(new LocalizedString("ErrorUpdatingPageRoleMapping", "Error updating page role mapping"));
            _mockLocalizer.Setup(l => l["PageRoleMappingUpdateFailed"])
                .Returns(new LocalizedString("PageRoleMappingUpdateFailed", "Page role mapping update failed"));
            _mockLocalizer.Setup(l => l["PageRoleMappingUpdatedSuccessfully"])
                .Returns(new LocalizedString("PageRoleMappingUpdatedSuccessfully", "Page role mapping updated successfully"));
            _mockLocalizer.Setup(l => l["ErrorDeletingPageRoleMapping"])
                .Returns(new LocalizedString("ErrorDeletingPageRoleMapping", "Error deleting page role mapping"));
            _mockLocalizer.Setup(l => l["PageRoleMappingDeletionFailed"])
                .Returns(new LocalizedString("PageRoleMappingDeletionFailed", "Page role mapping deletion failed"));
            _mockLocalizer.Setup(l => l["PageRoleMappingDeletedSuccessfully"])
                .Returns(new LocalizedString("PageRoleMappingDeletedSuccessfully", "Page role mapping deleted successfully"));
            _mockLocalizer.Setup(l => l["ErrorFetchingPagesByRoleId"])
                .Returns(new LocalizedString("ErrorFetchingPagesByRoleId", "Error fetching pages by role ID"));
            _mockLocalizer.Setup(l => l["GetPagesByRoleIdFailed"])
                .Returns(new LocalizedString("GetPagesByRoleIdFailed", "Get pages by role ID failed"));
            _mockLocalizer.Setup(l => l["ErrorFetchingRoles"])
                .Returns(new LocalizedString("ErrorFetchingRoles", "Error fetching roles"));
            _mockLocalizer.Setup(l => l["PagePathRequired"])
                .Returns(new LocalizedString("PagePathRequired", "Page path required"));
            _mockLocalizer.Setup(l => l["PagePathRequiredMessage"])
                .Returns(new LocalizedString("PagePathRequiredMessage", "Page path is required"));
            _mockLocalizer.Setup(l => l["RolesNotFoundMessage"])
                .Returns(new LocalizedString("RolesNotFoundMessage", "Roles not found"));
            _mockLocalizer.Setup(l => l["API Response: {ResponseData}"])
                .Returns(new LocalizedString("API Response: {ResponseData}", "API Response: {ResponseData}"));

            // Setup mock token service
            _mockTokenService = new Mock<ITokenService>();
            _mockTokenService.Setup(t => t.AccessToken).Returns(_accessToken);
            _mockTokenService.Setup(t => t.GetValidatedAccessTokenAsync()).ReturnsAsync(_accessToken);

            // Create PageRoleMappingService with mocked dependencies
            _pageRoleMappingService = new PageRoleMappingService(_httpClient, _mockLocalizer.Object, _mockLogger.Object, _mockTokenService.Object);
        }

        [TearDown]
        public void TearDown()
        {
            _httpClient.Dispose();
            Environment.SetEnvironmentVariable("MemberServiceURL", null);
        }

        [Test]
        public async Task GetPageRoleMappingsAsync_WhenSuccessful_ReturnsPageRoleMappings()
        {
            // Arrange
            var expectedMappings = new List<PageRoleMappingData>
            {
                new PageRoleMappingData
                {
                    Id = Guid.NewGuid(),
                    PagePath = "/test-path-1",
                    RoleId = Guid.NewGuid(),
                    RoleName = "Test Role 1",
                    IsActive = true
                },
                new PageRoleMappingData
                {
                    Id = Guid.NewGuid(),
                    PagePath = "/test-path-2",
                    RoleId = Guid.NewGuid(),
                    RoleName = "Test Role 2",
                    IsActive = true
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedMappings)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _pageRoleMappingService.GetPageRoleMappingsAsync();

            // Assert
            Assert.That(result, Is.Not.Null);
            var resultList = result.ToList();
            Assert.That(resultList.Count, Is.EqualTo(expectedMappings.Count));
            Assert.That(resultList[0].Id, Is.EqualTo(expectedMappings[0].Id));
            Assert.That(resultList[0].PagePath, Is.EqualTo(expectedMappings[0].PagePath));
            Assert.That(resultList[0].RoleId, Is.EqualTo(expectedMappings[0].RoleId));
            Assert.That(resultList[0].RoleName, Is.EqualTo(expectedMappings[0].RoleName));
            Assert.That(resultList[0].IsActive, Is.EqualTo(expectedMappings[0].IsActive));
        }

        [Test]
        public void GetPageRoleMappingsAsync_WhenExceptionThrown_PropagatesException()
        {
            // Arrange
            var expectedException = new HttpRequestException("Test exception");

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ThrowsAsync(expectedException);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _pageRoleMappingService.GetPageRoleMappingsAsync());

            Assert.That(exception.Message, Is.EqualTo(expectedException.Message));
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.Is<Exception>(ex => ex.Message == expectedException.Message),
                    It.Is<Func<It.IsAnyType, Exception, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }

        [Test]
        public async Task GetPageRoleMappingByIdAsync_WhenSuccessful_ReturnsPageRoleMapping()
        {
            // Arrange
            var mappingId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var expectedMapping = new PageRoleMappingData
            {
                Id = mappingId,
                PagePath = "/test-path",
                RoleId = Guid.NewGuid(),
                RoleName = "Test Role",
                IsActive = true
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedMapping)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _pageRoleMappingService.GetPageRoleMappingByIdAsync(mappingId, orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Id, Is.EqualTo(expectedMapping.Id));
            Assert.That(result.PagePath, Is.EqualTo(expectedMapping.PagePath));
            Assert.That(result.RoleId, Is.EqualTo(expectedMapping.RoleId));
            Assert.That(result.RoleName, Is.EqualTo(expectedMapping.RoleName));
            Assert.That(result.IsActive, Is.EqualTo(expectedMapping.IsActive));
        }

        [Test]
        public void GetPageRoleMappingByIdAsync_WhenNotFound_ThrowsHttpRequestException()
        {
            // Arrange
            var mappingId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.NotFound,
                Content = new StringContent("Not found")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _pageRoleMappingService.GetPageRoleMappingByIdAsync(mappingId, orgId, subscription));

            Assert.That(exception.Message, Is.EqualTo("Get by ID failed"));
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.IsAny<Exception>(),
                    It.Is<Func<It.IsAnyType, Exception, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }

        [Test]
        public async Task AddPageRoleMappingAsync_WhenSuccessful_CompletesSuccessfully()
        {
            // Arrange
            var pageRoleMapping = new PageRoleMappingData
            {
                Id = Guid.NewGuid(),
                PagePath = "/test-path",
                RoleId = Guid.NewGuid(),
                RoleName = "Test Role",
                IsActive = true
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.Created,
                Content = new StringContent("Success")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.DoesNotThrowAsync(async () => await _pageRoleMappingService.AddPageRoleMappingAsync(pageRoleMapping));

            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Information,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.IsAny<Exception>(),
                    It.Is<Func<It.IsAnyType, Exception, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }

        [Test]
        public void AddPageRoleMappingAsync_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var pageRoleMapping = new PageRoleMappingData
            {
                Id = Guid.NewGuid(),
                PagePath = "/test-path",
                RoleId = Guid.NewGuid(),
                RoleName = "Test Role",
                IsActive = true
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _pageRoleMappingService.AddPageRoleMappingAsync(pageRoleMapping));

            Assert.That(exception.Message, Is.EqualTo("Add page role mapping failed"));
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.IsAny<Exception>(),
                    It.Is<Func<It.IsAnyType, Exception, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }

        [Test]
        public async Task UpdatePageRoleMappingAsync_WhenSuccessful_CompletesSuccessfully()
        {
            // Arrange
            var pageRoleMapping = new PageRoleMappingData
            {
                Id = Guid.NewGuid(),
                PagePath = "/test-path-updated",
                RoleId = Guid.NewGuid(),
                RoleName = "Test Role Updated",
                IsActive = true
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent("Success")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.DoesNotThrowAsync(async () => await _pageRoleMappingService.UpdatePageRoleMappingAsync(pageRoleMapping));

            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Information,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.IsAny<Exception>(),
                    It.Is<Func<It.IsAnyType, Exception, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }

        [Test]
        public void UpdatePageRoleMappingAsync_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var pageRoleMapping = new PageRoleMappingData
            {
                Id = Guid.NewGuid(),
                PagePath = "/test-path-updated",
                RoleId = Guid.NewGuid(),
                RoleName = "Test Role Updated",
                IsActive = true
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _pageRoleMappingService.UpdatePageRoleMappingAsync(pageRoleMapping));

            Assert.That(exception.Message, Is.EqualTo("Page role mapping update failed"));
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.IsAny<Exception>(),
                    It.Is<Func<It.IsAnyType, Exception, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }

        [Test]
        public async Task DeletePageRoleMappingByIdAsync_WhenSuccessful_CompletesSuccessfully()
        {
            // Arrange
            var mappingId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.NoContent
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.DoesNotThrowAsync(async () => await _pageRoleMappingService.DeletePageRoleMappingByIdAsync(mappingId, orgId, subscription));

            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Information,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.IsAny<Exception>(),
                    It.Is<Func<It.IsAnyType, Exception, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }

        [Test]
        public void DeletePageRoleMappingByIdAsync_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var mappingId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.NotFound,
                Content = new StringContent("Not found")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _pageRoleMappingService.DeletePageRoleMappingByIdAsync(mappingId, orgId, subscription));

            Assert.That(exception.Message, Is.EqualTo("Page role mapping deletion failed"));
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.IsAny<Exception>(),
                    It.Is<Func<It.IsAnyType, Exception, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }

        [Test]
        public async Task GetPagesByRoleIdAsync_WhenSuccessful_ReturnsPageRoleMappings()
        {
            // Arrange
            var roleId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var expectedMappings = new List<PageRoleMappingData>
            {
                new PageRoleMappingData
                {
                    Id = Guid.NewGuid(),
                    PagePath = "/test-path-1",
                    RoleId = roleId,
                    RoleName = "Test Role",
                    IsActive = true
                },
                new PageRoleMappingData
                {
                    Id = Guid.NewGuid(),
                    PagePath = "/test-path-2",
                    RoleId = roleId,
                    RoleName = "Test Role",
                    IsActive = true
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedMappings)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _pageRoleMappingService.GetPagesByRoleIdAsync(roleId, orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            var resultList = result.ToList();
            Assert.That(resultList.Count, Is.EqualTo(expectedMappings.Count));
            Assert.That(resultList[0].RoleId, Is.EqualTo(roleId));
            Assert.That(resultList[1].RoleId, Is.EqualTo(roleId));
        }

        [Test]
        public void GetPagesByRoleIdAsync_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var roleId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.NotFound,
                Content = new StringContent("Not found")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _pageRoleMappingService.GetPagesByRoleIdAsync(roleId, orgId, subscription));

            Assert.That(exception.Message, Is.EqualTo("Get pages by role ID failed"));
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.IsAny<Exception>(),
                    It.Is<Func<It.IsAnyType, Exception, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }

        [Test]
        public async Task GetRolesByPagePathAsync_WhenSuccessful_ReturnsRoles()
        {
            // Arrange
            var pagePath = "/test-path";
            var organizationId = Guid.NewGuid();
            var subscription = true;
            var expectedRoles = new List<string> { "Admin", "User", "Manager" };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedRoles)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.Is<HttpRequestMessage>(req =>
                        req.RequestUri.ToString().Contains("/api/PageRoleMapping/roles-by-pagepath") &&
                        req.Headers.Authorization != null &&
                        req.Headers.Authorization.Scheme == "Bearer" &&
                        req.Headers.Authorization.Parameter == _accessToken),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _pageRoleMappingService.GetRolesByPagePathAsync(pagePath, organizationId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            var resultList = result.ToList();
            Assert.That(resultList.Count, Is.EqualTo(expectedRoles.Count));
            Assert.That(resultList, Is.EquivalentTo(expectedRoles));
        }

        [Test]
        public void GetRolesByPagePathAsync_WhenPagePathIsEmpty_ThrowsArgumentException()
        {
            // Arrange
            var pagePath = "";
            var organizationId = Guid.NewGuid();
            var subscription = true;

            // Act & Assert
            var exception = Assert.ThrowsAsync<ArgumentException>(async () =>
                await _pageRoleMappingService.GetRolesByPagePathAsync(pagePath, organizationId, subscription));

            Assert.That(exception.Message, Is.EqualTo("Page path is required"));
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Warning,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.IsAny<Exception>(),
                    It.Is<Func<It.IsAnyType, Exception, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }

        [Test]
        public void GetRolesByPagePathAsync_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var pagePath = "/test-path";
            var organizationId = Guid.NewGuid();
            var subscription = true;

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.NotFound,
                Content = new StringContent("Not found")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.Is<HttpRequestMessage>(req => req.RequestUri.ToString().Contains("/api/PageRoleMapping/roles-by-pagepath")),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _pageRoleMappingService.GetRolesByPagePathAsync(pagePath, organizationId, subscription));

            Assert.That(exception.Message, Is.EqualTo("Roles not found"));
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.IsAny<Exception>(),
                    It.Is<Func<It.IsAnyType, Exception, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }
    }
}



