using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Moq;
using Moq.Protected;
using NUnit.Framework;
using TeyaUIModels.Model;
using TeyaWebApp.Services;
using TeyaUIViewModels.TeyaUIViewModelResources;
using TeyaUIViewModels.ViewModel;

namespace TeyaWebClientUIViewModelTestCases
{
    [TestFixture]
    public class OrderSetServiceTests
    {
        private Mock<HttpMessageHandler> _mockHttpMessageHandler;
        private Mock<IConfiguration> _mockConfiguration;
        private Mock<IStringLocalizer<OrderSetService>> _mockLocalizer;
        private Mock<ITokenService> _mockTokenService;
        private Mock<ILogger<OrderSetService>> _mockLogger;
        private HttpClient _httpClient;
        private OrderSetService _orderSetService;

        private const string TestAlertServiceUrl = "https://test-alertservice.com";
        private const string TestAccessToken = "test-access-token";

        [SetUp]
        public void SetUp()
        {
            _mockHttpMessageHandler = new Mock<HttpMessageHandler>();
            _mockConfiguration = new Mock<IConfiguration>();
            _mockLocalizer = new Mock<IStringLocalizer<OrderSetService>>();
            _mockTokenService = new Mock<ITokenService>();
            _mockLogger = new Mock<ILogger<OrderSetService>>();

            _httpClient = new HttpClient(_mockHttpMessageHandler.Object);

            // Set up environment variable
            Environment.SetEnvironmentVariable("AlertsServiceURL", TestAlertServiceUrl);

            // Set up mock responses
            _mockTokenService.Setup(t => t.AccessToken).Returns(TestAccessToken);
            _mockTokenService.Setup(t => t.GetValidatedAccessTokenAsync()).ReturnsAsync(TestAccessToken);
            _mockLocalizer.Setup(l => l["CountryUpdatedSuccessfully"]).Returns(new LocalizedString("CountryUpdatedSuccessfully", "Order set updated successfully"));
            _mockLocalizer.Setup(l => l["ErrorFetchingAllOrderSets"]).Returns(new LocalizedString("ErrorFetchingAllOrderSets", "Error fetching all order sets"));

            _orderSetService = new OrderSetService(
                _httpClient,
                _mockLocalizer.Object,
                _mockLogger.Object,
                _mockTokenService.Object);
        }

        [TearDown]
        public void TearDown()
        {
            _httpClient?.Dispose();
            Environment.SetEnvironmentVariable("AlertsServiceURL", null);
        }

        private CompleteOrderSet CreateTestCompleteOrderSet()
        {
            return new CompleteOrderSet
            {
                Id = Guid.NewGuid(),
                orderSet = new OrderSet
                {
                    Id = Guid.NewGuid(),
                    OrderSetName = "Standard Lab Panel",
                    Diagnosis = "Routine Health Check",
                    OrganizationId = Guid.NewGuid(),
                    PCPId = Guid.NewGuid(),
                    CreatedBy = Guid.NewGuid(),
                    UpdatedBy = Guid.NewGuid(),
                    CreatedDate = DateTime.UtcNow,
                    UpdatedDate = DateTime.UtcNow,
                    isActive = true,
                    Subscription = false
                },
                orderSetLabTests = new List<OrderSetLabTests>(),
                orderSetActiveMedication = new List<OrderSetActiveMedication>(),
                orderSetImmunizationData = new List<OrderSetImmunizationData>(),
                orderSetProcedures = new List<OrderSetProcedures>(),
                orderSetDiagnosticImaging = new OrderSetDiagnosticImaging()
            };
        }

        [Test]
        public async Task GetAllOrderSetAsync_WhenSuccessful_ReturnsOrderSetList()
        {
            // Arrange
            var expectedOrderSets = new List<CompleteOrderSet>
            {
                CreateTestCompleteOrderSet(),
                CreateTestCompleteOrderSet()
            };

            var responseContent = JsonSerializer.Serialize(expectedOrderSets);
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(responseContent, Encoding.UTF8, "application/json")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _orderSetService.GetAllOrderSetAsync();

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count(), Is.EqualTo(2));
            Assert.That(result.First().orderSet.OrderSetName, Is.EqualTo("Standard Lab Panel"));
        }

        [Test]
        public async Task GetAllOrderSetAsync_WhenHttpRequestFails_ThrowsException()
        {
            // Arrange
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.InternalServerError,
                Content = new StringContent("Internal Server Error")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _orderSetService.GetAllOrderSetAsync());

            Assert.That(exception.Message, Does.Contain("Internal Server Error"));
        }

        [Test]
        public async Task GetOrderSetByIdAsync_WhenSuccessful_ReturnsOrderSet()
        {
            // Arrange
            var orderSetId = Guid.NewGuid();
            var expectedOrderSet = CreateTestCompleteOrderSet();
            expectedOrderSet.Id = orderSetId;

            var responseContent = JsonSerializer.Serialize(expectedOrderSet);
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(responseContent, Encoding.UTF8, "application/json")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _orderSetService.GetOrderSetByIdAsync(orderSetId);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Id, Is.EqualTo(orderSetId));
            Assert.That(result.orderSet.OrderSetName, Is.EqualTo("Standard Lab Panel"));
        }

        [Test]
        public async Task GetOrderSetByIdAsync_WhenNotFound_ThrowsException()
        {
            // Arrange
            var orderSetId = Guid.NewGuid();
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.NotFound,
                Content = new StringContent("Not Found")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _orderSetService.GetOrderSetByIdAsync(orderSetId));

            Assert.That(exception.Message, Does.Contain("Exception of type"));
        }

        [Test]
        public async Task GetOrderSetByNameAsync_WhenSuccessful_ReturnsOrderSetList()
        {
            // Arrange
            var orderSetName = "Standard Lab Panel";
            var expectedOrderSets = new List<CompleteOrderSet>
            {
                CreateTestCompleteOrderSet()
            };

            var responseContent = JsonSerializer.Serialize(expectedOrderSets);
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(responseContent, Encoding.UTF8, "application/json")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _orderSetService.GetOrderSetByNameAsync(orderSetName);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count(), Is.EqualTo(1));
            Assert.That(result.First().orderSet.OrderSetName, Is.EqualTo("Standard Lab Panel"));
        }

        [Test]
        public async Task AddOrderSetAsync_WhenSuccessful_CompletesWithoutException()
        {
            // Arrange
            var orderSet = CreateTestCompleteOrderSet();

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.Created
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            // No exception should be thrown
            await _orderSetService.AddOrderSetAsync(orderSet);
        }

        [Test]
        public async Task AddOrderSetAsync_WhenHttpRequestFails_ThrowsException()
        {
            // Arrange
            var orderSet = CreateTestCompleteOrderSet();
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad Request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _orderSetService.AddOrderSetAsync(orderSet));

            Assert.That(exception.Message, Does.Contain("Exception of type"));
        }

        [Test]
        public async Task UpdateOrderSetAsync_WhenSuccessful_CompletesWithoutException()
        {
            // Arrange
            var orderSet = CreateTestCompleteOrderSet();

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            // No exception should be thrown
            await _orderSetService.UpdateOrderSetAsync(orderSet);

            // Verify logger was called
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Information,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception, string>>()),
                Times.AtLeastOnce);
        }

        [Test]
        public async Task UpdateOrderSetAsync_WhenHttpRequestFails_ThrowsException()
        {
            // Arrange
            var orderSet = CreateTestCompleteOrderSet();
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad Request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _orderSetService.UpdateOrderSetAsync(orderSet));

            Assert.That(exception.Message, Does.Contain("Exception of type"));
        }

        [Test]
        public async Task DeleteOrderSetByIdAsync_WhenSuccessful_CompletesWithoutException()
        {
            // Arrange
            var orderSetId = Guid.NewGuid();

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.NoContent
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            // No exception should be thrown
            await _orderSetService.DeleteOrderSetByIdAsync(orderSetId);
        }

        [Test]
        public async Task DeleteOrderSetByIdAsync_WhenHttpRequestFails_ThrowsException()
        {
            // Arrange
            var orderSetId = Guid.NewGuid();
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.NotFound,
                Content = new StringContent("Not Found")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _orderSetService.DeleteOrderSetByIdAsync(orderSetId));

            Assert.That(exception.Message, Does.Contain("Exception of type"));
        }
    }
}



