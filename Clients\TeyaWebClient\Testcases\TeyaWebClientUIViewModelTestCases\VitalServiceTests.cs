using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using Moq;
using Moq.Protected;
using NUnit.Framework;
using TeyaUIModels.Model;
using TeyaUIViewModels.ViewModel;
using TeyaUIViewModels.TeyaUIViewModelResources;
using System.Net.Http.Json;
using System.Text.Json;

namespace TeyaWebClientUIViewModelTestCases
{
    [TestFixture]
    public class VitalServiceTests
    {
        private Mock<HttpMessageHandler> _mockHttpMessageHandler;
        private HttpClient _httpClient;
        private Mock<IConfiguration> _mockConfiguration;
        private Mock<IStringLocalizer<TeyaUIViewModelsStrings>> _mockLocalizer;
        private Mock<ITokenService> _mockTokenService;
        private VitalService _vitalService;
        private readonly string _baseUrl = "http://test-api.com";
        private readonly string _accessToken = "test-access-token";

        [SetUp]
        public void Setup()
        {
            // Setup environment variable
            Environment.SetEnvironmentVariable("EncounterNotesURL", _baseUrl);

            // Setup mock HTTP handler
            _mockHttpMessageHandler = new Mock<HttpMessageHandler>();

            // Setup HttpClient with mock handler
            _httpClient = new HttpClient(_mockHttpMessageHandler.Object)
            {
                BaseAddress = new Uri(_baseUrl)
            };

            // Setup mock configuration
            _mockConfiguration = new Mock<IConfiguration>();

            // Setup mock localizer
            _mockLocalizer = new Mock<IStringLocalizer<TeyaUIViewModelsStrings>>();
            _mockLocalizer.Setup(l => l["AddressRetrievalFailure"])
                .Returns(new LocalizedString("AddressRetrievalFailure", "Address retrieval failure"));
            _mockLocalizer.Setup(l => l["TasksRetrievalFailure"])
                .Returns(new LocalizedString("TasksRetrievalFailure", "Tasks retrieval failure"));
            _mockLocalizer.Setup(l => l["VitalRetrievalFailure"])
                .Returns(new LocalizedString("VitalRetrievalFailure", "Vital retrieval failure"));

            // Setup mock token service
            _mockTokenService = new Mock<ITokenService>();
            _mockTokenService.Setup(t => t.AccessToken).Returns(_accessToken);
            _mockTokenService.Setup(t => t.GetValidatedAccessTokenAsync()).ReturnsAsync(_accessToken);
            _mockTokenService.Setup(t => t.GetValidatedAccessTokenAsync()).ReturnsAsync(_accessToken);
            _mockTokenService.Setup(t => t.GetValidatedAccessTokenAsync()).ReturnsAsync(_accessToken);

            // Create VitalService with mocked dependencies
            _vitalService = new VitalService(_httpClient, _mockConfiguration.Object, _mockLocalizer.Object, _mockTokenService.Object);
        }

        [TearDown]
        public void TearDown()
        {
            _httpClient.Dispose();
            Environment.SetEnvironmentVariable("EncounterNotesURL", null);
        }

        [Test]
        public async Task GetVitalsByIdAsync_WhenSuccessful_ReturnsVitals()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var expectedVitals = new List<PatientVitals>
            {
                new PatientVitals
                {
                    VitalId = Guid.NewGuid(),
                    PatientId = patientId,
                    Temperature = 98.6f.ToString(),
                    Weight = 150.5f.ToString(),
                    Height = 68.0f.ToString(),
                    isActive = true,
                    CreatedDate = DateTime.Now.AddDays(-1)
                },
                new PatientVitals
                {
                    VitalId = Guid.NewGuid(),
                    PatientId = patientId,
                    Temperature = 99.2f.ToString(),
                    Weight = 151.0f.ToString(),
                    Height = 68.0f.ToString(),
                    isActive = true,
                    CreatedDate = DateTime.Now.AddDays(-7)
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedVitals)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.Is<HttpRequestMessage>(req =>
                        req.Method == HttpMethod.Get &&
                        req.RequestUri.ToString() == $"{_baseUrl}/{patientId}/{orgId}/{subscription}" &&
                        req.Headers.Authorization.Scheme == "Bearer" &&
                        req.Headers.Authorization.Parameter == _accessToken),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _vitalService.GetVitalsByIdAsync(patientId, orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(expectedVitals.Count));
            Assert.That(result[0].VitalId, Is.EqualTo(expectedVitals[0].VitalId));
            Assert.That(result[0].PatientId, Is.EqualTo(expectedVitals[0].PatientId));
            Assert.That(result[0].Temperature, Is.EqualTo(expectedVitals[0].Temperature));
            Assert.That(result[0].Weight, Is.EqualTo(expectedVitals[0].Weight));
            Assert.That(result[0].Height, Is.EqualTo(expectedVitals[0].Height));
            Assert.That(result[0].isActive, Is.EqualTo(expectedVitals[0].isActive));
        }

        [Test]
        public void GetVitalsByIdAsync_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.NotFound,
                Content = new StringContent("Not found")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.Is<HttpRequestMessage>(req =>
                        req.Method == HttpMethod.Get &&
                        req.RequestUri.ToString() == $"{_baseUrl}/{patientId}/{orgId}/{subscription}"),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _vitalService.GetVitalsByIdAsync(patientId, orgId, subscription));

            Assert.That(exception.Message, Is.EqualTo("Address retrieval failure"));
        }

        [Test]
        public async Task GetVitalsByIdAsyncAndIsActive_WhenSuccessful_ReturnsActiveVitals()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var expectedVitals = new List<PatientVitals>
            {
                new PatientVitals
                {
                    VitalId = Guid.NewGuid(),
                    PatientId = patientId,
                    Temperature = 98.4f.ToString(),
                    Weight = 149.8f.ToString(),
                    Height = 68.0f.ToString(),
                    isActive = true,
                    CreatedDate = DateTime.Now.AddHours(-2)
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedVitals)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.Is<HttpRequestMessage>(req =>
                        req.Method == HttpMethod.Get &&
                        req.RequestUri.ToString() == $"{_baseUrl}/api/Vital/{patientId}/isActive/{orgId}/{subscription}" &&
                        req.Headers.Authorization.Scheme == "Bearer" &&
                        req.Headers.Authorization.Parameter == _accessToken),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _vitalService.GetVitalsByIdAsyncAndIsActive(patientId, orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(expectedVitals.Count));
            Assert.That(result[0].isActive, Is.True);
        }

        [Test]
        public void GetVitalsByIdAsyncAndIsActive_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.Is<HttpRequestMessage>(req =>
                        req.Method == HttpMethod.Get &&
                        req.RequestUri.ToString() == $"{_baseUrl}/api/Vital/{patientId}/isActive/{orgId}/{subscription}"),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _vitalService.GetVitalsByIdAsyncAndIsActive(patientId, orgId, subscription));

            Assert.That(exception.Message, Is.EqualTo("Address retrieval failure"));
        }

        [Test]
        public async Task AddVitalAsync_WhenSuccessful_CompletesSuccessfully()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;
            var vitals = new List<PatientVitals>
            {
                new PatientVitals
                {
                    VitalId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    Temperature = 98.8f.ToString(),
                    Weight = 152.0f.ToString(),
                    Height = 69.0f.ToString(),
                    isActive = true,
                    CreatedDate = DateTime.Now
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.Created,
                Content = new StringContent("Vitals added successfully")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.Is<HttpRequestMessage>(req =>
                        req.Method == HttpMethod.Post &&
                        req.RequestUri.ToString() == $"{_baseUrl}/api/Vital/AddVitals/{orgId}/{subscription}" &&
                        req.Headers.Authorization.Scheme == "Bearer" &&
                        req.Headers.Authorization.Parameter == _accessToken),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.DoesNotThrowAsync(async () => await _vitalService.AddVitalAsync(vitals, orgId, subscription));
        }

        [Test]
        public void AddVitalAsync_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;
            var vitals = new List<PatientVitals>
            {
                new PatientVitals
                {
                    VitalId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    Temperature = 105.0f.ToString(), // Invalid high temperature
                    Weight = 300.0f.ToString(),
                    Height = 60.0f.ToString(),
                    isActive = true,
                    CreatedDate = DateTime.Now
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.Is<HttpRequestMessage>(req =>
                        req.Method == HttpMethod.Post &&
                        req.RequestUri.ToString() == $"{_baseUrl}/api/Vital/AddVitals/{orgId}/{subscription}"),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _vitalService.AddVitalAsync(vitals, orgId, subscription));

            Assert.That(exception.Message, Is.EqualTo("Tasks retrieval failure"));
        }

        [Test]
        public async Task UpdateVitalAsync_WhenSuccessful_CompletesSuccessfully()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;
            var vital = new PatientVitals
            {
                VitalId = Guid.NewGuid(),
                PatientId = Guid.NewGuid(),
                Temperature = 98.2f.ToString(),
                Weight = 148.5f.ToString(),
                Height = 67.5f.ToString(),
                isActive = true,
                CreatedDate = DateTime.Now.AddDays(-5)
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent("Vital updated successfully")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.Is<HttpRequestMessage>(req =>
                        req.Method == HttpMethod.Put &&
                        req.RequestUri.ToString() == $"{_baseUrl}/{vital.PatientId}/{orgId}/{subscription}" &&
                        req.Headers.Authorization.Scheme == "Bearer" &&
                        req.Headers.Authorization.Parameter == _accessToken),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.DoesNotThrowAsync(async () => await _vitalService.UpdateVitalAsync(vital, orgId, subscription));
        }

        [Test]
        public void UpdateVitalAsync_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;
            var vital = new PatientVitals
            {
                VitalId = Guid.NewGuid(),
                PatientId = Guid.NewGuid(),
                Temperature = 102.5f.ToString(),
                Weight = 200.0f.ToString(),
                Height = 65.0f.ToString(),
                isActive = true,
                CreatedDate = DateTime.Now.AddDays(-5)
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.Is<HttpRequestMessage>(req =>
                        req.Method == HttpMethod.Put &&
                        req.RequestUri.ToString() == $"{_baseUrl}/{vital.PatientId}/{orgId}/{subscription}"),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _vitalService.UpdateVitalAsync(vital, orgId, subscription));
        }

        [Test]
        public async Task DeleteVitalAsync_WhenSuccessful_CompletesSuccessfully()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;
            var vital = new PatientVitals
            {
                VitalId = Guid.NewGuid(),
                PatientId = Guid.NewGuid(),
                Temperature = 98.6f.ToString(),
                Weight = 150.0f.ToString(),
                Height = 68.0f.ToString(),
                isActive = false,
                CreatedDate = DateTime.Now.AddDays(-30)
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent("Vital deleted successfully")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.Is<HttpRequestMessage>(req =>
                        req.Method == HttpMethod.Delete &&
                        req.RequestUri.ToString() == $"{_baseUrl}/{orgId}/{subscription}" &&
                        req.Headers.Authorization.Scheme == "Bearer" &&
                        req.Headers.Authorization.Parameter == _accessToken),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.DoesNotThrowAsync(async () => await _vitalService.DeleteVitalAsync(vital, orgId, subscription));
        }

        [Test]
        public void DeleteVitalAsync_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;
            var vital = new PatientVitals
            {
                VitalId = Guid.NewGuid(),
                PatientId = Guid.NewGuid(),
                Temperature = 98.6f.ToString(),
                Weight = 150.0f.ToString(),
                Height = 68.0f.ToString(),
                isActive = false,
                CreatedDate = DateTime.Now.AddDays(-30)
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.Is<HttpRequestMessage>(req =>
                        req.Method == HttpMethod.Delete &&
                        req.RequestUri.ToString() == $"{_baseUrl}/{orgId}/{subscription}"),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _vitalService.DeleteVitalAsync(vital, orgId, subscription));

            Assert.That(exception.Message, Is.EqualTo("Tasks retrieval failure"));
        }

        [Test]
        public async Task UpdateVitalsListAsync_WhenSuccessful_CompletesSuccessfully()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;
            var vitals = new List<PatientVitals>
            {
                new PatientVitals
                {
                    VitalId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    Temperature = 98.4f.ToString(),
                    Weight = 149.0f.ToString(),
                    Height = 67.0f.ToString(),
                    isActive = true,
                    CreatedDate = DateTime.Now.AddDays(-2)
                },
                new PatientVitals
                {
                    VitalId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    Temperature = 98.8f.ToString(),
                    Weight = 155.0f.ToString(),
                    Height = 70.0f.ToString(),
                    isActive = true,
                    CreatedDate = DateTime.Now.AddDays(-1)
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent("Vitals list updated successfully")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.Is<HttpRequestMessage>(req =>
                        req.Method == HttpMethod.Put &&
                        req.RequestUri.ToString() == $"{_baseUrl}/api/Vital/UpdateVitalsList/{orgId}/{subscription}" &&
                        req.Headers.Authorization.Scheme == "Bearer" &&
                        req.Headers.Authorization.Parameter == _accessToken),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.DoesNotThrowAsync(async () => await _vitalService.UpdateVitalsListAsync(vitals, orgId, subscription));
        }

        [Test]
        public void UpdateVitalsListAsync_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;
            var vitals = new List<PatientVitals>
            {
                new PatientVitals
                {
                    VitalId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    Temperature = 110.0f.ToString(), // Invalid temperature
                    Weight = 400.0f.ToString(),
                    Height = 50.0f.ToString(),
                    isActive = true,
                    CreatedDate = DateTime.Now.AddDays(-2)
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.Is<HttpRequestMessage>(req =>
                        req.Method == HttpMethod.Put &&
                        req.RequestUri.ToString() == $"{_baseUrl}/api/Vital/UpdateVitalsList/{orgId}/{subscription}"),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _vitalService.UpdateVitalsListAsync(vitals, orgId, subscription));

            Assert.That(exception.Message, Is.EqualTo("Tasks retrieval failure"));
        }

        [Test]
        public async Task GetVitalsByIdAsync_WhenEmptyResponse_ReturnsEmptyList()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var expectedVitals = new List<PatientVitals>();

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedVitals)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.Is<HttpRequestMessage>(req =>
                        req.Method == HttpMethod.Get &&
                        req.RequestUri.ToString() == $"{_baseUrl}/{patientId}/{orgId}/{subscription}" &&
                        req.Headers.Authorization.Scheme == "Bearer" &&
                        req.Headers.Authorization.Parameter == _accessToken),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _vitalService.GetVitalsByIdAsync(patientId, orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(0));
        }

        [Test]
        public void GetVitalsByIdAsync_WhenExceptionThrown_PropagatesException()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var expectedException = new HttpRequestException("Test exception");

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.Is<HttpRequestMessage>(req =>
                        req.Method == HttpMethod.Get &&
                        req.RequestUri.ToString() == $"{_baseUrl}/{patientId}/{orgId}/{subscription}"),
                    ItExpr.IsAny<CancellationToken>())
                .ThrowsAsync(expectedException);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _vitalService.GetVitalsByIdAsync(patientId, orgId, subscription));

            Assert.That(exception.Message, Is.EqualTo("Test exception"));
        }

        [Test]
        public async Task GetVitalsByIdAsyncAndIsActive_WhenEmptyResponse_ReturnsEmptyList()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var expectedVitals = new List<PatientVitals>();

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedVitals)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.Is<HttpRequestMessage>(req =>
                        req.Method == HttpMethod.Get &&
                        req.RequestUri.ToString() == $"{_baseUrl}/api/Vital/{patientId}/isActive/{orgId}/{subscription}" &&
                        req.Headers.Authorization.Scheme == "Bearer" &&
                        req.Headers.Authorization.Parameter == _accessToken),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _vitalService.GetVitalsByIdAsyncAndIsActive(patientId, orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(0));
        }
    }
}
