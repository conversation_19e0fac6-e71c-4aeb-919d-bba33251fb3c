using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using Moq;
using Moq.Protected;
using NUnit.Framework;
using TeyaUIModels.Model;
using TeyaUIViewModels.ViewModel;
using TeyaUIViewModels.TeyaUIViewModelResources;
using System.Net.Http.Json;
using System.Linq;

namespace TeyaWebClientUIViewModelTestCases
{
    [TestFixture]
    public class ChiefComplaintServiceTests
    {
        private Mock<HttpMessageHandler> _mockHttpMessageHandler;
        private HttpClient _httpClient;
        private Mock<IConfiguration> _mockConfiguration;
        private Mock<IStringLocalizer<TeyaUIViewModelsStrings>> _mockLocalizer;
        private Mock<ITokenService> _mockTokenService;
        private Mock<ILogger<ChiefComplaintService>> _mockLogger;
        private ChiefComplaintService _chiefComplaintService;
        private readonly string _baseUrl = "http://test-api.com";
        private readonly string _accessToken = "test-token";

        [SetUp]
        public void Setup()
        {
            // Setup environment variable
            Environment.SetEnvironmentVariable("EncounterNotesURL", _baseUrl);

            // Setup mock HTTP handler
            _mockHttpMessageHandler = new Mock<HttpMessageHandler>();

            // Setup HttpClient with mock handler
            _httpClient = new HttpClient(_mockHttpMessageHandler.Object)
            {
                BaseAddress = new Uri(_baseUrl)
            };

            // Setup mock configuration
            _mockConfiguration = new Mock<IConfiguration>();

            // Setup mock localizer
            _mockLocalizer = new Mock<IStringLocalizer<TeyaUIViewModelsStrings>>();
            _mockLocalizer.Setup(l => l["ErrorFetchingComplaints"])
                .Returns(new LocalizedString("ErrorFetchingComplaints", "Error fetching complaints"));

            // Setup mock logger
            _mockLogger = new Mock<ILogger<ChiefComplaintService>>();

            // Setup mock token service
            _mockTokenService = new Mock<ITokenService>();
            _mockTokenService.Setup(t => t.AccessToken).Returns(_accessToken);

            // Create ChiefComplaintService with mocked dependencies
            _chiefComplaintService = new ChiefComplaintService(_httpClient, _mockConfiguration.Object, _mockLocalizer.Object, _mockTokenService.Object, _mockLogger.Object);
        }

        [TearDown]
        public void TearDown()
        {
            _httpClient.Dispose();
            Environment.SetEnvironmentVariable("EncounterNotesURL", null);
        }

        [Test]
        public async Task GetAllComplaintsAsync_WhenSuccessful_ReturnsComplaints()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;
            var expectedComplaints = new List<ChiefComplaintDTO>
            {
                new ChiefComplaintDTO
                {
                    Id = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    Description = "Headache",
                    DateOfComplaint = DateTime.Now,
                    OrganizationId = orgId,
                    PcpId = Guid.NewGuid(),
                    IsDeleted = false
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedComplaints)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _chiefComplaintService.GetAllComplaintsAsync(orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(1));
            Assert.That(result[0].Description, Is.EqualTo("Headache"));
        }

        [Test]
        public void GetAllComplaintsAsync_WhenHttpRequestFails_ThrowsException()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;
            var expectedException = new HttpRequestException("Test exception");

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ThrowsAsync(expectedException);

            // Act & Assert
            var exception = Assert.ThrowsAsync<Exception>(async () =>
                await _chiefComplaintService.GetAllComplaintsAsync(orgId, subscription));

            Assert.That(exception.Message, Does.Contain("Error retrieving complaints"));
        }

        [Test]
        public async Task AddAsync_WhenSuccessful_CompletesWithoutException()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;
            var complaint = new ChiefComplaintDTO
            {
                Id = Guid.NewGuid(),
                PatientId = Guid.NewGuid(),
                Description = "Headache",
                DateOfComplaint = DateTime.Now,
                OrganizationId = orgId,
                PcpId = Guid.NewGuid(),
                IsDeleted = false
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            // No exception should be thrown
            await _chiefComplaintService.AddAsync(complaint, orgId, subscription);
        }

        [Test]
        public void AddAsync_WhenHttpRequestFails_ThrowsException()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;
            var complaint = new ChiefComplaintDTO
            {
                Id = Guid.NewGuid(),
                PatientId = Guid.NewGuid(),
                Description = "Headache",
                DateOfComplaint = DateTime.Now,
                OrganizationId = orgId,
                PcpId = Guid.NewGuid(),
                IsDeleted = false
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<Exception>(async () =>
                await _chiefComplaintService.AddAsync(complaint, orgId, subscription));

            Assert.That(exception.Message, Does.Contain("Error adding complaint"));
        }

        [Test]
        public async Task UpdateComplaintAsync_WhenSuccessful_CompletesWithoutException()
        {
            // Arrange
            var complaintId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var complaint = new ChiefComplaintDTO
            {
                Id = complaintId,
                PatientId = Guid.NewGuid(),
                Description = "Headache",
                DateOfComplaint = DateTime.Now,
                OrganizationId = orgId,
                PcpId = Guid.NewGuid(),
                IsDeleted = false
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            // No exception should be thrown
            await _chiefComplaintService.UpdateComplaintAsync(complaintId, complaint, orgId, subscription);
        }

        [Test]
        public void UpdateComplaintAsync_WhenHttpRequestFails_ThrowsException()
        {
            // Arrange
            var complaintId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var complaint = new ChiefComplaintDTO
            {
                Id = complaintId,
                PatientId = Guid.NewGuid(),
                Description = "Headache",
                DateOfComplaint = DateTime.Now,
                OrganizationId = orgId,
                PcpId = Guid.NewGuid(),
                IsDeleted = false
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<Exception>(async () =>
                await _chiefComplaintService.UpdateComplaintAsync(complaintId, complaint, orgId, subscription));

            Assert.That(exception.Message, Does.Contain("Error updating complaint"));
        }

        [Test]
        public async Task DeleteComplaintAsync_WhenSuccessful_CompletesWithoutException()
        {
            // Arrange
            var complaintId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            // No exception should be thrown
            await _chiefComplaintService.DeleteComplaintAsync(complaintId, orgId, subscription);
        }

        [Test]
        public void DeleteComplaintAsync_WhenHttpRequestFails_ThrowsException()
        {
            // Arrange
            var complaintId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<Exception>(async () =>
                await _chiefComplaintService.DeleteComplaintAsync(complaintId, orgId, subscription));

            Assert.That(exception.Message, Does.Contain("Error deleting complaint"));
        }

        [Test]
        public async Task GetByPatientIdAsync_WhenSuccessful_ReturnsComplaints()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var expectedComplaints = new List<ChiefComplaintDTO>
            {
                new ChiefComplaintDTO
                {
                    Id = Guid.NewGuid(),
                    PatientId = patientId,
                    Description = "Headache",
                    DateOfComplaint = DateTime.Now,
                    OrganizationId = orgId,
                    PcpId = Guid.NewGuid(),
                    IsDeleted = false
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedComplaints)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _chiefComplaintService.GetByPatientIdAsync(patientId, orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count(), Is.EqualTo(1));
            Assert.That(result.First().Description, Is.EqualTo("Headache"));
        }

        [Test]
        public void GetByPatientIdAsync_WhenHttpRequestFails_ThrowsException()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var expectedException = new HttpRequestException("Test exception");

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ThrowsAsync(expectedException);

            // Act & Assert
            var exception = Assert.ThrowsAsync<Exception>(async () =>
                await _chiefComplaintService.GetByPatientIdAsync(patientId, orgId, subscription));

            Assert.That(exception.Message, Does.Contain($"Error retrieving complaints for PatientId {patientId}"));
        }

        [Test]
        public async Task UpdateComplaintListAsync_WhenSuccessful_CompletesWithoutException()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;
            var complaints = new List<ChiefComplaintDTO>
            {
                new ChiefComplaintDTO
                {
                    Id = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    Description = "Headache",
                    DateOfComplaint = DateTime.Now,
                    OrganizationId = orgId,
                    PcpId = Guid.NewGuid(),
                    IsDeleted = false
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            // No exception should be thrown
            await _chiefComplaintService.UpdateComplaintListAsync(complaints, orgId, subscription);
        }

        [Test]
        public void UpdateComplaintListAsync_WhenHttpRequestFails_ThrowsException()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;
            var complaints = new List<ChiefComplaintDTO>
            {
                new ChiefComplaintDTO
                {
                    Id = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    Description = "Headache",
                    DateOfComplaint = DateTime.Now,
                    OrganizationId = orgId,
                    PcpId = Guid.NewGuid(),
                    IsDeleted = false
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                ReasonPhrase = "Bad Request"
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<Exception>(async () =>
                await _chiefComplaintService.UpdateComplaintListAsync(complaints, orgId, subscription));

            Assert.That(exception.Message, Does.Contain("Error during bulk update of complaints"));
        }

        [Test]
        public void UpdateComplaintListAsync_WhenComplaintsIsNull_ThrowsArgumentException()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;
            List<ChiefComplaintDTO> complaints = null;

            // Act & Assert
            var exception = Assert.ThrowsAsync<Exception>(async () =>
                await _chiefComplaintService.UpdateComplaintListAsync(complaints, orgId, subscription));

            Assert.That(exception.Message, Does.Contain("Complaint list is empty"));
        }

        [Test]
        public void UpdateComplaintListAsync_WhenComplaintsIsEmpty_ThrowsArgumentException()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;
            var complaints = new List<ChiefComplaintDTO>();

            // Act & Assert
            var exception = Assert.ThrowsAsync<Exception>(async () =>
                await _chiefComplaintService.UpdateComplaintListAsync(complaints, orgId, subscription));

            Assert.That(exception.Message, Does.Contain("Complaint list is empty"));
        }
    }
}



