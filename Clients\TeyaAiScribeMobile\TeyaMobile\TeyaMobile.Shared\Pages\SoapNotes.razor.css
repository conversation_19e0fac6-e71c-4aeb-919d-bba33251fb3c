﻿/* Compact mobile design styles */
::deep .soapnotes-container {
    width: 100% !important;
    max-width: 100vw !important;
    box-sizing: border-box !important;
    overflow-x: hidden !important;
}

::deep .mud-container,
::deep .mud-stack,
::deep .mud-card,
::deep .mud-card-content,
::deep .mud-expansion-panels,
::deep .mud-expansion-panel,
::deep .mud-expansion-panel-header,
::deep .mud-expansion-panel-content {
    width: 100% !important;
    max-width: 100% !important;
    box-sizing: border-box !important;
    overflow-x: hidden !important;
}

/* Mobile specific compact adjustments */
@media (max-width: 767px) {
    ::deep .mud-expansion-panel-header {
        padding: 6px 8px !important;
        font-size: 0.8rem !important;
        min-height: 36px !important;
    }

    ::deep .mud-container {
        padding: 4px !important;
        width: 100% !important;
        max-width: 100vw !important;
    }

    ::deep .mud-card {
        margin-bottom: 8px !important;
        width: 100% !important;
        max-width: 100% !important;
    }

    ::deep .mud-card-header {
        padding: 8px 12px !important;
    }

    ::deep .mud-card-content {
        padding: 4px !important;
    }

    ::deep .mud-paper {
        padding: 8px !important;
    }

    ::deep .mud-button {
        font-size: 0.75rem !important;
        padding: 4px 8px !important;
    }

    ::deep .mud-input {
        font-size: 0.8rem !important;
    }

    ::deep .mud-input-label {
        font-size: 0.75rem !important;
    }

    ::deep .mud-typography-body1 {
        font-size: 0.8rem !important;
    }

    ::deep .mud-typography-body2 {
        font-size: 0.75rem !important;
    }

    ::deep .mud-typography-caption {
        font-size: 0.7rem !important;
    }

    ::deep .mud-alert {
        font-size: 0.7rem !important;
        padding: 6px 8px !important;
    }

    ::deep .mud-icon-size-small {
        font-size: 1.2rem !important;
    }

    ::deep .mud-icon-size-medium {
        font-size: 1.4rem !important;
    }

    ::deep .mud-progress-circular {
        width: 20px !important;
        height: 20px !important;
    }

    ::deep .mud-stack {
        gap: 4px !important;
    }
}

/* Tablet adjustments */
@media (min-width: 768px) and (max-width: 1199px) {
    ::deep .mud-expansion-panel-header {
        padding: 8px 12px !important;
        font-size: 0.9rem !important;
    }

    ::deep .mud-container {
        padding: 12px !important;
        width: 100% !important;
        max-width: 100vw !important;
    }
}

/* Desktop adjustments */
@media (min-width: 1200px) {
    ::deep .mud-expansion-panel-header {
        padding: 12px 16px !important;
        font-size: 1.0rem !important;
    }

    ::deep .mud-container {
        max-width: 1400px !important;
        margin: 0 auto !important;
    }
}

/* Ensure text content doesn't overflow */
::deep .mud-typography {
    word-wrap: break-word !important;
    overflow-wrap: break-word !important;
    box-sizing: border-box !important;
}

/* Compact markdown content styling */
::deep .markdown-content {
    width: 100% !important;
    max-width: 100% !important;
    box-sizing: border-box !important;
}

    ::deep .markdown-content p {
        margin-bottom: 3px !important;
        word-wrap: break-word !important;
        overflow-wrap: break-word !important;
    }

    ::deep .markdown-content ul,
    ::deep .markdown-content ol {
        padding-left: 12px !important;
        margin-bottom: 3px !important;
    }

    ::deep .markdown-content li {
        margin-bottom: 2px !important;
    }

/* Compact autocomplete styling */
::deep .mud-autocomplete {
    width: 100% !important;
    max-width: 100% !important;
    box-sizing: border-box !important;
}

::deep .mud-input-control {
    position: relative !important;
    width: 100% !important;
    max-width: 100% !important;
}

/* Fix for autocomplete dropdown positioning */
::deep .mud-popover {
    max-width: calc(100vw - 16px) !important;
}

@media (max-width: 767px) {
    ::deep .mud-expansion-panel-header {
        padding: 3px 6px !important;
        font-size: 0.65rem !important;
        min-height: 24px !important;
    }

    ::deep .mud-container {
        padding: 2px !important;
        width: 100% !important;
        max-width: 100vw !important;
    }

    ::deep .mud-card {
        margin-bottom: 4px !important;
        border: 1px solid #e9ecef !important;
        box-shadow: none !important;
    }

    ::deep .mud-card-header {
        padding: 4px 6px !important;
    }

    ::deep .mud-card-content {
        padding: 2px !important;
    }

    ::deep .mud-paper {
        padding: 4px !important;
        box-shadow: none !important;
        border: 1px solid #e9ecef !important;
    }

    ::deep .mud-button {
        font-size: 0.65rem !important;
        padding: 2px 6px !important;
        min-height: 24px !important;
    }

    ::deep .mud-input {
        font-size: 0.7rem !important;
    }

    ::deep .mud-input-label {
        font-size: 0.65rem !important;
    }

    ::deep .mud-typography-overline {
        font-size: 0.6rem !important;
        line-height: 1.2 !important;
    }

    ::deep .mud-typography-caption {
        font-size: 0.65rem !important;
        line-height: 1.2 !important;
    }

    ::deep .mud-alert {
        font-size: 0.6rem !important;
        padding: 3px 6px !important;
    }

    ::deep .mud-icon-size-small {
        font-size: 1.0rem !important;
    }

    ::deep .mud-progress-circular {
        width: 16px !important;
        height: 16px !important;
    }

    ::deep .mud-stack {
        gap: 2px !important;
    }

    ::deep .mud-expansion-panel-content {
        padding: 2px 4px !important;
    }

    /* Ultra-compact markdown content */
    ::deep .markdown-content {
        font-size: 0.65rem !important;
        line-height: 1.2 !important;
    }

        ::deep .markdown-content p {
            margin-bottom: 1px !important;
        }

        ::deep .markdown-content ul,
        ::deep .markdown-content ol {
            padding-left: 8px !important;
            margin-bottom: 1px !important;
        }

        ::deep .markdown-content li {
            margin-bottom: 1px !important;
        }
}