﻿@page "/appointments"
@using TeyaHealthMobileModel.Model
@using TeyaHealthMobileViewModel.ViewModel
@using Syncfusion.Blazor.Schedule
@using Syncfusion.Blazor.Calendars
@using Syncfusion.Blazor.Buttons
@using Syncfusion.Blazor.Popups
@using MudBlazor
@using TeyaMobile.Shared.Components
@inject IAppointmentService AppointmentService
@inject IAuthenticationService AuthenticationService
@inject NavigationManager Navigation
@inject IJSRuntime JSRuntime
@inject IFormFactor FormFactor
@implements IDisposable

<PageTitle>Today's Schedule</PageTitle>

@code {
    private bool IsTablet => FormFactor.GetFormFactor() == "Tablet";
    private string GetStatCardClass(FilterType filterType)
    {
        return $"stat-card {(activeFilter == filterType ? "active" : "")}";
    }
    private string GetStatCardClassTablet(FilterType filterType)
    {
        return $"stat-card-tablet {(activeFilter == filterType ? "active" : "")}";
    }
    private string GetPendingCardClass()
    {
        return $"stat-card pending {(activeFilter == FilterType.Pending ? "active" : "")}";
    }
    private string GetPendingCardClassTablet()
    {
        return $"stat-card-tablet pending {(activeFilter == FilterType.Pending ? "active" : "")}";
    }
}

@if (IsTablet)
{
    <MudPaper Elevation="2" Class="stats-header-section-tablet" Style="padding: 18px 24px; margin-bottom: 18px;">
        <MudGrid GutterSize="16px" AlignItems="AlignItems.Center" Justify="Justify.SpaceBetween" Style="width: 100%;">
            <MudItem xs="4">
                <MudCard Class="@GetStatCardClassTablet(FilterType.Total)" @onclick="() => ApplyFilter(FilterType.Total)">
                    <MudCardContent Class="stat-content-tablet">
                        <MudStack AlignItems="AlignItems.Center" Spacing="1">
                            <MudIcon Icon="@Icons.Material.Filled.Event" Size="Size.Large" Color="@(activeFilter == FilterType.Total ? Color.Primary : Color.Default)" />
                            <MudText Typo="Typo.h5" Class="stat-count-tablet">@totalAppointments</MudText>
                            <MudText Typo="Typo.caption" Class="stat-label-tablet">TOTAL</MudText>
                        </MudStack>
                    </MudCardContent>
                </MudCard>
            </MudItem>
            <MudItem xs="4">
                <MudCard Class="@GetStatCardClassTablet(FilterType.Completed)" @onclick="() => ApplyFilter(FilterType.Completed)">
                    <MudCardContent Class="stat-content-tablet">
                        <MudStack AlignItems="AlignItems.Center" Spacing="1">
                            <MudIcon Icon="@Icons.Material.Filled.CheckCircle" Size="Size.Large" Color="@(activeFilter == FilterType.Completed ? Color.Success : Color.Default)" />
                            <MudText Typo="Typo.h5" Class="stat-count-tablet" Style="color: var(--mud-palette-success-main);">@completedAppointments</MudText>
                            <MudText Typo="Typo.caption" Class="stat-label-tablet">DONE</MudText>
                        </MudStack>
                    </MudCardContent>
                </MudCard>
            </MudItem>
            <MudItem xs="4">
                <MudCard Class="@GetPendingCardClassTablet()" @onclick="() => ApplyFilter(FilterType.Pending)">
                    <MudCardContent Class="stat-content-tablet">
                        <MudStack AlignItems="AlignItems.Center" Spacing="1">
                            <MudIcon Icon="@Icons.Material.Filled.Schedule" Size="Size.Large" Color="@(activeFilter == FilterType.Pending ? Color.Warning : Color.Default)" />
                            <MudText Typo="Typo.h5" Class="stat-count-tablet" Style="color: var(--mud-palette-warning-main);">@pendingAppointments</MudText>
                            <MudText Typo="Typo.caption" Class="stat-label-tablet">PENDING</MudText>
                        </MudStack>
                    </MudCardContent>
                </MudCard>
            </MudItem>
        </MudGrid>
    </MudPaper>
}
else
{
    <MudPaper Elevation="2" Class="stats-header-section" Style="padding: 10px; margin-bottom: 10px;">
        <MudStack Row="true" AlignItems="AlignItems.Center" Justify="Justify.SpaceBetween" Style="width: 100%;">
            <!-- Stats Cards Section -->
            <MudStack Row="true" AlignItems="AlignItems.Center" Spacing="2" Style="flex: 1; overflow-x: auto; min-width: 0;">
                <!-- Total Appointments Card -->
                <MudCard Class="@(GetStatCardClass(FilterType.Total))" @onclick="() => ApplyFilter(FilterType.Total)" Style="min-width: 70px; font-size: 0.95em;">
                    <MudCardContent Class="stat-content" Style="padding: 6px 4px !important;">
                        <MudStack AlignItems="AlignItems.Center" Spacing="1">
                            <MudIcon Icon="@Icons.Material.Filled.Event" Size="Size.Medium" Color="@(activeFilter == FilterType.Total ? Color.Primary : Color.Default)" />
                            <MudText Typo="Typo.h6" Class="stat-count" Style="font-weight: 600; line-height: 1;">@totalAppointments</MudText>
                            <MudText Typo="Typo.caption" Class="stat-label" Style="font-size: 0.7rem; text-transform: uppercase; letter-spacing: 0.5px;">TOTAL</MudText>
                        </MudStack>
                    </MudCardContent>
                </MudCard>
                <!-- Completed Appointments Card -->
                <MudCard Class="@(GetStatCardClass(FilterType.Completed))" @onclick="() => ApplyFilter(FilterType.Completed)" Style="min-width: 70px; font-size: 0.95em;">
                    <MudCardContent Class="stat-content" Style="padding: 6px 4px !important;">
                        <MudStack AlignItems="AlignItems.Center" Spacing="1">
                            <MudIcon Icon="@Icons.Material.Filled.CheckCircle" Size="Size.Medium" Color="@(activeFilter == FilterType.Completed ? Color.Success : Color.Default)" />
                            <MudText Typo="Typo.h6" Class="stat-count" Style="font-weight: 600; line-height: 1; color: var(--mud-palette-success-main);">@completedAppointments</MudText>
                            <MudText Typo="Typo.caption" Class="stat-label" Style="font-size: 0.7rem; text-transform: uppercase; letter-spacing: 0.5px;">DONE</MudText>
                        </MudStack>
                    </MudCardContent>
                </MudCard>
                <!-- Pending Appointments Card -->
                <MudCard Class="@(GetPendingCardClass())" @onclick="() => ApplyFilter(FilterType.Pending)" Style="min-width: 70px; font-size: 0.95em;">
                    <MudCardContent Class="stat-content" Style="padding: 6px 4px !important;">
                        <MudStack AlignItems="AlignItems.Center" Spacing="1">
                            <MudIcon Icon="@Icons.Material.Filled.Schedule" Size="Size.Medium" Color="@(activeFilter == FilterType.Pending ? Color.Warning : Color.Default)" />
                            <MudText Typo="Typo.h6" Class="stat-count" Style="font-weight: 600; line-height: 1; color: var(--mud-palette-warning-main);">@pendingAppointments</MudText>
                            <MudText Typo="Typo.caption" Class="stat-label" Style="font-size: 0.7rem; text-transform: uppercase; letter-spacing: 0.5px;">PENDING</MudText>
                        </MudStack>
                    </MudCardContent>
                </MudCard>
            </MudStack>
            <!-- Calendar Section -->
            <MudStack AlignItems="AlignItems.Center" Spacing="1" Style="margin-left: 8px;">
                <MudText Typo="Typo.caption" Style="font-size: 0.7rem; color: var(--mud-palette-text-secondary); text-align: center;">@selectedDate.ToString("MMM dd")</MudText>
                <MudFab Color="Color.Primary" StartIcon="@Icons.Material.Filled.CalendarToday" Size="Size.Small" OnClick="OpenDatePicker" Style="width: 24px; height: 24px; min-height: 24px; box-shadow: 0 1px 4px rgba(0,0,0,0.10);" Class="calendar-fab-btn" />
                <MudText Typo="Typo.caption" Style="font-size: 0.7rem; color: var(--mud-palette-text-secondary); text-align: center;">@selectedDate.ToString("dddd").Substring(0, 3).ToUpper()</MudText>
            </MudStack>
        </MudStack>
    </MudPaper>
}

<MudContainer MaxWidth="MaxWidth.Medium" Class="appointments-container" Style="margin: 0 auto; padding: 24px;">
    <!-- Loading Section -->
    @if (isLoading)
    {
        <MudPaper Elevation="2" Class="loading-section">
            <MudStack AlignItems="AlignItems.Center" Spacing="3">
                <MudProgressCircular Color="Color.Primary" Size="Size.Large" Indeterminate="true" />
                <MudText Typo="Typo.body1">Loading appointments...</MudText>
            </MudStack>
        </MudPaper>
    }
    <!-- Scheduler Section -->
    <MudPaper Elevation="3" Class="scheduler-section" Style="margin-top: 20px;">
        <Syncfusion.Blazor.Schedule.SfSchedule TValue="AppointmentData"
                                               @ref="ScheduleRef"
                                               SelectedDate="@selectedDate"
                                               CurrentView="View.Day"
                                               Height="@schedulerHeight"
                                               StartHour="00:00"
                                               EndHour="23:00"
                                               ShowQuickInfo="false"
                                               ShowHeaderBar="false"
                                               AllowDragAndDrop="false"
                                               AllowResizing="false"
                                               CssClass="mud-themed-scheduler">
            <ScheduleViews>
                <ScheduleView Option="View.Day">
                    <ScheduleViewTimeScale Enable="true" Interval="30" SlotCount="2"></ScheduleViewTimeScale>
                </ScheduleView>
            </ScheduleViews>
            <ScheduleEventSettings DataSource="@filteredAppointments"
                                   AllowAdding="false"
                                   AllowDeleting="false"
                                   AllowEditing="false">
                <Template>
                    @{
                        var appointment = context as AppointmentData;
                    }
                    <MudCard Elevation="2"
                             Class="appointment-card {GetAppointmentStatusClass(appointment)}"
                             Style="font-size: 1.05em; min-height: 70px; margin-bottom: 10px; overflow: hidden;"
                             @onclick="() => OnAppointmentCardClick(appointment)">
                        <MudCardContent Class="appointment-content">
                            <div class="appointment-details-vertical">
                                <div class="name-row-responsive">
                                    <MudText Typo="Typo.subtitle1" Class="patient-name">@appointment.PatientName</MudText>
                                    @if (!string.IsNullOrEmpty(appointment.RoomNumber))
                                    {
                                        <MudText Typo="Typo.caption" Class="room-number-inline">
                                            Room @appointment.RoomNumber
                                        </MudText>
                                    }
                                </div>
                                <MudText Typo="Typo.body2" Class="visit-type-block">
                                    @(!string.IsNullOrEmpty(appointment.VisitType) ? appointment.VisitType : "Consultation")
                                </MudText>
                                @if (!string.IsNullOrEmpty(appointment.Reason))
                                {
                                    <MudText Typo="Typo.caption" Class="reason">
                                        @appointment.Reason
                                    </MudText>
                                }
                            </div>
                        </MudCardContent>
                    </MudCard>
                </Template>
            </ScheduleEventSettings>
        </Syncfusion.Blazor.Schedule.SfSchedule>
    </MudPaper>
</MudContainer>

<!-- Fixed: Proper Syncfusion Date Picker Dialog -->
<Syncfusion.Blazor.Popups.SfDialog @ref="calendarDialogRef"
                                   Width="300px"
                                   Height="auto"
                                   IsModal="true"
                                   ShowCloseIcon="true"
                                   Visible="@isCalendarDialogVisible"
                                   VisibleChanged="@((bool visible) => { isCalendarDialogVisible = visible; StateHasChanged(); })"
                                   CssClass="compact-date-picker-dialog"
                                   Position="@dialogPosition">
    <DialogTemplates>
        <Content>
            <div class="compact-calendar-container">
                <Syncfusion.Blazor.Calendars.SfCalendar TValue="DateTime"
                                                        Value="@selectedDate"
                                                        ValueChanged="@OnCalendarDateSelected"
                                                        CssClass="compact-calendar">
                </Syncfusion.Blazor.Calendars.SfCalendar>
            </div>
        </Content>
    </DialogTemplates>
</Syncfusion.Blazor.Popups.SfDialog>