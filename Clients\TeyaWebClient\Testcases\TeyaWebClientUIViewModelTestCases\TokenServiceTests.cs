using System;
using System.Collections.Generic;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using Microsoft.AspNetCore.Components;
using Moq;
using NUnit.Framework;
using TeyaUIViewModels.ViewModel;

namespace TeyaWebClientUIViewModelTestCases
{
    [TestFixture]
    public class TokenServiceTests
    {
        private Mock<IHttpContextAccessor> _mockHttpContextAccessor;
        private Mock<IStringLocalizer<TokenService>> _mockLocalizer;
        private TestNavigationManager _testNavigationManager;
        private Mock<HttpContext> _mockHttpContext;
        private TestSession _testSession;
        private Mock<ILogger<TokenService>> _mockLogger;
        private TokenService _tokenService;

        // Custom test session implementation to avoid extension method mocking issues
        private class TestSession : ISession
        {
            private readonly Dictionary<string, byte[]> _sessionData = new Dictionary<string, byte[]>();

            public string Id => "test-session-id";
            public bool IsAvailable => true;
            public IEnumerable<string> Keys => _sessionData.Keys;

            public void Clear() => _sessionData.Clear();

            public Task CommitAsync(CancellationToken cancellationToken = default) => Task.CompletedTask;

            public Task LoadAsync(CancellationToken cancellationToken = default) => Task.CompletedTask;

            public void Remove(string key) => _sessionData.Remove(key);

            public void Set(string key, byte[] value) => _sessionData[key] = value;

            public bool TryGetValue(string key, out byte[] value) => _sessionData.TryGetValue(key, out value);

            // Extension method implementations for testing
            public void SetString(string key, string value)
            {
                if (value == null)
                {
                    Remove(key);
                    return;
                }
                Set(key, Encoding.UTF8.GetBytes(value));
            }

            public string GetString(string key)
            {
                if (TryGetValue(key, out var value))
                {
                    return Encoding.UTF8.GetString(value);
                }
                return null;
            }
        }

        // Custom test navigation manager to avoid initialization issues
        private class TestNavigationManager : NavigationManager
        {
            public TestNavigationManager() : base()
            {
                Initialize("https://localhost/", "https://localhost/");
            }

            protected override void NavigateToCore(string uri, bool forceLoad)
            {
                // Do nothing for tests
            }
        }

        [SetUp]
        public void Setup()
        {
            // Setup mock HTTP context accessor
            _mockHttpContextAccessor = new Mock<IHttpContextAccessor>();

            // Setup mock localizer
            _mockLocalizer = new Mock<IStringLocalizer<TokenService>>();
            _mockLocalizer.Setup(l => l["/authentication/login"]).Returns(new LocalizedString("/authentication/login", "/authentication/login"));

            // Setup test navigation manager (concrete implementation to avoid initialization issues)
            _testNavigationManager = new TestNavigationManager();

            // Setup mock HTTP context
            _mockHttpContext = new Mock<HttpContext>();

            // Setup test session (not mocked to avoid extension method issues)
            _testSession = new TestSession();

            // Setup mock logger
            _mockLogger = new Mock<ILogger<TokenService>>();

            // Setup the chain: HttpContextAccessor -> HttpContext -> Session
            _mockHttpContext.Setup(c => c.Session).Returns(_testSession);
            _mockHttpContextAccessor.Setup(a => a.HttpContext).Returns(_mockHttpContext.Object);

            // Create TokenService with mocked dependencies
            _tokenService = new TokenService(_mockHttpContextAccessor.Object, _mockLocalizer.Object, _testNavigationManager);
        }

        [Test]
        public void AccessToken_WhenSessionHasToken_ReturnsToken()
        {
            // Arrange
            var expectedToken = "test-access-token-12345";
            _testSession.SetString("AccessToken", expectedToken);

            // Act
            var result = _tokenService.AccessToken;

            // Assert
            Assert.That(result, Is.EqualTo(expectedToken));
        }

        [Test]
        public void AccessToken_WhenSessionDoesNotHaveToken_ReturnsNull()
        {
            // Arrange
            // Session is empty by default

            // Act
            var result = _tokenService.AccessToken;

            // Assert
            Assert.That(result, Is.Null);
        }

        [Test]
        public void AccessToken_WhenHttpContextIsNull_ReturnsNull()
        {
            // Arrange
            _mockHttpContextAccessor.Setup(a => a.HttpContext).Returns((HttpContext)null);

            // Act
            var result = _tokenService.AccessToken;

            // Assert
            Assert.That(result, Is.Null);
        }

        [Test]
        public void AccessToken_WhenSessionIsNull_ThrowsNullReferenceException()
        {
            // Arrange
            _mockHttpContext.Setup(c => c.Session).Returns((ISession)null);

            // Act & Assert
            // The actual implementation throws NullReferenceException when session is null
            Assert.Throws<NullReferenceException>(() => { var result = _tokenService.AccessToken; });
        }

        [Test]
        public void AccessToken_Set_WhenValidToken_SetsTokenInSession()
        {
            // Arrange
            var token = "new-access-token-67890";

            // Act
            _tokenService.AccessToken = token;

            // Assert
            var storedToken = _testSession.GetString("AccessToken");
            Assert.That(storedToken, Is.EqualTo(token));
        }

        [Test]
        public async Task GetValidatedAccessTokenAsync_WhenTokenExists_ReturnsToken()
        {
            // Arrange
            var expectedToken = "valid-access-token-12345";
            _testSession.SetString("AccessToken", expectedToken);

            // Act
            var result = await _tokenService.GetValidatedAccessTokenAsync();

            // Assert
            Assert.That(result, Is.EqualTo(expectedToken));
        }

        [Test]
        public async Task GetValidatedAccessTokenAsync_WhenTokenIsNull_ReturnsNull()
        {
            // Arrange
            // Session is empty by default

            // Act
            var result = await _tokenService.GetValidatedAccessTokenAsync();

            // Assert
            Assert.That(result, Is.Null);
            // Navigation is called but we can't easily verify it with our test implementation
        }

        [Test]
        public async Task GetValidatedAccessToken2Async_WhenTokenExists_ReturnsToken()
        {
            // Arrange
            var expectedToken = "valid-access-token2-12345";
            _testSession.SetString("AccessToken2", expectedToken);

            // Act
            var result = await _tokenService.GetValidatedAccessToken2Async();

            // Assert
            Assert.That(result, Is.EqualTo(expectedToken));
        }

        [Test]
        public async Task GetValidatedAccessToken2Async_WhenTokenIsNull_ReturnsNull()
        {
            // Arrange
            // Session is empty by default

            // Act
            var result = await _tokenService.GetValidatedAccessToken2Async();

            // Assert
            Assert.That(result, Is.Null);
            // Navigation is called but we can't easily verify it with our test implementation
        }

        [Test]
        public void UserDetails_WhenSessionHasUserDetails_ReturnsUserDetails()
        {
            // Arrange
            var expectedUserDetails = "user-details-json";
            _testSession.SetString("UserDetails", expectedUserDetails);

            // Act
            var result = _tokenService.UserDetails;

            // Assert
            Assert.That(result, Is.EqualTo(expectedUserDetails));
        }

        [Test]
        public void UserDetails_WhenSessionDoesNotHaveUserDetails_ReturnsNull()
        {
            // Arrange
            // Session is empty by default

            // Act
            var result = _tokenService.UserDetails;

            // Assert
            Assert.That(result, Is.Null);
        }

        [Test]
        public void UserDetails_Set_WhenValidUserDetails_SetsUserDetailsInSession()
        {
            // Arrange
            var userDetails = "new-user-details-json";

            // Act
            _tokenService.UserDetails = userDetails;

            // Assert
            var storedUserDetails = _testSession.GetString("UserDetails");
            Assert.That(storedUserDetails, Is.EqualTo(userDetails));
        }

        [Test]
        public void AccessToken2_Set_WhenValidToken_SetsTokenInSession()
        {
            // Arrange
            var token = "new-access-token2-67890";

            // Act
            _tokenService.AccessToken2 = token;

            // Assert
            var storedToken = _testSession.GetString("AccessToken2");
            Assert.That(storedToken, Is.EqualTo(token));
        }

        [Test]
        public void TokenService_Constructor_WhenValidDependencies_CreatesInstance()
        {
            // Act & Assert
            Assert.That(_tokenService, Is.Not.Null);
            Assert.That(_tokenService, Is.InstanceOf<TokenService>());
        }

        [Test]
        public void TokenService_WhenHttpContextAccessorIsNull_DoesNotThrowException()
        {
            // Act & Assert - The actual implementation doesn't validate null parameters
            Assert.DoesNotThrow(() =>
                new TokenService(null, _mockLocalizer.Object, _testNavigationManager));
        }

        [Test]
        public void TokenService_WhenLocalizerIsNull_DoesNotThrowException()
        {
            // Act & Assert - The actual implementation doesn't validate null parameters
            Assert.DoesNotThrow(() =>
                new TokenService(_mockHttpContextAccessor.Object, null, _testNavigationManager));
        }

        [Test]
        public void TokenService_WhenNavigationManagerIsNull_DoesNotThrowException()
        {
            // Act & Assert - The actual implementation doesn't validate null parameters
            Assert.DoesNotThrow(() =>
                new TokenService(_mockHttpContextAccessor.Object, _mockLocalizer.Object, null));
        }
    }
}
