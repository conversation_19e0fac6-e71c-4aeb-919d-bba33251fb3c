﻿@using TeyaUIModels.Model
@using TeyaUIViewModels.ViewModel
@using Microsoft.Extensions.Localization
@using Microsoft.Extensions.Logging
@inject ICosigningService CosigningService
@inject ICosigningCommentHelper CommentHelper
@inject IStringLocalizer<TeyaAIScribeResource> Localizer
@inject ILogger<CosigningComponent> Logger
@inject ISnackbar Snackbar

<MudContainer MaxWidth="MaxWidth.False" Class="cosigning-container">
    @if (ShowCosigningSection)
    {
        <MudPaper Class="cosigning-paper" Elevation="2" Style="padding: 20px; border-radius: 8px;">
            <!-- Header Section -->
            <MudStack Row AlignItems="AlignItems.Center" Justify="Justify.SpaceBetween" Class="mb-4">
                <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
                    <MudIcon Icon="@Icons.Material.Filled.VerifiedUser"
                             Color="Color.Primary"
                             Size="Size.Medium" />
                    <MudText Typo="Typo.h6" Style="font-weight: 600; color: #1976d2;">
                        @Localizer["DocumentSignature"]
                    </MudText>
                </MudStack>
                <MudChip T="string"
                         Size="Size.Medium"
                         Color="@GetStatusColor()"
                         Variant="Variant.Filled"
                         Icon="@GetCosigningRequestIcon()">
                    @GetStatusText()
                </MudChip>
            </MudStack>

            <MudDivider Class="mb-4" />

            <!-- Active Cosigning Request Alert -->
            @if (ActiveCosigningRequest != null)
            {
                <MudAlert Severity="@GetCosigningRequestSeverity()"
                          Icon="@GetCosigningRequestIcon()"
                          Class="mb-4">
                    <MudStack Row AlignItems="AlignItems.Center" Justify="Justify.SpaceBetween">
                        <MudStack Spacing="1">
                            <MudText Typo="Typo.subtitle2" Style="font-weight: 600;">
                                @GetCosigningRequestStatusText()
                            </MudText>
                            <MudText Typo="Typo.body2">
                                @Localizer["RequestedFrom"]: @ActiveCosigningRequest.ReviewerName
                            </MudText>
                            <MudText Typo="Typo.caption">
                                @Localizer["RequestedOn"]: @ActiveCosigningRequest.RequestedDate.ToString("MM/dd/yyyy HH:mm")
                            </MudText>
                        </MudStack>
                        @if (ActiveCosigningRequest.Status == CosigningRequestStatus.Pending)
                        {
                            <MudButton Variant="Variant.Text"
                                       Color="Color.Error"
                                       Size="Size.Small"
                                       OnClick="CancelActiveCosigningRequest"
                                       Disabled="@IsProcessing">
                                @Localizer["Cancel"]
                            </MudButton>
                        }
                    </MudStack>
                </MudAlert>
            }

            <!-- Step 1: Signing Form -->
            @if (_showSigningForm)
            {
                <MudPaper Class="mb-4" Elevation="3" Style="padding: 20px; border: 2px solid #1976d2; background: #f8f9fa;">
                    <MudStack Spacing="3">
                        <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
                            <MudIcon Icon="@Icons.Material.Filled.Edit" Color="Color.Primary" Size="Size.Medium" />
                            <MudText Typo="Typo.h6" Style="font-weight: 600; color: #1976d2;">
                                @Localizer["SignDocument"]
                            </MudText>
                            <MudSpacer />
                            <MudIconButton Icon="@Icons.Material.Filled.Close"
                                           Size="Size.Small"
                                           OnClick="CancelSigning"
                                           Color="Color.Error" />
                        </MudStack>

                        <MudDivider />

                        <MudAlert Severity="Severity.Info" Dense="true">
                            @Localizer["SigningAs"]: @CurrentUser.displayName
                        </MudAlert>

                        <MudStack Row Justify="Justify.FlexEnd" Spacing="2">
                            <MudButton Variant="Variant.Outlined"
                                       Color="Color.Secondary"
                                       OnClick="CancelSigning"
                                       Size="Size.Medium">
                                @Localizer["Cancel"]
                            </MudButton>

                            <MudButton Variant="Variant.Filled"
                                       Color="Color.Primary"
                                       OnClick="ProcessSignature"
                                       Size="Size.Medium"
                                       StartIcon="@Icons.Material.Filled.Draw"
                                       Disabled="@IsProcessing">
                                @if (IsProcessing)
                                {
                                    <MudProgressCircular Size="Size.Small" Indeterminate="true" />
                                    @Localizer["Signing"]
                                }
                                else
                                {
                                    @Localizer["Sign"]
                                }
                            </MudButton>
                        </MudStack>
                    </MudStack>
                </MudPaper>
            }

            <!-- Step 2: Post-Sign Actions (shown after signing) -->
            @if (_showPostSignActions)
            {
                <MudPaper Class="mb-4" Elevation="3" Style="padding: 20px; border: 2px solid #4caf50; background: #f1f8e9;">
                    <MudStack Spacing="3">
                        <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
                            <MudIcon Icon="@Icons.Material.Filled.CheckCircle" Color="Color.Success" Size="Size.Medium" />
                            <MudText Typo="Typo.h6" Style="font-weight: 600; color: #4caf50;">
                                @Localizer["DocumentSigned"]
                            </MudText>
                        </MudStack>

                        <MudDivider />

                        <MudText Typo="Typo.body1" Style="font-weight: 500;">
                            @Localizer["ChooseNextAction"]
                        </MudText>

                        <MudStack Row Spacing="3" Justify="Justify.Center">
                            <!-- Option 1: Lock Document -->
                            <MudPaper Class="pa-4" Elevation="2" Style="border: 1px solid #ff9800; border-radius: 8px; text-align: center; min-width: 200px;">
                                <MudStack Spacing="2" AlignItems="AlignItems.Center">
                                    <MudIcon Icon="@Icons.Material.Filled.Lock" Color="Color.Warning" Size="Size.Large" />
                                    <MudText Typo="Typo.subtitle1" Style="font-weight: 600;">
                                        @Localizer["LockDocument"]
                                    </MudText>
                                    <MudText Typo="Typo.body2" Class="text-center">
                                        @Localizer["LockDocumentDescription"]
                                    </MudText>
                                    <MudButton Variant="Variant.Filled"
                                               Color="Color.Warning"
                                               OnClick="LockDocument"
                                               Disabled="@IsProcessing"
                                               FullWidth="true">
                                        @Localizer["Lock"]
                                    </MudButton>
                                </MudStack>
                            </MudPaper>

                            <!-- Option 2: Request Cosigning -->
                            <MudPaper Class="pa-4" Elevation="2" Style="border: 1px solid #2196f3; border-radius: 8px; text-align: center; min-width: 200px;">
                                <MudStack Spacing="2" AlignItems="AlignItems.Center">
                                    <MudIcon Icon="@Icons.Material.Filled.PersonAdd" Color="Color.Primary" Size="Size.Large" />
                                    <MudText Typo="Typo.subtitle1" Style="font-weight: 600;">
                                        @Localizer["RequestCosigning"]
                                    </MudText>
                                    <MudText Typo="Typo.body2" Class="text-center">
                                        @Localizer["RequestCosigningDescription"]
                                    </MudText>
                                    <MudButton Variant="Variant.Filled"
                                               Color="Color.Primary"
                                               OnClick="ShowCosigningRequestForm"
                                               Disabled="@IsProcessing"
                                               FullWidth="true">
                                        @Localizer["RequestCosigning"]
                                    </MudButton>
                                </MudStack>
                            </MudPaper>
                        </MudStack>
                    </MudStack>
                </MudPaper>
            }

            <!-- Cosigning Request Form -->
            @if (_showCosigningRequestForm)
            {
                <MudPaper Class="mb-4" Elevation="3" Style="padding: 20px; border: 2px solid #2196f3; background: #f3f8ff;">
                    <MudStack Spacing="3">
                        <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
                            <MudIcon Icon="@Icons.Material.Filled.PersonAdd" Color="Color.Primary" Size="Size.Medium" />
                            <MudText Typo="Typo.h6" Style="font-weight: 600; color: #2196f3;">
                                @Localizer["RequestCosigning"]
                            </MudText>
                            <MudSpacer />
                            <MudIconButton Icon="@Icons.Material.Filled.Close"
                                           Size="Size.Small"
                                           OnClick="CancelCosigningRequest"
                                           Color="Color.Error" />
                        </MudStack>

                        <MudDivider />

                        <MudSelect @bind-Value="_selectedReviewProvider"
                                   Label="@Localizer["SelectProvider"]"
                                   Variant="Variant.Outlined"
                                   FullWidth="true">
                            @foreach (var provider in _providerList)
                            {
                                <MudSelectItem Value="@provider">@provider.UserName</MudSelectItem>
                            }
                        </MudSelect>

                        <MudStack Row Justify="Justify.FlexEnd" Spacing="2">
                            <MudButton Variant="Variant.Outlined"
                                       Color="Color.Secondary"
                                       OnClick="CancelCosigningRequest"
                                       Size="Size.Medium">
                                @Localizer["Cancel"]
                            </MudButton>

                            <MudButton Variant="Variant.Filled"
                                       Color="Color.Primary"
                                       OnClick="SubmitCosigningRequest"
                                       Size="Size.Medium"
                                       StartIcon="@Icons.Material.Filled.Send"
                                       Disabled="@(IsProcessing || _selectedReviewProvider == null)">
                                @if (IsProcessing)
                                {
                                    <MudProgressCircular Size="Size.Small" Indeterminate="true" />
                                    @Localizer["Sending"]
                                }
                                else
                                {
                                    @Localizer["SendRequest"]
                                }
                            </MudButton>
                        </MudStack>
                    </MudStack>
                </MudPaper>
            }

            <!-- Signature Display Section -->
            <MudStack Spacing="3">
                <MudText Typo="Typo.subtitle1" Style="font-weight: 600;">
                    @Localizer["CurrentSignature"]
                </MudText>

                <!-- Primary Signature Display -->
                <MudTextField Variant="Variant.Outlined"
                              Value="@GetSignatureText()"
                              ReadOnly="true"
                              FullWidth="true"
                              Lines="3"
                              Multiline="true"
                              Style="font-family: monospace; background: #f8fff8; border: 1px solid #e0e0e0;" />

                <!-- Action Buttons -->
                <MudStack Row Justify="Justify.FlexEnd" Spacing="2">
                    @if (CanPerformSigningActions())
                    {
                        <MudButton Variant="Variant.Outlined"
                                   Color="Color.Primary"
                                   StartIcon="@Icons.Material.Filled.Edit"
                                   OnClick="ShowSigningForm"
                                   Disabled="@IsProcessing"
                                   Size="Size.Medium">
                            @GetSignButtonText()
                        </MudButton>
                    }
            </MudStack>

            <!-- Comments Section -->
            @if (ActiveCosigningRequest != null && !string.IsNullOrEmpty(ActiveCosigningRequest.CommentsJson))
            {
                <MudPaper Class="mt-4" Elevation="2" Style="padding: 16px; background: #f8f9fa; border-left: 4px solid #1976d2;">
                    <MudStack Spacing="2">
                        <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
                            <MudIcon Icon="@Icons.Material.Filled.Comment" Color="Color.Primary" Size="Size.Small" />
                            <MudText Typo="Typo.subtitle2" Style="font-weight: 600; color: #1976d2;">
                                @Localizer["ReviewComments"]
                            </MudText>
                        </MudStack>

                        <div style="max-height: 300px; overflow-y: auto;">
                            @foreach (var comment in CommentHelper.GetComments(ActiveCosigningRequest.CommentsJson).OrderBy(c => c.CommentDate))
                            {
                                <MudCard Class="mb-2" Elevation="1" Style="background: white;">
                                    <MudCardContent Class="pa-3">
                                        <MudStack Row Justify="Justify.SpaceBetween" AlignItems="AlignItems.Start" Class="mb-2">
                                            <MudText Typo="Typo.caption" Style="font-weight: 600; color: #1976d2;">
                                                @comment.CommenterName
                                            </MudText>
                                            <MudText Typo="Typo.caption" Class="text-muted">
                                                @comment.CommentDate.ToString("MM/dd/yyyy HH:mm")
                                            </MudText>
                                        </MudStack>

                                        <MudText Typo="Typo.body2" Style="line-height: 1.5; margin-bottom: 8px;">
                                            @comment.Comment
                                        </MudText>

                                        @if (comment.IsResolved)
                                        {
                                            <MudChip T="string" Color="Color.Success" Size="Size.Small" Icon="@Icons.Material.Filled.CheckCircle">
                                                @Localizer["Resolved"]
                                            </MudChip>
                                        }
                                        else
                                        {
                                            <MudChip T="string" Color="Color.Warning" Size="Size.Small" Icon="@Icons.Material.Filled.Schedule">
                                                @Localizer["NeedsAttention"]
                                            </MudChip>
                                        }
                                    </MudCardContent>
                                </MudCard>
                            }
                        </div>
                    </MudStack>
                </MudPaper>
            }

            <!-- Lock Status -->
            @if (CurrentCosigning.IsLocked)
            {
                <MudAlert Severity="Severity.Success"
                          Icon="@Icons.Material.Filled.Lock"
                          Class="mt-4">
                    <MudText Style="font-weight: 600;">
                        @Localizer["DocumentLocked"]
                    </MudText>
                    <MudText Typo="Typo.body2">
                        @Localizer["DocumentLockedDescription"]
                    </MudText>
                </MudAlert>
            }
        </MudPaper>
    }
</MudContainer>

<style>
    .cosigning-container {
        padding: 0;
        margin: 0;
        width: 100%;
    }

    .cosigning-paper {
        background: #ffffff;
        border: 1px solid #e0e0e0;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .text-muted {
        color: #6c757d;
    }

    /* Workflow step styling */
    .workflow-step {
        border-radius: 8px;
        transition: all 0.3s ease;
    }

    .workflow-step:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    }

    /* Action card styling */
    .action-card {
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .action-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 16px rgba(0,0,0,0.15);
    }

    /* Status indicators */
    .status-pending {
        border-left: 4px solid #ff9800;
    }

    .status-approved {
        border-left: 4px solid #4caf50;
    }

    .status-changes-requested {
        border-left: 4px solid #f44336;
    }

    /* Comment styling */
    .comment-card {
        border-radius: 6px;
        border: 1px solid #e0e0e0;
        transition: box-shadow 0.2s ease;
    }

    .comment-card:hover {
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    /* Button styling */
    .signature-button {
        min-width: 120px;
        font-weight: 500;
        text-transform: none;
        border-radius: 6px;
        transition: all 0.2s ease;
    }

    .signature-button:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    }
</style>