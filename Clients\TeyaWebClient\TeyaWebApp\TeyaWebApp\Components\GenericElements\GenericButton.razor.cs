using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Web;
using MudBlazor;

namespace TeyaWebApp.Components.GenericElements
{
    public partial class GenericButton:ComponentBase
    {
        [Parameter] public Variant Variant { get; set; } 
        [Parameter] public Color Color { get; set; } = Color.Primary;
        [Parameter] public bool Disabled { get; set; } = false;
        [Parameter] public EventCallback<MouseEventArgs> OnClick { get; set; }
        [Parameter] public RenderFragment ChildContent { get; set; }

        [Parameter] public string ButtonStyle { get; set; } = "";

    }
}