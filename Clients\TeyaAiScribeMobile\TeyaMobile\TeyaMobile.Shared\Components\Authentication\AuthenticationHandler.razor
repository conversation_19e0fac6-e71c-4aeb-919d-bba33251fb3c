@using Microsoft.Extensions.Logging
@using TeyaHealthMobileViewModel.ViewModel
@using TeyaHealthMobileModel.Model
@using System.Text.Json
@inject IAuthenticationService AuthService
@inject NavigationManager Navigation
@inject IOrganizationService OrganizationService
@inject IMemberService MemberService
@inject IRoleService RoleService
@inject ILogger<AuthenticationHandler> logger
@inject GraphApiService GraphService
@inject ActiveUser user
@inject IRoleslistService _RoleslistService
@inject StorageContainer StorageContainer

@code {
    private bool _userRegistrationProcessed = false;
    private bool Subscription = false;
    private const int zero = 0;
    private Guid OrganizationId { get; set; }
    private List<String> roles;

    protected override async Task OnInitializedAsync()
    {
        // Check for cached user session first
        if (!StorageContainer.OrganizationId.HasValue && !string.IsNullOrWhiteSpace(user.OrganizationName))
            {
                try
                {
                    var orgId = await OrganizationService.GetOrganizationIdByNameAsync(user.OrganizationName);
                    StorageContainer.OrganizationId = orgId;
                    logger.LogInformation($"StorageContainer.OrganizationId set for user: {user.OrganizationName}");
                }
                catch (Exception ex)
                {
                    logger.LogError(ex, $"Failed to set StorageContainer.OrganizationId for user: {user.OrganizationName}");
                }
            
            _userRegistrationProcessed = true;
            logger.LogInformation("Loaded ActiveUser from session cache.");
            return;
        }
        await CheckAuthenticationAndRegisterUser();
    }

    private async Task CheckAuthenticationAndRegisterUser()
    {
        try
        {
            if (AuthService.IsAuthenticated && !_userRegistrationProcessed)
            {
                _userRegistrationProcessed = true;
                await PopulateActiveUserOrganizationId();
                await HandleUserRegistration();
                // Cache the user after registration/organization population
                // await UserSessionService.SetActiveUserAsync(user);
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error in authentication handler: {Error}", ex.Message);
        }
    }

    private async Task PopulateActiveUserOrganizationId()
    {
        try
        {
            // Only populate if OrganizationName exists and StorageContainer.OrganizationId is not already set
            if (!string.IsNullOrWhiteSpace(user.OrganizationName) && !StorageContainer.OrganizationId.HasValue)
            {
                try
                {
                    var orgId = await OrganizationService.GetOrganizationIdByNameAsync(user.OrganizationName);
                    StorageContainer.OrganizationId = orgId;
                    logger.LogInformation("Successfully populated StorageContainer.OrganizationId: {OrganizationId} for organization: {OrganizationName}",
                        orgId, user.OrganizationName);
                }
                catch (KeyNotFoundException)
                {
                    logger.LogInformation("Organization '{OrganizationName}' not found, StorageContainer.OrganizationId will be set after organization creation",
                        user.OrganizationName);
                    StorageContainer.OrganizationId = null;
                }
                catch (Exception ex)
                {
                    logger.LogError(ex, "Error getting organization ID for: {OrganizationName}", user.OrganizationName);
                    StorageContainer.OrganizationId = null;
                }
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error populating StorageContainer OrganizationId");
        }
    }

    private async Task HandleUserRegistration()
    {
        try
        {
            var orgIdToCheck = user.OrganizationId ?? OrganizationId;
            var email_Is_Present = await MemberService.SearchMembersEmailAsync(user.mail, orgIdToCheck, Subscription);
            if (email_Is_Present)
            {
                Navigation.NavigateTo("/");
            }
            else
            {
                await RegisterNewUser();
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error in user registration: {Error}", ex.Message);
        }
    }

    private async Task RegisterNewUser()
    {
        try
        {
            await GraphService.GetLoggedInUserDetailsAsync();
            var result = await GraphService.GetUserDetailsAsync();

            if (result)
            {
                var userDetails = JsonSerializer.Deserialize<Dictionary<string, object>>(GraphService.UserDetails);

                try
                {
                    if (StorageContainer.OrganizationId.HasValue)
                    {
                        OrganizationId = StorageContainer.OrganizationId.Value;
                    }
                    else
                    {
                        OrganizationId = await OrganizationService.GetOrganizationIdByNameAsync(user.OrganizationName);
                        StorageContainer.OrganizationId = OrganizationId;
                    }
                }
                catch (KeyNotFoundException)
                {
                    logger.LogInformation("Organization '{OrganizationName}' not found, will be created during member registration", user.OrganizationName);
                    OrganizationId = Guid.Empty;
                    StorageContainer.OrganizationId = null;
                }

                bool userExists = false;
                if (OrganizationId != Guid.Empty)
                {
                    userExists = await MemberService.SearchMembersEmailAsync(user.mail, OrganizationId, Subscription);
                }

                if (!userExists)
                {
                    var newMember = await CreateMemberFromUserDetails();
                    if (newMember != null)
                    {
                        var responseMessage = await MemberService.RegisterMembersContentAsync(new List<Member> { newMember });

                        if (responseMessage != null)
                        {
                            logger.LogInformation("Member registered successfully");
                            OrganizationId = newMember.OrganizationID ?? Guid.Empty;

                            StorageContainer.OrganizationId = OrganizationId;

                            // Cache the user after registration
                            // await UserSessionService.SetActiveUserAsync(user);

                            Navigation.NavigateTo("/appointments");
                        }
                    }
                }
            }
        }
        catch (Exception ex)
        {
            var message = ex.Message;
            var message2 = ex.InnerException;
            logger.LogError(ex, "Error registering new user: {Error}", ex.Message);
        }
    }

    private async Task<Member> CreateMemberFromUserDetails()
    {
        TeyaHealthMobileModel.Model.Organization organization = null;
        Member registeredMember = null;

        try
        {
            var organizationDetails = await OrganizationService.GetOrganizationsByNameAsync(user.OrganizationName);
            organization = organizationDetails.FirstOrDefault();

            if (organization == null)
            {
                organization = new TeyaHealthMobileModel.Model.Organization
                {
                    OrganizationId = Guid.NewGuid(),
                    OrganizationName = user.OrganizationName
                };
                roles = await _RoleslistService.GetAllRoleNamesAsync();
                var createdOrganization = await OrganizationService.RegisterOrganizationsAsync(organization);

                if (createdOrganization == null)
                {
                    logger.LogError("Organization creation failed");
                    throw new Exception("Organization creation failed");
                }

                logger.LogInformation("New organization created: {OrganizationId}", createdOrganization.OrganizationId);
                organization = createdOrganization;
            }

            user.OrganizationId = organization.OrganizationId;

            registeredMember = new Member
            {
                Id = Guid.TryParse(user.id, out var userId) ? userId : Guid.NewGuid(),
                Email = user.mail,
                FirstName = user.givenName,
                LastName = user.surname,
                UserName = $"{user.givenName}{user.surname}",
                PhoneNumber = user.mobilePhone,
                Country = user.country,
                OrganizationID = organization.OrganizationId,
                OrganizationName = user.OrganizationName,
                Subscription = Subscription,
                Address = new Address
                {
                    AddressLine1 = user.streetAddress,
                    PostalCode = user.postalCode,
                    State = user.state,
                    Country = user.country,
                    Subscription = Subscription,
                },
                IsActive = true
            };

            var rolesdata = await RoleService.GetAllRolesByOrgIdAsync(organization.OrganizationId, Subscription);
            if (rolesdata.Count == zero)
            {
                foreach (var role in roles)
                {
                    var newRole = new Role
                    {
                        RoleId = Guid.NewGuid(),
                        RoleName = role,
                        CreatedDate = DateTime.Now,
                        IsActive = true,
                        UpdatedDate = DateTime.Now,
                        UpdatedBy = Guid.Parse(user.id),
                        OrganizationID = organization.OrganizationId,
                        Subscription = Subscription
                    };
                    await RoleService.RegisterRoleAsync(newRole);

                    if (role == "Admin")
                    {
                        registeredMember.RoleID = newRole.RoleId;
                        registeredMember.RoleName = "Admin";
                    }
                }
            }
            else
            {
                var adminRole = rolesdata.FirstOrDefault(r => r.RoleName == "Admin");
                if (adminRole != null)
                {
                    registeredMember.RoleID = adminRole.RoleId;
                    registeredMember.RoleName = "Admin";
                }
            }

            var updateFields = new Dictionary<string, object>
            {
                { "displayName", registeredMember.UserName }
            };

            bool updateSuccessful = await GraphService.UpdateUserProfileAsync(user.id, updateFields);
            if (!updateSuccessful)
            {
                logger.LogError("Failed to update display name");
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error processing organization: {Error}", ex.Message);
        }

        return registeredMember;
    }

    // Helper to copy properties from cached user to injected user instance
    private void CopyUser(ActiveUser source, ActiveUser target)
    {
        target.id = source.id;
        target.displayName = source.displayName;
        target.givenName = source.givenName;
        target.surname = source.surname;
        target.userType = source.userType;
        target.jobTitle = source.jobTitle;
        target.companyName = source.companyName;
        target.department = source.department;
        target.officeLocation = source.officeLocation;
        target.streetAddress = source.streetAddress;
        target.city = source.city;
        target.state = source.state;
        target.postalCode = source.postalCode;
        target.country = source.country;
        target.role = source.role;
        target.businessPhones = source.businessPhones;
        target.mobilePhone = source.mobilePhone;
        target.mail = source.mail;
        target.OrganizationName = source.OrganizationName;
        target.Address = source.Address;
        target.OrganizationId = source.OrganizationId;
        target.oDataContext = source.oDataContext;
    }
}
