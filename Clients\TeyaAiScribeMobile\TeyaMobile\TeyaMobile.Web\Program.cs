using Blazored.LocalStorage;
using Microsoft.AspNetCore.Authentication.OpenIdConnect;
using Microsoft.AspNetCore.SignalR;
using Microsoft.Identity.Web;
using Microsoft.Identity.Web.UI;
using MudBlazor.Services;
using Syncfusion.Blazor;
using Syncfusion.Licensing;
using System.Text.Json;
using System.Text.Json.Serialization;
using TeyaHealthMobileModel.Model;
using TeyaHealthMobileViewModel.ViewModel;
using TeyaMobile.Shared.Services;
using TeyaMobile.Shared.Services.TeyaMobile.Shared.Services;
using TeyaMobile.Web.Components;
using TeyaMobile.Web.Services;

var builder = WebApplication.CreateBuilder(args);

var syncfusionKey = builder.Configuration["SyncfusionKey"];
if (!string.IsNullOrEmpty(syncfusionKey))
{
    SyncfusionLicenseProvider.RegisterLicense(syncfusionKey);
}

builder.Services.AddRazorComponents()
    .AddInteractiveServerComponents();
builder.Services.AddMudServices();
//builder.Services.AddMudServices(options =>
//{
//    // Optional: Disable popover provider check for unit testing
//    options.PopoverOptions.CheckForPopoverProvider = false;
//});

// Microsoft Identity authentication with token acquisition
builder.Services.AddAuthentication(OpenIdConnectDefaults.AuthenticationScheme)
    .AddMicrosoftIdentityWebApp(builder.Configuration.GetSection("AzureAd"))
    .EnableTokenAcquisitionToCallDownstreamApi()
    .AddDistributedTokenCaches(); // Use distributed cache instead of in-memory

// Configure OpenID Connect options
builder.Services.Configure<OpenIdConnectOptions>(OpenIdConnectDefaults.AuthenticationScheme, options =>
{
    // Set the correct callback path
    options.CallbackPath = "/signin-oidc";
    options.SignedOutCallbackPath = "/signout-callback-oidc";

    // Save tokens to authentication properties - THIS IS CRITICAL FOR TOKEN RETRIEVAL
    options.SaveTokens = true;

    // Configure token refresh
    options.UseTokenLifetime = false; // Don't use token lifetime for authentication cookie expiration

    // Request the required scopes including Graph API for user context
    options.Scope.Clear();
    options.Scope.Add("openid");
    options.Scope.Add("profile");
    options.Scope.Add("email");
    options.Scope.Add("https://graph.microsoft.com/.default");
    options.Scope.Add("offline_access");

    // Configure response type for better token handling
    options.ResponseType = "code";
    options.UsePkce = true;
    options.GetClaimsFromUserInfoEndpoint = false; // CIAM doesn't support UserInfo endpoint
    options.MapInboundClaims = false; // Don't map claims to legacy format

    options.Events.OnRedirectToIdentityProvider = context =>
    {
        context.ProtocolMessage.RedirectUri = $"{context.Request.Scheme}://{context.Request.Host}/signin-oidc";
        return Task.CompletedTask;
    };

    options.Events.OnRedirectToIdentityProviderForSignOut = context =>
    {
        // Ensure we use the correct post logout redirect URI
        context.ProtocolMessage.PostLogoutRedirectUri = context.Request.Scheme + "://" + context.Request.Host + "/signout-callback-oidc";
        return Task.CompletedTask;
    };

    options.Events.OnAuthenticationFailed = async context =>
    {
        var logger = context.HttpContext.RequestServices.GetRequiredService<ILogger<Program>>();
        logger.LogError("Authentication failed: {Error}", context.Exception?.Message);

        context.Response.Redirect("/MicrosoftIdentity/Account/AccessDenied");
        context.HandleResponse();
    };



    options.Events.OnTicketReceived = context =>
    {
        // Let the default flow continue - don't force redirect here
        // The authentication middleware will handle the redirect properly
        return Task.CompletedTask;
    };

    // Add token refresh event handling
    options.Events.OnTokenValidated = context =>
    {
        // Token validated successfully - this is where refresh tokens are also stored
        return Task.CompletedTask;
    };

    options.Events.OnAccessDenied = context =>
    {
        // Handle access denied - redirect to home page for re-authentication
        context.Response.Redirect("/");
        context.HandleResponse();
        return Task.CompletedTask;
    };

    // Handle authentication failures
    options.Events.OnRemoteFailure = async context =>
    {
        var logger = context.HttpContext.RequestServices.GetRequiredService<ILogger<Program>>();
        logger.LogError("Remote authentication failure: {Error}", context.Failure?.Message);

        context.Response.Redirect("/");
        context.HandleResponse();
    };
});

// Add Microsoft Identity UI
builder.Services.AddRazorPages()
    .AddMicrosoftIdentityUI();

// Authorization
builder.Services.AddAuthorization();

// HTTP Context Accessor
builder.Services.AddHttpContextAccessor();
builder.Services.AddHttpClient();
builder.Services.AddLocalization();

builder.Services.AddBlazoredLocalStorage();
builder.Services.AddScoped<ISpeechService, SpeechService>();
builder.Services.AddScoped<IAudioRecorder, DummyAudioRecorder>();
builder.Services.AddScoped<IAppointmentService, AppointmentService>();
builder.Services.AddScoped<IProgressNotesService, ProgressNotesService>();
builder.Services.AddScoped<IPredefinedTemplateService, PredefinedTemplateService>();
builder.Services.AddScoped<StorageContainer>();

builder.Services.Configure<JsonSerializerOptions>(options =>
{
    options.PropertyNamingPolicy = JsonNamingPolicy.CamelCase;
    options.DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull;
    options.Converters.Add(new ActiveUserConverter());
    options.PropertyNameCaseInsensitive = true;
});

builder.Services.AddScoped<IOrganizationService, OrganizationService>();
builder.Services.AddScoped<TeyaHealthMobileViewModel.ViewModel.IAuthenticationService, SimpleWebAuthenticationService>();
builder.Services.AddScoped<TeyaHealthMobileModel.Model.ActiveUser>();
builder.Services.AddScoped<IRoleslistService, RoleslistService>();
builder.Services.AddScoped<IRoleService, RoleService>();
builder.Services.AddScoped<GraphApiService>();
builder.Services.AddScoped<IMemberService, MemberService>();
builder.Services.AddSingleton<IFormFactor, FormFactor>();
builder.Services.AddScoped<TeyaHealthMobileViewModel.ViewModel.IGraphAdminService, GraphAdminService>();
builder.Services.AddSingleton<INavigationHistoryService, NavigationHistoryService>();
builder.Services.AddSingleton<INavigationService, NavigationService>();

builder.Configuration
    .SetBasePath(Directory.GetCurrentDirectory())
    .AddJsonFile("appsettings.json", optional: true, reloadOnChange: true)
    .AddEnvironmentVariables();
builder.Services.AddSyncfusionBlazor();

// Enhanced logging for SignalR and Blazor Server debugging
builder.Logging.AddConsole();
if (builder.Environment.IsDevelopment())
{
    builder.Logging.SetMinimumLevel(Microsoft.Extensions.Logging.LogLevel.Debug);
    builder.Logging.AddFilter("Microsoft.AspNetCore.SignalR", Microsoft.Extensions.Logging.LogLevel.Debug);
    builder.Logging.AddFilter("Microsoft.AspNetCore.Http.Connections", Microsoft.Extensions.Logging.LogLevel.Debug);
    builder.Logging.AddFilter("Microsoft.AspNetCore.Components.Server.Circuits", Microsoft.Extensions.Logging.LogLevel.Debug);
}

var app = builder.Build();

if (!app.Environment.IsDevelopment())
{
    app.UseExceptionHandler("/Error", createScopeForErrors: true);
    // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
    app.UseHsts();
}

app.UseHttpsRedirection();
app.UseStaticFiles();
app.UseRouting();

// Authentication & Authorization
app.UseAuthentication();
app.UseAuthorization();
app.UseAntiforgery();

app.MapRazorComponents<App>()
    .AddInteractiveServerRenderMode()
    .AddAdditionalAssemblies(typeof(TeyaMobile.Shared._Imports).Assembly);

// Map Microsoft Identity routes for authentication
app.MapControllers();
app.MapRazorPages();

app.Run();
