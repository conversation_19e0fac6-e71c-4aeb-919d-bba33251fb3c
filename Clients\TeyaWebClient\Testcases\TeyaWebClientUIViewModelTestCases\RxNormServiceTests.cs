using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using Moq;
using Moq.Protected;
using NUnit.Framework;
using TeyaUIModels.Model;
using TeyaUIViewModels.ViewModel;
using TeyaUIViewModels.TeyaUIViewModelResources;
using System.Net.Http.Json;
using System.Text.Json;

namespace TeyaWebClientUIViewModelTestCases
{
    [TestFixture]
    public class RxNormServiceTests
    {
        private Mock<HttpMessageHandler> _mockHttpMessageHandler;
        private HttpClient _httpClient;
        private Mock<IConfiguration> _mockConfiguration;
        private Mock<IStringLocalizer<TeyaUIViewModelsStrings>> _mockLocalizer;
        private Mock<ITokenService> _mockTokenService;
        private RxNormService _rxNormService;
        private readonly string _baseUrl = "http://test-api.com";
        private readonly string _rxNormUrl = "https://rxnav.nlm.nih.gov/REST";
        private readonly string _accessToken = "test-access-token";

        [SetUp]
        public void Setup()
        {
            // Setup environment variables
            Environment.SetEnvironmentVariable("EncounterNotesURL", _baseUrl);
            Environment.SetEnvironmentVariable("RxNormUrl", _rxNormUrl);
            Environment.SetEnvironmentVariable("RxNormBrandListUrl", "https://rxnav.nlm.nih.gov/REST/brands.json");
            Environment.SetEnvironmentVariable("RxNormBrandSBDListUrl", "https://rxnav.nlm.nih.gov/REST/rxcui/{0}/related.json?tty=SBD");
            Environment.SetEnvironmentVariable("RxNormDrugListUrl", "https://rxnav.nlm.nih.gov/REST/allconcepts.json?tty=IN");

            // Setup mock HTTP handler
            _mockHttpMessageHandler = new Mock<HttpMessageHandler>();

            // Setup HttpClient with mock handler
            _httpClient = new HttpClient(_mockHttpMessageHandler.Object)
            {
                BaseAddress = new Uri(_baseUrl)
            };

            // Setup mock configuration
            _mockConfiguration = new Mock<IConfiguration>();

            // Setup mock localizer
            _mockLocalizer = new Mock<IStringLocalizer<TeyaUIViewModelsStrings>>();
            _mockLocalizer.Setup(l => l["AddressRetrievalFailure"])
                .Returns(new LocalizedString("AddressRetrievalFailure", "Address retrieval failure"));

            // Setup mock token service
            _mockTokenService = new Mock<ITokenService>();
            _mockTokenService.Setup(t => t.AccessToken).Returns(_accessToken);

            // Create RxNormService with mocked dependencies
            _rxNormService = new RxNormService(_httpClient, _mockConfiguration.Object, _mockLocalizer.Object, _mockTokenService.Object);
        }

        [TearDown]
        public void TearDown()
        {
            _httpClient.Dispose();
            Environment.SetEnvironmentVariable("EncounterNotesURL", null);
            Environment.SetEnvironmentVariable("RxNormUrl", null);
            Environment.SetEnvironmentVariable("RxNormBrandListUrl", null);
            Environment.SetEnvironmentVariable("RxNormBrandSBDListUrl", null);
            Environment.SetEnvironmentVariable("RxNormDrugListUrl", null);
        }

        [Test]
        public async Task GetAllRxNormMedications_WhenSuccessful_ReturnsRxNormConcepts()
        {
            // Arrange
            var expectedConcepts = new List<RxNormConcept>
            {
                new RxNormConcept
                {
                    RXCUI = "123456",
                    STR = "Aspirin",
                    TTY = "IN",
                    LAT = "ENG",
                    SUPPRESS = "N",
                    RXAUI = "A123456",
                    SAUI = "S123456",
                    SCUI = "C123456",
                    SAB = "RXNORM",
                    CODE = "123456",
                    CVF = "4096"
                },
                new RxNormConcept
                {
                    RXCUI = "789012",
                    STR = "Ibuprofen",
                    TTY = "IN",
                    LAT = "ENG",
                    SUPPRESS = "N",
                    RXAUI = "A789012",
                    SAUI = "S789012",
                    SCUI = "C789012",
                    SAB = "RXNORM",
                    CODE = "789012",
                    CVF = "4096"
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedConcepts)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _rxNormService.GetAllRxNormMedications();

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(expectedConcepts.Count));
            Assert.That(result[0].RXCUI, Is.EqualTo(expectedConcepts[0].RXCUI));
            Assert.That(result[0].STR, Is.EqualTo(expectedConcepts[0].STR));
            Assert.That(result[0].STR, Is.EqualTo(expectedConcepts[0].STR));
            Assert.That(result[0].TTY, Is.EqualTo(expectedConcepts[0].TTY));
            Assert.That(result[1].STR, Is.EqualTo(expectedConcepts[1].STR));
        }

        [Test]
        public void GetAllRxNormMedications_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.NotFound,
                Content = new StringContent("Not found")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _rxNormService.GetAllRxNormMedications());

            Assert.That(exception.Message, Is.EqualTo("Address retrieval failure"));
        }

        [Test]
        public async Task GetRxNormSBDCMedications_WhenSuccessful_ReturnsFilteredConcepts()
        {
            // Arrange
            var searchString = "aspirin";
            var expectedConcepts = new List<RxNormConcept>
            {
                new RxNormConcept
                {
                    RXCUI = "123456",
                    STR = "Aspirin 325 MG Oral Tablet",
                    TTY = "SBD",
                    LAT = "ENG",
                    SUPPRESS = "N",
                    RXAUI = "A123456",
                    SAUI = "S123456",
                    SCUI = "C123456",
                    SAB = "RXNORM",
                    CODE = "123456",
                    CVF = "4096"
                },
                new RxNormConcept
                {
                    RXCUI = "123457",
                    STR = "Aspirin 81 MG Oral Tablet",
                    TTY = "SBD",
                    LAT = "ENG",
                    SUPPRESS = "N",
                    RXAUI = "A123457",
                    SAUI = "S123457",
                    SCUI = "C123457",
                    SAB = "RXNORM",
                    CODE = "123457",
                    CVF = "4096"
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedConcepts)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _rxNormService.GetRxNormSBDCMedications(searchString);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(expectedConcepts.Count));
            Assert.That(result[0].STR, Does.Contain("Aspirin"));
            Assert.That(result[0].TTY, Is.EqualTo("SBD"));
            Assert.That(result[1].STR, Does.Contain("Aspirin"));
        }

        [Test]
        public void GetRxNormSBDCMedications_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var searchString = "invalid";

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _rxNormService.GetRxNormSBDCMedications(searchString));

            Assert.That(exception.Message, Is.EqualTo("Address retrieval failure"));
        }

        [Test]
        public async Task GetAllBrandNames_WhenSuccessful_ReturnsBrandNames()
        {
            // Arrange
            var brandNamesResponse = @"{
                ""minConceptGroup"": {
                    ""minConcept"": [
                        {
                            ""name"": ""Tylenol"",
                            ""rxcui"": ""202433""
                        },
                        {
                            ""name"": ""Advil"",
                            ""rxcui"": ""153165""
                        }
                    ]
                }
            }";

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(brandNamesResponse)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.Is<HttpRequestMessage>(req => req.RequestUri.ToString().Contains("allconcepts.json?tty=BN")),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _rxNormService.GetAllBrandNames();

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(2));
            Assert.That(result, Does.Contain("Tylenol"));
            Assert.That(result, Does.Contain("Advil"));
        }

        [Test]
        public async Task GetAllBrandNames_WhenEmptyResponse_ReturnsEmptyList()
        {
            // Arrange
            var brandNamesResponse = @"{
                ""minConceptGroup"": {
                    ""minConcept"": []
                }
            }";

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(brandNamesResponse)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.Is<HttpRequestMessage>(req => req.RequestUri.ToString().Contains("allconcepts.json?tty=BN")),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _rxNormService.GetAllBrandNames();

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(0));
        }

        [Test]
        public void GetAllBrandNames_WhenExceptionThrown_WrapsException()
        {
            // Arrange
            var expectedException = new HttpRequestException("Test exception");

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.Is<HttpRequestMessage>(req => req.RequestUri.ToString().Contains("allconcepts.json?tty=BN")),
                    ItExpr.IsAny<CancellationToken>())
                .ThrowsAsync(expectedException);

            // Act & Assert
            var exception = Assert.ThrowsAsync<Exception>(async () =>
                await _rxNormService.GetAllBrandNames());

            Assert.That(exception.Message, Does.Contain("Request error"));
            Assert.That(exception.InnerException, Is.EqualTo(expectedException));
        }

        [Test]
        public async Task GetSBDNamesAsync_WhenSuccessful_ReturnsSBDNames()
        {
            // Arrange
            var brandName = "Tylenol";
            var sbdResponse = @"{
                ""drugGroup"": {
                    ""conceptGroup"": [
                        {
                            ""tty"": ""SBD"",
                            ""conceptProperties"": [
                                {
                                    ""name"": ""Tylenol 325 MG Oral Tablet"",
                                    ""rxcui"": ""209387""
                                },
                                {
                                    ""name"": ""Tylenol 500 MG Oral Tablet"",
                                    ""rxcui"": ""209388""
                                }
                            ]
                        }
                    ]
                }
            }";

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(sbdResponse)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.Is<HttpRequestMessage>(req => req.RequestUri.ToString().Contains("drugs.json?name=")),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _rxNormService.GetSBDNamesAsync(brandName);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(2));
            Assert.That(result, Does.Contain("Tylenol 325 MG Oral Tablet"));
            Assert.That(result, Does.Contain("Tylenol 500 MG Oral Tablet"));
        }

        [Test]
        public async Task GetAllDrugNames_WhenCacheEmpty_FetchesFromAPI()
        {
            // Arrange
            var drugNamesResponse = @"{
                ""minConceptGroup"": {
                    ""minConcept"": [
                        {
                            ""name"": ""Acetaminophen"",
                            ""rxcui"": ""161""
                        },
                        {
                            ""name"": ""Ibuprofen"",
                            ""rxcui"": ""5640""
                        }
                    ]
                }
            }";

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(drugNamesResponse)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.Is<HttpRequestMessage>(req => req.RequestUri.ToString().Contains("allconcepts.json?tty=IN")),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _rxNormService.GetAllDrugNames();

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(2));
            Assert.That(result, Does.Contain("Acetaminophen"));
            Assert.That(result, Does.Contain("Ibuprofen"));
        }

        [Test]
        public void GetRxcuiByName_WhenNameExists_ReturnsRxcui()
        {
            // This test would require the cache to be populated first
            // For now, we'll test the basic functionality
            
            // Arrange
            var drugName = "Acetaminophen";

            // Act
            var result = _rxNormService.GetRxcuiByName(drugName);

            // Assert
            // Since the cache is empty in this test, it should return null or empty
            Assert.That(result, Is.Null.Or.Empty);
        }

        [Test]
        public void GetRxcuiByName_WhenNameDoesNotExist_ReturnsNull()
        {
            // Arrange
            var drugName = "NonExistentDrug";

            // Act
            var result = _rxNormService.GetRxcuiByName(drugName);

            // Assert
            Assert.That(result, Is.Null.Or.Empty);
        }
    }
}



