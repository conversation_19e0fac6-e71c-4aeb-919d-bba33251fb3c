using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using Moq;
using Moq.Protected;
using NUnit.Framework;
using TeyaUIModels.Model;
using TeyaUIViewModels.ViewModel;
using TeyaUIViewModels.TeyaUIViewModelResources;
using System.Net.Http.Json;
using System.Text.Json;

namespace TeyaWebClientUIViewModelTestCases
{
    [TestFixture]
    public class ReviewOfSystemServiceTests
    {
        private Mock<HttpMessageHandler> _mockHttpMessageHandler;
        private HttpClient _httpClient;
        private Mock<IConfiguration> _mockConfiguration;
        private Mock<IStringLocalizer<TeyaUIViewModelsStrings>> _mockLocalizer;
        private Mock<ITokenService> _mockTokenService;
        private ReviewOfSystemService _reviewOfSystemService;
        private readonly string _baseUrl = "http://test-api.com";
        private readonly string _accessToken = "test-access-token";

        [SetUp]
        public void Setup()
        {
            // Setup environment variable
            Environment.SetEnvironmentVariable("EncounterNotesURL", _baseUrl);

            // Setup mock HTTP handler
            _mockHttpMessageHandler = new Mock<HttpMessageHandler>();

            // Setup HttpClient with mock handler
            _httpClient = new HttpClient(_mockHttpMessageHandler.Object)
            {
                BaseAddress = new Uri(_baseUrl)
            };

            // Setup mock configuration
            _mockConfiguration = new Mock<IConfiguration>();

            // Setup mock localizer
            _mockLocalizer = new Mock<IStringLocalizer<TeyaUIViewModelsStrings>>();
            _mockLocalizer.Setup(l => l["TasksRetrievalFailure"])
                .Returns(new LocalizedString("TasksRetrievalFailure", "Tasks retrieval failure"));
            _mockLocalizer.Setup(l => l["DatabaseError"])
                .Returns(new LocalizedString("DatabaseError", "Database error"));

            // Setup mock token service
            _mockTokenService = new Mock<ITokenService>();
            _mockTokenService.Setup(t => t.AccessToken).Returns(_accessToken);

            // Create ReviewOfSystemService with mocked dependencies
            _reviewOfSystemService = new ReviewOfSystemService(_httpClient, _mockConfiguration.Object, _mockLocalizer.Object, _mockTokenService.Object);
        }

        [TearDown]
        public void TearDown()
        {
            _httpClient.Dispose();
            Environment.SetEnvironmentVariable("EncounterNotesURL", null);
        }

        [Test]
        public async Task GetAllByIdAsync_WhenSuccessful_ReturnsReviewOfSystems()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var expectedReviews = new List<ReviewOfSystem>
            {
                new ReviewOfSystem
                {
                    ReviewOfSystemId = Guid.NewGuid(),
                    PatientId = patientId,
                    OrganizationId = Guid.NewGuid(),
                    PcpId = Guid.NewGuid(),
                    Decription = "No abnormalities detected",
                    IsActive = true,
                    CreatedDate = DateTime.Now.AddDays(-7),
                    RunnyNose = false,
                    Congestion = false,
                    ShortnessOfBreath = false,
                    ChestPain = false,
                    ItchyEyes = false,
                    Subscription = true
                },
                new ReviewOfSystem
                {
                    ReviewOfSystemId = Guid.NewGuid(),
                    PatientId = patientId,
                    OrganizationId = Guid.NewGuid(),
                    PcpId = Guid.NewGuid(),
                    Decription = "No respiratory distress",
                    IsActive = true,
                    CreatedDate = DateTime.Now.AddDays(-7),
                    RunnyNose = false,
                    Congestion = false,
                    ShortnessOfBreath = false,
                    ChestPain = false,
                    ItchyEyes = false,
                    Subscription = true
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedReviews)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _reviewOfSystemService.GetAllByIdAsync(patientId, orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(expectedReviews.Count));
            Assert.That(result[0].ReviewOfSystemId, Is.EqualTo(expectedReviews[0].ReviewOfSystemId));
            Assert.That(result[0].PatientId, Is.EqualTo(expectedReviews[0].PatientId));
            Assert.That(result[0].Decription, Is.EqualTo(expectedReviews[0].Decription));
            Assert.That(result[0].IsActive, Is.EqualTo(expectedReviews[0].IsActive));
        }

        [Test]
        public void GetAllByIdAsync_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.NotFound,
                Content = new StringContent("Not found")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _reviewOfSystemService.GetAllByIdAsync(patientId, orgId, subscription));

            Assert.That(exception.Message, Does.Contain("Exception of type"));
        }

        [Test]
        public async Task GetAllByIdAndIsActiveAsync_WhenSuccessful_ReturnsActiveReviewOfSystems()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var expectedReviews = new List<ReviewOfSystem>
            {
                new ReviewOfSystem
                {
                    ReviewOfSystemId = Guid.NewGuid(),
                    PatientId = patientId,
                    OrganizationId = Guid.NewGuid(),
                    PcpId = Guid.NewGuid(),
                    Decription = "Normal neurological exam",
                    IsActive = true,
                    CreatedDate = DateTime.Now.AddDays(-3),
                    RunnyNose = false,
                    Congestion = false,
                    ShortnessOfBreath = false,
                    ChestPain = false,
                    ItchyEyes = false,
                    Subscription = true
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedReviews)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _reviewOfSystemService.GetAllByIdAndIsActiveAsync(patientId, orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(expectedReviews.Count));
            Assert.That(result[0].IsActive, Is.True);
            Assert.That(result[0].Decription, Is.EqualTo("Normal neurological exam"));
        }

        [Test]
        public void GetAllByIdAndIsActiveAsync_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _reviewOfSystemService.GetAllByIdAndIsActiveAsync(patientId, orgId, subscription));

            Assert.That(exception.Message, Does.Contain("Exception of type"));
        }

        [Test]
        public async Task AddReviewOfSystemAsync_WhenSuccessful_CompletesSuccessfully()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;
            var reviewOfSystems = new List<ReviewOfSystem>
            {
                new ReviewOfSystem
                {
                    ReviewOfSystemId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    OrganizationId = Guid.NewGuid(),
                    PcpId = Guid.NewGuid(),
                    Decription = "No abdominal pain",
                    IsActive = true,
                    CreatedDate = DateTime.Now,
                    RunnyNose = false,
                    Congestion = false,
                    ShortnessOfBreath = false,
                    ChestPain = false,
                    ItchyEyes = false,
                    Subscription = true
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.Created,
                Content = new StringContent("Success")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.DoesNotThrowAsync(async () => await _reviewOfSystemService.AddReviewOfSystemAsync(reviewOfSystems, orgId, subscription));
        }

        [Test]
        public void AddReviewOfSystemAsync_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;
            var reviewOfSystems = new List<ReviewOfSystem>
            {
                new ReviewOfSystem
                {
                    ReviewOfSystemId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    OrganizationId = Guid.NewGuid(),
                    PcpId = Guid.NewGuid(),
                    Decription = "No abdominal pain",
                    IsActive = true,
                    CreatedDate = DateTime.Now,
                    RunnyNose = false,
                    Congestion = false,
                    ShortnessOfBreath = false,
                    ChestPain = false,
                    ItchyEyes = false,
                    Subscription = true
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _reviewOfSystemService.AddReviewOfSystemAsync(reviewOfSystems, orgId, subscription));

            Assert.That(exception.Message, Is.EqualTo("Database error"));
        }

        [Test]
        public async Task UpdateReviewOfSystemAsync_WhenSuccessful_CompletesSuccessfully()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;
            var reviewOfSystem = new ReviewOfSystem
            {
                ReviewOfSystemId = Guid.NewGuid(),
                PatientId = Guid.NewGuid(),
                OrganizationId = orgId,
                PcpId = Guid.NewGuid(),
                Decription = "Musculoskeletal: Full range of motion, no joint pain or stiffness",
                IsActive = true,
                RunnyNose = false,
                Congestion = false,
                ShortnessOfBreath = false,
                ChestPain = false,
                ItchyEyes = false,
                Subscription = subscription,
                CreatedDate = DateTime.Now.AddDays(-1),
                UpdatedDate = DateTime.Now
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent("Success")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.DoesNotThrowAsync(async () => await _reviewOfSystemService.UpdateReviewOfSystemAsync(reviewOfSystem, orgId, subscription));
        }

        [Test]
        public void UpdateReviewOfSystemAsync_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;
            var reviewOfSystem = new ReviewOfSystem
            {
                ReviewOfSystemId = Guid.NewGuid(),
                PatientId = Guid.NewGuid(),
                OrganizationId = orgId,
                PcpId = Guid.NewGuid(),
                Decription = "Musculoskeletal: Full range of motion, no joint pain or stiffness",
                IsActive = true,
                RunnyNose = false,
                Congestion = false,
                ShortnessOfBreath = false,
                ChestPain = false,
                ItchyEyes = false,
                Subscription = subscription,
                CreatedDate = DateTime.Now.AddDays(-1),
                UpdatedDate = DateTime.Now
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _reviewOfSystemService.UpdateReviewOfSystemAsync(reviewOfSystem, orgId, subscription));

            Assert.That(exception, Is.Not.Null);
        }

        [Test]
        public async Task DeleteReviewOfSystemAsync_WhenSuccessful_CompletesSuccessfully()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;
            var reviewOfSystem = new ReviewOfSystem
            {
                ReviewOfSystemId = Guid.NewGuid(),
                PatientId = Guid.NewGuid(),
                OrganizationId = orgId,
                PcpId = Guid.NewGuid(),
                Decription = "Dermatological: No rashes or lesions, skin appears normal",
                IsActive = false,
                RunnyNose = false,
                Congestion = false,
                ShortnessOfBreath = false,
                ChestPain = false,
                ItchyEyes = false,
                Subscription = subscription,
                CreatedDate = DateTime.Now.AddDays(-10),
                UpdatedDate = DateTime.Now.AddDays(-9)
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent("Success")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.DoesNotThrowAsync(async () => await _reviewOfSystemService.DeleteReviewOfSystemByEntityAsync(reviewOfSystem, orgId, subscription));
        }

        [Test]
        public void DeleteReviewOfSystemAsync_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;
            var reviewOfSystem = new ReviewOfSystem
            {
                ReviewOfSystemId = Guid.NewGuid(),
                PatientId = Guid.NewGuid(),
                OrganizationId = orgId,
                PcpId = Guid.NewGuid(),
                Decription = "Dermatological: No rashes or lesions, skin appears normal",
                IsActive = false,
                RunnyNose = false,
                Congestion = false,
                ShortnessOfBreath = false,
                ChestPain = false,
                ItchyEyes = false,
                Subscription = subscription,
                CreatedDate = DateTime.Now.AddDays(-10),
                UpdatedDate = DateTime.Now.AddDays(-9)
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _reviewOfSystemService.DeleteReviewOfSystemByEntityAsync(reviewOfSystem, orgId, subscription));

            Assert.That(exception.Message, Does.Contain("Exception of type"));
        }

        [Test]
        public async Task UpdateReviewOfSystemListAsync_WhenSuccessful_CompletesSuccessfully()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;
            var reviewOfSystems = new List<ReviewOfSystem>
            {
                new ReviewOfSystem
                {
                    ReviewOfSystemId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    OrganizationId = orgId,
                    PcpId = Guid.NewGuid(),
                    Decription = "Endocrine: No thyroid enlargement, normal endocrine function",
                    IsActive = true,
                    RunnyNose = false,
                    Congestion = false,
                    ShortnessOfBreath = false,
                    ChestPain = false,
                    ItchyEyes = false,
                    Subscription = subscription,
                    CreatedDate = DateTime.Now.AddDays(-2),
                    UpdatedDate = DateTime.Now.AddDays(-1)
                },
                new ReviewOfSystem
                {
                    ReviewOfSystemId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    OrganizationId = orgId,
                    PcpId = Guid.NewGuid(),
                    Decription = "Hematologic: No bleeding or bruising, normal blood work",
                    IsActive = true,
                    RunnyNose = false,
                    Congestion = false,
                    ShortnessOfBreath = false,
                    ChestPain = false,
                    ItchyEyes = false,
                    Subscription = subscription,
                    CreatedDate = DateTime.Now.AddDays(-1),
                    UpdatedDate = DateTime.Now
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent("Success")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.DoesNotThrowAsync(async () => await _reviewOfSystemService.UpdateReviewOfSystemListAsync(reviewOfSystems, orgId, subscription));
        }

        [Test]
        public void UpdateReviewOfSystemListAsync_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;
            var reviewOfSystems = new List<ReviewOfSystem>
            {
                new ReviewOfSystem
                {
                    ReviewOfSystemId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    OrganizationId = orgId,
                    PcpId = Guid.NewGuid(),
                    Decription = "Endocrine: No thyroid enlargement, normal endocrine function",
                    IsActive = true,
                    RunnyNose = false,
                    Congestion = false,
                    ShortnessOfBreath = false,
                    ChestPain = false,
                    ItchyEyes = false,
                    Subscription = subscription,
                    CreatedDate = DateTime.Now.AddDays(-2),
                    UpdatedDate = DateTime.Now.AddDays(-1)
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _reviewOfSystemService.UpdateReviewOfSystemListAsync(reviewOfSystems, orgId, subscription));

            Assert.That(exception, Is.Not.Null);
        }

        [Test]
        public async Task GetAllByIdAsync_WhenEmptyResponse_ReturnsEmptyList()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var expectedReviews = new List<ReviewOfSystem>();

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedReviews)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _reviewOfSystemService.GetAllByIdAsync(patientId, orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(0));
        }

        [Test]
        public void GetAllByIdAsync_WhenExceptionThrown_PropagatesException()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var expectedException = new HttpRequestException("Test exception");

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ThrowsAsync(expectedException);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _reviewOfSystemService.GetAllByIdAsync(patientId, orgId, subscription));

            Assert.That(exception, Is.Not.Null);
            Assert.That(exception.Message, Is.EqualTo("Test exception"));
        }
    }
}



