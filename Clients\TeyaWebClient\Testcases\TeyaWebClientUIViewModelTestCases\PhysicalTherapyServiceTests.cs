using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using Moq;
using Moq.Protected;
using NUnit.Framework;
using TeyaUIModels.Model;
using TeyaUIViewModels.ViewModel;
using TeyaUIViewModels.TeyaUIViewModelResources;
using System.Net.Http.Json;
using System.Text.Json;

// Test helper class to simulate input data structure
public class PhysicalTherapyInput
{
    public Guid Id { get; set; }
    public Guid PatientId { get; set; }
    public string TherapyType { get; set; }
    public string Duration { get; set; }
    public string Frequency { get; set; }
    public string Notes { get; set; }
    public bool IsActive { get; set; }
}

namespace TeyaWebClientUIViewModelTestCases
{
    [TestFixture]
    public class PhysicalTherapyServiceTests
    {
        private Mock<HttpMessageHandler> _mockHttpMessageHandler;
        private HttpClient _httpClient;
        private Mock<IConfiguration> _mockConfiguration;
        private Mock<IStringLocalizer<TeyaUIViewModelsStrings>> _mockLocalizer;
        private Mock<ITokenService> _mockTokenService;
        private PhysicalTherapyService _physicalTherapyService;
        private readonly string _baseUrl = "http://test-api.com";
        private readonly string _accessToken = "test-access-token";

        [SetUp]
        public void Setup()
        {
            // Setup environment variable
            Environment.SetEnvironmentVariable("EncounterNotesURL", _baseUrl);

            // Setup mock HTTP handler
            _mockHttpMessageHandler = new Mock<HttpMessageHandler>();

            // Setup HttpClient with mock handler
            _httpClient = new HttpClient(_mockHttpMessageHandler.Object)
            {
                BaseAddress = new Uri(_baseUrl)
            };

            // Setup mock configuration
            _mockConfiguration = new Mock<IConfiguration>();

            // Setup mock localizer
            _mockLocalizer = new Mock<IStringLocalizer<TeyaUIViewModelsStrings>>();
            _mockLocalizer.Setup(l => l["RecordNotFound"])
                .Returns(new LocalizedString("RecordNotFound", "Address retrieval failure"));
            _mockLocalizer.Setup(l => l["DatabaseError"])
                .Returns(new LocalizedString("DatabaseError", "Tasks retrieval failure"));
            _mockLocalizer.Setup(l => l["DeleteLogError"])
                .Returns(new LocalizedString("DeleteLogError", "Tasks retrieval failure"));

            // Setup mock token service
            _mockTokenService = new Mock<ITokenService>();
            _mockTokenService.Setup(t => t.AccessToken).Returns(_accessToken);

            // Create PhysicalTherapyService with mocked dependencies
            _physicalTherapyService = new PhysicalTherapyService(_httpClient, _mockConfiguration.Object, _mockLocalizer.Object, _mockTokenService.Object);
        }

        [TearDown]
        public void TearDown()
        {
            _httpClient.Dispose();
            Environment.SetEnvironmentVariable("EncounterNotesURL", null);
        }

        [Test]
        public async Task GetAllByIdAsync_WhenSuccessful_ReturnsPhysicalTherapyData()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var expectedTherapyData = new List<PhysicalTherapyData>
            {
                new PhysicalTherapyData
                {
                    PhysicalTherapyID = Guid.NewGuid(),
                    PatientId = patientId,
                    OrganizationId = orgId,
                    TherapyAssessment = "Strength Training Assessment",
                    ShortTermGoals = "Improve strength by 20% in 4 weeks",
                    LongTermGoals = "Return to full activity level",
                    PhysicalTherapyDiagnosis = "Muscle weakness",
                    PhysicalTherapyProgram = "Strength Training - 30 minutes, 3 times per week",
                    Disabilities = "Reduced strength",
                    Functionals = "Limited lifting capacity",
                    Limitations = "Cannot lift over 15 lbs",
                    IsActive = true,
                    CreatedDate = DateTime.Now.AddDays(-30),
                    UpdatedDate = DateTime.Now.AddDays(-1),
                    PCPId = Guid.NewGuid(),
                    Subscription = subscription
                },
                new PhysicalTherapyData
                {
                    PhysicalTherapyID = Guid.NewGuid(),
                    PatientId = patientId,
                    OrganizationId = orgId,
                    TherapyAssessment = "Range of Motion Assessment",
                    ShortTermGoals = "Increase shoulder ROM by 30 degrees",
                    LongTermGoals = "Full shoulder mobility",
                    PhysicalTherapyDiagnosis = "Shoulder stiffness",
                    PhysicalTherapyProgram = "Range of Motion - 45 minutes, Daily",
                    Disabilities = "Limited shoulder movement",
                    Functionals = "Difficulty reaching overhead",
                    Limitations = "Cannot reach above shoulder level",
                    IsActive = true,
                    CreatedDate = DateTime.Now.AddDays(-20),
                    UpdatedDate = DateTime.Now.AddDays(-1),
                    PCPId = Guid.NewGuid(),
                    Subscription = subscription
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedTherapyData)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _physicalTherapyService.GetAllByIdAsync(patientId, orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(expectedTherapyData.Count));
            Assert.That(result[0].PhysicalTherapyID, Is.EqualTo(expectedTherapyData[0].PhysicalTherapyID));
            Assert.That(result[0].PatientId, Is.EqualTo(expectedTherapyData[0].PatientId));
            Assert.That(result[0].TherapyAssessment, Is.EqualTo(expectedTherapyData[0].TherapyAssessment));
            Assert.That(result[0].ShortTermGoals, Is.EqualTo(expectedTherapyData[0].ShortTermGoals));
            Assert.That(result[0].LongTermGoals, Is.EqualTo(expectedTherapyData[0].LongTermGoals));
            Assert.That(result[0].PhysicalTherapyProgram, Is.EqualTo(expectedTherapyData[0].PhysicalTherapyProgram));
            Assert.That(result[0].IsActive, Is.EqualTo(expectedTherapyData[0].IsActive));
        }

        [Test]
        public void GetAllByIdAsync_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.NotFound,
                Content = new StringContent("Not found")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _physicalTherapyService.GetAllByIdAsync(patientId, orgId, subscription));

            Assert.That(exception.Message, Is.EqualTo("Address retrieval failure"));
        }

        [Test]
        public async Task GetAllByIdAndIsActiveAsync_WhenSuccessful_ReturnsActiveTherapyData()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var expectedTherapyData = new List<PhysicalTherapyData>
            {
                new PhysicalTherapyData
                {
                    PhysicalTherapyID = Guid.NewGuid(),
                    PatientId = patientId,
                    OrganizationId = orgId,
                    TherapyAssessment = "Strength Training Assessment",
                    ShortTermGoals = "Improve strength by 20% in 4 weeks",
                    LongTermGoals = "Return to full activity level",
                    PhysicalTherapyDiagnosis = "Muscle weakness",
                    PhysicalTherapyProgram = "Strength Training - 30 minutes, 3 times per week",
                    Disabilities = "Reduced strength",
                    Functionals = "Limited lifting capacity",
                    Limitations = "Cannot lift over 15 lbs",
                    IsActive = true,
                    CreatedDate = DateTime.Now.AddDays(-30),
                    UpdatedDate = DateTime.Now.AddDays(-1),
                    PCPId = Guid.NewGuid(),
                    Subscription = subscription
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedTherapyData)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _physicalTherapyService.GetAllByIdAndIsActiveAsync(patientId, orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(expectedTherapyData.Count));
            Assert.That(result[0].IsActive, Is.True);
        }

        [Test]
        public void GetAllByIdAndIsActiveAsync_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.NotFound,
                Content = new StringContent("Not found")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _physicalTherapyService.GetAllByIdAndIsActiveAsync(patientId, orgId, subscription));

            Assert.That(exception.Message, Is.EqualTo("Address retrieval failure"));
        }

        [Test]
        public async Task AddPhysicalTherapyAsync_WhenSuccessful_CompletesSuccessfully()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;
            var therapyData = new List<PhysicalTherapyData>
            {
                new PhysicalTherapyData
                {
                    PhysicalTherapyID = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    OrganizationId = orgId,
                    TherapyAssessment = "Strength Training Assessment",
                    ShortTermGoals = "Improve strength",
                    LongTermGoals = "Return to full activity",
                    PhysicalTherapyDiagnosis = "Muscle weakness",
                    PhysicalTherapyProgram = "Strength Training - 30 minutes, 3 times per week",
                    IsActive = true,
                    Subscription = subscription,
                    CreatedDate = DateTime.Now,
                    PCPId = Guid.NewGuid()
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.Created,
                Content = new StringContent("Success")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.DoesNotThrowAsync(async () => await _physicalTherapyService.AddPhysicalTherapyAsync(therapyData, orgId, subscription));
        }

        [Test]
        public void AddPhysicalTherapyAsync_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;
            var therapyData = new List<PhysicalTherapyData>
            {
                new PhysicalTherapyData
                {
                    PhysicalTherapyID = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    OrganizationId = orgId,
                    TherapyAssessment = "Strength Training Assessment",
                    ShortTermGoals = "Improve strength",
                    LongTermGoals = "Return to full activity",
                    PhysicalTherapyDiagnosis = "Muscle weakness",
                    PhysicalTherapyProgram = "Strength Training - 30 minutes, 3 times per week",
                    IsActive = true,
                    Subscription = subscription,
                    CreatedDate = DateTime.Now,
                    PCPId = Guid.NewGuid()
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _physicalTherapyService.AddPhysicalTherapyAsync(therapyData, orgId, subscription));

            Assert.That(exception.Message, Is.EqualTo("Tasks retrieval failure"));
        }

        [Test]
        public async Task UpdatePhysicalTherapyAsync_WhenSuccessful_CompletesSuccessfully()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;
            var therapy = new PhysicalTherapyData
            {
                PhysicalTherapyID = Guid.NewGuid(),
                PatientId = Guid.NewGuid(),
                OrganizationId = orgId,
                TherapyAssessment = "Updated Strength Training Assessment",
                ShortTermGoals = "Improved strength goals",
                LongTermGoals = "Enhanced activity level",
                PhysicalTherapyDiagnosis = "Improved muscle condition",
                PhysicalTherapyProgram = "Enhanced Training - 45 minutes daily",
                IsActive = true,
                Subscription = subscription,
                CreatedDate = DateTime.Now.AddDays(-30),
                UpdatedDate = DateTime.Now,
                PCPId = Guid.NewGuid()
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent("Success")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.DoesNotThrowAsync(async () => await _physicalTherapyService.UpdatePhysicalTherapyAsync(therapy, orgId, subscription));
        }

        [Test]
        public void UpdatePhysicalTherapyAsync_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;
            var therapy = new PhysicalTherapyData
            {
                PhysicalTherapyID = Guid.NewGuid(),
                PatientId = Guid.NewGuid(),
                OrganizationId = orgId,
                TherapyAssessment = "Updated Strength Training Assessment",
                ShortTermGoals = "Improved strength goals",
                LongTermGoals = "Enhanced activity level",
                PhysicalTherapyDiagnosis = "Improved muscle condition",
                PhysicalTherapyProgram = "Enhanced Training - 45 minutes daily",
                IsActive = true,
                Subscription = subscription,
                CreatedDate = DateTime.Now.AddDays(-30),
                UpdatedDate = DateTime.Now,
                PCPId = Guid.NewGuid()
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _physicalTherapyService.UpdatePhysicalTherapyAsync(therapy, orgId, subscription));

            Assert.That(exception, Is.Not.Null);
        }

        [Test]
        public async Task DeletePhysicalTherapyByEntityAsync_WhenSuccessful_CompletesSuccessfully()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;
            var therapy = new PhysicalTherapyData
            {
                PhysicalTherapyID = Guid.NewGuid(),
                PatientId = Guid.NewGuid(),
                OrganizationId = orgId,
                TherapyAssessment = "Strength Training Assessment",
                ShortTermGoals = "Short term goals",
                LongTermGoals = "Long term goals",
                PhysicalTherapyDiagnosis = "Test diagnosis",
                PhysicalTherapyProgram = "30 minutes, 3 times per week",
                IsActive = true,
                Subscription = subscription,
                CreatedDate = DateTime.Now,
                PCPId = Guid.NewGuid()
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.NoContent,
                Content = new StringContent("Success")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.DoesNotThrowAsync(async () => await _physicalTherapyService.DeletePhysicalTherapyByEntityAsync(therapy, orgId, subscription));
        }

        [Test]
        public void DeletePhysicalTherapyByEntityAsync_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;
            var therapy = new PhysicalTherapyData
            {
                PhysicalTherapyID = Guid.NewGuid(),
                PatientId = Guid.NewGuid(),
                OrganizationId = orgId,
                TherapyAssessment = "Strength Training Assessment",
                ShortTermGoals = "Short term goals",
                LongTermGoals = "Long term goals",
                PhysicalTherapyDiagnosis = "Test diagnosis",
                PhysicalTherapyProgram = "30 minutes, 3 times per week",
                IsActive = true,
                Subscription = subscription,
                CreatedDate = DateTime.Now,
                PCPId = Guid.NewGuid()
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _physicalTherapyService.DeletePhysicalTherapyByEntityAsync(therapy, orgId, subscription));

            Assert.That(exception.Message, Is.EqualTo("Tasks retrieval failure"));
        }

        [Test]
        public async Task UpdatePhysicalTherapyListAsync_WhenSuccessful_CompletesSuccessfully()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;
            var therapyList = new List<PhysicalTherapyData>
            {
                new PhysicalTherapyData
                {
                    PhysicalTherapyID = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    OrganizationId = orgId,
                    TherapyAssessment = "Strength Training Assessment 1",
                    ShortTermGoals = "Short term goals 1",
                    LongTermGoals = "Long term goals 1",
                    PhysicalTherapyDiagnosis = "Test diagnosis 1",
                    PhysicalTherapyProgram = "30 minutes, 3 times per week",
                    IsActive = true,
                    Subscription = subscription,
                    CreatedDate = DateTime.Now,
                    PCPId = Guid.NewGuid()
                },
                new PhysicalTherapyData
                {
                    PhysicalTherapyID = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    OrganizationId = orgId,
                    TherapyAssessment = "Range of Motion Assessment 2",
                    ShortTermGoals = "Short term goals 2",
                    LongTermGoals = "Long term goals 2",
                    PhysicalTherapyDiagnosis = "Test diagnosis 2",
                    PhysicalTherapyProgram = "45 minutes daily",
                    IsActive = true,
                    Subscription = subscription,
                    CreatedDate = DateTime.Now,
                    PCPId = Guid.NewGuid()
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent("Success")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.DoesNotThrowAsync(async () => await _physicalTherapyService.UpdatePhysicalTherapyListAsync(therapyList, orgId, subscription));
        }

        [Test]
        public void UpdatePhysicalTherapyListAsync_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;
            var therapyList = new List<PhysicalTherapyData>
            {
                new PhysicalTherapyData
                {
                    PhysicalTherapyID = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    OrganizationId = orgId,
                    TherapyAssessment = "Strength Training Assessment 1",
                    ShortTermGoals = "Short term goals 1",
                    LongTermGoals = "Long term goals 1",
                    PhysicalTherapyDiagnosis = "Test diagnosis 1",
                    PhysicalTherapyProgram = "30 minutes, 3 times per week",
                    IsActive = true,
                    Subscription = subscription,
                    CreatedDate = DateTime.Now,
                    PCPId = Guid.NewGuid()
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _physicalTherapyService.UpdatePhysicalTherapyListAsync(therapyList, orgId, subscription));

            Assert.That(exception.Message, Is.EqualTo("Tasks retrieval failure"));
        }
    }
}



