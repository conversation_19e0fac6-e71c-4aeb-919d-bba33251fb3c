using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using Moq;
using Moq.Protected;
using NUnit.Framework;
using TeyaUIModels.Model;
using TeyaUIViewModels.ViewModel;
using TeyaUIViewModels.TeyaUIViewModelResources;
using System.Net.Http.Json;
using System.Text.Json;

// Remove dummy class - use actual PatientReferralOutgoing from TeyaUIModels.Model

namespace TeyaWebClientUIViewModelTestCases
{
    [TestFixture]
    public class ReferralOutgoingServiceTests
    {
        private Mock<HttpMessageHandler> _mockHttpMessageHandler;
        private HttpClient _httpClient;
        private Mock<IConfiguration> _mockConfiguration;
        private Mock<IStringLocalizer<TeyaUIViewModelsStrings>> _mockLocalizer;
        private Mock<ITokenService> _mockTokenService;
        private ReferralOutgoingService _referralOutgoingService;
        private readonly string _baseUrl = "http://test-api.com";
        private readonly string _accessToken = "test-access-token";

        [SetUp]
        public void Setup()
        {
            // Setup environment variable
            Environment.SetEnvironmentVariable("EncounterNotesURL", _baseUrl);

            // Setup mock HTTP handler
            _mockHttpMessageHandler = new Mock<HttpMessageHandler>();

            // Setup HttpClient with mock handler
            _httpClient = new HttpClient(_mockHttpMessageHandler.Object)
            {
                BaseAddress = new Uri(_baseUrl)
            };

            // Setup mock configuration
            _mockConfiguration = new Mock<IConfiguration>();

            // Setup mock localizer
            _mockLocalizer = new Mock<IStringLocalizer<TeyaUIViewModelsStrings>>();
            _mockLocalizer.Setup(l => l["TasksRetrievalFailure"])
                .Returns(new LocalizedString("TasksRetrievalFailure", "Tasks retrieval failure"));

            // Setup mock token service
            _mockTokenService = new Mock<ITokenService>();
            _mockTokenService.Setup(t => t.AccessToken).Returns(_accessToken);

            // Create ReferralOutgoingService with mocked dependencies
            _referralOutgoingService = new ReferralOutgoingService(_httpClient, _mockConfiguration.Object, _mockLocalizer.Object, _mockTokenService.Object);
        }

        [TearDown]
        public void TearDown()
        {
            _httpClient.Dispose();
            Environment.SetEnvironmentVariable("EncounterNotesURL", null);
        }

        [Test]
        public async Task GetReferralOutgoingByIdAsync_WhenSuccessful_ReturnsReferrals()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var expectedReferrals = new List<PatientReferralOutgoing>
            {
                new PatientReferralOutgoing
                {
                    PlanReferralId = Guid.NewGuid(),
                    PatientId = patientId,
                    OrganizationId = orgId,
                    PCPId = Guid.NewGuid(),
                    CreatedBy = Guid.NewGuid(),
                    UpdatedBy = Guid.NewGuid(),
                    CreatedDate = DateTime.Now.AddDays(-7),
                    UpdatedDate = DateTime.Now.AddDays(-6),
                    ReferralTo = "Dr. Specialist",
                    ReferralFrom = "Dr. Primary",
                    ReferralReason = "Cardiology consultation",
                    TreatmentPlan = "Cardiac evaluation",
                    Tests = "ECG, Echo",
                    isActive = true,
                    Subscription = subscription
                },
                new PatientReferralOutgoing
                {
                    PlanReferralId = Guid.NewGuid(),
                    PatientId = patientId,
                    OrganizationId = orgId,
                    PCPId = Guid.NewGuid(),
                    CreatedBy = Guid.NewGuid(),
                    UpdatedBy = Guid.NewGuid(),
                    CreatedDate = DateTime.Now.AddDays(-3),
                    UpdatedDate = DateTime.Now.AddDays(-2),
                    ReferralTo = "Dr. Orthopedic",
                    ReferralFrom = "Dr. Primary",
                    ReferralReason = "Knee pain evaluation",
                    TreatmentPlan = "Orthopedic consultation",
                    Tests = "X-ray, MRI",
                    isActive = true,
                    Subscription = subscription
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedReferrals)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _referralOutgoingService.GetReferralOutgoingsByIdAsync(patientId, orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(expectedReferrals.Count));
            Assert.That(result[0].PlanReferralId, Is.EqualTo(expectedReferrals[0].PlanReferralId));
            Assert.That(result[0].PatientId, Is.EqualTo(expectedReferrals[0].PatientId));
            Assert.That(result[0].ReferralTo, Is.EqualTo(expectedReferrals[0].ReferralTo));
            Assert.That(result[0].ReferralReason, Is.EqualTo(expectedReferrals[0].ReferralReason));
            Assert.That(result[0].TreatmentPlan, Is.EqualTo(expectedReferrals[0].TreatmentPlan));
            Assert.That(result[0].Tests, Is.EqualTo(expectedReferrals[0].Tests));
            Assert.That(result[0].isActive, Is.EqualTo(expectedReferrals[0].isActive));
        }

        [Test]
        public void GetReferralOutgoingByIdAsync_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.NotFound,
                Content = new StringContent("Not found")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _referralOutgoingService.GetReferralOutgoingsByIdAsync(patientId, orgId, subscription));

            Assert.That(exception.Message, Does.Contain("Exception of type"));
        }

        [Test]
        public async Task GetReferralOutgoingByIdAsyncAndIsActive_WhenSuccessful_ReturnsActiveReferrals()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var expectedReferrals = new List<PatientReferralOutgoing>
            {
                new PatientReferralOutgoing
                {
                    PlanReferralId = Guid.NewGuid(),
                    PatientId = patientId,
                    OrganizationId = orgId,
                    PCPId = Guid.NewGuid(),
                    CreatedBy = Guid.NewGuid(),
                    UpdatedBy = Guid.NewGuid(),
                    CreatedDate = DateTime.Now.AddDays(-5),
                    UpdatedDate = DateTime.Now.AddDays(-4),
                    ReferralTo = "Dr. Active Specialist",
                    ReferralReason = "Active consultation",
                    TreatmentPlan = "Active treatment plan",
                    Tests = "Active tests",
                    isActive = true,
                    Subscription = subscription
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedReferrals)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _referralOutgoingService.GetReferralOutgoingsByIdAsyncAndIsActive(patientId, orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(expectedReferrals.Count));
            Assert.That(result[0].isActive, Is.True);
            Assert.That(result[0].ReferralTo, Is.EqualTo("Dr. Active Specialist"));
        }

        [Test]
        public void GetReferralOutgoingByIdAsyncAndIsActive_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _referralOutgoingService.GetReferralOutgoingsByIdAsyncAndIsActive(patientId, orgId, subscription));

            Assert.That(exception.Message, Does.Contain("Exception of type"));
        }

        [Test]
        public async Task AddReferralOutgoingAsync_WhenSuccessful_CompletesSuccessfully()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;
            var referrals = new List<PatientReferralOutgoing>
            {
                new PatientReferralOutgoing
                {
                    PlanReferralId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    OrganizationId = orgId,
                    PCPId = Guid.NewGuid(),
                    CreatedBy = Guid.NewGuid(),
                    UpdatedBy = Guid.NewGuid(),
                    CreatedDate = DateTime.Now,
                    UpdatedDate = DateTime.Now,
                    ReferralTo = "Dr. New Specialist",
                    ReferralReason = "New consultation needed",
                    TreatmentPlan = "New treatment plan",
                    Tests = "New tests",
                    isActive = true,
                    Subscription = subscription
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.Created,
                Content = new StringContent("Success")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.DoesNotThrowAsync(async () => await _referralOutgoingService.AddReferralOutgoingAsync(referrals, orgId, subscription));
        }

        [Test]
        public void AddReferralOutgoingAsync_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;
            var referrals = new List<PatientReferralOutgoing>
            {
                new PatientReferralOutgoing
                {
                    PlanReferralId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    OrganizationId = orgId,
                    PCPId = Guid.NewGuid(),
                    CreatedBy = Guid.NewGuid(),
                    UpdatedBy = Guid.NewGuid(),
                    CreatedDate = DateTime.Now,
                    UpdatedDate = DateTime.Now,
                    ReferralTo = "Dr. New Specialist",
                    ReferralFrom = "Dr. Primary",
                    ReferralReason = "New consultation needed",
                    TreatmentPlan = "Specialist consultation",
                    Tests = "Blood work",
                    isActive = true,
                    Subscription = subscription
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _referralOutgoingService.AddReferralOutgoingAsync(referrals, orgId, subscription));

            Assert.That(exception.Message, Is.EqualTo("Tasks retrieval failure"));
        }

        [Test]
        public async Task UpdateReferralOutgoingAsync_WhenSuccessful_CompletesSuccessfully()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;
            var referral = new PatientReferralOutgoing
            {
                PlanReferralId = Guid.NewGuid(),
                PatientId = Guid.NewGuid(),
                OrganizationId = orgId,
                PCPId = Guid.NewGuid(),
                CreatedBy = Guid.NewGuid(),
                UpdatedBy = Guid.NewGuid(),
                CreatedDate = DateTime.Now.AddDays(-1),
                UpdatedDate = DateTime.Now,
                ReferralTo = "Dr. Updated Specialist",
                ReferralFrom = "Dr. Primary",
                ReferralReason = "Updated consultation reason",
                TreatmentPlan = "Updated treatment plan",
                Tests = "Updated tests",
                isActive = true,
                Subscription = subscription
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent("Success")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.DoesNotThrowAsync(async () => await _referralOutgoingService.UpdateReferralOutgoingAsync(referral, orgId, subscription));
        }

        [Test]
        public void UpdateReferralOutgoingAsync_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;
            var referral = new PatientReferralOutgoing
            {
                PlanReferralId = Guid.NewGuid(),
                PatientId = Guid.NewGuid(),
                OrganizationId = orgId,
                PCPId = Guid.NewGuid(),
                CreatedBy = Guid.NewGuid(),
                UpdatedBy = Guid.NewGuid(),
                CreatedDate = DateTime.Now.AddDays(-1),
                UpdatedDate = DateTime.Now,
                ReferralTo = "Dr. Updated Specialist",
                ReferralFrom = "Dr. Primary",
                ReferralReason = "Updated consultation reason",
                TreatmentPlan = "Updated treatment plan",
                Tests = "Updated tests",
                isActive = true,
                Subscription = subscription
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _referralOutgoingService.UpdateReferralOutgoingAsync(referral, orgId, subscription));

            Assert.That(exception, Is.Not.Null);
        }

        [Test]
        public async Task DeleteReferralOutgoingAsync_WhenSuccessful_CompletesSuccessfully()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;
            var referral = new PatientReferralOutgoing
            {
                PlanReferralId = Guid.NewGuid(),
                PatientId = Guid.NewGuid(),
                OrganizationId = orgId,
                PCPId = Guid.NewGuid(),
                CreatedBy = Guid.NewGuid(),
                UpdatedBy = Guid.NewGuid(),
                CreatedDate = DateTime.Now.AddDays(-10),
                UpdatedDate = DateTime.Now,
                ReferralTo = "Dr. To Delete",
                ReferralFrom = "Dr. Primary",
                ReferralReason = "Referral to be deleted",
                TreatmentPlan = "Cancelled treatment",
                Tests = "Cancelled tests",
                isActive = false,
                Subscription = subscription
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent("Success")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.DoesNotThrowAsync(async () => await _referralOutgoingService.DeleteReferralOutgoingAsync(referral, orgId, subscription));
        }

        [Test]
        public void DeleteReferralOutgoingAsync_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;
            var referral = new PatientReferralOutgoing
            {
                PlanReferralId = Guid.NewGuid(),
                PatientId = Guid.NewGuid(),
                OrganizationId = orgId,
                PCPId = Guid.NewGuid(),
                CreatedBy = Guid.NewGuid(),
                UpdatedBy = Guid.NewGuid(),
                CreatedDate = DateTime.Now.AddDays(-10),
                UpdatedDate = DateTime.Now,
                ReferralTo = "Dr. To Delete",
                ReferralFrom = "Dr. Primary",
                ReferralReason = "Referral to be deleted",
                TreatmentPlan = "Cancelled treatment",
                Tests = "Cancelled tests",
                isActive = false,
                Subscription = subscription
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _referralOutgoingService.DeleteReferralOutgoingAsync(referral, orgId, subscription));

            Assert.That(exception.Message, Is.EqualTo("Tasks retrieval failure"));
        }

        [Test]
        public async Task UpdateReferralOutgoingListAsync_WhenSuccessful_CompletesSuccessfully()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;
            var referrals = new List<PatientReferralOutgoing>
            {
                new PatientReferralOutgoing
                {
                    PlanReferralId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    OrganizationId = orgId,
                    PCPId = Guid.NewGuid(),
                    CreatedBy = Guid.NewGuid(),
                    UpdatedBy = Guid.NewGuid(),
                    CreatedDate = DateTime.Now.AddDays(-2),
                    UpdatedDate = DateTime.Now,
                    ReferralTo = "Dr. Batch Update 1",
                    ReferralFrom = "Dr. Primary",
                    ReferralReason = "Batch update reason 1",
                    TreatmentPlan = "Updated treatment plan 1",
                    Tests = "Updated tests 1",
                    isActive = true,
                    Subscription = subscription
                },
                new PatientReferralOutgoing
                {
                    PlanReferralId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    OrganizationId = orgId,
                    PCPId = Guid.NewGuid(),
                    CreatedBy = Guid.NewGuid(),
                    UpdatedBy = Guid.NewGuid(),
                    CreatedDate = DateTime.Now.AddDays(-1),
                    UpdatedDate = DateTime.Now,
                    ReferralTo = "Dr. Batch Update 2",
                    ReferralFrom = "Dr. Primary",
                    ReferralReason = "Batch update reason 2",
                    TreatmentPlan = "Updated treatment plan 2",
                    Tests = "Updated tests 2",
                    isActive = true,
                    Subscription = subscription
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent("Success")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.DoesNotThrowAsync(async () => await _referralOutgoingService.UpdateReferralOutgoingsListAsync(referrals, orgId, subscription));
        }

        [Test]
        public void UpdateReferralOutgoingListAsync_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;
            var referrals = new List<PatientReferralOutgoing>
            {
                new PatientReferralOutgoing
                {
                    PlanReferralId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    OrganizationId = orgId,
                    PCPId = Guid.NewGuid(),
                    CreatedBy = Guid.NewGuid(),
                    UpdatedBy = Guid.NewGuid(),
                    CreatedDate = DateTime.Now.AddDays(-2),
                    UpdatedDate = DateTime.Now,
                    ReferralTo = "Dr. Batch Update 1",
                    ReferralFrom = "Dr. Primary",
                    ReferralReason = "Batch update reason 1",
                    TreatmentPlan = "Updated treatment plan 1",
                    Tests = "Updated tests 1",
                    isActive = true,
                    Subscription = subscription
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _referralOutgoingService.UpdateReferralOutgoingsListAsync(referrals, orgId, subscription));

            Assert.That(exception, Is.Not.Null);
        }

        [Test]
        public async Task GetReferralOutgoingByIdAsync_WhenEmptyResponse_ReturnsEmptyList()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var expectedReferrals = new List<PatientReferralOutgoing>();

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedReferrals)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _referralOutgoingService.GetReferralOutgoingsByIdAsync(patientId, orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(0));
        }

        [Test]
        public void GetReferralOutgoingByIdAsync_WhenExceptionThrown_PropagatesException()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var expectedException = new HttpRequestException("Test exception");

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ThrowsAsync(expectedException);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _referralOutgoingService.GetReferralOutgoingsByIdAsync(patientId, orgId, subscription));

            Assert.That(exception, Is.Not.Null);
            Assert.That(exception.Message, Is.EqualTo("Test exception"));
        }
    }
}



