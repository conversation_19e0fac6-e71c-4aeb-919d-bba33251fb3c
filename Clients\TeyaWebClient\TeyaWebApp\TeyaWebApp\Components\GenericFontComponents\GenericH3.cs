﻿using Microsoft.AspNetCore.Components;

namespace TeyaWebApp.Components.GenericFontComponents
{
    public partial class GenericH3 : ComponentBase
    {
        [Parameter]
        public RenderFragment ChildContent { get; set; }

        [Parameter]
        public string Color { get; set; } = "inherit";

        [Parameter]
        public string Class { get; set; } = "";

        [Parameter]
        public string MarginBottom { get; set; } = "0.5rem";

        [Parameter]
        public string MarginTop { get; set; } = "0";

        [Parameter(CaptureUnmatchedValues = true)]
        public Dictionary<string, object> AdditionalAttributes { get; set; }

        protected override void BuildRenderTree(Microsoft.AspNetCore.Components.Rendering.RenderTreeBuilder builder)
        {
            builder.OpenElement(0, "h3");
            builder.AddAttribute(1, "class", $"generic-heading h3 {Class}");
            builder.AddAttribute(2, "style", GetStyle());

            if (AdditionalAttributes != null)
            {
                foreach (var attribute in AdditionalAttributes)
                {
                    builder.AddAttribute(3, attribute.Key, attribute.Value);
                }
            }

            builder.AddContent(4, ChildContent);
            builder.CloseElement();
        }

        private string GetStyle()
        {
            return $"font-family: {FontFamily.Heading}; font-weight: {FontWeight.Medium}; font-size: {FontSize.XLarge}; line-height: 1.3; margin-bottom: {MarginBottom}; margin-top: {MarginTop}; color: {Color};";
        }
    }
}
