name: build

on:    
  pull_request:
    branches: [main]
  push:
    branches: [main]
   

permissions:
  id-token: write
  contents: write
  actions: read
  pull-requests: write
  issues: write

jobs:  
  check-branch-name:
    runs-on: ubuntu-latest    
    if: github.event_name == 'pull_request' 
    steps:
      - name: Check Branch Name
        uses: actions/github-script@v6
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          script: |
            const branchName = context.payload.pull_request.head.ref;
            const regex = /^(TWC|TM|TB|PO|DEV|VER)-\d+-[\w-]+$/;

            if (!regex.test(branchName)) {
              core.setFailed('Branch name does not match the required format: TWC/TM/TB/PO/DEV/VER-<NUMBER>-<desc>');
            } else {
              console.log('Branch name matches the required format');
            }

      - name: Notify MS Teams on Branch Name Success
        if: success()
        run: |
          curl -H "Content-Type: application/json" \
          -d '{
            "text": "✅ Branch name check passed!\n- Workflow: check-branch-name\n- Project:Check Branch Name\n- Branch: ${{ github.head_ref }}\n- Triggered by: ${{ github.actor }}\n- Commit: ${{ github.sha }}"
          }' \
          ${{ secrets.TEAMS_WEBHOOK_URL }}

      - name: Notify MS Teams on Branch Name Failure
        if: failure()
        run: |
          curl -H "Content-Type: application/json" \
          -d '{
            "text": "❌ Branch name check failed!\n- Workflow: check-branch-name\n- Project:Check Branch Name\n- Branch: ${{ github.head_ref }}\n- Triggered by: ${{ github.actor }}\n- Commit: ${{ github.sha }}"
          }' \
          ${{ secrets.TEAMS_WEBHOOK_URL }}

  build-and-test:
      runs-on: ubuntu-latest
      needs: check-branch-name
      if: always() && (needs.check-branch-name.result == 'success' || needs.check-branch-name.result == 'skipped')
      steps:
        - name: Checkout repository
          uses: actions/checkout@v4

        - name: Restore test project dependencies
          shell: pwsh
          run: |
            $testProjects = Get-ChildItem -Path . -Recurse -Include *.csproj | Where-Object { $_.FullName -match 'Test' }
            if ($testProjects.Count -eq 0) {
              Write-Host "No test projects found."
              exit 1
            }
            foreach ($proj in $testProjects) {
              Write-Host "Restoring $($proj.FullName)"
              dotnet restore $proj.FullName
            }

        - name: Discover and run all test projects (fail-fast)
          shell: pwsh
          run: |
            $testProjects = Get-ChildItem -Path . -Recurse -Include *.csproj | Where-Object { $_.FullName -match 'Test' }
            foreach ($proj in $testProjects) {
              Write-Host "Running tests in $($proj.FullName)"
              dotnet test $proj.FullName --no-restore --verbosity normal
              if ($LASTEXITCODE -ne 0) {
                Write-Error "❌ Tests failed in $($proj.FullName). Stopping workflow."
                exit 1
              }
            }

        - name: Notify MS Teams on Test Success
          if: success()
          run: |
            curl -H "Content-Type: application/json" \
            -d '{
              "text": "✅ All unit tests passed!\n- Workflow: build-and-test\n- Project: Build and Test\n- Branch: ${{ github.ref_name }}\n- Commit: ${{ github.sha }}\n- Triggered by: ${{ github.actor }}"
            }' \
            ${{ secrets.TEAMS_WEBHOOK_URL }}

        - name: Notify MS Teams on Test Failure
          if: failure()
          run: |
            curl -H "Content-Type: application/json" \
            -d '{
              "text": "❌ Unit tests failed!\n- Workflow: build-and-test\n- Project: Build and Test\n- Branch: ${{ github.ref_name }}\n- Commit: ${{ github.sha }}\n- Triggered by: ${{ github.actor }}"
            }' \
            ${{ secrets.TEAMS_WEBHOOK_URL }}
   


  cache-dependencies:
    runs-on: ubuntu-latest
    needs: [build-and-test]
    if: ( needs.build-and-test.result == 'success') 
    steps:
      - name: Cache Docker layers
        uses: actions/cache@v3
        with:
          path: /tmp/.buildx-cache
          key: ${{ runner.os }}-docker-${{ hashFiles('/Dockerfile') }}
          restore-keys: |
            ${{ runner.os }}-docker-

  build-matrix:
    runs-on: ubuntu-latest
    needs: [cache-dependencies]
    if: success()
    strategy:
      matrix:
        include:
          - name: MemberService API
            CONTAINER_REGISTRY_LOGIN_SERVER: teyahealthdev.azurecr.io
            DOCKER_FILE_PATH: Services/MemberServiceApi/Dockerfile
            PROJECT_NAME_FOR_DOCKER: memberservice
            REGISTRY_USERNAME: TeyaHealthDev
          - name: EncounterNotesService API
            CONTAINER_REGISTRY_LOGIN_SERVER: teyahealthdev.azurecr.io
            DOCKER_FILE_PATH: Services/EncounterNotesApi/Dockerfile
            PROJECT_NAME_FOR_DOCKER: encounternotes
            REGISTRY_USERNAME: TeyaHealthDev
          - name: Practice API
            CONTAINER_REGISTRY_LOGIN_SERVER: teyahealthdev.azurecr.io
            DOCKER_FILE_PATH: Services/PracticeApi/Dockerfile
            PROJECT_NAME_FOR_DOCKER: practice
            REGISTRY_USERNAME: TeyaHealthDev
          - name: Appointments API
            CONTAINER_REGISTRY_LOGIN_SERVER: teyahealthdev.azurecr.io
            DOCKER_FILE_PATH: Services/AppointmentsApi/Dockerfile
            PROJECT_NAME_FOR_DOCKER: appointments
            REGISTRY_USERNAME: TeyaHealthDev
          - name: TeyaWebApp
            CONTAINER_REGISTRY_LOGIN_SERVER: teyahealthdev.azurecr.io
            DOCKER_FILE_PATH: Clients/TeyaWebClient/TeyaWebApp/TeyaWebApp/Dockerfile
            PROJECT_NAME_FOR_DOCKER: teyawebapp
            REGISTRY_USERNAME: TeyaHealthDev

    name: Build ${{ matrix.name }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to container registry
        uses: docker/login-action@v3
        with:
          registry: ${{ matrix.CONTAINER_REGISTRY_LOGIN_SERVER }}
          username: ${{ matrix.REGISTRY_USERNAME }}
          password: ${{ secrets.REGISTRY_PASSWORD }}

      - name: Build container image
        uses: docker/build-push-action@v5
        with:
          context: .
          push: false
          tags: ${{ matrix.CONTAINER_REGISTRY_LOGIN_SERVER }}/${{ matrix.PROJECT_NAME_FOR_DOCKER }}:${{ github.sha }}
          file: ${{ matrix.DOCKER_FILE_PATH }}
          cache-from: type=local,src=/tmp/.buildx-cache
          cache-to: type=local,dest=/tmp/.buildx-cache-new

      - name: Move cache
        run: |
          rm -rf /tmp/.buildx-cache
          mv /tmp/.buildx-cache-new /tmp/.buildx-cache

      - name: Notify MS Teams on Success
        if: success()
        run: |
          curl -H "Content-Type: application/json" \
          -d '{
            "text": "✅ Build succeeded!\n- Workflow: build\n- Project: ${{ matrix.name }}\n- Triggered by: ${{ github.actor }}\n- Branch: ${{ github.ref_name }}\n- Commit: ${{ github.sha }}"
          }' \
          ${{ secrets.TEAMS_WEBHOOK_URL }}

      - name: Notify MS Teams on Failure
        if: failure()
        run: |
          curl -H "Content-Type: application/json" \
          -d '{
            "text": "❌ Build failed!\n- Workflow: build\n- Project: ${{ matrix.name }}\n- Triggered by: ${{ github.actor }}\n- Branch: ${{ github.ref_name }}\n- Commit: ${{ github.sha }}"
          }' \
          ${{ secrets.TEAMS_WEBHOOK_URL }}

  build-sql-database:
    runs-on: ubuntu-latest
    name: Build SQL Database
    needs: [build-and-test]
    if: ( needs.build-and-test.result == 'success')
    steps:
      - uses: actions/checkout@v4

      - name: Setup .NET SDK
        uses: actions/setup-dotnet@v3
        with:
          dotnet-version: '6.0.x'

      - name: Build Database Project
        run: dotnet build Database/TeyaHealthSQLDatabase/TeyaHealthSQLDatabase.sqlproj -c Debug

      - name: Install SQLPackage
        run: |
          curl -L https://aka.ms/sqlpackage-linux -o sqlpackage.zip
          sudo mkdir -p /usr/local/sqlpackage
          sudo unzip -o sqlpackage.zip -d /usr/local/sqlpackage
          sudo chmod +x /usr/local/sqlpackage/sqlpackage
          echo "/usr/local/sqlpackage" >> $GITHUB_PATH

      - name: Notify MS Teams on DB Deploy Success
        if: success()
        run: |
          curl -H "Content-Type: application/json" \
          -d '{
            "text": "✅ Azure SQL Database deployment succeeded!\n- Workflow: Build Database Project\n- Project: sql-database-build\n- Triggered by: ${{ github.actor }}\n- Branch: ${{ github.ref_name }}\n- Commit: ${{ github.sha }}"
          }' \
          ${{ secrets.TEAMS_WEBHOOK_URL }}

      - name: Notify MS Teams on DB Deploy Failure
        if: failure()
        run: |
          curl -H "Content-Type: application/json" \
          -d '{
            "text": "❌ Azure SQL Database deployment failed!\n- Workflow: Build Database Project\n- Project: sql-database-build\n- Triggered by: ${{ github.actor }}\n- Branch: ${{ github.ref_name }}\n- Commit: ${{ github.sha }}"
          }' \
          ${{ secrets.TEAMS_WEBHOOK_URL }}

  
