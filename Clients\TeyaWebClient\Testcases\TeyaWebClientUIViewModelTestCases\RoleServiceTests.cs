using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Moq;
using Moq.Protected;
using NUnit.Framework;
using TeyaUIModels.Model;
using TeyaUIModels.ViewModel;
using System.Net.Http.Json;
using System.Text.Json;

namespace TeyaWebClientUIViewModelTestCases
{
    [TestFixture]
    public class RoleServiceTests
    {
        private Mock<HttpMessageHandler> _mockHttpMessageHandler;
        private HttpClient _httpClient;
        private Mock<IStringLocalizer<RoleService>> _mockLocalizer;
        private Mock<ILogger<RoleService>> _mockLogger;
        private RoleService _roleService;
        private readonly string _baseUrl = "http://test-api.com";

        [SetUp]
        public void Setup()
        {
            // Setup environment variable
            Environment.SetEnvironmentVariable("MemberServiceURL", _baseUrl);

            // Setup mock HTTP handler
            _mockHttpMessageHandler = new Mock<HttpMessageHandler>();

            // Setup HttpClient with mock handler
            _httpClient = new HttpClient(_mockHttpMessageHandler.Object)
            {
                BaseAddress = new Uri(_baseUrl)
            };

            // Setup mock localizer
            _mockLocalizer = new Mock<IStringLocalizer<RoleService>>();
            _mockLocalizer.Setup(l => l["RoleAlreadyExists"])
                .Returns(new LocalizedString("RoleAlreadyExists", "Role already exists"));
            _mockLocalizer.Setup(l => l["RegistrationFailed"])
                .Returns(new LocalizedString("RegistrationFailed", "Registration failed"));
            _mockLocalizer.Setup(l => l["RoleRegistrationError"])
                .Returns(new LocalizedString("RoleRegistrationError", "Role registration error"));
            _mockLocalizer.Setup(l => l["GetByIdFailed"])
                .Returns(new LocalizedString("GetByIdFailed", "Get by ID failed"));
            _mockLocalizer.Setup(l => l["ErrorFetchingRoleById"])
                .Returns(new LocalizedString("ErrorFetchingRoleById", "Error fetching role by ID: {0}"));
            _mockLocalizer.Setup(l => l["ErrorFetchingAllRoles"])
                .Returns(new LocalizedString("ErrorFetchingAllRoles", "Error fetching all roles"));
            _mockLocalizer.Setup(l => l["RoleDeletionFailed"])
                .Returns(new LocalizedString("RoleDeletionFailed", "Role deletion failed"));
            _mockLocalizer.Setup(l => l["ErrorDeletingRole"])
                .Returns(new LocalizedString("ErrorDeletingRole", "Error deleting role: {0}"));
            _mockLocalizer.Setup(l => l["InvalidRole"])
                .Returns(new LocalizedString("InvalidRole", "Invalid role"));
            _mockLocalizer.Setup(l => l["UpdateFailed"])
                .Returns(new LocalizedString("UpdateFailed", "Update failed"));
            _mockLocalizer.Setup(l => l["ErrorUpdatingRole"])
                .Returns(new LocalizedString("ErrorUpdatingRole", "Error updating role: {0}"));
            _mockLocalizer.Setup(l => l["ErrorGettingRoles"])
                .Returns(new LocalizedString("ErrorGettingRoles", "Error getting roles: {0}"));

            // Setup mock logger
            _mockLogger = new Mock<ILogger<RoleService>>();

            // Create RoleService with mocked dependencies
            _roleService = new RoleService(_httpClient, _mockLocalizer.Object, _mockLogger.Object);
        }

        [TearDown]
        public void TearDown()
        {
            _httpClient.Dispose();
            Environment.SetEnvironmentVariable("MemberServiceURL", null);
        }

        [Test]
        public async Task RegisterRoleAsync_WhenSuccessful_ReturnsRole()
        {
            // Arrange
            var role = new Role
            {
                RoleId = Guid.NewGuid(),
                RoleName = "Test Role",
                IsActive = true,
                OrganizationID = Guid.NewGuid(),
                Subscription = true,
                CreatedDate = DateTime.Now
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.Created,
                Content = new StringContent(JsonSerializer.Serialize(role))
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _roleService.RegisterRoleAsync(role);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.RoleId, Is.EqualTo(role.RoleId));
            Assert.That(result.RoleName, Is.EqualTo(role.RoleName));
            Assert.That(result.IsActive, Is.EqualTo(role.IsActive));
        }

        [Test]
        public void RegisterRoleAsync_WhenConflict_ThrowsHttpRequestException()
        {
            // Arrange
            var role = new Role
            {
                RoleId = Guid.NewGuid(),
                RoleName = "Existing Role",
                IsActive = true,
                OrganizationID = Guid.NewGuid(),
                Subscription = true,
                CreatedDate = DateTime.Now
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.Conflict,
                Content = new StringContent("Role already exists")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _roleService.RegisterRoleAsync(role));

            Assert.That(exception.Message, Is.EqualTo("Role already exists"));
        }

        [Test]
        public void RegisterRoleAsync_WhenBadRequest_ThrowsHttpRequestException()
        {
            // Arrange
            var role = new Role
            {
                RoleId = Guid.NewGuid(),
                RoleName = "Invalid Role",
                IsActive = true,
                OrganizationID = Guid.NewGuid(),
                Subscription = true,
                CreatedDate = DateTime.Now
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _roleService.RegisterRoleAsync(role));

            Assert.That(exception.Message, Is.EqualTo("Registration failed"));
        }

        [Test]
        public async Task GetRoleByIdAsync_WhenSuccessful_ReturnsRole()
        {
            // Arrange
            var roleId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var expectedRole = new Role
            {
                RoleId = roleId,
                RoleName = "Retrieved Role",
                IsActive = true,
                OrganizationID = orgId,
                Subscription = subscription,
                CreatedDate = DateTime.Now.AddDays(-10)
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(JsonSerializer.Serialize(expectedRole))
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _roleService.GetRoleByIdAsync(roleId, orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.RoleId, Is.EqualTo(expectedRole.RoleId));
            Assert.That(result.RoleName, Is.EqualTo(expectedRole.RoleName));
            Assert.That(result.IsActive, Is.EqualTo(expectedRole.IsActive));
            Assert.That(result.OrganizationID, Is.EqualTo(expectedRole.OrganizationID));
        }

        [Test]
        public void GetRoleByIdAsync_WhenNotFound_ThrowsHttpRequestException()
        {
            // Arrange
            var roleId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.NotFound,
                Content = new StringContent("Role not found")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _roleService.GetRoleByIdAsync(roleId, orgId, subscription));

            Assert.That(exception.Message, Is.EqualTo("Get by ID failed"));
        }

        [Test]
        public async Task GetAllRolesAsync_WhenSuccessful_ReturnsRoles()
        {
            // Arrange
            var expectedRoles = new List<Role>
            {
                new Role
                {
                    RoleId = Guid.NewGuid(),
                    RoleName = "Admin",
                    IsActive = true,
                    OrganizationID = Guid.NewGuid(),
                    Subscription = true,
                    CreatedDate = DateTime.Now.AddDays(-30)
                },
                new Role
                {
                    RoleId = Guid.NewGuid(),
                    RoleName = "User",
                    IsActive = true,
                    OrganizationID = Guid.NewGuid(),
                    Subscription = true,
                    CreatedDate = DateTime.Now.AddDays(-20)
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(JsonSerializer.Serialize(expectedRoles))
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _roleService.GetAllRolesAsync();

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(expectedRoles.Count));
            Assert.That(result[0].RoleName, Is.EqualTo("Admin"));
            Assert.That(result[1].RoleName, Is.EqualTo("User"));
        }

        [Test]
        public void GetAllRolesAsync_WhenNotSuccessful_ThrowsException()
        {
            // Arrange
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.InternalServerError,
                Content = new StringContent("Internal server error")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _roleService.GetAllRolesAsync());

            Assert.That(exception, Is.Not.Null);
        }

        [Test]
        public async Task DeleteRoleByIdAsync_WhenSuccessful_CompletesSuccessfully()
        {
            // Arrange
            var roleId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent("Success")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.DoesNotThrowAsync(async () => await _roleService.DeleteRoleByIdAsync(roleId, orgId, subscription));
        }

        [Test]
        public void DeleteRoleByIdAsync_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var roleId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _roleService.DeleteRoleByIdAsync(roleId, orgId, subscription));

            Assert.That(exception.Message, Is.EqualTo("Role deletion failed"));
        }

        [Test]
        public async Task UpdateRoleByIdAsync_WhenSuccessful_CompletesSuccessfully()
        {
            // Arrange
            var roleId = Guid.NewGuid();
            var role = new Role
            {
                RoleId = roleId,
                RoleName = "Updated Role",
                IsActive = true,
                OrganizationID = Guid.NewGuid(),
                Subscription = true,
                CreatedDate = DateTime.Now.AddDays(-5)
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent("Success")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.DoesNotThrowAsync(async () => await _roleService.UpdateRoleByIdAsync(roleId, role));
        }

        [Test]
        public void UpdateRoleByIdAsync_WhenRoleIsNull_ThrowsArgumentException()
        {
            // Arrange
            var roleId = Guid.NewGuid();
            Role role = null;

            // Act & Assert
            var exception = Assert.ThrowsAsync<ArgumentException>(async () =>
                await _roleService.UpdateRoleByIdAsync(roleId, role));

            Assert.That(exception.Message, Is.EqualTo("Invalid role"));
        }

        [Test]
        public void UpdateRoleByIdAsync_WhenRoleIdMismatch_ThrowsArgumentException()
        {
            // Arrange
            var roleId = Guid.NewGuid();
            var role = new Role
            {
                RoleId = Guid.NewGuid(), // Different ID
                RoleName = "Mismatched Role",
                IsActive = true,
                OrganizationID = Guid.NewGuid(),
                Subscription = true,
                CreatedDate = DateTime.Now
            };

            // Act & Assert
            var exception = Assert.ThrowsAsync<ArgumentException>(async () =>
                await _roleService.UpdateRoleByIdAsync(roleId, role));

            Assert.That(exception.Message, Is.EqualTo("Invalid role"));
        }

        [Test]
        public void UpdateRoleByIdAsync_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var roleId = Guid.NewGuid();
            var role = new Role
            {
                RoleId = roleId,
                RoleName = "Failed Update Role",
                IsActive = true,
                OrganizationID = Guid.NewGuid(),
                Subscription = true,
                CreatedDate = DateTime.Now
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _roleService.UpdateRoleByIdAsync(roleId, role));

            Assert.That(exception.Message, Is.EqualTo("Update failed"));
        }

        [Test]
        public async Task GetRolesByNameAsync_WhenSuccessful_ReturnsRoles()
        {
            // Arrange
            var name = "Admin";
            var orgId = Guid.NewGuid();
            var subscription = true;
            var expectedRoles = new List<Role>
            {
                new Role
                {
                    RoleId = Guid.NewGuid(),
                    RoleName = "Admin",
                    IsActive = true,
                    OrganizationID = orgId,
                    Subscription = subscription,
                    CreatedDate = DateTime.Now.AddDays(-15)
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedRoles)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _roleService.GetRolesByNameAsync(name, orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(expectedRoles.Count));
            Assert.That(result[0].RoleName, Is.EqualTo("Admin"));
        }

        [Test]
        public async Task GetAllRolesByOrgIdAsync_WhenSuccessful_ReturnsRoles()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;
            var expectedRoles = new List<Role>
            {
                new Role
                {
                    RoleId = Guid.NewGuid(),
                    RoleName = "Org Admin",
                    IsActive = true,
                    OrganizationID = orgId,
                    Subscription = subscription,
                    CreatedDate = DateTime.Now.AddDays(-25)
                },
                new Role
                {
                    RoleId = Guid.NewGuid(),
                    RoleName = "Org User",
                    IsActive = true,
                    OrganizationID = orgId,
                    Subscription = subscription,
                    CreatedDate = DateTime.Now.AddDays(-20)
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(JsonSerializer.Serialize(expectedRoles))
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _roleService.GetAllRolesByOrgIdAsync(orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(expectedRoles.Count));
            Assert.That(result[0].RoleName, Is.EqualTo("Org Admin"));
            Assert.That(result[1].RoleName, Is.EqualTo("Org User"));
        }

        [Test]
        public void GetAllRolesByOrgIdAsync_WhenNotSuccessful_ThrowsException()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.InternalServerError,
                Content = new StringContent("Internal server error")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _roleService.GetAllRolesByOrgIdAsync(orgId, subscription));

            Assert.That(exception, Is.Not.Null);
        }
    }
}



