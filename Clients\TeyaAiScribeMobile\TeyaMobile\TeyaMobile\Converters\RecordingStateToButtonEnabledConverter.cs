using System.Globalization;
using TeyaHealthMobileModel.Model;

namespace TeyaMobile.Converters
{
    public class RecordingStateToButtonEnabledConverter : IValueConverter
    {
        public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            if (value is RecordingState state && parameter is string buttonType)
            {
                return buttonType.ToLower() switch
                {
                    "record" => state == RecordingState.Stopped,
                    "pause" => state == RecordingState.Recording,
                    "resume" => state == RecordingState.Paused,
                    "stop" => state == RecordingState.Recording || state == RecordingState.Paused,
                    _ => false
                };
            }

            return false;
        }

        public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
