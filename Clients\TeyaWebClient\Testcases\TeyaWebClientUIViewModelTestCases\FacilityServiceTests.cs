using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Moq;
using Moq.Protected;
using NUnit.Framework;
using TeyaUIModels.Model;
using TeyaUIViewModels.ViewModel;
using TeyaUIModels.ViewModel;
using TeyaUIViewModels.TeyaUIViewModelResources;
using System.Net.Http.Json;
using System.Text;

namespace TeyaWebClientUIViewModelTestCases
{
    [TestFixture]
    public class FacilityServiceTests
    {
        private Mock<HttpMessageHandler> _mockHttpMessageHandler;
        private HttpClient _httpClient;
        private Mock<IStringLocalizer<FacilityService>> _mockLocalizer;
        private Mock<ILogger<FacilityService>> _mockLogger;
        private FacilityService _facilityService;
        private readonly string _baseUrl = "http://test-api.com";

        [SetUp]
        public void Setup()
        {
            // Setup environment variable
            Environment.SetEnvironmentVariable("MemberServiceURL", _baseUrl);

            // Setup mock HTTP handler
            _mockHttpMessageHandler = new Mock<HttpMessageHandler>();

            // Setup HttpClient with mock handler
            _httpClient = new HttpClient(_mockHttpMessageHandler.Object)
            {
                BaseAddress = new Uri(_baseUrl)
            };

            // Setup mock localizer
            _mockLocalizer = new Mock<IStringLocalizer<FacilityService>>();
            _mockLocalizer.Setup(l => l["Error registering facility"])
                .Returns(new LocalizedString("Error registering facility", "Error registering facility"));
            _mockLocalizer.Setup(l => l["Error retrieving facility with ID {facilityId}"])
                .Returns(new LocalizedString("Error retrieving facility with ID {facilityId}", "Error retrieving facility"));
            _mockLocalizer.Setup(l => l["Error retrieving all facilities"])
                .Returns(new LocalizedString("Error retrieving all facilities", "Error retrieving all facilities"));

            // Setup mock logger
            _mockLogger = new Mock<ILogger<FacilityService>>();

            // Create FacilityService with mocked dependencies
            _facilityService = new FacilityService(_httpClient, _mockLocalizer.Object, _mockLogger.Object);
        }

        [TearDown]
        public void TearDown()
        {
            _httpClient.Dispose();
            Environment.SetEnvironmentVariable("MemberServiceURL", null);
        }

        [Test]
        public async Task RegisterFacilityAsync_WhenSuccessful_ReturnsFacility()
        {
            // Arrange
            var facility = new Facility
            {
                FacilityId = Guid.NewGuid(),
                FacilityName = "Test Facility",
                StreetName = "123 Main St",
                City = "Test City",
                State = "TS",
                Zipcode = "12345",
                Country = "Test Country",
                IsActive = true
            };

            var jsonResponse = System.Text.Json.JsonSerializer.Serialize(facility);
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(jsonResponse, Encoding.UTF8, "application/json")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _facilityService.RegisterFacilityAsync(facility);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.FacilityId, Is.EqualTo(facility.FacilityId));
            Assert.That(result.FacilityName, Is.EqualTo("Test Facility"));
        }

        [Test]
        public void RegisterFacilityAsync_WhenHttpRequestFails_ThrowsException()
        {
            // Arrange
            var facility = new Facility
            {
                FacilityId = Guid.NewGuid(),
                FacilityName = "Test Facility",
                StreetName = "123 Main St",
                City = "Test City",
                State = "TS",
                Zipcode = "12345",
                Country = "Test Country",
                IsActive = true
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _facilityService.RegisterFacilityAsync(facility));
        }

        [Test]
        public async Task GetFacilityByIdAsync_WhenSuccessful_ReturnsFacility()
        {
            // Arrange
            var facilityId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var expectedFacility = new Facility
            {
                FacilityId = facilityId,
                FacilityName = "Test Facility",
                StreetName = "123 Main St",
                City = "Test City",
                State = "TS",
                Zipcode = "12345",
                Country = "Test Country",
                IsActive = true
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedFacility)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _facilityService.GetFacilityByIdAsync(facilityId, orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.FacilityId, Is.EqualTo(facilityId));
            Assert.That(result.FacilityName, Is.EqualTo("Test Facility"));
        }

        [Test]
        public void GetFacilityByIdAsync_WhenHttpRequestFails_ThrowsException()
        {
            // Arrange
            var facilityId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _facilityService.GetFacilityByIdAsync(facilityId, orgId, subscription));
        }

        [Test]
        public async Task GetAllFacilitiesAsync_WhenSuccessful_ReturnsFacilities()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;
            var expectedFacilities = new List<Facility>
            {
                new Facility
                {
                    FacilityId = Guid.NewGuid(),
                    FacilityName = "Test Facility 1",
                    StreetName = "123 Main St",
                    City = "Test City",
                    State = "TS",
                    Zipcode = "12345",
                    Country = "Test Country",
                    IsActive = true
                },
                new Facility
                {
                    FacilityId = Guid.NewGuid(),
                    FacilityName = "Test Facility 2",
                    StreetName = "456 Oak St",
                    City = "Test City",
                    State = "TS",
                    Zipcode = "67890",
                    Country = "Test Country",
                    IsActive = true
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedFacilities)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _facilityService.GetAllFacilitiesAsync(orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(2));
            Assert.That(result[0].FacilityName, Is.EqualTo("Test Facility 1"));
            Assert.That(result[1].FacilityName, Is.EqualTo("Test Facility 2"));
        }

        [Test]
        public void GetAllFacilitiesAsync_WhenHttpRequestFails_ThrowsException()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _facilityService.GetAllFacilitiesAsync(orgId, subscription));
        }

        [Test]
        public async Task GetFacilityNamesAsync_WhenSuccessful_ReturnsFacilityNames()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;
            var expectedFacilities = new List<Facility>
            {
                new Facility
                {
                    FacilityId = Guid.NewGuid(),
                    FacilityName = "Test Facility 1",
                    StreetName = "123 Main St",
                    City = "Test City",
                    State = "TS",
                    Zipcode = "12345",
                    Country = "Test Country",
                    IsActive = true
                },
                new Facility
                {
                    FacilityId = Guid.NewGuid(),
                    FacilityName = "Test Facility 2",
                    StreetName = "456 Oak St",
                    City = "Test City",
                    State = "TS",
                    Zipcode = "67890",
                    Country = "Test Country",
                    IsActive = true
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedFacilities)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _facilityService.GetFacilityNamesAsync(orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(2));
            Assert.That(result[0], Is.EqualTo("Test Facility 1"));
            Assert.That(result[1], Is.EqualTo("Test Facility 2"));
        }

        [Test]
        public async Task DeleteFacilityByIdAsync_WhenSuccessful_CompletesWithoutException()
        {
            // Arrange
            var facilityId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            // No exception should be thrown
            await _facilityService.DeleteFacilityByIdAsync(facilityId, orgId, subscription);
        }

        [Test]
        public void DeleteFacilityByIdAsync_WhenHttpRequestFails_ThrowsException()
        {
            // Arrange
            var facilityId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _facilityService.DeleteFacilityByIdAsync(facilityId, orgId, subscription));
        }

        [Test]
        public async Task UpdateFacilityByIdAsync_WhenSuccessful_CompletesWithoutException()
        {
            // Arrange
            var facilityId = Guid.NewGuid();
            var facility = new Facility
            {
                FacilityId = facilityId,
                FacilityName = "Updated Facility",
                StreetName = "123 Main St",
                City = "Test City",
                State = "TS",
                Zipcode = "12345",
                Country = "Test Country",
                IsActive = true
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            // No exception should be thrown
            await _facilityService.UpdateFacilityByIdAsync(facilityId, facility);
        }

        [Test]
        public void UpdateFacilityByIdAsync_WhenHttpRequestFails_ThrowsException()
        {
            // Arrange
            var facilityId = Guid.NewGuid();
            var facility = new Facility
            {
                FacilityId = facilityId,
                FacilityName = "Updated Facility",
                StreetName = "123 Main St",
                City = "Test City",
                State = "TS",
                Zipcode = "12345",
                Country = "Test Country",
                IsActive = true
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _facilityService.UpdateFacilityByIdAsync(facilityId, facility));
        }

        [Test]
        public async Task GetAllFacilitiesByOrgIdAsync_WhenSuccessful_ReturnsFacilities()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;
            var expectedFacilities = new List<Facility>
            {
                new Facility
                {
                    FacilityId = Guid.NewGuid(),
                    FacilityName = "Test Facility 1",
                    StreetName = "123 Main St",
                    City = "Test City",
                    State = "TS",
                    Zipcode = "12345",
                    Country = "Test Country",
                    IsActive = true,
                    OrganizationId = orgId
                },
                new Facility
                {
                    FacilityId = Guid.NewGuid(),
                    FacilityName = "Test Facility 2",
                    StreetName = "456 Oak St",
                    City = "Test City",
                    State = "TS",
                    Zipcode = "67890",
                    Country = "Test Country",
                    IsActive = true,
                    OrganizationId = orgId
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedFacilities)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _facilityService.GetAllFacilitiesByOrgIdAsync(orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(2));
            Assert.That(result[0].FacilityName, Is.EqualTo("Test Facility 1"));
            Assert.That(result[1].FacilityName, Is.EqualTo("Test Facility 2"));
        }
    }
}



