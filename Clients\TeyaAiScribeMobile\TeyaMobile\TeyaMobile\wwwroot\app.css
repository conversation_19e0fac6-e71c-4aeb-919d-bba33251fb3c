.status-bar-safe-area {
    display: none;
}

@supports (-webkit-touch-callout: none) {
    .status-bar-safe-area {
        display: flex;
        position: sticky;
        top: 0;
        height: env(safe-area-inset-top);
        background-color: #f7f7f7;
        width: 100%;
        z-index: 1;
    }

    .flex-column, .navbar-brand {
        padding-left: env(safe-area-inset-left);
    }
}

.mud-appbar-brand {
    display: flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
    color: inherit;
}

.mud-brand-logo {
    height: 32px;
    width: auto;
}

.mud-user-menu {
    display: flex;
    align-items: center;
    gap: 8px;
}

.mud-nav-drawer .mud-nav-item {
    margin: 4px 8px;
    border-radius: 4px;
}

    .mud-nav-drawer .mud-nav-item:hover {
        background-color: rgba(0,0,0,0.04);
    }

.mud-user-info {
    padding: 16px;
    border-bottom: 1px solid rgba(0,0,0,0.12);
}

.mud-user-avatar {
    margin-bottom: 8px;
}

.mud-user-details {
    text-align: center;
}