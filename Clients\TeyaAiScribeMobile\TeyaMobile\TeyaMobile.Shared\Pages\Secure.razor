@page "/secure"
@attribute [Authorize]
@inject TeyaHealthMobileViewModel.ViewModel.IAuthenticationService AuthService
@inject NavigationManager Navigation
@using TeyaMobile.Shared.Components

<PageTitle>Secure Area - Access Token</PageTitle>

<AppBar Title="Secure Area" />

<div class="container-fluid p-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h3 class="mb-0">
                        <i class="fas fa-shield-alt me-2"></i>
                        Secure Area - Authentication Details
                    </h3>
                </div>
                <div class="card-body">
                    @if (AuthService.IsAuthenticated)
                    {
                        <div class="alert alert-success" role="alert">
                            <i class="fas fa-check-circle me-2"></i>
                            <strong>Authentication Status:</strong> Successfully Authenticated
                        </div>

                        <!-- User Information Section -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="card border-info">
                                    <div class="card-header bg-info text-white">
                                        <h5 class="mb-0">
                                            <i class="fas fa-user me-2"></i>
                                            User Information
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label class="form-label fw-bold">Display Name:</label>
                                            <div class="form-control-plaintext">@userName</div>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label fw-bold">Email Address:</label>
                                            <div class="form-control-plaintext">@userEmail</div>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label fw-bold">Authentication Time:</label>
                                            <div class="form-control-plaintext">@DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card border-warning">
                                    <div class="card-header bg-warning text-dark">
                                        <h5 class="mb-0">
                                            <i class="fas fa-info-circle me-2"></i>
                                            Token Information
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label class="form-label fw-bold">Token Status:</label>
                                            <div class="form-control-plaintext">
                                                @if (!string.IsNullOrEmpty(accessToken))
                                                {
                                                    <span class="badge bg-success">Valid Token Retrieved</span>
                                                }
                                                else
                                                {
                                                    <span class="badge bg-danger">No Token Available</span>
                                                }
                                            </div>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label fw-bold">Token Length:</label>
                                            <div class="form-control-plaintext">@(accessToken?.Length ?? 0) characters</div>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label fw-bold">Token Type:</label>
                                            <div class="form-control-plaintext">Bearer Token (JWT)</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Access Token Display Section -->
                        <div class="card border-secondary mb-4">
                            <div class="card-header bg-secondary text-white">
                                <h5 class="mb-0">
                                    <i class="fas fa-key me-2"></i>
                                    Access Token
                                    <button class="btn btn-sm btn-outline-light float-end" @onclick="CopyTokenToClipboard">
                                        <i class="fas fa-copy me-1"></i>
                                        Copy Token
                                    </button>
                                </h5>
                            </div>
                            <div class="card-body">
                                @if (!string.IsNullOrEmpty(accessToken))
                                {
                                    <div class="alert alert-info">
                                        <small><strong>Note:</strong> This is your current access token. Keep it secure and do not share it.</small>
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label fw-bold">Full Access Token:</label>
                                        <textarea class="form-control font-monospace" 
                                                  rows="8" 
                                                  readonly 
                                                  style="font-size: 12px; word-break: break-all;">@accessToken</textarea>
                                    </div>
                                    
                                    <!-- Token Preview -->
                                    <div class="mt-3">
                                        <label class="form-label fw-bold">Token Preview (First 100 characters):</label>
                                        <div class="form-control-plaintext bg-light p-2 rounded font-monospace" style="font-size: 12px;">
                                            @(accessToken.Length > 100 ? accessToken.Substring(0, 100) + "..." : accessToken)
                                        </div>
                                    </div>
                                }
                                else
                                {
                                    <div class="alert alert-warning">
                                        <i class="fas fa-exclamation-triangle me-2"></i>
                                        <strong>No Access Token Available</strong>
                                        <p class="mb-0 mt-2">Unable to retrieve access token. This might be due to:</p>
                                        <ul class="mb-0 mt-2">
                                            <li>Token has expired</li>
                                            <li>Authentication session is invalid</li>
                                            <li>Network connectivity issues</li>
                                        </ul>
                                    </div>
                                }
                            </div>
                        </div>

                        <!-- Graph API Token Section -->
                        <div class="card border-success mb-4">
                            <div class="card-header bg-success text-white">
                                <h5 class="mb-0">
                                    <i class="fas fa-microsoft me-2"></i>
                                    Graph API Token
                                    <button class="btn btn-sm btn-outline-light float-end" @onclick="GetGraphToken">
                                        <i class="fas fa-download me-1"></i>
                                        Get Graph Token
                                    </button>
                                </h5>
                            </div>
                            <div class="card-body">
                                @if (!string.IsNullOrEmpty(graphApiToken))
                                {
                                    <div class="alert alert-success">
                                        <small><strong>Success:</strong> Graph API token retrieved successfully. This token can be used to access Microsoft Graph APIs.</small>
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label fw-bold">Graph API Access Token:</label>
                                        <textarea class="form-control font-monospace"
                                                  rows="6"
                                                  readonly
                                                  style="font-size: 12px; word-break: break-all;">@graphApiToken</textarea>
                                    </div>

                                    <!-- Token Preview -->
                                    <div class="mt-3">
                                        <label class="form-label fw-bold">Graph Token Preview (First 100 characters):</label>
                                        <div class="form-control-plaintext bg-light p-2 rounded font-monospace" style="font-size: 12px;">
                                            @(graphApiToken.Length > 100 ? graphApiToken.Substring(0, 100) + "..." : graphApiToken)
                                        </div>
                                    </div>
                                    <div class="mt-3">
                                        <label class="form-label fw-bold">Token Length:</label>
                                        <div class="form-control-plaintext">@graphApiToken.Length characters</div>
                                    </div>
                                }
                                else if (graphTokenError)
                                {
                                    <div class="alert alert-danger">
                                        <i class="fas fa-exclamation-triangle me-2"></i>
                                        <strong>Error retrieving Graph API token</strong>
                                        <p class="mb-0 mt-2">@graphTokenErrorMessage</p>
                                    </div>
                                }
                                else
                                {
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle me-2"></i>
                                        <strong>Graph API Token</strong>
                                        <p class="mb-0 mt-2">Click "Get Graph Token" to retrieve an access token with Graph API scope (https://graph.microsoft.com/.default).</p>
                                    </div>
                                }
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="d-flex gap-2 flex-wrap">
                            <button class="btn btn-primary" @onclick="RefreshToken">
                                <i class="fas fa-sync-alt me-2"></i>
                                Refresh Token
                            </button>
                            <button class="btn btn-secondary" @onclick="NavigateToHome">
                                <i class="fas fa-home me-2"></i>
                                Go to Home
                            </button>
                            <button class="btn btn-danger" @onclick="Logout">
                                <i class="fas fa-sign-out-alt me-2"></i>
                                Sign Out
                            </button>
                        </div>
                    }
                    else
                    {
                        <div class="alert alert-danger" role="alert">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            <strong>Access Denied:</strong> You are not authenticated. Please log in to access this page.
                        </div>
                        <button class="btn btn-primary" @onclick="NavigateToLogin">
                            <i class="fas fa-sign-in-alt me-2"></i>
                            Go to Login
                        </button>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    private string accessToken = string.Empty;
    private string userName = string.Empty;
    private string userEmail = string.Empty;
    private bool isLoading = true;

    // Graph API token variables
    private string graphApiToken = string.Empty;
    private bool graphTokenError = false;
    private string graphTokenErrorMessage = string.Empty;

    protected override async Task OnInitializedAsync()
    {
        await LoadUserData();
    }

    private async Task LoadUserData()
    {
        try
        {
            isLoading = true;
            StateHasChanged();

            if (AuthService.IsAuthenticated)
            {
                accessToken = await AuthService.GetAccessTokenAsync();
                userName = await AuthService.GetUserNameAsync();
                userEmail = await AuthService.GetUserEmailAsync();
            }
        }
        catch (Exception ex)
        {
            // Log error (in a real app, use proper logging)
            Console.WriteLine($"Error loading user data: {ex.Message}");
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private async Task RefreshToken()
    {
        await LoadUserData();
    }

    private async Task GetGraphToken()
    {
        try
        {
            graphTokenError = false;
            graphTokenErrorMessage = string.Empty;
            graphApiToken = string.Empty;
            StateHasChanged();

            if (AuthService.IsAuthenticated)
            {
                graphApiToken = await AuthService.GetGraphApiScopeAsync();

                if (string.IsNullOrEmpty(graphApiToken))
                {
                    graphTokenError = true;
                    graphTokenErrorMessage = "Failed to retrieve Graph API token. The token may be empty or the request failed.";
                }
            }
            else
            {
                graphTokenError = true;
                graphTokenErrorMessage = "User is not authenticated. Please log in first.";
            }
        }
        catch (Exception ex)
        {
            graphTokenError = true;
            graphTokenErrorMessage = $"Error retrieving Graph API token: {ex.Message}";
            Console.WriteLine($"Error getting Graph API token: {ex}");
        }
        finally
        {
            StateHasChanged();
        }
    }

    private async Task CopyTokenToClipboard()
    {
        if (!string.IsNullOrEmpty(accessToken))
        {
            // Note: In a real MAUI app, you might need to use platform-specific clipboard APIs
            // For now, this is a placeholder - the token is displayed for manual copying
            await Task.CompletedTask;
        }
    }

    private void NavigateToHome()
    {
        Navigation.NavigateTo("/");
    }

    private void NavigateToLogin()
    {
        Navigation.NavigateTo("/login");
    }

    private async Task Logout()
    {
        try
        {
            await AuthService.LogoutAsync();
            Navigation.NavigateTo("/");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error during logout: {ex.Message}");
        }
    }
}
