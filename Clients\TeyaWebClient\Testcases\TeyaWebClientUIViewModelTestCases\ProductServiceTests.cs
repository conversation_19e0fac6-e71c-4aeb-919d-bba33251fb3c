using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using Moq;
using Moq.Protected;
using NUnit.Framework;
using TeyaUIModels.Model;
using TeyaUIViewModels.ViewModel;
using TeyaUIViewModels.TeyaUIViewModelResources;
using System.Net.Http.Json;
using System.Text.Json;

namespace TeyaWebClientUIViewModelTestCases
{
    [TestFixture]
    public class ProductServiceTests
    {
        private Mock<HttpMessageHandler> _mockHttpMessageHandler;
        private HttpClient _httpClient;
        private Mock<IConfiguration> _mockConfiguration;
        private Mock<IStringLocalizer<TeyaUIViewModelsStrings>> _mockLocalizer;
        private Mock<ITokenService> _mockTokenService;
        private ProductService _productService;
        private readonly string _baseUrl = "http://test-api.com";
        private readonly string _accessToken = "test-access-token";

        [SetUp]
        public void Setup()
        {
            // Setup environment variable
            Environment.SetEnvironmentVariable("MemberServiceURL", _baseUrl);

            // Setup mock HTTP handler
            _mockHttpMessageHandler = new Mock<HttpMessageHandler>();

            // Setup HttpClient with mock handler
            _httpClient = new HttpClient(_mockHttpMessageHandler.Object)
            {
                BaseAddress = new Uri(_baseUrl)
            };

            // Setup mock configuration
            _mockConfiguration = new Mock<IConfiguration>();

            // Setup mock localizer
            _mockLocalizer = new Mock<IStringLocalizer<TeyaUIViewModelsStrings>>();
            _mockLocalizer.Setup(l => l["ProductRetrievalFailure"])
                .Returns(new LocalizedString("ProductRetrievalFailure", "Product retrieval failure"));
            _mockLocalizer.Setup(l => l["NoProduct"])
                .Returns(new LocalizedString("NoProduct", "No product"));
            _mockLocalizer.Setup(l => l["MemberRetrievalFailure"])
                .Returns(new LocalizedString("MemberRetrievalFailure", "Member retrieval failure"));

            // Setup mock token service
            _mockTokenService = new Mock<ITokenService>();
            _mockTokenService.Setup(t => t.AccessToken).Returns(_accessToken);
            _mockTokenService.Setup(t => t.GetValidatedAccessTokenAsync()).ReturnsAsync(_accessToken);

            // Create ProductService with mocked dependencies
            _productService = new ProductService(_httpClient, _mockConfiguration.Object, _mockLocalizer.Object, _mockTokenService.Object);
        }

        [TearDown]
        public void TearDown()
        {
            _httpClient.Dispose();
            Environment.SetEnvironmentVariable("MemberServiceURL", null);
        }

        [Test]
        public async Task GetProductsAsync_WhenSuccessful_ReturnsProducts()
        {
            // Arrange
            var expectedProducts = new List<Product>
            {
                new Product
                {
                    Id = Guid.NewGuid(),
                    Name = "Product 1",
                    Description = "Description for product 1",
                    Byproduct = "Version 1.0.0",
                    Subscription = true,
                    OrganizationId = Guid.NewGuid()
                },
                new Product
                {
                    Id = Guid.NewGuid(),
                    Name = "Product 2",
                    Description = "Description for product 2",
                    Byproduct = "Version 2.0.0",
                    Subscription = true,
                    OrganizationId = Guid.NewGuid()
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedProducts)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _productService.GetProductsAsync();

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(expectedProducts.Count));
            Assert.That(result[0].Id, Is.EqualTo(expectedProducts[0].Id));
            Assert.That(result[0].Name, Is.EqualTo(expectedProducts[0].Name));
            Assert.That(result[0].Description, Is.EqualTo(expectedProducts[0].Description));
            Assert.That(result[0].Byproduct, Is.EqualTo(expectedProducts[0].Byproduct));
            Assert.That(result[0].Subscription, Is.EqualTo(expectedProducts[0].Subscription));
        }

        [Test]
        public void GetProductsAsync_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.NotFound,
                Content = new StringContent("Not found")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _productService.GetProductsAsync());

            Assert.That(exception.Message, Is.EqualTo("Product retrieval failure"));
        }

        [Test]
        public async Task GetProductByIdAsync_WhenSuccessful_ReturnsProduct()
        {
            // Arrange
            var productId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var expectedProduct = new Product
            {
                Id = productId,
                Name = "Test Product",
                Description = "Test product description",
                Byproduct = "Version 1.0.0",
                Subscription = true,
                OrganizationId = orgId
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedProduct)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _productService.GetProductByIdAsync(productId, orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Id, Is.EqualTo(expectedProduct.Id));
            Assert.That(result.Name, Is.EqualTo(expectedProduct.Name));
            Assert.That(result.Description, Is.EqualTo(expectedProduct.Description));
            Assert.That(result.Byproduct, Is.EqualTo(expectedProduct.Byproduct));
            Assert.That(result.Subscription, Is.EqualTo(expectedProduct.Subscription));
        }

        [Test]
        public void GetProductByIdAsync_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var productId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.NotFound,
                Content = new StringContent("Not found")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _productService.GetProductByIdAsync(productId, orgId, subscription));

            Assert.That(exception.Message, Is.EqualTo("Product retrieval failure"));
        }

        [Test]
        public async Task UpdateProductAsync_WhenSuccessful_CompletesSuccessfully()
        {
            // Arrange
            var productId = Guid.NewGuid();
            var product = new Product
            {
                Id = productId,
                Name = "Updated Product",
                Description = "Updated product description",
                Byproduct = "Version 2.0.0",
                Subscription = true,
                OrganizationId = Guid.NewGuid()
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent("Success")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.DoesNotThrowAsync(async () => await _productService.UpdateProductAsync(productId, product));
        }

        [Test]
        public void UpdateProductAsync_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var productId = Guid.NewGuid();
            var product = new Product
            {
                Id = productId,
                Name = "Updated Product",
                Description = "Updated product description",
                Byproduct = "Version 2.0.0",
                Subscription = true,
                OrganizationId = Guid.NewGuid()
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _productService.UpdateProductAsync(productId, product));

            Assert.That(exception, Is.Not.Null);
        }

        [Test]
        public async Task DeleteProductByIdAsync_WhenSuccessful_CompletesSuccessfully()
        {
            // Arrange
            var productId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.NoContent,
                Content = new StringContent("Success")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.DoesNotThrowAsync(async () => await _productService.DeleteProductByIdAsync(productId, orgId, subscription));
        }

        [Test]
        public async Task DeleteProductByEntityAsync_WhenSuccessful_CompletesSuccessfully()
        {
            // Arrange
            var product = new Product
            {
                Id = Guid.NewGuid(),
                Name = "Product to Delete",
                Description = "Product description",
                Byproduct = "Version 1.0.0",
                Subscription = false,
                OrganizationId = Guid.NewGuid()
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.NoContent,
                Content = new StringContent("Success")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.DoesNotThrowAsync(async () => await _productService.DeleteProductByEntityAsync(product));
        }

        [Test]
        public async Task RegisterProductsAsync_WhenSuccessful_CompletesSuccessfully()
        {
            // Arrange
            var registrations = new List<ProductRegistrationDto>
            {
                new ProductRegistrationDto
                {
                    Id = Guid.NewGuid(),
                    Name = "Test Product 1",
                    Description = "Test product description 1",
                    ByProduct = "Brand A",
                    OrganizationId = Guid.NewGuid()
                },
                new ProductRegistrationDto
                {
                    Id = Guid.NewGuid(),
                    Name = "Test Product 2",
                    Description = "Test product description 2",
                    ByProduct = "Brand B",
                    OrganizationId = Guid.NewGuid()
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.Created,
                Content = new StringContent("Success")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.DoesNotThrowAsync(async () => await _productService.RegisterProductsAsync(registrations));
        }

        [Test]
        public void RegisterProductsAsync_WhenRegistrationsIsNull_ThrowsArgumentException()
        {
            // Arrange
            List<ProductRegistrationDto> registrations = null;

            // Act & Assert
            var exception = Assert.ThrowsAsync<ArgumentException>(async () =>
                await _productService.RegisterProductsAsync(registrations));

            Assert.That(exception.Message, Is.EqualTo("No product"));
        }

        [Test]
        public void RegisterProductsAsync_WhenRegistrationsIsEmpty_ThrowsArgumentException()
        {
            // Arrange
            var registrations = new List<ProductRegistrationDto>();

            // Act & Assert
            var exception = Assert.ThrowsAsync<ArgumentException>(async () =>
                await _productService.RegisterProductsAsync(registrations));

            Assert.That(exception.Message, Is.EqualTo("No product"));
        }

        [Test]
        public async Task GetMembersForProductAsync_WhenSuccessful_ReturnsMembers()
        {
            // Arrange
            var productId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var expectedMembers = new List<Member>
            {
                new Member
                {
                    Id = Guid.NewGuid(),
                    FirstName = "John",
                    LastName = "Doe",
                    Email = "<EMAIL>",
                    IsActive = true
                },
                new Member
                {
                    Id = Guid.NewGuid(),
                    FirstName = "Jane",
                    LastName = "Smith",
                    Email = "<EMAIL>",
                    IsActive = true
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedMembers)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _productService.GetMembersForProductAsync(productId, orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(expectedMembers.Count));
            Assert.That(result[0].FirstName, Is.EqualTo(expectedMembers[0].FirstName));
            Assert.That(result[0].LastName, Is.EqualTo(expectedMembers[0].LastName));
            Assert.That(result[0].Email, Is.EqualTo(expectedMembers[0].Email));
        }

        [Test]
        public async Task GetMembersForProductAsync_WhenNotFound_ReturnsEmptyList()
        {
            // Arrange
            var productId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.NotFound,
                Content = new StringContent("Not found")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _productService.GetMembersForProductAsync(productId, orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(0));
        }

        [Test]
        public async Task UpdateMembersAccessAsync_WhenSuccessful_CompletesSuccessfully()
        {
            // Arrange
            var productId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var memberAccessUpdates = new MemberAccessUpdate
            {
                MemberId = Guid.NewGuid(),
                HasAccess = true
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent("Success")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.DoesNotThrowAsync(async () => await _productService.UpdateMembersAccessAsync(productId, memberAccessUpdates, orgId, subscription));
        }
    }
}



