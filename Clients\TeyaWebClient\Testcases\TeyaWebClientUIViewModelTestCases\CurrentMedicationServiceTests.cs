using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using Moq;
using Moq.Protected;
using NUnit.Framework;
using TeyaUIModels.Model;
using TeyaUIViewModels.ViewModel;
using TeyaUIViewModels.TeyaUIViewModelResources;
using System.Net.Http.Json;
using System.Text;

namespace TeyaWebClientUIViewModelTestCases
{
    [TestFixture]
    public class CurrentMedicationServiceTests
    {
        private Mock<HttpMessageHandler> _mockHttpMessageHandler;
        private HttpClient _httpClient;
        private Mock<IConfiguration> _mockConfiguration;
        private Mock<IStringLocalizer<TeyaUIViewModelsStrings>> _mockLocalizer;
        private Mock<ITokenService> _mockTokenService;
        private CurrentMedicationService _currentMedicationService;
        private readonly string _baseUrl = "http://test-api.com";
        private readonly string _accessToken = "test-token";

        [SetUp]
        public void Setup()
        {
            // Setup environment variable
            Environment.SetEnvironmentVariable("EncounterNotesURL", _baseUrl);

            // Setup mock HTTP handler
            _mockHttpMessageHandler = new Mock<HttpMessageHandler>();

            // Setup HttpClient with mock handler
            _httpClient = new HttpClient(_mockHttpMessageHandler.Object)
            {
                BaseAddress = new Uri(_baseUrl)
            };

            // Setup mock configuration
            _mockConfiguration = new Mock<IConfiguration>();

            // Setup mock localizer
            _mockLocalizer = new Mock<IStringLocalizer<TeyaUIViewModelsStrings>>();
            _mockLocalizer.Setup(l => l["AddressRetrievalFailure"])
                .Returns(new LocalizedString("AddressRetrievalFailure", "Failed to retrieve address"));

            // Setup mock token service
            _mockTokenService = new Mock<ITokenService>();
            _mockTokenService.Setup(t => t.AccessToken).Returns(_accessToken);

            // Create CurrentMedicationService with mocked dependencies
            _currentMedicationService = new CurrentMedicationService(_httpClient, _mockConfiguration.Object, _mockLocalizer.Object, _mockTokenService.Object);
        }

        [TearDown]
        public void TearDown()
        {
            _httpClient.Dispose();
            Environment.SetEnvironmentVariable("EncounterNotesURL", null);
        }

        [Test]
        public async Task GetMedicationsByIdAsync_WhenSuccessful_ReturnsMedications()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var expectedMedications = new List<ActiveMedication>
            {
                new ActiveMedication
                {
                    MedicineId = Guid.NewGuid(),
                    PatientId = patientId,
                    BrandName = "Aspirin",
                    DrugDetails = "Pain reliever",
                    Quantity = "1 tablet",
                    Frequency = "Daily",
                    isActive = true
                },
                new ActiveMedication
                {
                    MedicineId = Guid.NewGuid(),
                    PatientId = patientId,
                    BrandName = "Lisinopril",
                    DrugDetails = "Blood pressure medication",
                    Quantity = "1 tablet",
                    Frequency = "Daily",
                    isActive = true
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedMedications)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _currentMedicationService.GetMedicationsByIdAsync(patientId, orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(2));
            Assert.That(result[0].BrandName, Is.EqualTo("Aspirin"));
            Assert.That(result[1].BrandName, Is.EqualTo("Lisinopril"));
        }

        [Test]
        public void GetMedicationsByIdAsync_WhenHttpRequestFails_ThrowsException()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _currentMedicationService.GetMedicationsByIdAsync(patientId, orgId, subscription));

            Assert.That(exception.Message, Is.EqualTo("Failed to retrieve address"));
        }

        [Test]
        public async Task GetMedicationsByIdAsyncAndIsActive_WhenSuccessful_ReturnsActiveMedications()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var expectedMedications = new List<ActiveMedication>
            {
                new ActiveMedication
                {
                    MedicineId = Guid.NewGuid(),
                    PatientId = patientId,
                    BrandName = "Aspirin",
                    DrugDetails = "Pain reliever",
                    Quantity = "1 tablet",
                    Frequency = "Daily",
                    isActive = true
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedMedications)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _currentMedicationService.GetMedicationsByIdAsyncAndIsActive(patientId, orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(1));
            Assert.That(result[0].BrandName, Is.EqualTo("Aspirin"));
            Assert.That(result[0].isActive, Is.True);
        }

        [Test]
        public void GetMedicationsByIdAsyncAndIsActive_WhenHttpRequestFails_ThrowsException()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _currentMedicationService.GetMedicationsByIdAsyncAndIsActive(patientId, orgId, subscription));

            Assert.That(exception.Message, Is.EqualTo("Failed to retrieve address"));
        }

        [Test]
        public async Task AddMedicationAsync_WhenSuccessful_CompletesWithoutException()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var medications = new List<ActiveMedication>
            {
                new ActiveMedication
                {
                    MedicineId = Guid.NewGuid(),
                    PatientId = patientId,
                    BrandName = "Aspirin",
                    DrugDetails = "Pain reliever",
                    Quantity = "1 tablet",
                    Frequency = "Daily",
                    isActive = true
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            // No exception should be thrown
            await _currentMedicationService.AddMedicationAsync(medications, orgId, subscription);
        }

        [Test]
        public void AddMedicationAsync_WhenHttpRequestFails_ThrowsException()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var medications = new List<ActiveMedication>
            {
                new ActiveMedication
                {
                    MedicineId = Guid.NewGuid(),
                    PatientId = patientId,
                    BrandName = "Aspirin",
                    DrugDetails = "Pain reliever",
                    Quantity = "1 tablet",
                    Frequency = "Daily",
                    isActive = true
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _currentMedicationService.AddMedicationAsync(medications, orgId, subscription));
        }

        [Test]
        public async Task DeletemedicationAsync_WhenSuccessful_CompletesWithoutException()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var medicineId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var medication = new ActiveMedication
            {
                MedicineId = medicineId,
                PatientId = patientId,
                BrandName = "Aspirin",
                DrugDetails = "Pain reliever",
                Quantity = "1 tablet",
                Frequency = "Daily",
                isActive = true
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            // No exception should be thrown
            await _currentMedicationService.DeletemedicationAsync(medication, orgId, subscription);
        }

        [Test]
        public void DeletemedicationAsync_WhenHttpRequestFails_ThrowsException()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var medicineId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var medication = new ActiveMedication
            {
                MedicineId = medicineId,
                PatientId = patientId,
                BrandName = "Aspirin",
                DrugDetails = "Pain reliever",
                Quantity = "1 tablet",
                Frequency = "Daily",
                isActive = true
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _currentMedicationService.DeletemedicationAsync(medication, orgId, subscription));
        }

        [Test]
        public async Task UpdateMedicationAsync_WhenSuccessful_CompletesWithoutException()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var medication = new ActiveMedication
            {
                MedicineId = Guid.NewGuid(),
                PatientId = patientId,
                BrandName = "Aspirin",
                DrugDetails = "Pain reliever",
                Quantity = "1 tablet",
                Frequency = "Daily",
                isActive = true
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            // No exception should be thrown
            await _currentMedicationService.UpdateMedicationAsync(medication, orgId, subscription);
        }

        [Test]
        public void UpdateMedicationAsync_WhenHttpRequestFails_ThrowsException()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var medication = new ActiveMedication
            {
                MedicineId = Guid.NewGuid(),
                PatientId = patientId,
                BrandName = "Aspirin",
                DrugDetails = "Pain reliever",
                Quantity = "1 tablet",
                Frequency = "Daily",
                isActive = true
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _currentMedicationService.UpdateMedicationAsync(medication, orgId, subscription));
        }

        [Test]
        public async Task UpdateMedicationListAsync_WhenSuccessful_CompletesWithoutException()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var medications = new List<ActiveMedication>
            {
                new ActiveMedication
                {
                    MedicineId = Guid.NewGuid(),
                    PatientId = patientId,
                    BrandName = "Aspirin",
                    DrugDetails = "Pain reliever",
                    Quantity = "1 tablet",
                    Frequency = "Daily",
                    isActive = true
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            // No exception should be thrown
            await _currentMedicationService.UpdateMedicationListAsync(medications, orgId, subscription);
        }

        [Test]
        public void UpdateMedicationListAsync_WhenHttpRequestFails_ThrowsException()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var medications = new List<ActiveMedication>
            {
                new ActiveMedication
                {
                    MedicineId = Guid.NewGuid(),
                    PatientId = patientId,
                    BrandName = "Aspirin",
                    DrugDetails = "Pain reliever",
                    Quantity = "1 tablet",
                    Frequency = "Daily",
                    isActive = true
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _currentMedicationService.UpdateMedicationListAsync(medications, orgId, subscription));
        }
    }
}



