﻿using System.ComponentModel.DataAnnotations;

namespace TeyaHealthMobileModel.Model
{
    public class Organization : IModel
    {
        public Guid OrganizationId { get; set; }

        [Required(ErrorMessage = "Organization Name is required")]
        [StringLength(100, ErrorMessage = "Organization Name cannot exceed 100 characters")]
        [RegularExpression(@"^[a-zA-Z0-9\s]*$", ErrorMessage = "Organization Name cannot contain special symbols")]
        public string OrganizationName { get; set; }

        [Required(ErrorMessage = "Country is required")]
        [RegularExpression(@"^[a-zA-Z\s]*$", ErrorMessage = "Country cannot contain special symbols or numbers")]
        public string Country { get; set; }
        public DateTime? CreatedDate { get; set; } = DateTime.Now;
        public DateTime? UpdatedDate { get; set; }
        public Guid? UpdatedBy { get; set; }

        [StringLength(200, ErrorMessage = "Address cannot exceed 200 characters")]
        [RegularExpression(@"^[a-zA-Z0-9\s,.-]*$", ErrorMessage = "Address cannot contain special symbols other than , . -")]
        public string Address { get; set; }

        [RegularExpression(@"^\+?[1-9]\d{1,14}$", ErrorMessage = "Invalid Contact Number")]
        public string ContactNumber { get; set; }

        [EmailAddress(ErrorMessage = "Invalid Email Address")]
        public string Email { get; set; }

        public bool IsActive { get; set; } = true;
    }

    public class OrganizationsResponse
    {
        public List<Organization>? value { get; set; }
    }
}
