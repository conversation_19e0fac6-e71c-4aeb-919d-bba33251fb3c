using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using Moq;
using Moq.Protected;
using NUnit.Framework;
using TeyaUIModels.Model;
using TeyaUIViewModels.ViewModel;
using TeyaUIViewModels.TeyaUIViewModelResources;
using System.Net.Http.Json;
using System.Text;
using System.Text.Json;

namespace TeyaWebClientUIViewModelTestCases
{
    [TestFixture]
    public class AlertServiceTests
    {
        private Mock<HttpMessageHandler> _mockHttpMessageHandler;
        private HttpClient _httpClient;
        private Mock<IConfiguration> _mockConfiguration;
        private Mock<IStringLocalizer<TeyaUIViewModelsStrings>> _mockLocalizer;
        private Mock<ITokenService> _mockTokenService;
        private AlertService _alertService;
        private readonly string _baseUrl = "http://test-api.com";
        private readonly string _accessToken = "test-token";

        [SetUp]
        public void Setup()
        {
            // Setup environment variable
            Environment.SetEnvironmentVariable("AlertsServiceURL", _baseUrl);

            // Setup mock HTTP handler
            _mockHttpMessageHandler = new Mock<HttpMessageHandler>();

            // Setup HttpClient with mock handler
            _httpClient = new HttpClient(_mockHttpMessageHandler.Object)
            {
                BaseAddress = new Uri(_baseUrl)
            };

            // Setup mock configuration
            _mockConfiguration = new Mock<IConfiguration>();

            // Setup mock localizer
            _mockLocalizer = new Mock<IStringLocalizer<TeyaUIViewModelsStrings>>();
            _mockLocalizer.Setup(l => l["RecordNotFound"])
                .Returns(new LocalizedString("RecordNotFound", "Record not found"));
            _mockLocalizer.Setup(l => l["DatabaseError"])
                .Returns(new LocalizedString("DatabaseError", "Database error"));

            // Setup mock token service
            _mockTokenService = new Mock<ITokenService>();
            _mockTokenService.Setup(t => t.AccessToken).Returns(_accessToken);

            // Create AlertService with mocked dependencies
            _alertService = new AlertService(_httpClient, _mockConfiguration.Object, _mockLocalizer.Object, _mockTokenService.Object);
        }

        [TearDown]
        public void TearDown()
        {
            _httpClient.Dispose();
            Environment.SetEnvironmentVariable("AlertsServiceURL", null);
        }

        [Test]
        public async Task GetAllByIdAsync_WhenSuccessful_ReturnsAlerts()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var expectedAlerts = new List<Alert>
            {
                new Alert
                {
                    AlertId = Guid.NewGuid(),
                    PatientId = patientId,
                    PatientName = "Test Patient",
                    Severity = "High",
                    AlertType = "Medication",
                    Description = "Test Description",
                    Solution = "Test Solution",
                    OrganizationId = orgId,
                    IsActive = true
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedAlerts)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _alertService.GetAllByIdAsync(patientId, orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(1));
            Assert.That(result[0].AlertId, Is.EqualTo(expectedAlerts[0].AlertId));
            Assert.That(result[0].PatientName, Is.EqualTo(expectedAlerts[0].PatientName));
            Assert.That(result[0].Severity, Is.EqualTo(expectedAlerts[0].Severity));
            Assert.That(result[0].AlertType, Is.EqualTo(expectedAlerts[0].AlertType));
            Assert.That(result[0].Description, Is.EqualTo(expectedAlerts[0].Description));
            Assert.That(result[0].Solution, Is.EqualTo(expectedAlerts[0].Solution));
        }

        [Test]
        public void GetAllByIdAsync_WhenHttpRequestFails_ThrowsException()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var expectedException = new HttpRequestException("Test exception");

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ThrowsAsync(expectedException);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _alertService.GetAllByIdAsync(patientId, orgId, subscription));

            Assert.That(exception.Message, Is.EqualTo(expectedException.Message));
        }

        [Test]
        public async Task GetAllByIdAndIsActiveAsync_WhenSuccessful_ReturnsActiveAlerts()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var expectedAlerts = new List<Alert>
            {
                new Alert
                {
                    AlertId = Guid.NewGuid(),
                    PatientId = patientId,
                    PatientName = "Test Patient",
                    Severity = "High",
                    AlertType = "Medication",
                    Description = "Test Description",
                    Solution = "Test Solution",
                    OrganizationId = orgId,
                    IsActive = true
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedAlerts)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _alertService.GetAllByIdAndIsActiveAsync(patientId, orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(1));
            Assert.That(result[0].AlertId, Is.EqualTo(expectedAlerts[0].AlertId));
            Assert.That(result[0].IsActive, Is.True);
        }

        [Test]
        public void GetAllByIdAndIsActiveAsync_WhenHttpRequestFails_ThrowsException()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var expectedException = new HttpRequestException("Test exception");

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ThrowsAsync(expectedException);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _alertService.GetAllByIdAndIsActiveAsync(patientId, orgId, subscription));

            Assert.That(exception.Message, Is.EqualTo(expectedException.Message));
        }

        [Test]
        public async Task AddAlertsAsync_WhenSuccessful_CompletesWithoutException()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;
            var alerts = new List<Alert>
            {
                new Alert
                {
                    AlertId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    PatientName = "Test Patient",
                    Severity = "High",
                    AlertType = "Medication",
                    Description = "Test Description",
                    Solution = "Test Solution",
                    OrganizationId = orgId,
                    IsActive = true
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.DoesNotThrowAsync(async () => await _alertService.AddAlertsAsync(alerts, orgId, subscription));
        }

        [Test]
        public void AddAlertsAsync_WhenHttpRequestFails_ThrowsException()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;
            var alerts = new List<Alert>
            {
                new Alert
                {
                    AlertId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    PatientName = "Test Patient",
                    Severity = "High",
                    AlertType = "Medication",
                    Description = "Test Description",
                    Solution = "Test Solution",
                    OrganizationId = orgId,
                    IsActive = true
                }
            };

            var expectedException = new HttpRequestException("Test exception");

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ThrowsAsync(expectedException);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _alertService.AddAlertsAsync(alerts, orgId, subscription));

            Assert.That(exception.Message, Is.EqualTo(expectedException.Message));
        }

        [Test]
        public async Task UpdateAlertAsync_WhenSuccessful_CompletesWithoutException()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;
            var alert = new Alert
            {
                AlertId = Guid.NewGuid(),
                PatientId = Guid.NewGuid(),
                PatientName = "Test Patient",
                Severity = "High",
                AlertType = "Medication",
                Description = "Test Description",
                Solution = "Test Solution",
                OrganizationId = orgId,
                IsActive = true
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.DoesNotThrowAsync(async () => await _alertService.UpdateAlertAsync(alert, orgId, subscription));
        }

        [Test]
        public void UpdateAlertAsync_WhenHttpRequestFails_ThrowsException()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;
            var alert = new Alert
            {
                AlertId = Guid.NewGuid(),
                PatientId = Guid.NewGuid(),
                PatientName = "Test Patient",
                Severity = "High",
                AlertType = "Medication",
                Description = "Test Description",
                Solution = "Test Solution",
                OrganizationId = orgId,
                IsActive = true
            };

            var expectedException = new HttpRequestException("Test exception");

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ThrowsAsync(expectedException);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _alertService.UpdateAlertAsync(alert, orgId, subscription));

            Assert.That(exception.Message, Is.EqualTo(expectedException.Message));
        }

        [Test]
        public async Task UpdateAlertsListAsync_WhenSuccessful_CompletesWithoutException()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;
            var alerts = new List<Alert>
            {
                new Alert
                {
                    AlertId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    PatientName = "Test Patient",
                    Severity = "High",
                    AlertType = "Medication",
                    Description = "Test Description",
                    Solution = "Test Solution",
                    OrganizationId = orgId,
                    IsActive = true
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.DoesNotThrowAsync(async () => await _alertService.UpdateAlertsListAsync(alerts, orgId, subscription));
        }

        [Test]
        public void UpdateAlertsListAsync_WhenHttpRequestFails_ThrowsException()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;
            var alerts = new List<Alert>
            {
                new Alert
                {
                    AlertId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    PatientName = "Test Patient",
                    Severity = "High",
                    AlertType = "Medication",
                    Description = "Test Description",
                    Solution = "Test Solution",
                    OrganizationId = orgId,
                    IsActive = true
                }
            };

            var expectedException = new HttpRequestException("Test exception");

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ThrowsAsync(expectedException);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _alertService.UpdateAlertsListAsync(alerts, orgId, subscription));

            Assert.That(exception.Message, Is.EqualTo(expectedException.Message));
        }

        [Test]
        public async Task DeleteAlertByEntityAsync_WhenSuccessful_CompletesWithoutException()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;
            var alert = new Alert
            {
                AlertId = Guid.NewGuid(),
                PatientId = Guid.NewGuid(),
                PatientName = "Test Patient",
                Severity = "High",
                AlertType = "Medication",
                Description = "Test Description",
                Solution = "Test Solution",
                OrganizationId = orgId,
                IsActive = true
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.DoesNotThrowAsync(async () => await _alertService.DeleteAlertByEntityAsync(alert, orgId, subscription));
        }

        [Test]
        public void DeleteAlertByEntityAsync_WhenHttpRequestFails_ThrowsException()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;
            var alert = new Alert
            {
                AlertId = Guid.NewGuid(),
                PatientId = Guid.NewGuid(),
                PatientName = "Test Patient",
                Severity = "High",
                AlertType = "Medication",
                Description = "Test Description",
                Solution = "Test Solution",
                OrganizationId = orgId,
                IsActive = true
            };

            var expectedException = new HttpRequestException("Test exception");

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ThrowsAsync(expectedException);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _alertService.DeleteAlertByEntityAsync(alert, orgId, subscription));

            Assert.That(exception.Message, Is.EqualTo(expectedException.Message));
        }
    }
}



