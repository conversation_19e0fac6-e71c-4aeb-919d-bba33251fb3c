﻿using Android.App;
using Android.Content;
using Android.Content.PM;
using Android.OS;
using AndroidX.Core.App;
using AndroidX.Core.Content;
using Microsoft.Extensions.Logging;
using Microsoft.Identity.Client;

namespace TeyaMobile
{
    [Activity(Theme = "@style/Maui.SplashTheme", LaunchMode = LaunchMode.SingleTop, MainLauncher = true, ConfigurationChanges = ConfigChanges.ScreenSize | ConfigChanges.Orientation | ConfigChanges.UiMode | ConfigChanges.ScreenLayout | ConfigChanges.SmallestScreenSize | ConfigChanges.Density)]
    public class MainActivity : MauiAppCompatActivity
    {
        private const int REQUEST_PERMISSIONS = 1;
        private readonly string[] _requiredPermissions =
        [
            Android.Manifest.Permission.RecordAudio,
            Android.Manifest.Permission.WriteExternalStorage,
            Android.Manifest.Permission.ReadExternalStorage
        ];

        private ILogger<MainActivity>? _logger;
        private bool _permissionsRequested = false;
        private bool _permissionsCheckedOnResume = false;
        protected override void OnCreate(Bundle? savedInstanceState)
        {
            base.OnCreate(savedInstanceState);
            _logger = Microsoft.Maui.Controls.Application.Current?.Handler.MauiContext?.Services.GetService<ILogger<MainActivity>>();
            CheckAndRequestPermissions();

            // Initialize MSAL
            Platform.Init(this, savedInstanceState);
        }

        protected override void OnActivityResult(int requestCode, Result resultCode, Intent data)
        {
            base.OnActivityResult(requestCode, resultCode, data);
            AuthenticationContinuationHelper.SetAuthenticationContinuationEventArgs(requestCode, resultCode, data);
        }

        protected override void OnResume()
        {
            base.OnResume();
            if (!_permissionsCheckedOnResume)
            {
                _permissionsCheckedOnResume = true;
                CheckAndRequestPermissions();
            }
        }

        protected override void OnPause()
        {
            base.OnPause();
            _permissionsCheckedOnResume = false;
        }

        private void CheckAndRequestPermissions()
        {
            try
            {
                if (_permissionsRequested)
                {
                    return;
                }
                bool needsPermissions = false;
                foreach (var permission in _requiredPermissions)
                {
                    var permissionStatus = ContextCompat.CheckSelfPermission(this, permission);
                    if (permissionStatus != Permission.Granted)
                    {
                        needsPermissions = true;
                    }
                }
                if (needsPermissions)
                {
                    ActivityCompat.RequestPermissions(
                        this,
                        _requiredPermissions,
                        REQUEST_PERMISSIONS);
                    _permissionsRequested = true;
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error requesting permissions");
            }
        }

        public override void OnRequestPermissionsResult(int requestCode, string[] permissions, Permission[] grantResults)
        {
#pragma warning disable CA1416
            base.OnRequestPermissionsResult(requestCode, permissions, grantResults);
#pragma warning restore CA1416
            try
            {
                if (requestCode == REQUEST_PERMISSIONS)
                {
                    bool allGranted = true;
                    for (int i = 0; i < grantResults.Length; i++)
                    {
                        if (grantResults[i] != Permission.Granted)
                        {
                            allGranted = false;
                        }
                    }
                    _permissionsRequested = true;
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error in OnRequestPermissionsResult");
            }
        }
    }
}
