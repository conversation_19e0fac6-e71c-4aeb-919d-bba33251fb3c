using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Moq;
using Moq.Protected;
using NUnit.Framework;
using TeyaUIModels.Model;
using TeyaUIModels.ViewModel;
using TeyaWebApp.ViewModel;

namespace TeyaWebClientUIViewModelTestCases
{
    [TestFixture]
    public class OrganizationServiceTests
    {
        private Mock<HttpMessageHandler> _mockHttpMessageHandler;
        private Mock<IStringLocalizer<OrganizationService>> _mockLocalizer;
        private Mock<ILogger<OrganizationService>> _mockLogger;
        private HttpClient _httpClient;
        private OrganizationService _organizationService;

        private const string TestMemberServiceUrl = "https://test-memberservice.com";

        [SetUp]
        public void SetUp()
        {
            _mockHttpMessageHandler = new Mock<HttpMessageHandler>();
            _mockLocalizer = new Mock<IStringLocalizer<OrganizationService>>();
            _mockLogger = new Mock<ILogger<OrganizationService>>();

            _httpClient = new HttpClient(_mockHttpMessageHandler.Object);

            // Set up environment variable
            Environment.SetEnvironmentVariable("MemberServiceURL", TestMemberServiceUrl);

            // Set up mock localizer responses
            _mockLocalizer.Setup(l => l["ErrorFetchingAllOrganizations"]).Returns(new LocalizedString("ErrorFetchingAllOrganizations", "Error fetching all organizations"));
            _mockLocalizer.Setup(l => l["ErrorFetchingOrganizationById"]).Returns(new LocalizedString("ErrorFetchingOrganizationById", "Error fetching organization by ID"));
            _mockLocalizer.Setup(l => l["ErrorRegisteringOrganization"]).Returns(new LocalizedString("ErrorRegisteringOrganization", "Error registering organization"));
            _mockLocalizer.Setup(l => l["ErrorUpdatingOrganization"]).Returns(new LocalizedString("ErrorUpdatingOrganization", "Error updating organization"));
            _mockLocalizer.Setup(l => l["ErrorDeletingOrganization"]).Returns(new LocalizedString("ErrorDeletingOrganization", "Error deleting organization"));
            _mockLocalizer.Setup(l => l["ErrorFetchingOrganizationsByName"]).Returns(new LocalizedString("ErrorFetchingOrganizationsByName", "Error fetching organizations by name"));
            _mockLocalizer.Setup(l => l["ErrorFetchingOrganizationIdByName"]).Returns(new LocalizedString("ErrorFetchingOrganizationIdByName", "Error fetching organization ID by name"));

            _organizationService = new OrganizationService(
                _httpClient,
                _mockLocalizer.Object,
                _mockLogger.Object);
        }

        [TearDown]
        public void TearDown()
        {
            _httpClient?.Dispose();
            Environment.SetEnvironmentVariable("MemberServiceURL", null);
        }

        private Organization CreateTestOrganization()
        {
            return new Organization
            {
                OrganizationId = Guid.NewGuid(),
                OrganizationName = "Test Healthcare Organization",
                Country = "United States",
                Address = "123 Main Street, Anytown, USA",
                ContactNumber = "+**********",
                Email = "<EMAIL>",
                CreatedDate = DateTime.UtcNow,
                UpdatedDate = DateTime.UtcNow,
                UpdatedBy = Guid.NewGuid(),
                IsActive = true
            };
        }

        [Test]
        public async Task RegisterOrganizationsAsync_WhenSuccessful_ReturnsOrganization()
        {
            // Arrange
            var organization = CreateTestOrganization();
            var responseContent = JsonSerializer.Serialize(organization);
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.Created,
                Content = new StringContent(responseContent, Encoding.UTF8, "application/json")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _organizationService.RegisterOrganizationsAsync(organization);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.OrganizationName, Is.EqualTo("Test Healthcare Organization"));
            Assert.That(result.Country, Is.EqualTo("United States"));
        }

        [Test]
        public async Task RegisterOrganizationsAsync_WhenHttpRequestFails_ThrowsException()
        {
            // Arrange
            var organization = CreateTestOrganization();
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad Request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _organizationService.RegisterOrganizationsAsync(organization));

            Assert.That(exception.Message, Does.Contain("Exception of type"));
        }

        [Test]
        public async Task GetOrganizationByIdAsync_WhenSuccessful_ReturnsOrganization()
        {
            // Arrange
            var organizationId = Guid.NewGuid();
            var expectedOrganization = CreateTestOrganization();
            expectedOrganization.OrganizationId = organizationId;

            var responseContent = JsonSerializer.Serialize(expectedOrganization);
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(responseContent, Encoding.UTF8, "application/json")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _organizationService.GetOrganizationByIdAsync(organizationId);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.OrganizationId, Is.EqualTo(organizationId));
            Assert.That(result.OrganizationName, Is.EqualTo("Test Healthcare Organization"));
        }

        [Test]
        public async Task GetOrganizationByIdAsync_WhenNotFound_ThrowsException()
        {
            // Arrange
            var organizationId = Guid.NewGuid();
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.NotFound,
                Content = new StringContent("Not Found")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _organizationService.GetOrganizationByIdAsync(organizationId));

            Assert.That(exception.Message, Does.Contain("Exception of type"));
        }

        [Test]
        public async Task GetAllOrganizationsAsync_WhenSuccessful_ReturnsOrganizationList()
        {
            // Arrange
            var expectedOrganizations = new List<Organization>
            {
                CreateTestOrganization(),
                CreateTestOrganization()
            };

            var responseContent = JsonSerializer.Serialize(expectedOrganizations);
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(responseContent, Encoding.UTF8, "application/json")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _organizationService.GetAllOrganizationsAsync();

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(2));
            Assert.That(result[0].OrganizationName, Is.EqualTo("Test Healthcare Organization"));
        }

        [Test]
        public async Task GetAllOrganizationsAsync_WhenHttpRequestFails_ThrowsException()
        {
            // Arrange
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.InternalServerError,
                Content = new StringContent("Internal Server Error")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _organizationService.GetAllOrganizationsAsync());

            Assert.That(exception.Message, Does.Contain("Internal Server Error"));
        }

        [Test]
        public async Task DeleteOrganizationByIdAsync_WhenSuccessful_CompletesWithoutException()
        {
            // Arrange
            var organizationId = Guid.NewGuid();
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.NoContent
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            // No exception should be thrown
            await _organizationService.DeleteOrganizationByIdAsync(organizationId);
        }

        [Test]
        public async Task UpdateOrganizationByIdAsync_WhenSuccessful_CompletesWithoutException()
        {
            // Arrange
            var organizationId = Guid.NewGuid();
            var organization = CreateTestOrganization();
            organization.OrganizationId = organizationId; // Ensure the organization ID matches the parameter
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            // No exception should be thrown
            await _organizationService.UpdateOrganizationByIdAsync(organizationId, organization);
        }

        [Test]
        public async Task GetOrganizationsByNameAsync_WhenSuccessful_ReturnsOrganizationList()
        {
            // Arrange
            var organizationName = "Test Healthcare";
            var expectedOrganizations = new List<Organization>
            {
                CreateTestOrganization()
            };

            var responseContent = JsonSerializer.Serialize(expectedOrganizations);
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(responseContent, Encoding.UTF8, "application/json")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _organizationService.GetOrganizationsByNameAsync(organizationName);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(1));
            Assert.That(result[0].OrganizationName, Is.EqualTo("Test Healthcare Organization"));
        }

        [Test]
        public async Task GetOrganizationIdByNameAsync_WhenSuccessful_ReturnsOrganizationId()
        {
            // Arrange
            var organizationName = "Test Healthcare Organization";
            var expectedOrganizationId = Guid.NewGuid();
            var organization = CreateTestOrganization();
            organization.OrganizationId = expectedOrganizationId;
            organization.OrganizationName = organizationName;

            // The service expects a list of organizations from GetOrganizationsByNameAsync
            var organizationsList = new List<Organization> { organization };
            var responseContent = JsonSerializer.Serialize(organizationsList);
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(responseContent, Encoding.UTF8, "application/json")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _organizationService.GetOrganizationIdByNameAsync(organizationName);

            // Assert
            Assert.That(result, Is.EqualTo(expectedOrganizationId));
        }

        [Test]
        public async Task GetOrganizationIdByNameAsync_WhenNotFound_ThrowsException()
        {
            // Arrange
            var organizationName = "Nonexistent Organization";
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.NotFound,
                Content = new StringContent("Not Found")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<KeyNotFoundException>(async () =>
                await _organizationService.GetOrganizationIdByNameAsync(organizationName));

            Assert.That(exception.Message, Does.Contain("Exception of type"));
        }
    }
}



