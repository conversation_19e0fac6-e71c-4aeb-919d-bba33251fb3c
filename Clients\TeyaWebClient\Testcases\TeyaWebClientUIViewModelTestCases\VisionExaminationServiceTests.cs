using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using Moq;
using Moq.Protected;
using NUnit.Framework;
using TeyaUIModels.Model;
using TeyaUIViewModels.ViewModel;
using System.Net.Http.Json;
using System.Text.Json;

namespace TeyaWebClientUIViewModelTestCases
{
    [TestFixture]
    public class VisionExaminationServiceTests
    {
        private Mock<HttpMessageHandler> _mockHttpMessageHandler;
        private HttpClient _httpClient;
        private Mock<IConfiguration> _mockConfiguration;
        private Mock<IStringLocalizer<VisionExaminationService>> _mockLocalizer;
        private Mock<ITokenService> _mockTokenService;
        private VisionExaminationService _visionExaminationService;
        private readonly string _baseUrl = "http://test-api.com";
        private readonly string _accessToken = "test-access-token";

        [SetUp]
        public void Setup()
        {
            // Setup environment variable
            Environment.SetEnvironmentVariable("EncounterNotesURL", _baseUrl);

            // Setup mock HTTP handler
            _mockHttpMessageHandler = new Mock<HttpMessageHandler>();

            // Setup HttpClient with mock handler
            _httpClient = new HttpClient(_mockHttpMessageHandler.Object)
            {
                BaseAddress = new Uri(_baseUrl)
            };

            // Setup mock configuration
            _mockConfiguration = new Mock<IConfiguration>();

            // Setup mock localizer
            _mockLocalizer = new Mock<IStringLocalizer<VisionExaminationService>>();
            _mockLocalizer.Setup(l => l["VisionRecordRetrievalFailure"])
                .Returns(new LocalizedString("VisionRecordRetrievalFailure", "Vision record retrieval failure"));
            _mockLocalizer.Setup(l => l["RelationRetrievalFailure"])
                .Returns(new LocalizedString("RelationRetrievalFailure", "Relation retrieval failure"));
            _mockLocalizer.Setup(l => l["Error"])
                .Returns(new LocalizedString("Error", "Error"));

            // Setup mock token service
            _mockTokenService = new Mock<ITokenService>();
            _mockTokenService.Setup(t => t.AccessToken).Returns(_accessToken);
            _mockTokenService.Setup(t => t.GetValidatedAccessTokenAsync()).ReturnsAsync(_accessToken);

            // Create VisionExaminationService with mocked dependencies
            _visionExaminationService = new VisionExaminationService(_httpClient, _mockConfiguration.Object, _mockLocalizer.Object, _mockTokenService.Object);
        }

        [TearDown]
        public void TearDown()
        {
            _httpClient.Dispose();
            Environment.SetEnvironmentVariable("EncounterNotesURL", null);
        }

        [Test]
        public async Task GetVisionExaminationAsync_WhenSuccessful_ReturnsVisionExaminations()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var expectedExaminations = new List<VisionRx>
            {
                new VisionRx
                {
                    ExaminationId = Guid.NewGuid(),
                    PatientId = patientId,
                    OrganizationId = orgId,
                    CreatedBy = Guid.NewGuid(),
                    UpdatedBy = Guid.NewGuid(),
                    CreatedDate = DateTime.Now.AddDays(-30),
                    UpdatedDate = DateTime.Now.AddDays(-29),
                    IsActive = true,
                    Subscription = subscription,
                    ManifestSphOD = "-1.25",
                    ManifestSphOS = "-1.50",
                    ManifestCylOD = "-0.50",
                    ManifestCylOS = "-0.75",
                    ManifestAxisOD = "90",
                    ManifestAxisOS = "85",
                    PdDistOU = 62.5m
                },
                new VisionRx
                {
                    ExaminationId = Guid.NewGuid(),
                    PatientId = patientId,
                    OrganizationId = orgId,
                    CreatedBy = Guid.NewGuid(),
                    UpdatedBy = Guid.NewGuid(),
                    CreatedDate = DateTime.Now.AddDays(-180),
                    UpdatedDate = DateTime.Now.AddDays(-179),
                    IsActive = true,
                    Subscription = subscription,
                    ManifestSphOD = "-1.00",
                    ManifestSphOS = "-1.25",
                    ManifestCylOD = "-0.25",
                    ManifestCylOS = "-0.50",
                    ManifestAxisOD = "95",
                    ManifestAxisOS = "90",
                    PdDistOU = 62.0m
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedExaminations)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _visionExaminationService.GetVisionExaminationAsync(patientId, orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(expectedExaminations.Count));
            Assert.That(result[0].ExaminationId, Is.EqualTo(expectedExaminations[0].ExaminationId));
            Assert.That(result[0].PatientId, Is.EqualTo(expectedExaminations[0].PatientId));
            Assert.That(result[0].OrganizationId, Is.EqualTo(expectedExaminations[0].OrganizationId));
            Assert.That(result[0].ManifestSphOD, Is.EqualTo(expectedExaminations[0].ManifestSphOD));
            Assert.That(result[0].ManifestSphOS, Is.EqualTo(expectedExaminations[0].ManifestSphOS));
            Assert.That(result[0].ManifestCylOD, Is.EqualTo(expectedExaminations[0].ManifestCylOD));
            Assert.That(result[0].ManifestCylOS, Is.EqualTo(expectedExaminations[0].ManifestCylOS));
            Assert.That(result[0].ManifestAxisOD, Is.EqualTo(expectedExaminations[0].ManifestAxisOD));
            Assert.That(result[0].ManifestAxisOS, Is.EqualTo(expectedExaminations[0].ManifestAxisOS));
            Assert.That(result[0].PdDistOU, Is.EqualTo(expectedExaminations[0].PdDistOU));
            Assert.That(result[0].IsActive, Is.EqualTo(expectedExaminations[0].IsActive));
            Assert.That(result[0].Subscription, Is.EqualTo(expectedExaminations[0].Subscription));
        }

        [Test]
        public void GetVisionExaminationAsync_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.NotFound,
                Content = new StringContent("Not found")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _visionExaminationService.GetVisionExaminationAsync(patientId, orgId, subscription));

            Assert.That(exception.Message, Is.EqualTo("Vision record retrieval failure"));
        }

        [Test]
        public async Task GetVisionExaminationByIdAsyncAndIsActive_WhenSuccessful_ReturnsActiveVisionExaminations()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var expectedExaminations = new List<VisionRx>
            {
                new VisionRx
                {
                    ExaminationId = Guid.NewGuid(),
                    PatientId = patientId,
                    OrganizationId = orgId,
                    CreatedBy = Guid.NewGuid(),
                    UpdatedBy = Guid.NewGuid(),
                    CreatedDate = DateTime.Now.AddDays(-10),
                    UpdatedDate = DateTime.Now.AddDays(-9),
                    IsActive = true,
                    Subscription = subscription,
                    ManifestSphOD = "-2.00",
                    ManifestSphOS = "-2.25",
                    ManifestCylOD = "-0.75",
                    ManifestCylOS = "-1.00",
                    ManifestAxisOD = "180",
                    ManifestAxisOS = "175",
                    PdDistOU = 63.0m
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedExaminations)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _visionExaminationService.GetVisionExaminationByIdAsyncAndIsActive(patientId, orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(expectedExaminations.Count));
            Assert.That(result[0].IsActive, Is.True);
            Assert.That(result[0].ManifestSphOD, Is.EqualTo("-2.00"));
            Assert.That(result[0].PdDistOU, Is.EqualTo(63.0m));
        }

        [Test]
        public void GetVisionExaminationByIdAsyncAndIsActive_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _visionExaminationService.GetVisionExaminationByIdAsyncAndIsActive(patientId, orgId, subscription));
        }

        [Test]
        public async Task CreateVisionExaminationAsync_WhenSuccessful_CompletesSuccessfully()
        {
            // Arrange
            var visionExamination = new VisionRx
            {
                ExaminationId = Guid.NewGuid(),
                PatientId = Guid.NewGuid(),
                OrganizationId = Guid.NewGuid(),
                CreatedBy = Guid.NewGuid(),
                UpdatedBy = Guid.NewGuid(),
                CreatedDate = DateTime.Now,
                UpdatedDate = DateTime.Now,
                IsActive = true,
                Subscription = true,
                ManifestSphOD = "-1.75",
                ManifestSphOS = "-2.00",
                ManifestCylOD = "-0.50",
                ManifestCylOS = "-0.75",
                ManifestAxisOD = "90",
                ManifestAxisOS = "85",
                PdDistOU = 64.0m
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.Created,
                Content = new StringContent("Vision examination created successfully")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.DoesNotThrowAsync(async () => await _visionExaminationService.CreateVisionExaminationAsync(visionExamination));
        }

        [Test]
        public async Task UpdateVisionRecordsAsync_WhenSuccessful_CompletesSuccessfully()
        {
            // Arrange
            var visionExamination = new VisionRx
            {
                ExaminationId = Guid.NewGuid(),
                PatientId = Guid.NewGuid(),
                OrganizationId = Guid.NewGuid(),
                CreatedBy = Guid.NewGuid(),
                UpdatedBy = Guid.NewGuid(),
                CreatedDate = DateTime.Now.AddDays(-10),
                UpdatedDate = DateTime.Now.AddDays(-5),
                IsActive = true,
                Subscription = true,
                ManifestSphOD = "-1.50",
                ManifestSphOS = "-1.75",
                ManifestCylOD = "-0.25",
                ManifestCylOS = "-0.50",
                ManifestAxisOD = "95",
                ManifestAxisOS = "90",
                PdDistOU = 63.5m
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent("Vision examination updated successfully")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.DoesNotThrowAsync(async () => await _visionExaminationService.UpdateVisionRecordsAsync(visionExamination));
        }

        [Test]
        public void UpdateVisionRecordsAsync_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var visionExamination = new VisionRx
            {
                ExaminationId = Guid.NewGuid(),
                PatientId = Guid.NewGuid(),
                OrganizationId = Guid.NewGuid(),
                CreatedBy = Guid.NewGuid(),
                UpdatedBy = Guid.NewGuid(),
                CreatedDate = DateTime.Now.AddDays(-10),
                UpdatedDate = DateTime.Now.AddDays(-5),
                IsActive = true,
                Subscription = true,
                ManifestSphOD = "-3.00",
                ManifestSphOS = "-3.25",
                ManifestCylOD = "-1.00",
                ManifestCylOS = "-1.25",
                ManifestAxisOD = "180",
                ManifestAxisOS = "175",
                PdDistOU = 65.0m
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _visionExaminationService.UpdateVisionRecordsAsync(visionExamination));
        }

        [Test]
        public async Task GetVisionRecordsAsync_WhenSuccessful_ReturnsVisionRecords()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;
            var expectedRecords = new List<VisionRx>
            {
                new VisionRx
                {
                    ExaminationId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    OrganizationId = orgId,
                    CreatedBy = Guid.NewGuid(),
                    UpdatedBy = Guid.NewGuid(),
                    CreatedDate = DateTime.Now.AddDays(-20),
                    UpdatedDate = DateTime.Now.AddDays(-19),
                    IsActive = true,
                    Subscription = subscription,
                    ManifestSphOD = "-2.50",
                    ManifestSphOS = "-2.75",
                    ManifestCylOD = "-0.75",
                    ManifestCylOS = "-1.00",
                    ManifestAxisOD = "85",
                    ManifestAxisOS = "80",
                    PdDistOU = 61.5m
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedRecords)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _visionExaminationService.GetVisionRecordsAsync(orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(expectedRecords.Count));
            Assert.That(result[0].ManifestSphOD, Is.EqualTo("-2.50"));
            Assert.That(result[0].OrganizationId, Is.EqualTo(orgId));
        }

        [Test]
        public void GetVisionRecordsAsync_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.InternalServerError,
                Content = new StringContent("Internal server error")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _visionExaminationService.GetVisionRecordsAsync(orgId, subscription));

            Assert.That(exception.Message, Is.EqualTo("Relation retrieval failure"));
        }

        [Test]
        public async Task DeleteVisionRecordsAsync_WhenSuccessful_CompletesSuccessfully()
        {
            // Arrange
            var memberId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent("Vision record deleted successfully")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.DoesNotThrowAsync(async () => await _visionExaminationService.DeleteVisionRecordsAsync(memberId, orgId, subscription));
        }

        [Test]
        public void DeleteVisionRecordsAsync_WhenNotSuccessful_ThrowsException()
        {
            // Arrange
            var memberId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<Exception>(async () =>
                await _visionExaminationService.DeleteVisionRecordsAsync(memberId, orgId, subscription));

            Assert.That(exception.Message, Is.EqualTo("Error"));
        }

        [Test]
        public async Task GetVisionExaminationAsync_WhenEmptyResponse_ReturnsEmptyList()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var expectedExaminations = new List<VisionRx>();

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedExaminations)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _visionExaminationService.GetVisionExaminationAsync(patientId, orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(0));
        }

        [Test]
        public void GetVisionExaminationAsync_WhenExceptionThrown_PropagatesException()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var expectedException = new HttpRequestException("Test exception");

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ThrowsAsync(expectedException);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _visionExaminationService.GetVisionExaminationAsync(patientId, orgId, subscription));

            Assert.That(exception.Message, Is.EqualTo("Test exception"));
        }
    }
}



