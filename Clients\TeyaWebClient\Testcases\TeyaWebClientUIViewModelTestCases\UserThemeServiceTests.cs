using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Moq;
using Moq.Protected;
using NUnit.Framework;
using TeyaUIModels.Model;
using TeyaWebApp.Services;
using TeyaUIViewModels.ViewModel;
using System.Net.Http.Json;
using System.Text.Json;
using System.Linq;

namespace TeyaWebClientUIViewModelTestCases
{
    [TestFixture]
    public class UserThemeServiceTests
    {
        private Mock<HttpMessageHandler> _mockHttpMessageHandler;
        private HttpClient _httpClient;
        private Mock<IStringLocalizer<UserThemeService>> _mockLocalizer;
        private Mock<ILogger<UserThemeService>> _mockLogger;
        private Mock<ITokenService> _mockTokenService;
        private UserThemeService _userThemeService;
        private readonly string _baseUrl = "http://test-api.com";
        private readonly string _accessToken = "test-access-token";

        [SetUp]
        public void Setup()
        {
            // Setup environment variable
            Environment.SetEnvironmentVariable("MemberServiceURL", _baseUrl);

            // Setup mock HTTP handler
            _mockHttpMessageHandler = new Mock<HttpMessageHandler>();

            // Setup HttpClient with mock handler
            _httpClient = new HttpClient(_mockHttpMessageHandler.Object)
            {
                BaseAddress = new Uri(_baseUrl)
            };

            // Setup mock localizer
            _mockLocalizer = new Mock<IStringLocalizer<UserThemeService>>();
            _mockLocalizer.Setup(l => l["ErrorFetchingAllUserThemes"])
                .Returns(new LocalizedString("ErrorFetchingAllUserThemes", "Error fetching all user themes"));
            _mockLocalizer.Setup(l => l["ErrorFetchingUserThemeById"])
                .Returns(new LocalizedString("ErrorFetchingUserThemeById", "Error fetching user theme by ID"));
            _mockLocalizer.Setup(l => l["GetByIdFailed"])
                .Returns(new LocalizedString("GetByIdFailed", "Get by ID failed"));
            _mockLocalizer.Setup(l => l["ErrorAddingUserTheme"])
                .Returns(new LocalizedString("ErrorAddingUserTheme", "Error adding user theme"));
            _mockLocalizer.Setup(l => l["AddUserThemeFailed"])
                .Returns(new LocalizedString("AddUserThemeFailed", "Add user theme failed"));
            _mockLocalizer.Setup(l => l["ErrorUpdatingUserTheme"])
                .Returns(new LocalizedString("ErrorUpdatingUserTheme", "Error updating user theme"));
            _mockLocalizer.Setup(l => l["UserThemeUpdateFailed"])
                .Returns(new LocalizedString("UserThemeUpdateFailed", "User theme update failed"));
            _mockLocalizer.Setup(l => l["ErrorDeletingUserTheme"])
                .Returns(new LocalizedString("ErrorDeletingUserTheme", "Error deleting user theme"));
            _mockLocalizer.Setup(l => l["UserThemeDeletionFailed"])
                .Returns(new LocalizedString("UserThemeDeletionFailed", "User theme deletion failed"));
            _mockLocalizer.Setup(l => l["API Response: {ResponseData}"])
                .Returns(new LocalizedString("API Response: {ResponseData}", "API Response: {ResponseData}"));
            _mockLocalizer.Setup(l => l["UserThemeUpdatedSuccessfully"])
                .Returns(new LocalizedString("UserThemeUpdatedSuccessfully", "User theme updated successfully"));
            _mockLocalizer.Setup(l => l["UserThemeDeletedSuccessfully"])
                .Returns(new LocalizedString("UserThemeDeletedSuccessfully", "User theme deleted successfully"));

            // Setup mock logger
            _mockLogger = new Mock<ILogger<UserThemeService>>();

            // Setup mock token service
            _mockTokenService = new Mock<ITokenService>();
            _mockTokenService.Setup(t => t.AccessToken).Returns(_accessToken);
            _mockTokenService.Setup(t => t.GetValidatedAccessTokenAsync()).ReturnsAsync(_accessToken);

            // Create UserThemeService with mocked dependencies
            _userThemeService = new UserThemeService(_httpClient, _mockLocalizer.Object, _mockLogger.Object, _mockTokenService.Object);
        }

        [TearDown]
        public void TearDown()
        {
            _httpClient.Dispose();
            Environment.SetEnvironmentVariable("MemberServiceURL", null);
        }

        [Test]
        public async Task GetUserThemesAsync_WhenSuccessful_ReturnsUserThemes()
        {
            // Arrange
            var expectedThemes = new List<UserTheme>
            {
                new UserTheme
                {
                    UserId = Guid.NewGuid(),
                    ThemeName = "Dark Theme"
                },
                new UserTheme
                {
                    UserId = Guid.NewGuid(),
                    ThemeName = "Light Theme"
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(JsonSerializer.Serialize(expectedThemes, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                }))
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var orgId = Guid.NewGuid();
            bool subscription = false;
            var result = await _userThemeService.GetUserThemesAsync(orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            var resultList = result.ToList();
            Assert.That(resultList.Count, Is.EqualTo(expectedThemes.Count));
            Assert.That(resultList[0].UserId, Is.EqualTo(expectedThemes[0].UserId));
            Assert.That(resultList[0].ThemeName, Is.EqualTo(expectedThemes[0].ThemeName));
            Assert.That(resultList[1].UserId, Is.EqualTo(expectedThemes[1].UserId));
            Assert.That(resultList[1].ThemeName, Is.EqualTo(expectedThemes[1].ThemeName));
        }

        [Test]
        public void GetUserThemesAsync_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.InternalServerError,
                Content = new StringContent("Internal server error")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var orgId = Guid.NewGuid();
            bool subscription = false;
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _userThemeService.GetUserThemesAsync(orgId, subscription));

            Assert.That(exception, Is.Not.Null);

            // Verify error was logged
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.IsAny<Exception>(),
                    It.Is<Func<It.IsAnyType, Exception, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }

        [Test]
        public async Task GetUserThemeByIdAsync_WhenSuccessful_ReturnsUserTheme()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var expectedTheme = new UserTheme
            {
                UserId = userId,
                ThemeName = "Custom Theme"
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(JsonSerializer.Serialize(expectedTheme, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                }))
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var orgId = Guid.NewGuid();
            bool subscription = false;
            var result = await _userThemeService.GetUserThemeByIdAsync(userId, orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.UserId, Is.EqualTo(expectedTheme.UserId));
            Assert.That(result.ThemeName, Is.EqualTo(expectedTheme.ThemeName));
        }

        [Test]
        public void GetUserThemeByIdAsync_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var userId = Guid.NewGuid();

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.NotFound,
                Content = new StringContent("Not found")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var orgId = Guid.NewGuid();
            bool subscription = false;
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _userThemeService.GetUserThemeByIdAsync(userId, orgId, subscription));

            Assert.That(exception.Message, Is.EqualTo("Get by ID failed"));

            // Verify error was logged
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.IsAny<Exception>(),
                    It.Is<Func<It.IsAnyType, Exception, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }

        [Test]
        public async Task AddUserThemeAsync_WhenSuccessful_CompletesSuccessfully()
        {
            // Arrange
            var userTheme = new UserTheme
            {
                UserId = Guid.NewGuid(),
                ThemeName = "New Theme"
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.Created,
                Content = new StringContent("Theme created successfully")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.DoesNotThrowAsync(async () => await _userThemeService.AddUserThemeAsync(userTheme));

            // Verify info was logged
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Information,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.IsAny<Exception>(),
                    It.Is<Func<It.IsAnyType, Exception, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }

        [Test]
        public void AddUserThemeAsync_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var userTheme = new UserTheme
            {
                UserId = Guid.NewGuid(),
                ThemeName = "Failed Theme"
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _userThemeService.AddUserThemeAsync(userTheme));

            Assert.That(exception.Message, Is.EqualTo("Add user theme failed"));

            // Verify error was logged
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.IsAny<Exception>(),
                    It.Is<Func<It.IsAnyType, Exception, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }

        [Test]
        public async Task UpdateUserThemeAsync_WhenSuccessful_CompletesSuccessfully()
        {
            // Arrange
            var userTheme = new UserTheme
            {
                UserId = Guid.NewGuid(),
                ThemeName = "Updated Theme"
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent("Theme updated successfully")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.DoesNotThrowAsync(async () => await _userThemeService.UpdateUserThemeAsync(userTheme));

            // Verify info was logged
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Information,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.IsAny<Exception>(),
                    It.Is<Func<It.IsAnyType, Exception, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }

        [Test]
        public void UpdateUserThemeAsync_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var userTheme = new UserTheme
            {
                UserId = Guid.NewGuid(),
                ThemeName = "Failed Update Theme"
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _userThemeService.UpdateUserThemeAsync(userTheme));

            Assert.That(exception.Message, Is.EqualTo("User theme update failed"));

            // Verify error was logged
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.IsAny<Exception>(),
                    It.Is<Func<It.IsAnyType, Exception, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }

        [Test]
        public async Task DeleteUserThemeAsync_WhenSuccessful_CompletesSuccessfully()
        {
            // Arrange
            var userId = Guid.NewGuid();

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent("Theme deleted successfully")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var orgId = Guid.NewGuid();
            bool subscription = false;
            Assert.DoesNotThrowAsync(async () => await _userThemeService.DeleteUserThemeByIdAsync(userId, orgId, subscription));

            // Verify info was logged
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Information,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.IsAny<Exception>(),
                    It.Is<Func<It.IsAnyType, Exception, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }

        [Test]
        public void DeleteUserThemeAsync_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var userId = Guid.NewGuid();

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.NotFound,
                Content = new StringContent("Not found")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var orgId = Guid.NewGuid();
            bool subscription = false;
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _userThemeService.DeleteUserThemeByIdAsync(userId, orgId, subscription));

            Assert.That(exception.Message, Is.EqualTo("User theme deletion failed"));

            // Verify error was logged
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.IsAny<Exception>(),
                    It.Is<Func<It.IsAnyType, Exception, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }

        [Test]
        public async Task GetUserThemesAsync_WhenEmptyResponse_ReturnsEmptyCollection()
        {
            // Arrange
            var expectedThemes = new List<UserTheme>();

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(JsonSerializer.Serialize(expectedThemes, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                }))
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var orgId = Guid.NewGuid();
            bool subscription = false;
            var result = await _userThemeService.GetUserThemesAsync(orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count(), Is.EqualTo(0));
        }

        [Test]
        public void GetUserThemesAsync_WhenExceptionThrown_PropagatesException()
        {
            // Arrange
            var expectedException = new HttpRequestException("Test exception");

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ThrowsAsync(expectedException);

            // Act & Assert
            var orgId = Guid.NewGuid();
            bool subscription = false;
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _userThemeService.GetUserThemesAsync(orgId, subscription));

            Assert.That(exception, Is.EqualTo(expectedException));

            // Verify error was logged
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.Is<Exception>(ex => ex == expectedException),
                    It.Is<Func<It.IsAnyType, Exception, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }

        [Test]
        public async Task GetUserThemeByIdAsync_WhenComplexThemeData_ReturnsCorrectData()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var expectedTheme = new UserTheme
            {
                UserId = userId,
                ThemeName = "Complex theme with special characters: !@#$%^&*()"
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(JsonSerializer.Serialize(expectedTheme, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                }))
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var orgId = Guid.NewGuid();
            bool subscription = false;
            var result = await _userThemeService.GetUserThemeByIdAsync(userId, orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.UserId, Is.EqualTo(expectedTheme.UserId));
            Assert.That(result.ThemeName, Is.EqualTo(expectedTheme.ThemeName));
        }
    }
}



