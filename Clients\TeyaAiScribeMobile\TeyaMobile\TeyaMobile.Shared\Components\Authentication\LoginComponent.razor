﻿@using Microsoft.AspNetCore.Components
@using MudBlazor
@using Syncfusion.Blazor.Buttons
@using TeyaHealthMobileViewModel.ViewModel
@inject IAuthenticationService AuthService
@inject NavigationManager Navigation
@inject ISnackbar Snackbar

<div class="login-container">
    @if (IsAuthenticated)
    {
        <!-- User Profile Card with MudBlazor -->
        <MudCard Class="user-profile-card" Elevation="8">
            <MudCardContent Class="pa-6">
                <MudStack Spacing="4" AlignItems="AlignItems.Center">

                    <!-- User Avatar Section -->
                    <MudStack Row="true" AlignItems="AlignItems.Center" Spacing="3" Class="profile-header">
                        <MudAvatar Size="Size.Large"
                                   Class="user-avatar"
                                   Style="width: 80px; height: 80px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); position: relative;">
                            <MudText Typo="Typo.h4" Style="color: white; font-weight: 700;">
                                @(string.IsNullOrEmpty(UserName) ? "U" : UserName.Substring(0, 1).ToUpper())
                            </MudText>
                            <div class="online-indicator"></div>
                        </MudAvatar>

                        <MudStack Spacing="1" AlignItems="AlignItems.Start">
                            <MudText Typo="Typo.h5" Class="user-name" Style="font-weight: 700; color: #1a1a1a;">
                                @UserName
                            </MudText>
                            <MudText Typo="Typo.body2" Class="user-email" Color="Color.Secondary">
                                @UserEmail
                            </MudText>
                            <MudChip T="string"
                                     Size="Size.Small"
                                     Color="Color.Success"
                                     Variant="Variant.Filled"
                                     Icon="@Icons.Material.Filled.CheckCircle">
                                Active
                            </MudChip>
                        </MudStack>
                    </MudStack>

                    <!-- Logout Action -->
                    <MudDivider Class="my-4" />

                    <MudButton Variant="Variant.Outlined"
                               Color="Color.Error"
                               StartIcon="@Icons.Material.Filled.Logout"
                               Size="Size.Large"
                               FullWidth="true"
                               Class="logout-button"
                               OnClick="HandleLogout"
                               Disabled="@IsLoading">
                        @if (IsLoading)
                        {
                            <MudProgressCircular Size="Size.Small" Indeterminate="true" Class="mr-2" />
                            <span>Signing out...</span>
                        }
                        else
                        {
                            <span>Sign Out</span>
                        }
                    </MudButton>
                </MudStack>
            </MudCardContent>
        </MudCard>
    }
    else
    {
        <!-- Login Card with MudBlazor -->
        <MudCard Class="login-card" Elevation="8">
            <MudCardContent Class="pa-6">
                <MudStack Spacing="4" AlignItems="AlignItems.Center">

                    <!-- Brand Header -->
                    <MudStack Spacing="3" AlignItems="AlignItems.Center" Class="brand-section">
                        <MudStack Row="true" AlignItems="AlignItems.Center" Spacing="2" Class="teya-logo">
                            <MudPaper Class="logo-icon" Elevation="0">
                                <MudIcon Icon="@Icons.Material.Filled.LocalHospital"
                                         Size="Size.Large"
                                         Style="color: white;" />
                            </MudPaper>
                            <MudText Typo="Typo.h4" Class="logo-text" Style="font-weight: 700;">
                                TeyaHealth
                            </MudText>
                        </MudStack>

                        <MudStack Spacing="1" AlignItems="AlignItems.Center">
                            <MudText Typo="Typo.h4" Style="color: white; font-weight: 700; text-align: center;">
                                Welcome back
                            </MudText>
                            <MudText Typo="Typo.body1" Style="color: white; text-align: center;">
                                Sign in to access your medical dashboard
                            </MudText>
                        </MudStack>
                    </MudStack>

                    <!-- Error Alert -->
                    @if (!string.IsNullOrEmpty(ErrorMessage))
                    {
                        <MudAlert Severity="Severity.Error"
                                  Variant="Variant.Filled"
                                  ShowCloseIcon="true"
                                  CloseIconClicked="ClearError"
                                  Class="error-alert">
                            <MudStack Spacing="1">
                                <MudText Typo="Typo.subtitle2" Style="font-weight: 600;">
                                    Authentication Error
                                </MudText>
                                <MudText Typo="Typo.body2">
                                    @ErrorMessage
                                </MudText>
                            </MudStack>
                        </MudAlert>
                    }

                    <!-- Login Button with Syncfusion -->
                    <MudStack Spacing="3" Style="width: 100%;">
                        <SfButton IsPrimary="true"
                                  CssClass="login-primary-button"
                                  Disabled="@IsLoading"
                                  OnClick="HandleLogin">
                            @if (IsLoading)
                            {
                                <MudStack Row="true" AlignItems="AlignItems.Center" Spacing="2" Justify="Justify.Center">
                                    <MudProgressCircular Size="Size.Small" Indeterminate="true" Style="color: white;" />
                                    <span>Authenticating...</span>
                                </MudStack>
                            }
                            else
                            {
                                <MudStack Row="true" AlignItems="AlignItems.Center" Spacing="2" Justify="Justify.Center">
                                    <MudIcon Icon="@Icons.Material.Filled.Login" Size="Size.Medium" Style="color: white;" />
                                    <span>Sign in with Teya Health</span>
                                </MudStack>
                            }
                        </SfButton>

                        <!-- Security Info -->
                        <MudDivider>
                            <MudText Typo="Typo.caption" Style="color: white;">
                                Secure authentication powered by Microsoft
                            </MudText>
                        </MudDivider>

                        <!-- Security Features -->
                        <MudGrid Spacing="2" Justify="Justify.Center">
                            <MudItem xs="12" sm="4">
                                <MudPaper Class="feature-item" Elevation="1">
                                    <MudStack Row="true" AlignItems="AlignItems.Center" Spacing="2">
                                        <MudIcon Icon="@Icons.Material.Filled.Security" Color="Color.Primary" Size="Size.Small" />
                                        <MudText Typo="Typo.caption" Style="color: white; font-weight: 500;">
                                            Enterprise Security
                                        </MudText>
                                    </MudStack>
                                </MudPaper>
                            </MudItem>
                            <MudItem xs="12" sm="4">
                                <MudPaper Class="feature-item" Elevation="1">
                                    <MudStack Row="true" AlignItems="AlignItems.Center" Spacing="2">
                                        <MudIcon Icon="@Icons.Material.Filled.Speed" Color="Color.Success" Size="Size.Small" />
                                        <MudText Typo="Typo.caption" Style="color: white; font-weight: 500;">
                                            Single Sign-On
                                        </MudText>
                                    </MudStack>
                                </MudPaper>
                            </MudItem>
                            <MudItem xs="12" sm="4">
                                <MudPaper Class="feature-item" Elevation="1">
                                    <MudStack Row="true" AlignItems="AlignItems.Center" Spacing="2">
                                        <MudIcon Icon="@Icons.Material.Filled.VerifiedUser" Color="Color.Info" Size="Size.Small" />
                                        <MudText Typo="Typo.caption" Style="color: white; font-weight: 500;">
                                            HIPAA Compliant
                                        </MudText>
                                    </MudStack>
                                </MudPaper>
                            </MudItem>
                        </MudGrid>
                    </MudStack>

                    <!-- Footer Links -->
                    <MudDivider Class="my-2" />

                    <MudStack Spacing="2" AlignItems="AlignItems.Center">
                        <MudText Typo="Typo.caption" Color="Color.Secondary">
                            Need help?
                            <MudLink Href="/support" Color="Color.Primary" Underline="Underline.Hover">
                                Contact Support
                            </MudLink>
                        </MudText>
                        <MudText Typo="Typo.caption" Color="Color.Secondary" Style="text-align: center;">
                            By signing in, you agree to our
                            <MudLink Href="/terms" Color="Color.Primary" Underline="Underline.Hover">Terms of Service</MudLink>
                            and
                            <MudLink Href="/privacy" Color="Color.Primary" Underline="Underline.Hover">Privacy Policy</MudLink>
                        </MudText>
                    </MudStack>
                </MudStack>
            </MudCardContent>
        </MudCard>
    }
</div>

<style>
    .login-container {
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        padding: 20px;
        position: relative;
        overflow: hidden;
    }

        .login-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
            pointer-events: none;
        }

    .login-card, .user-profile-card {
        background: rgba(255, 255, 255, 0.95) !important;
        backdrop-filter: blur(20px);
        border-radius: 24px !important;
        max-width: 440px;
        width: 100%;
        position: relative;
        z-index: 1;
        animation: slideUp 0.6s ease-out;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1), 0 0 0 1px rgba(255, 255, 255, 0.2) !important;
    }

    @@keyframes slideUp {
        from

    {
        opacity: 0;
        transform: translateY(30px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }

    }

    .logo-icon {
        width: 48px !important;
        height: 48px !important;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
        border-radius: 12px !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
    }

    .logo-text {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .login-primary-button {
        width: 100% !important;
        height: 56px !important;
        border-radius: 12px !important;
        font-size: 16px !important;
        font-weight: 600 !important;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
        border: none !important;
        color: white !important;
        transition: all 0.3s ease !important;
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4) !important;
    }

        .login-primary-button:hover:not(:disabled) {
            transform: translateY(-2px) !important;
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.5) !important;
        }

        .login-primary-button:disabled {
            opacity: 0.7 !important;
            cursor: not-allowed !important;
            transform: none !important;
        }

    .feature-item {
        padding: 12px !important;
        background: rgba(102, 126, 234, 0.05) !important;
        border-radius: 8px !important;
        border: 1px solid rgba(102, 126, 234, 0.1) !important;
        transition: all 0.2s ease !important;
    }

        .feature-item:hover {
            background: rgba(102, 126, 234, 0.1) !important;
            border-color: rgba(102, 126, 234, 0.2) !important;
        }

    .user-avatar {
        position: relative !important;
    }

    .online-indicator {
        position: absolute;
        bottom: 4px;
        right: 4px;
        width: 16px;
        height: 16px;
        background: #10b981;
        border: 3px solid white;
        border-radius: 50%;
        animation: pulse 2s infinite;
    }

    @@keyframes pulse {
        0%, 100%

    {
        opacity: 1;
    }

    50% {
        opacity: 0.7;
    }

    }

    .logout-button {
        border-radius: 12px !important;
        height: 48px !important;
        font-weight: 600 !important;
        transition: all 0.3s ease !important;
    }

        .logout-button:hover:not(:disabled) {
            transform: translateY(-1px) !important;
            box-shadow: 0 4px 12px rgba(244, 67, 54, 0.3) !important;
        }

    .error-alert {
        width: 100% !important;
        border-radius: 12px !important;
        animation: shake 0.5s ease-in-out !important;
    }

    @@keyframes shake {
        0%, 100%

    {
        transform: translateX(0);
    }

    25% {
        transform: translateX(-5px);
    }

    75% {
        transform: translateX(5px);
    }

    }

    /* Mobile Responsive */
    @@media (max-width: 480px) {
        .login-container

    {
        padding: 16px;
    }

    .login-card, .user-profile-card {
        border-radius: 16px !important;
    }

    .logo-text {
        font-size: 1.5rem !important;
    }

    .teya-logo {
        flex-direction: column !important;
        gap: 8px !important;
    }

    .profile-header {
        flex-direction: column !important;
        text-align: center !important;
    }

    }

    /* Dark mode support */
    @@media (prefers-color-scheme: dark) {
        .login-card, .user-profile-card

    {
        background: rgba(17, 24, 39, 0.95) !important;
        border: 1px solid rgba(75, 85, 99, 0.3) !important;
    }

    .feature-item {
        background: rgba(102, 126, 234, 0.1) !important;
        border-color: rgba(102, 126, 234, 0.2) !important;
    }

    }

    /* Accessibility improvements */
    @@media (prefers-reduced-motion: reduce) {
        *

    {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    }

    /* Tablet/iPad Responsive Enhancements */
    @@media (min-width: 768px) and (max-width: 1200px) {
        .mud-stack-row

    {
        flex-direction: row !important;
        gap: 32px !important;
    }

    .mud-button {
        font-size: 1.2rem !important;
        height: 56px !important;
        min-width: 160px !important;
    }

    .user-profile-card, .login-card {
        max-width: 600px;
        padding: 56px;
        border-radius: 32px;
    }

    .logo-icon {
        width: 56px;
        height: 56px;
    }

    .logo-text {
        font-size: 2.2rem !important;
    }

    }
    @@media (min-width: 1200px) {
        .mud-stack-row

    {
        flex-direction: row !important;
        gap: 48px !important;
    }

    .mud-button {
        font-size: 1.4rem !important;
        height: 64px !important;
        min-width: 200px !important;
    }

    .user-profile-card, .login-card {
        max-width: 800px;
        padding: 72px;
        border-radius: 40px;
    }

    .logo-icon {
        width: 72px;
        height: 72px;
    }

    .logo-text {
        font-size: 2.6rem !important;
    }

    }
</style>
