using NUnit.Framework;
using Moq;
using Moq.Protected;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using TeyaUIViewModels.ViewModel;
using TeyaWebApp.ViewModel;
using TeyaUIModels.Model;
using System;
using System.Net.Http;
using System.Threading.Tasks;
using System.Threading;
using System.Net;
using System.Text.Json;
using System.Text;

namespace TeyaWebClientUIViewModelTestCases
{
    [TestFixture]
    public class GraphApiServiceTests
    {
        private Mock<HttpMessageHandler> _mockHttpMessageHandler;
        private Mock<ITokenService> _mockTokenService;
        private Mock<ILogger<GraphApiService>> _mockLogger;
        private Mock<IStringLocalizer<GraphApiService>> _mockLocalizer;
        private Mock<IGraphAdminService> _mockAdminService;
        private Mock<IOptions<JsonSerializerOptions>> _mockJsonOptions;
        private ActiveUser _activeUser;
        private HttpClient _httpClient;
        private GraphApiService _graphApiService;
        private const string TestBaseUrl = "https://graph.microsoft.com/v1.0/";
        private const string TestAccessToken = "test-access-token";
        private const string TestAdminToken = "test-admin-token";

        [SetUp]
        public void SetUp()
        {
            _mockHttpMessageHandler = new Mock<HttpMessageHandler>();
            _mockTokenService = new Mock<ITokenService>();
            _mockLogger = new Mock<ILogger<GraphApiService>>();
            _mockLocalizer = new Mock<IStringLocalizer<GraphApiService>>();
            _mockAdminService = new Mock<IGraphAdminService>();
            _mockJsonOptions = new Mock<IOptions<JsonSerializerOptions>>();
            _activeUser = new ActiveUser();
            _httpClient = new HttpClient(_mockHttpMessageHandler.Object);

            // Set up environment variables
            Environment.SetEnvironmentVariable("GRAPH-API-BASE-URL", "https://graph.microsoft.com");
            Environment.SetEnvironmentVariable("EXTENSION-PREFIX", "extension_8a2d87f30a864e7e8b70f49083a6ff68");

            // Set up mock responses
            _mockTokenService.Setup(t => t.AccessToken).Returns(TestAccessToken);
            _mockTokenService.Setup(t => t.AccessToken2).Returns(TestAccessToken);
            _mockTokenService.Setup(t => t.GetValidatedAccessToken2Async()).ReturnsAsync(TestAccessToken);
            _mockAdminService.Setup(a => a.GetAdminAccessTokenAsync()).ReturnsAsync(TestAdminToken);
            _mockJsonOptions.Setup(o => o.Value).Returns(new JsonSerializerOptions());

            // Set up localizer mock responses
            _mockLocalizer.Setup(l => l["AccessTokenMissing"]).Returns(new LocalizedString("AccessTokenMissing", "Access token is missing"));
            _mockLocalizer.Setup(l => l["FailedToGetUserDetails", It.IsAny<object>()])
                .Returns((string key, object[] args) => new LocalizedString(key, $"Failed to get user details: {args[0]}"));
            _mockLocalizer.Setup(l => l["ErrorGettingUserDetails", It.IsAny<object>()])
                .Returns((string key, object[] args) => new LocalizedString(key, $"Error getting user details: {args[0]}"));
            _mockLocalizer.Setup(l => l["FailedToGetFullUserDetails", It.IsAny<object>()])
                .Returns((string key, object[] args) => new LocalizedString(key, $"Failed to get full user details: {args[0]}"));
            _mockLocalizer.Setup(l => l["ErrorGettingFullUserDetails", It.IsAny<object>()])
                .Returns((string key, object[] args) => new LocalizedString(key, $"Error getting full user details: {args[0]}"));
            _mockLocalizer.Setup(l => l["UserIdNotAvailable"]).Returns(new LocalizedString("UserIdNotAvailable", "User ID not available"));
            _mockLocalizer.Setup(l => l["UserIdNotProvided"]).Returns(new LocalizedString("UserIdNotProvided", "User ID not provided"));
            _mockLocalizer.Setup(l => l["ErrorCreatingUser"]).Returns(new LocalizedString("ErrorCreatingUser", "Error creating user"));
            _mockLocalizer.Setup(l => l["ErrorUpdatingUser", It.IsAny<object>()])
                .Returns((string key, object[] args) => new LocalizedString(key, $"Error updating user: {args[0]}"));

            _graphApiService = new GraphApiService(_httpClient, _mockTokenService.Object, _mockLogger.Object,
                _mockLocalizer.Object, _activeUser, _mockAdminService.Object, _mockJsonOptions.Object);
        }

        [TearDown]
        public void TearDown()
        {
            Environment.SetEnvironmentVariable("GRAPH-API-BASE-URL", null);
            Environment.SetEnvironmentVariable("EXTENSION-PREFIX", null);
            _httpClient?.Dispose();
        }

        [Test]
        public async Task GetUserDetailsAsync_WhenSuccessful_ReturnsTrue()
        {
            // Arrange
            var userResponse = new
            {
                id = "test-user-id",
                displayName = "Test User",
                mail = "<EMAIL>"
            };

            var userDetailsResponse = new
            {
                id = "test-user-id",
                displayName = "Test User",
                givenName = "Test",
                surname = "User",
                mail = "<EMAIL>"
            };

            var meResponseContent = JsonSerializer.Serialize(userResponse);
            var userDetailsResponseContent = JsonSerializer.Serialize(userDetailsResponse);

            var meResponse = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(meResponseContent, Encoding.UTF8, "application/json")
            };

            var userDetailsResponse2 = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(userDetailsResponseContent, Encoding.UTF8, "application/json")
            };

            _mockHttpMessageHandler
                .Protected()
                .SetupSequence<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(meResponse)
                .ReturnsAsync(userDetailsResponse2);

            // Act
            var result = await _graphApiService.GetUserDetailsAsync();

            // Assert
            Assert.That(result, Is.True);
        }

        [Test]
        public void GetUserDetailsAsync_WhenAccessTokenIsNull_ThrowsException()
        {
            // Arrange
            _mockTokenService.Setup(t => t.AccessToken).Returns((string)null);
            _mockTokenService.Setup(t => t.AccessToken2).Returns((string)null);
            _mockTokenService.Setup(t => t.GetValidatedAccessToken2Async()).ReturnsAsync((string)null);

            // Act & Assert
            var exception = Assert.ThrowsAsync<Exception>(async () =>
                await _graphApiService.GetUserDetailsAsync());

            Assert.That(exception.Message, Does.Contain("Access token is missing"));
        }

        [Test]
        public void GetUserDetailsAsync_WhenHttpRequestFails_ThrowsException()
        {
            // Arrange
            // Ensure we have a valid access token so the test gets to the HTTP request
            _mockTokenService.Setup(t => t.GetValidatedAccessToken2Async()).ReturnsAsync(TestAccessToken);

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.Unauthorized,
                Content = new StringContent("Unauthorized")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<Exception>(async () =>
                await _graphApiService.GetUserDetailsAsync());

            Assert.That(exception.Message, Does.Contain("Failed to get user details"));
        }

        [Test]
        public async Task GetFullUserAsync_WhenSuccessful_ReturnsUserDetails()
        {
            // Arrange
            var userId = "test-user-id";
            var expectedUserDetails = new
            {
                id = userId,
                displayName = "Test User",
                givenName = "Test",
                surname = "User",
                mail = "<EMAIL>",
                userType = "Member"
            };

            var responseContent = JsonSerializer.Serialize(expectedUserDetails);
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(responseContent, Encoding.UTF8, "application/json")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.Is<HttpRequestMessage>(req => req.RequestUri.ToString().Contains($"users/{userId}")),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _graphApiService.GetFullUserAsync(userId);

            // Assert
            Assert.That(result, Is.EqualTo(responseContent));
        }

        [Test]
        public void GetFullUserAsync_WhenAccessTokenIsNull_ThrowsException()
        {
            // Arrange
            var userId = "test-user-id";
            _mockAdminService.Setup(a => a.GetAdminAccessTokenAsync()).ReturnsAsync((string)null);

            // Act & Assert
            var exception = Assert.ThrowsAsync<Exception>(async () =>
                await _graphApiService.GetFullUserAsync(userId));

            Assert.That(exception.Message, Does.Contain("Access token is missing"));
        }

        [Test]
        public void GetFullUserAsync_WhenHttpRequestFails_ThrowsException()
        {
            // Arrange
            var userId = "test-user-id";
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.NotFound,
                Content = new StringContent("User not found")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.Is<HttpRequestMessage>(req => req.RequestUri.ToString().Contains($"users/{userId}")),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<Exception>(async () =>
                await _graphApiService.GetFullUserAsync(userId));

            Assert.That(exception.Message, Does.Contain("Error getting full user details"));
        }

        [Test]
        public async Task GetFullUserDetailsAsync_WhenSuccessful_ReturnsTrue()
        {
            // Arrange
            var userId = "test-user-id";
            var expectedUserDetails = new
            {
                id = userId,
                displayName = "Test User",
                givenName = "Test",
                surname = "User",
                mail = "<EMAIL>",
                userType = "Member"
            };

            var responseContent = JsonSerializer.Serialize(expectedUserDetails);
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(responseContent, Encoding.UTF8, "application/json")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.Is<HttpRequestMessage>(req => req.RequestUri.ToString().Contains($"users/{userId}")),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _graphApiService.GetFullUserDetailsAsync(userId);

            // Assert
            Assert.That(result, Is.True);
        }


    }
}



