﻿@using MudBlazor
@inject NavigationManager Navigation
@inject IFormFactor FormFactor
@inject ActiveUser user

<div class="tab-bar-container">
    <MudPaper style="color:teal" Class="tab-bar-paper" Elevation="8">
        <div class="tab-bar-content">
            <MudButton Class="@GetTabClass("/")"
                       OnClick="@(() => NavigateToTab("/"))"
                       DisableRipple="true">
                <div class="tab-item">
                    <div class="tab-icon-container">
                        <MudIcon Icon="@Icons.Material.Filled.Home"
                                 Class="@GetIconClass("/")" />
                        @if (IsActiveTab("/"))
                        {
                            <div class="active-indicator"></div>
                        }
                    </div>
                    <MudText Class="@GetLabelClass("/")" Typo="Typo.caption">
                        Home
                    </MudText>
                </div>
            </MudButton>

            <MudButton Class="@GetTabClass("/appointments")"
                       OnClick="@(() => NavigateToTab("/appointments"))"
                       DisableRipple="true">
                <div class="tab-item">
                    <div class="tab-icon-container">
                        <MudIcon Icon="@Icons.Material.Filled.CalendarToday"
                                 Class="@GetIconClass("/appointments")" />
                        @if (IsActiveTab("/appointments"))
                        {
                            <div class="active-indicator"></div>
                        }
                    </div>
                    <MudText Class="@GetLabelClass("/appointments")" Typo="Typo.caption">
                        Appointments
                    </MudText>
                </div>
            </MudButton>

            <MudButton Class="@GetTabClass("/soapnotes")"
                       OnClick="@(() => NavigateToTab(@GetSoapNotesUrl()))"
                       DisableRipple="true">
                <div class="tab-item">
                    <div class="tab-icon-container">
                        <MudIcon Icon="@Icons.Material.Filled.Description"
                                 Class="@GetIconClass("/soapnotes")" />
                        @if (IsActiveTab("/soapnotes"))
                        {
                            <div class="active-indicator"></div>
                        }
                    </div>
                    <MudText Class="@GetLabelClass("/soapnotes")" Typo="Typo.caption">
                        SOAP
                    </MudText>
                </div>
            </MudButton>

            @* <MudButton Class="@GetTabClass("more")"
                       OnClick="@(() => _showMoreMenu = !_showMoreMenu)"
                       DisableRipple="true">
                <div class="tab-item">
                    <div class="tab-icon-container">
                        <MudIcon Icon="@Icons.Material.Filled.MoreHoriz"
                                 Class="tab-icon" />
                        @if (_showMoreMenu)
                        {
                            <div class="active-indicator"></div>
                        }
                    </div>
                    <MudText Class="tab-label" Typo="Typo.caption">
                        More
                    </MudText>
                </div>
            </MudButton> *@
        </div>
    </MudPaper>

    <!-- More Menu Overlay // in future maybe useful -->
    @* @if (_showMoreMenu)
    {
        <div class="more-menu-backdrop" @onclick="@(() => _showMoreMenu = false)">
            <div class="more-menu-container" @onclick:stopPropagation="true">
                <MudPaper Class="more-menu-paper" Elevation="12">
                    <div class="more-menu-header">
                        <MudText Typo="Typo.h6" Class="more-menu-title">More Options</MudText>
                        <MudIconButton Icon="@Icons.Material.Filled.Close"
                                       Size="Size.Small"
                                       Class="close-button"
                                       OnClick="@(() => _showMoreMenu = false)" />
                    </div>

                    <div class="more-menu-items">
                        <MudButton Class="more-menu-item"
                                   StartIcon="@Icons.Material.Filled.People"
                                   OnClick="@(() => { NavigateToTab("/users"); _showMoreMenu = false; })"
                                   FullWidth="true">
                            <div class="more-item-content">
                                <MudText>Users</MudText>
                                <MudIcon Icon="@Icons.Material.Filled.ChevronRight" Size="Size.Small" />
                            </div>
                        </MudButton>

                        <MudButton Class="more-menu-item"
                                   StartIcon="@Icons.Material.Filled.Security"
                                   OnClick="@(() => { NavigateToTab("/security"); _showMoreMenu = false; })"
                                   FullWidth="true">
                            <div class="more-item-content">
                                <MudText>Security</MudText>
                                <MudIcon Icon="@Icons.Material.Filled.ChevronRight" Size="Size.Small" />
                            </div>
                        </MudButton>

                        <MudButton Class="more-menu-item"
                                   StartIcon="@Icons.Material.Filled.Shield"
                                   OnClick="@(() => { NavigateToTab("/secure"); _showMoreMenu = false; })"
                                   FullWidth="true">
                            <div class="more-item-content">
                                <MudText>Secure Area</MudText>
                                <MudIcon Icon="@Icons.Material.Filled.ChevronRight" Size="Size.Small" />
                            </div>
                        </MudButton>

                        <MudButton Class="more-menu-item"
                                   StartIcon="@Icons.Material.Filled.Info"
                                   OnClick="@(() => { NavigateToTab("/about"); _showMoreMenu = false; })"
                                   FullWidth="true">
                            <div class="more-item-content">
                                <MudText>About</MudText>
                                <MudIcon Icon="@Icons.Material.Filled.ChevronRight" Size="Size.Small" />
                            </div>
                        </MudButton>
                    </div>
                </MudPaper>
            </div>
        </div>
    } *@
</div>
<style>
    /* Tab Bar Container */
    .tab-bar-container {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        z-index: 1300;
        height: 80px;
        background: transparent;
    }

    .tab-bar-paper {
        height: 100%;
        border-radius: 0 !important;
        background: rgba(255, 255, 255, 0.95) !important;
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
        border-top: 1px solid rgba(0, 0, 0, 0.08);
        box-shadow: 0 -2px 16px rgba(0, 0, 0, 0.1) !important;
    }

    .tab-bar-content {
        display: flex;
        height: 100%;
        align-items: center;
        justify-content: space-around;
        padding: 8px 4px 12px 4px;
    }

    /* Tab Items */
    .tab-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 4px;
        padding: 4px 8px;
        min-width: 60px;
        transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .tab-icon-container {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 32px;
        height: 32px;
        border-radius: 16px;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .tab-icon {
        font-size: 20px !important;
        color: #6B7280;
        transition: all 0.2s ease;
    }

        .tab-icon.active {
            color: #3B82F6 !important;
            transform: scale(1.1);
        }

    .tab-label {
        font-size: 11px !important;
        font-weight: 500 !important;
        color: #6B7280;
        transition: all 0.2s ease;
        line-height: 1.2 !important;
        text-align: center;
    }

        .tab-label.active {
            color: #3B82F6 !important;
            font-weight: 600 !important;
        }

    /* Active Indicator */
    .active-indicator {
        position: absolute;
        top: -2px;
        left: 50%;
        transform: translateX(-50%);
        width: 4px;
        height: 4px;
        background: #3B82F6;
        border-radius: 2px;
        animation: bounce-in 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    }

    /* Tab Button Styling */
    .tab-button {
        background: transparent !important;
        border-radius: 16px !important;
        min-width: unset !important;
        padding: 8px 12px !important;
        transition: all 0.2s ease !important;
        box-shadow: none !important;
    }

        .tab-button:hover {
            background: rgba(59, 130, 246, 0.08) !important;
            transform: translateY(-1px);
        }

        .tab-button.active {
            background: rgba(59, 130, 246, 0.12) !important;
        }

            .tab-button.active .tab-icon-container {
                background: rgba(59, 130, 246, 0.15);
            }

    /* More Menu Styles */
    .more-menu-backdrop {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 80px;
        background: rgba(0, 0, 0, 0.4);
        z-index: 1400;
        display: flex;
        align-items: flex-end;
        justify-content: center;
        animation: fade-in 0.2s ease;
    }

    .more-menu-container {
        width: 100%;
        max-width: 400px;
        padding: 16px;
        animation: slide-up 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .more-menu-paper {
        border-radius: 20px 20px 0 0 !important;
        overflow: hidden;
        background: rgba(255, 255, 255, 0.98) !important;
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
    }

    .more-menu-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 20px 24px 16px 24px;
        border-bottom: 1px solid rgba(0, 0, 0, 0.08);
        background: linear-gradient(135deg, #3B82F6 0%, #1D4ED8 100%);
        color: white;
    }

    .more-menu-title {
        color: white !important;
        font-weight: 600 !important;
    }

    .close-button {
        color: white !important;
    }

    .more-menu-items {
        padding: 16px;
        display: flex;
        flex-direction: column;
        gap: 4px;
    }

    .more-menu-item {
        justify-content: flex-start !important;
        text-transform: none !important;
        padding: 16px 20px !important;
        border-radius: 12px !important;
        background: transparent !important;
        transition: all 0.2s ease !important;
        border: none !important;
    }

        .more-menu-item:hover {
            background: rgba(59, 130, 246, 0.08) !important;
            transform: translateX(4px);
        }

    .more-item-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
    }

    /* Animations */
    @@keyframes bounce-in {
        0% {
            transform: translateX(-50%) scale(0);
            opacity: 0;
        }

        50% {
            transform: translateX(-50%) scale(1.2);
        }

        100% {
            transform: translateX(-50%) scale(1);
            opacity: 1;
        }
    }

    @@keyframes fade-in {
        from {
            opacity: 0;
        }

        to {
            opacity: 1;
        }
    }

    @@keyframes slide-up {
        from {
            transform: translateY(100%);
            opacity: 0;
        }

        to {
            transform: translateY(0);
            opacity: 1;
        }
    }

    /* Responsive Design */
    @@media (max-width: 600px) {
        .tab-bar-container {
            height: 70px;
        }

        .tab-item {
            min-width: 50px;
            gap: 2px;
        }

        .tab-icon-container {
            width: 28px;
            height: 28px;
        }

        .tab-icon {
            font-size: 18px !important;
        }

        .tab-label {
            font-size: 10px !important;
        }
    }

    /* Tablet/iPad Responsive Enhancements */
    @@media (min-width: 768px) and (max-width: 1200px) {
        .tab-bar-container {
            height: 90px;
        }
        .tab-item {
            min-width: 80px;
            gap: 8px;
        }
        .tab-icon-container {
            width: 40px;
            height: 40px;
        }
        .tab-icon {
            font-size: 28px !important;
        }
        .tab-label {
            font-size: 16px !important;
        }
    }
    @@media (min-width: 1200px) {
        .tab-bar-container {
            height: 110px;
        }
        .tab-item {
            min-width: 100px;
            gap: 12px;
        }
        .tab-icon-container {
            width: 48px;
            height: 48px;
        }
        .tab-icon {
            font-size: 36px !important;
        }
        .tab-label {
            font-size: 20px !important;
        }
    }

    /* Dark Mode Support */
    @@media (prefers-color-scheme: dark) {
        .tab-bar-paper {
            background: rgba(17, 24, 39, 0.95) !important;
            border-top-color: rgba(255, 255, 255, 0.1);
        }

        .tab-icon {
            color: #9CA3AF;
        }

        .tab-label {
            color: #9CA3AF;
        }

        .more-menu-paper {
            background: rgba(17, 24, 39, 0.98) !important;
        }
    }
</style>

@code {
    private bool _showMoreMenu = false;
    private string _currentUrl = "";
    private bool IsTablet => FormFactor.GetFormFactor() == "Tablet";

    protected override void OnInitialized()
    {
        _currentUrl = Navigation.Uri;
        Navigation.LocationChanged += OnLocationChanged;
    }

    private void OnLocationChanged(object sender, LocationChangedEventArgs e)
    {
        _currentUrl = e.Location;
        _showMoreMenu = false;
        InvokeAsync(StateHasChanged);
    }

    private string GetTabClass(string route)
    {
        var isActive = IsActiveTab(route);
        return $"tab-button {(isActive ? "active" : "")}";
    }

    private string GetIconClass(string route)
    {
        var isActive = IsActiveTab(route);
        return $"tab-icon {(isActive ? "active" : "")}";
    }

    private string GetLabelClass(string route)
    {
        var isActive = IsActiveTab(route);
        return $"tab-label {(isActive ? "active" : "")}";
    }

    private bool IsActiveTab(string route)
    {
        var currentPath = new Uri(_currentUrl).AbsolutePath;

        if (route == "/" && currentPath == "/")
            return true;

        if (route != "/" && currentPath.StartsWith(route, StringComparison.OrdinalIgnoreCase))
            return true;

        return false;
    }

    private void NavigateToTab(string url)
    {
        _showMoreMenu = false;
        Navigation.NavigateTo(url);
    }

    private string GetSoapNotesUrl()
    {
        bool Sub = false;
        return $"/soapnotes?pcpId={user.id}&sub={Sub}";
    }

    public void Dispose()
    {
        Navigation.LocationChanged -= OnLocationChanged;
    }
}
