using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using Moq;
using Moq.Protected;
using NUnit.Framework;
using TeyaUIModels.Model;
using TeyaUIViewModels.ViewModel;
using TeyaUIViewModels.TeyaUIViewModelResources;
using System.Net.Http.Json;
using System.Text.Json;

namespace TeyaWebClientUIViewModelTestCases
{
    [TestFixture]
    public class ProductLicenseServiceTests
    {
        private Mock<HttpMessageHandler> _mockHttpMessageHandler;
        private HttpClient _httpClient;
        private Mock<IConfiguration> _mockConfiguration;
        private Mock<IStringLocalizer<TeyaUIViewModelsStrings>> _mockLocalizer;
        private Mock<ITokenService> _mockTokenService;
        private ProductLicenseService _productLicenseService;
        private readonly string _baseUrl = "http://test-api.com";
        private readonly string _accessToken = "test-access-token";

        [SetUp]
        public void Setup()
        {
            // Setup environment variable
            Environment.SetEnvironmentVariable("MemberServiceURL", _baseUrl);

            // Setup mock HTTP handler
            _mockHttpMessageHandler = new Mock<HttpMessageHandler>();

            // Setup HttpClient with mock handler
            _httpClient = new HttpClient(_mockHttpMessageHandler.Object)
            {
                BaseAddress = new Uri(_baseUrl)
            };

            // Setup mock configuration
            _mockConfiguration = new Mock<IConfiguration>();

            // Setup mock localizer
            _mockLocalizer = new Mock<IStringLocalizer<TeyaUIViewModelsStrings>>();
            _mockLocalizer.Setup(l => l["LicenseRetrievalFailure"])
                .Returns(new LocalizedString("LicenseRetrievalFailure", "License retrieval failure"));
            _mockLocalizer.Setup(l => l["UpdateAccessFailure"])
                .Returns(new LocalizedString("UpdateAccessFailure", "Update access failure"));

            // Setup mock token service
            _mockTokenService = new Mock<ITokenService>();
            _mockTokenService.Setup(t => t.AccessToken).Returns(_accessToken);

            // Create ProductLicenseService with mocked dependencies
            _productLicenseService = new ProductLicenseService(_httpClient, _mockConfiguration.Object, _mockLocalizer.Object, _mockTokenService.Object);
        }

        [TearDown]
        public void TearDown()
        {
            _httpClient.Dispose();
            Environment.SetEnvironmentVariable("MemberServiceURL", null);
        }

        [Test]
        public async Task GetProductsLicenseAsync_WhenSuccessful_ReturnsProductLicenses()
        {
            // Arrange
            var expectedLicenses = new List<ProductLicense>
            {
                new ProductLicense
                {
                    Id = Guid.NewGuid(),
                    ProductName = "Product 1",
                    Description = "Product license description 1",
                    IsLicenseActivated = true,
                    OrganizationID = Guid.NewGuid(),
                    Subscription = true
                },
                new ProductLicense
                {
                    Id = Guid.NewGuid(),
                    ProductName = "Product 2",
                    Description = "Product license description 2",
                    IsLicenseActivated = true,
                    OrganizationID = Guid.NewGuid(),
                    Subscription = false
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedLicenses)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _productLicenseService.GetProductsLicenseAsync();

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(expectedLicenses.Count));
            Assert.That(result[0].Id, Is.EqualTo(expectedLicenses[0].Id));
            Assert.That(result[0].ProductName, Is.EqualTo(expectedLicenses[0].ProductName));
            Assert.That(result[0].Description, Is.EqualTo(expectedLicenses[0].Description));
            Assert.That(result[0].IsLicenseActivated, Is.EqualTo(expectedLicenses[0].IsLicenseActivated));
            Assert.That(result[0].OrganizationID, Is.EqualTo(expectedLicenses[0].OrganizationID));
            Assert.That(result[0].Subscription, Is.EqualTo(expectedLicenses[0].Subscription));
        }

        [Test]
        public void GetProductsLicenseAsync_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.NotFound,
                Content = new StringContent("Not found")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _productLicenseService.GetProductsLicenseAsync());

            Assert.That(exception.Message, Is.EqualTo("License retrieval failure"));
        }

        [Test]
        public async Task GetProductsLicenseAsync_WhenEmptyResult_ReturnsEmptyList()
        {
            // Arrange
            var expectedLicenses = new List<ProductLicense>();

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedLicenses)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _productLicenseService.GetProductsLicenseAsync();

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(0));
        }

        [Test]
        public async Task UpdateLicenseAccessAsync_WhenSuccessful_CompletesSuccessfully()
        {
            // Arrange
            var licenseAccessUpdates = new List<ProductLicense>
            {
                new ProductLicense
                {
                    Id = Guid.NewGuid(),
                    ProductName = "Product 1 Updated",
                    Description = "Updated product license description",
                    IsLicenseActivated = true,
                    OrganizationID = Guid.NewGuid(),
                    Subscription = true
                },
                new ProductLicense
                {
                    Id = Guid.NewGuid(),
                    ProductName = "Product 2 Updated",
                    Description = "Updated product license description 2",
                    IsLicenseActivated = false,
                    OrganizationID = Guid.NewGuid(),
                    Subscription = false
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent("Success")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.DoesNotThrowAsync(async () => await _productLicenseService.UpdateLicenseAccessAsync(licenseAccessUpdates));
        }

        [Test]
        public void UpdateLicenseAccessAsync_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var licenseAccessUpdates = new List<ProductLicense>
            {
                new ProductLicense
                {
                    Id = Guid.NewGuid(),
                    ProductName = "Product 1 Updated",
                    Description = "Updated product license description",
                    IsLicenseActivated = true,
                    OrganizationID = Guid.NewGuid(),
                    Subscription = true
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _productLicenseService.UpdateLicenseAccessAsync(licenseAccessUpdates));

            Assert.That(exception, Is.Not.Null);
        }

        [Test]
        public void UpdateLicenseAccessAsync_WhenExceptionThrown_ThrowsHttpRequestExceptionWithMessage()
        {
            // Arrange
            var licenseAccessUpdates = new List<ProductLicense>
            {
                new ProductLicense
                {
                    Id = Guid.NewGuid(),
                    ProductName = "Product 1",
                    Description = "Product license description",
                    IsLicenseActivated = true,
                    OrganizationID = Guid.NewGuid(),
                    Subscription = true
                }
            };

            var expectedException = new HttpRequestException("Test exception");

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ThrowsAsync(expectedException);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _productLicenseService.UpdateLicenseAccessAsync(licenseAccessUpdates));

            Assert.That(exception.Message, Is.EqualTo("Update access failure"));
            Assert.That(exception.InnerException, Is.EqualTo(expectedException));
        }

        [Test]
        public async Task UpdateLicenseAccessAsync_WhenEmptyList_CompletesSuccessfully()
        {
            // Arrange
            var licenseAccessUpdates = new List<ProductLicense>();

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent("Success")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.DoesNotThrowAsync(async () => await _productLicenseService.UpdateLicenseAccessAsync(licenseAccessUpdates));
        }
    }
}



