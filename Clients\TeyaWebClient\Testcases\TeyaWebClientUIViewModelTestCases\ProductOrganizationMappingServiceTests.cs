using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using Moq;
using Moq.Protected;
using NUnit.Framework;
using TeyaUIModels.Model;
using TeyaWebApp.ViewModel;
using System.Net.Http.Json;
using System.Text.Json;

namespace TeyaWebClientUIViewModelTestCases
{
    [TestFixture]
    public class ProductOrganizationMappingServiceTests
    {
        private Mock<HttpMessageHandler> _mockHttpMessageHandler;
        private HttpClient _httpClient;
        private Mock<ILogger<ProductOrganizationMappingService>> _mockLogger;
        private Mock<IStringLocalizer<ProductOrganizationMappingService>> _mockLocalizer;
        private ProductOrganizationMappingService _productOrganizationMappingService;
        private readonly string _baseUrl = "http://test-api.com";

        [SetUp]
        public void Setup()
        {
            // Setup environment variable
            Environment.SetEnvironmentVariable("MemberServiceURL", _baseUrl);

            // Setup mock HTTP handler
            _mockHttpMessageHandler = new Mock<HttpMessageHandler>();

            // Setup HttpClient with mock handler
            _httpClient = new HttpClient(_mockHttpMessageHandler.Object)
            {
                BaseAddress = new Uri(_baseUrl)
            };

            // Setup mock logger
            _mockLogger = new Mock<ILogger<ProductOrganizationMappingService>>();

            // Setup mock localizer
            _mockLocalizer = new Mock<IStringLocalizer<ProductOrganizationMappingService>>();
            _mockLocalizer.Setup(l => l["API Response: {ResponseData}"])
                .Returns(new LocalizedString("API Response: {ResponseData}", "API Response: {ResponseData}"));
            _mockLocalizer.Setup(l => l["JsonDeserializationError"])
                .Returns(new LocalizedString("JsonDeserializationError", "JSON deserialization error: {0}"));
            _mockLocalizer.Setup(l => l["ProductOrganizationMappingAlreadyExists"])
                .Returns(new LocalizedString("ProductOrganizationMappingAlreadyExists", "Product organization mapping already exists"));
            _mockLocalizer.Setup(l => l["RegistrationFailed"])
                .Returns(new LocalizedString("RegistrationFailed", "Registration failed"));
            _mockLocalizer.Setup(l => l["ProductOrganizationMappingRegistrationError"])
                .Returns(new LocalizedString("ProductOrganizationMappingRegistrationError", "Product organization mapping registration error"));
            _mockLocalizer.Setup(l => l["GetByIdFailed"])
                .Returns(new LocalizedString("GetByIdFailed", "Get by ID failed"));
            _mockLocalizer.Setup(l => l["ErrorFetchingProductOrganizationMappingById"])
                .Returns(new LocalizedString("ErrorFetchingProductOrganizationMappingById", "Error fetching product organization mapping by ID"));
            _mockLocalizer.Setup(l => l["ErrorFetchingAllProductOrganizationMappings"])
                .Returns(new LocalizedString("ErrorFetchingAllProductOrganizationMappings", "Error fetching all product organization mappings"));
            _mockLocalizer.Setup(l => l["ProductOrganizationMappingDeletedSuccessfully"])
                .Returns(new LocalizedString("ProductOrganizationMappingDeletedSuccessfully", "Product organization mapping deleted successfully"));
            _mockLocalizer.Setup(l => l["ProductOrganizationMappingDeletionFailed"])
                .Returns(new LocalizedString("ProductOrganizationMappingDeletionFailed", "Product organization mapping deletion failed"));
            _mockLocalizer.Setup(l => l["ErrorDeletingProductOrganizationMapping"])
                .Returns(new LocalizedString("ErrorDeletingProductOrganizationMapping", "Error deleting product organization mapping"));
            _mockLocalizer.Setup(l => l["InvalidProductOrganizationMapping"])
                .Returns(new LocalizedString("InvalidProductOrganizationMapping", "Invalid product organization mapping"));
            _mockLocalizer.Setup(l => l["UpdateSuccessful"])
                .Returns(new LocalizedString("UpdateSuccessful", "Update successful"));
            _mockLocalizer.Setup(l => l["UpdateFailed"])
                .Returns(new LocalizedString("UpdateFailed", "Update failed"));
            _mockLocalizer.Setup(l => l["ErrorUpdatingProductOrganizationMapping"])
                .Returns(new LocalizedString("ErrorUpdatingProductOrganizationMapping", "Error updating product organization mapping"));
            _mockLocalizer.Setup(l => l["ErrorFetchingMappingsByProductId"])
                .Returns(new LocalizedString("ErrorFetchingMappingsByProductId", "Error fetching mappings by product ID"));

            // Create ProductOrganizationMappingService with mocked dependencies
            _productOrganizationMappingService = new ProductOrganizationMappingService(_httpClient, _mockLocalizer.Object, _mockLogger.Object);
        }

        [TearDown]
        public void TearDown()
        {
            _httpClient.Dispose();
            Environment.SetEnvironmentVariable("MemberServiceURL", null);
        }

        [Test]
        public async Task RegisterProductOrganizationMappingsAsync_WhenSuccessful_ReturnsMapping()
        {
            // Arrange
            var mapping = new ProductOrganizationMapping
            {
                ProductOrganizationMappingId = Guid.NewGuid(),
                ProductId = Guid.NewGuid(),
                OrganizationId = Guid.NewGuid(),
                IsActive = true,
                CreatedDate = DateTime.Now
            };

            var jsonResponse = System.Text.Json.JsonSerializer.Serialize(mapping);
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.Created,
                Content = new StringContent(jsonResponse, System.Text.Encoding.UTF8, "application/json")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _productOrganizationMappingService.RegisterProductOrganizationMappingsAsync(mapping);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.ProductOrganizationMappingId, Is.EqualTo(mapping.ProductOrganizationMappingId));
            Assert.That(result.ProductId, Is.EqualTo(mapping.ProductId));
            Assert.That(result.OrganizationId, Is.EqualTo(mapping.OrganizationId));
            Assert.That(result.IsActive, Is.EqualTo(mapping.IsActive));

            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Information,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.IsAny<Exception>(),
                    It.Is<Func<It.IsAnyType, Exception, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }

        [Test]
        public void RegisterProductOrganizationMappingsAsync_WhenConflict_ThrowsHttpRequestException()
        {
            // Arrange
            var mapping = new ProductOrganizationMapping
            {
                ProductOrganizationMappingId = Guid.NewGuid(),
                ProductId = Guid.NewGuid(),
                OrganizationId = Guid.NewGuid(),
                IsActive = true,
                CreatedDate = DateTime.Now
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.Conflict,
                Content = new StringContent("Conflict")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _productOrganizationMappingService.RegisterProductOrganizationMappingsAsync(mapping));

            Assert.That(exception.Message, Is.EqualTo("Product organization mapping already exists"));
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.IsAny<Exception>(),
                    It.Is<Func<It.IsAnyType, Exception, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }

        [Test]
        public void RegisterProductOrganizationMappingsAsync_WhenBadRequest_ThrowsHttpRequestException()
        {
            // Arrange
            var mapping = new ProductOrganizationMapping
            {
                ProductOrganizationMappingId = Guid.NewGuid(),
                ProductId = Guid.NewGuid(),
                OrganizationId = Guid.NewGuid(),
                IsActive = true,
                CreatedDate = DateTime.Now
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _productOrganizationMappingService.RegisterProductOrganizationMappingsAsync(mapping));

            Assert.That(exception.Message, Is.EqualTo("Registration failed"));
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.IsAny<Exception>(),
                    It.Is<Func<It.IsAnyType, Exception, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }

        [Test]
        public async Task GetProductOrganizationMappingByIdAsync_WhenSuccessful_ReturnsMapping()
        {
            // Arrange
            var mappingId = Guid.NewGuid();
            var expectedMapping = new ProductOrganizationMapping
            {
                ProductOrganizationMappingId = mappingId,
                ProductId = Guid.NewGuid(),
                OrganizationId = Guid.NewGuid(),
                IsActive = true,
                CreatedDate = DateTime.Now.AddDays(-10)
            };

            var jsonResponse = System.Text.Json.JsonSerializer.Serialize(expectedMapping);
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(jsonResponse, System.Text.Encoding.UTF8, "application/json")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _productOrganizationMappingService.GetProductOrganizationMappingByIdAsync(mappingId);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.ProductOrganizationMappingId, Is.EqualTo(expectedMapping.ProductOrganizationMappingId));
            Assert.That(result.ProductId, Is.EqualTo(expectedMapping.ProductId));
            Assert.That(result.OrganizationId, Is.EqualTo(expectedMapping.OrganizationId));
            Assert.That(result.IsActive, Is.EqualTo(expectedMapping.IsActive));
        }

        [Test]
        public void GetProductOrganizationMappingByIdAsync_WhenNotFound_ThrowsHttpRequestException()
        {
            // Arrange
            var mappingId = Guid.NewGuid();

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.NotFound,
                Content = new StringContent("Not found")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _productOrganizationMappingService.GetProductOrganizationMappingByIdAsync(mappingId));

            Assert.That(exception.Message, Is.EqualTo("Get by ID failed"));
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.IsAny<Exception>(),
                    It.Is<Func<It.IsAnyType, Exception, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }

        [Test]
        public async Task GetAllProductOrganizationMappingsAsync_WhenSuccessful_ReturnsMappings()
        {
            // Arrange
            var expectedMappings = new List<ProductOrganizationMapping>
            {
                new ProductOrganizationMapping
                {
                    ProductOrganizationMappingId = Guid.NewGuid(),
                    ProductId = Guid.NewGuid(),
                    OrganizationId = Guid.NewGuid(),
                    IsActive = true,
                    CreatedDate = DateTime.Now.AddDays(-30)
                },
                new ProductOrganizationMapping
                {
                    ProductOrganizationMappingId = Guid.NewGuid(),
                    ProductId = Guid.NewGuid(),
                    OrganizationId = Guid.NewGuid(),
                    IsActive = true,
                    CreatedDate = DateTime.Now.AddDays(-15)
                }
            };

            var jsonResponse = System.Text.Json.JsonSerializer.Serialize(expectedMappings);
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(jsonResponse, System.Text.Encoding.UTF8, "application/json")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _productOrganizationMappingService.GetAllProductOrganizationMappingsAsync();

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(expectedMappings.Count));
            Assert.That(result[0].ProductOrganizationMappingId, Is.EqualTo(expectedMappings[0].ProductOrganizationMappingId));
            Assert.That(result[1].ProductOrganizationMappingId, Is.EqualTo(expectedMappings[1].ProductOrganizationMappingId));
        }

        [Test]
        public void GetAllProductOrganizationMappingsAsync_WhenNotSuccessful_ThrowsException()
        {
            // Arrange
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.InternalServerError,
                Content = new StringContent("Internal server error")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _productOrganizationMappingService.GetAllProductOrganizationMappingsAsync());

            Assert.That(exception, Is.Not.Null);
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.IsAny<Exception>(),
                    It.Is<Func<It.IsAnyType, Exception, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }

        [Test]
        public async Task DeleteProductOrganizationMappingByIdAsync_WhenSuccessful_CompletesSuccessfully()
        {
            // Arrange
            var mappingId = Guid.NewGuid();

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.NoContent
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.DoesNotThrowAsync(async () => await _productOrganizationMappingService.DeleteProductOrganizationMappingByIdAsync(mappingId));

            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Information,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.IsAny<Exception>(),
                    It.Is<Func<It.IsAnyType, Exception, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }

        [Test]
        public void DeleteProductOrganizationMappingByIdAsync_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var mappingId = Guid.NewGuid();

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.NotFound,
                Content = new StringContent("Not found")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _productOrganizationMappingService.DeleteProductOrganizationMappingByIdAsync(mappingId));

            Assert.That(exception.Message, Is.EqualTo("Product organization mapping deletion failed"));
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.IsAny<Exception>(),
                    It.Is<Func<It.IsAnyType, Exception, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }

        [Test]
        public async Task UpdateProductOrganizationMappingByIdAsync_WhenSuccessful_CompletesSuccessfully()
        {
            // Arrange
            var mappingId = Guid.NewGuid();
            var mapping = new ProductOrganizationMapping
            {
                ProductOrganizationMappingId = mappingId,
                ProductId = Guid.NewGuid(),
                OrganizationId = Guid.NewGuid(),
                IsActive = false,
                CreatedDate = DateTime.Now.AddDays(-10)
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent("Success")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.DoesNotThrowAsync(async () => await _productOrganizationMappingService.UpdateProductOrganizationMappingByIdAsync(mappingId, mapping));

            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Information,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.IsAny<Exception>(),
                    It.Is<Func<It.IsAnyType, Exception, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }

        [Test]
        public void UpdateProductOrganizationMappingByIdAsync_WhenMappingIsNull_ThrowsArgumentException()
        {
            // Arrange
            var mappingId = Guid.NewGuid();
            ProductOrganizationMapping mapping = null;

            // Act & Assert
            var exception = Assert.ThrowsAsync<ArgumentException>(async () =>
                await _productOrganizationMappingService.UpdateProductOrganizationMappingByIdAsync(mappingId, mapping));

            Assert.That(exception.Message, Is.EqualTo("Invalid product organization mapping"));
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.IsAny<Exception>(),
                    It.Is<Func<It.IsAnyType, Exception, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }

        [Test]
        public void UpdateProductOrganizationMappingByIdAsync_WhenIdMismatch_ThrowsArgumentException()
        {
            // Arrange
            var mappingId = Guid.NewGuid();
            var mapping = new ProductOrganizationMapping
            {
                ProductOrganizationMappingId = Guid.NewGuid(), // Different ID
                ProductId = Guid.NewGuid(),
                OrganizationId = Guid.NewGuid(),
                IsActive = true,
                CreatedDate = DateTime.Now.AddDays(-10)
            };

            // Act & Assert
            var exception = Assert.ThrowsAsync<ArgumentException>(async () =>
                await _productOrganizationMappingService.UpdateProductOrganizationMappingByIdAsync(mappingId, mapping));

            Assert.That(exception.Message, Is.EqualTo("Invalid product organization mapping"));
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.IsAny<Exception>(),
                    It.Is<Func<It.IsAnyType, Exception, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }

        [Test]
        public void UpdateProductOrganizationMappingByIdAsync_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var mappingId = Guid.NewGuid();
            var mapping = new ProductOrganizationMapping
            {
                ProductOrganizationMappingId = mappingId,
                ProductId = Guid.NewGuid(),
                OrganizationId = Guid.NewGuid(),
                IsActive = false,
                CreatedDate = DateTime.Now.AddDays(-10)
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _productOrganizationMappingService.UpdateProductOrganizationMappingByIdAsync(mappingId, mapping));

            Assert.That(exception.Message, Is.EqualTo("Update failed"));
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.IsAny<Exception>(),
                    It.Is<Func<It.IsAnyType, Exception, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }

        [Test]
        public async Task GetMappingsByProductIdAsync_WhenSuccessful_ReturnsMappings()
        {
            // Arrange
            var productId = Guid.NewGuid();
            var expectedMappings = new List<ProductOrganizationMapping>
            {
                new ProductOrganizationMapping
                {
                    ProductOrganizationMappingId = Guid.NewGuid(),
                    ProductId = productId,
                    OrganizationId = Guid.NewGuid(),
                    IsActive = true,
                    CreatedDate = DateTime.Now.AddDays(-20)
                },
                new ProductOrganizationMapping
                {
                    ProductOrganizationMappingId = Guid.NewGuid(),
                    ProductId = productId,
                    OrganizationId = Guid.NewGuid(),
                    IsActive = true,
                    CreatedDate = DateTime.Now.AddDays(-10)
                }
            };

            var jsonResponse = System.Text.Json.JsonSerializer.Serialize(expectedMappings);
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(jsonResponse, System.Text.Encoding.UTF8, "application/json")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _productOrganizationMappingService.GetMappingsByProductIdAsync(productId);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(expectedMappings.Count));
            Assert.That(result[0].ProductId, Is.EqualTo(productId));
            Assert.That(result[1].ProductId, Is.EqualTo(productId));
        }

        [Test]
        public void GetMappingsByProductIdAsync_WhenNotSuccessful_ThrowsException()
        {
            // Arrange
            var productId = Guid.NewGuid();

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.NotFound,
                Content = new StringContent("Not found")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _productOrganizationMappingService.GetMappingsByProductIdAsync(productId));

            Assert.That(exception, Is.Not.Null);
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.IsAny<Exception>(),
                    It.Is<Func<It.IsAnyType, Exception, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }
    }
}



