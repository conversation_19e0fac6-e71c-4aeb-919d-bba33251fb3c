using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using Moq;
using Moq.Protected;
using NUnit.Framework;
using TeyaUIModels.Model;
using TeyaWebApp.Services;
using System.Net.Http.Json;
using System.Text.Json;
using TeyaUIViewModels.ViewModel;
using System.Linq;

namespace TeyaWebClientUIViewModelTestCases
{
    [TestFixture]
    public class ProductFeatureServiceTests
    {
        private Mock<HttpMessageHandler> _mockHttpMessageHandler;
        private HttpClient _httpClient;
        private Mock<ILogger<ProductFeatureService>> _mockLogger;
        private Mock<IStringLocalizer<ProductFeatureService>> _mockLocalizer;
        private Mock<ITokenService> _mockTokenService;
        private ProductFeatureService _productFeatureService;
        private readonly string _baseUrl = "http://test-api.com";
        private readonly string _accessToken = "test-access-token";

        [SetUp]
        public void Setup()
        {
            // Setup environment variable
            Environment.SetEnvironmentVariable("MemberServiceURL", _baseUrl);

            // Setup mock HTTP handler
            _mockHttpMessageHandler = new Mock<HttpMessageHandler>();

            // Setup HttpClient with mock handler
            _httpClient = new HttpClient(_mockHttpMessageHandler.Object)
            {
                BaseAddress = new Uri(_baseUrl)
            };

            // Setup mock logger
            _mockLogger = new Mock<ILogger<ProductFeatureService>>();

            // Setup mock localizer
            _mockLocalizer = new Mock<IStringLocalizer<ProductFeatureService>>();
            _mockLocalizer.Setup(l => l["ErrorFetchingAllProductFeatures"])
                .Returns(new LocalizedString("ErrorFetchingAllProductFeatures", "Error fetching all product features"));
            _mockLocalizer.Setup(l => l["ErrorFetchingProductFeatureById"])
                .Returns(new LocalizedString("ErrorFetchingProductFeatureById", "Error fetching product feature by ID"));
            _mockLocalizer.Setup(l => l["GetProductFeatureByIdFailed"])
                .Returns(new LocalizedString("GetProductFeatureByIdFailed", "Get product feature by ID failed"));
            _mockLocalizer.Setup(l => l["ErrorFetchingProductFeaturesByName"])
                .Returns(new LocalizedString("ErrorFetchingProductFeaturesByName", "Error fetching product features by name"));
            _mockLocalizer.Setup(l => l["GetProductFeaturesByNameFailed"])
                .Returns(new LocalizedString("GetProductFeaturesByNameFailed", "Get product features by name failed"));
            _mockLocalizer.Setup(l => l["ErrorAddingProductFeature"])
                .Returns(new LocalizedString("ErrorAddingProductFeature", "Error adding product feature"));
            _mockLocalizer.Setup(l => l["AddProductFeatureFailed"])
                .Returns(new LocalizedString("AddProductFeatureFailed", "Add product feature failed"));
            _mockLocalizer.Setup(l => l["ProductFeatureAddedSuccessfully"])
                .Returns(new LocalizedString("ProductFeatureAddedSuccessfully", "Product feature added successfully"));
            _mockLocalizer.Setup(l => l["ErrorUpdatingProductFeature"])
                .Returns(new LocalizedString("ErrorUpdatingProductFeature", "Error updating product feature"));
            _mockLocalizer.Setup(l => l["ProductFeatureUpdateFailed"])
                .Returns(new LocalizedString("ProductFeatureUpdateFailed", "Product feature update failed"));
            _mockLocalizer.Setup(l => l["ProductFeatureUpdatedSuccessfully"])
                .Returns(new LocalizedString("ProductFeatureUpdatedSuccessfully", "Product feature updated successfully"));
            _mockLocalizer.Setup(l => l["ErrorDeletingProductFeature"])
                .Returns(new LocalizedString("ErrorDeletingProductFeature", "Error deleting product feature"));
            _mockLocalizer.Setup(l => l["ProductFeatureDeletionFailed"])
                .Returns(new LocalizedString("ProductFeatureDeletionFailed", "Product feature deletion failed"));
            _mockLocalizer.Setup(l => l["ProductFeatureDeletedSuccessfully"])
                .Returns(new LocalizedString("ProductFeatureDeletedSuccessfully", "Product feature deleted successfully"));

            // Setup mock token service
            _mockTokenService = new Mock<ITokenService>();
            _mockTokenService.Setup(t => t.AccessToken).Returns(_accessToken);
            _mockTokenService.Setup(t => t.GetValidatedAccessTokenAsync()).ReturnsAsync(_accessToken);

            // Create ProductFeatureService with mocked dependencies
            _productFeatureService = new ProductFeatureService(_httpClient, _mockLocalizer.Object, _mockLogger.Object, _mockTokenService.Object);
        }

        [TearDown]
        public void TearDown()
        {
            _httpClient.Dispose();
            Environment.SetEnvironmentVariable("MemberServiceURL", null);
        }

        [Test]
        public async Task GetAllProductFeaturesAsync_WhenSuccessful_ReturnsProductFeatures()
        {
            // Arrange
            var expectedFeatures = new List<ProductFeature>
            {
                new ProductFeature
                {
                    Id = Guid.NewGuid(),
                    FeatureName = "Feature 1",
                    ProdId = Guid.NewGuid(),
                    ProdName = "Product 1",
                    Status = true,
                    Created = DateTime.Now.AddDays(-30)
                },
                new ProductFeature
                {
                    Id = Guid.NewGuid(),
                    FeatureName = "Feature 2",
                    ProdId = Guid.NewGuid(),
                    ProdName = "Product 2",
                    Status = true,
                    Created = DateTime.Now.AddDays(-15)
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedFeatures)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _productFeatureService.GetAllProductFeaturesAsync();

            // Assert
            Assert.That(result, Is.Not.Null);
            var resultList = result.ToList();
            Assert.That(resultList.Count, Is.EqualTo(expectedFeatures.Count));
            Assert.That(resultList[0].Id, Is.EqualTo(expectedFeatures[0].Id));
            Assert.That(resultList[0].FeatureName, Is.EqualTo(expectedFeatures[0].FeatureName));
            Assert.That(resultList[0].ProdName, Is.EqualTo(expectedFeatures[0].ProdName));
            Assert.That(resultList[0].Status, Is.EqualTo(expectedFeatures[0].Status));
        }

        [Test]
        public void GetAllProductFeaturesAsync_WhenExceptionThrown_PropagatesException()
        {
            // Arrange
            var expectedException = new HttpRequestException("Test exception");

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ThrowsAsync(expectedException);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _productFeatureService.GetAllProductFeaturesAsync());

            Assert.That(exception.Message, Is.EqualTo(expectedException.Message));
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.Is<Exception>(ex => ex.Message == expectedException.Message),
                    It.Is<Func<It.IsAnyType, Exception, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }

        [Test]
        public async Task GetProductFeatureByIdAsync_WhenSuccessful_ReturnsProductFeature()
        {
            // Arrange
            var featureId = Guid.NewGuid();
            var expectedFeature = new ProductFeature
            {
                Id = featureId,
                FeatureName = "Test Feature",
                ProdId = Guid.NewGuid(),
                ProdName = "Test Product",
                Status = true,
                Created = DateTime.Now.AddDays(-10)
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedFeature)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _productFeatureService.GetProductFeatureByIdAsync(featureId);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Id, Is.EqualTo(expectedFeature.Id));
            Assert.That(result.FeatureName, Is.EqualTo(expectedFeature.FeatureName));
            Assert.That(result.ProdName, Is.EqualTo(expectedFeature.ProdName));
            Assert.That(result.Status, Is.EqualTo(expectedFeature.Status));
        }

        [Test]
        public void GetProductFeatureByIdAsync_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var featureId = Guid.NewGuid();

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.NotFound,
                Content = new StringContent("Not found")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _productFeatureService.GetProductFeatureByIdAsync(featureId));

            Assert.That(exception.Message, Is.EqualTo("Get product feature by ID failed"));
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.IsAny<Exception>(),
                    It.Is<Func<It.IsAnyType, Exception, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }

        [Test]
        public async Task GetProductFeaturesByNameAsync_WhenSuccessful_ReturnsProductFeatures()
        {
            // Arrange
            var searchName = "Test Feature";
            var expectedFeatures = new List<ProductFeature>
            {
                new ProductFeature
                {
                    Id = Guid.NewGuid(),
                    FeatureName = "Test Feature 1",
                    ProdId = Guid.NewGuid(),
                    ProdName = "Test Product 1",
                    Status = true,
                    Created = DateTime.Now.AddDays(-20)
                },
                new ProductFeature
                {
                    Id = Guid.NewGuid(),
                    FeatureName = "Test Feature 2",
                    ProdId = Guid.NewGuid(),
                    ProdName = "Test Product 2",
                    Status = true,
                    Created = DateTime.Now.AddDays(-10)
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedFeatures)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _productFeatureService.GetProductFeaturesByNameAsync(searchName);

            // Assert
            Assert.That(result, Is.Not.Null);
            var resultList = result.ToList();
            Assert.That(resultList.Count, Is.EqualTo(expectedFeatures.Count));
            Assert.That(resultList[0].FeatureName, Does.Contain("Test Feature"));
            Assert.That(resultList[1].FeatureName, Does.Contain("Test Feature"));
        }

        [Test]
        public void GetProductFeaturesByNameAsync_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var searchName = "NonExistent Feature";

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.NotFound,
                Content = new StringContent("Not found")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _productFeatureService.GetProductFeaturesByNameAsync(searchName));

            Assert.That(exception.Message, Is.EqualTo("Get product features by name failed"));
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.IsAny<Exception>(),
                    It.Is<Func<It.IsAnyType, Exception, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }

        [Test]
        public async Task AddProductFeatureAsync_WhenSuccessful_CompletesSuccessfully()
        {
            // Arrange
            var productFeature = new ProductFeature
            {
                Id = Guid.NewGuid(),
                FeatureName = "New Feature",
                ProdId = Guid.NewGuid(),
                ProdName = "New Product",
                Status = true,
                Created = DateTime.Now
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.Created,
                Content = new StringContent("Success")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.DoesNotThrowAsync(async () => await _productFeatureService.AddProductFeatureAsync(productFeature));

            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Information,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.IsAny<Exception>(),
                    It.Is<Func<It.IsAnyType, Exception, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }

        [Test]
        public void AddProductFeatureAsync_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var productFeature = new ProductFeature
            {
                Id = Guid.NewGuid(),
                FeatureName = "New Feature",
                ProdId = Guid.NewGuid(),
                ProdName = "New Product",
                Status = true,
                Created = DateTime.Now
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _productFeatureService.AddProductFeatureAsync(productFeature));

            Assert.That(exception.Message, Is.EqualTo("Add product feature failed"));
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.IsAny<Exception>(),
                    It.Is<Func<It.IsAnyType, Exception, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }

        [Test]
        public async Task UpdateProductFeatureAsync_WhenSuccessful_CompletesSuccessfully()
        {
            // Arrange
            var productFeature = new ProductFeature
            {
                Id = Guid.NewGuid(),
                FeatureName = "Updated Feature",
                ProdId = Guid.NewGuid(),
                ProdName = "Updated Product",
                Status = true,
                Created = DateTime.Now.AddDays(-10)
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent("Success")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.DoesNotThrowAsync(async () => await _productFeatureService.UpdateProductFeatureAsync(productFeature));

            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Information,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.IsAny<Exception>(),
                    It.Is<Func<It.IsAnyType, Exception, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }

        [Test]
        public void UpdateProductFeatureAsync_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var productFeature = new ProductFeature
            {
                Id = Guid.NewGuid(),
                FeatureName = "Updated Feature",
                ProdId = Guid.NewGuid(),
                ProdName = "Updated Product",
                Status = true,
                Created = DateTime.Now.AddDays(-10)
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _productFeatureService.UpdateProductFeatureAsync(productFeature));

            Assert.That(exception.Message, Is.EqualTo("Product feature update failed"));
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.IsAny<Exception>(),
                    It.Is<Func<It.IsAnyType, Exception, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }

        [Test]
        public async Task DeleteProductFeatureByIdAsync_WhenSuccessful_CompletesSuccessfully()
        {
            // Arrange
            var featureId = Guid.NewGuid();

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.NoContent
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.DoesNotThrowAsync(async () => await _productFeatureService.DeleteProductFeatureByIdAsync(featureId));

            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Information,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.IsAny<Exception>(),
                    It.Is<Func<It.IsAnyType, Exception, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }

        [Test]
        public void DeleteProductFeatureByIdAsync_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var featureId = Guid.NewGuid();

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.NotFound,
                Content = new StringContent("Not found")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _productFeatureService.DeleteProductFeatureByIdAsync(featureId));

            Assert.That(exception.Message, Is.EqualTo("Product feature deletion failed"));
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.IsAny<Exception>(),
                    It.Is<Func<It.IsAnyType, Exception, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }
    }
}



