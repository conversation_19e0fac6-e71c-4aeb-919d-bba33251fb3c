using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using Moq;
using Moq.Protected;
using NUnit.Framework;
using TeyaUIModels.Model;
using TeyaUIViewModels.ViewModel;
using TeyaUIViewModels.TeyaUIViewModelResources;
using System.Net.Http.Json;
using System.Text.Json;

namespace TeyaWebClientUIViewModelTestCases
{
    [TestFixture]
    public class PredefinedTemplateServiceTests
    {
        private Mock<HttpMessageHandler> _mockHttpMessageHandler;
        private HttpClient _httpClient;
        private Mock<IConfiguration> _mockConfiguration;
        private Mock<IStringLocalizer<TeyaUIViewModelsStrings>> _mockLocalizer;
        private Mock<ITokenService> _mockTokenService;
        private PredefinedTemplateService _predefinedTemplateService;
        private readonly string _baseUrl = "http://test-api.com";
        private readonly string _accessToken = "test-access-token";

        [SetUp]
        public void Setup()
        {
            // Setup environment variable
            Environment.SetEnvironmentVariable("EncounterNotesURL", _baseUrl);

            // Setup mock HTTP handler
            _mockHttpMessageHandler = new Mock<HttpMessageHandler>();

            // Setup HttpClient with mock handler
            _httpClient = new HttpClient(_mockHttpMessageHandler.Object)
            {
                BaseAddress = new Uri(_baseUrl)
            };

            // Setup mock configuration
            _mockConfiguration = new Mock<IConfiguration>();

            // Setup mock localizer
            _mockLocalizer = new Mock<IStringLocalizer<TeyaUIViewModelsStrings>>();
            _mockLocalizer.Setup(l => l["TemplateRetrievalFailure"])
                .Returns(new LocalizedString("TemplateRetrievalFailure", "Template retrieval failure"));
            _mockLocalizer.Setup(l => l["Error"])
                .Returns(new LocalizedString("Error", "Error"));

            // Setup mock token service
            _mockTokenService = new Mock<ITokenService>();
            _mockTokenService.Setup(t => t.AccessToken).Returns(_accessToken);
            _mockTokenService.Setup(t => t.GetValidatedAccessTokenAsync()).ReturnsAsync(_accessToken);

            // Create PredefinedTemplateService with mocked dependencies
            _predefinedTemplateService = new PredefinedTemplateService(_httpClient, _mockConfiguration.Object, _mockLocalizer.Object, _mockTokenService.Object);
        }

        [TearDown]
        public void TearDown()
        {
            _httpClient.Dispose();
            Environment.SetEnvironmentVariable("EncounterNotesURL", null);
        }

        [Test]
        public async Task GetTemplatesAsync_WhenSuccessful_ReturnsTemplates()
        {
            // Arrange
            var expectedTemplates = new List<TemplateData>
            {
                new TemplateData
                {
                    Id = Guid.NewGuid(),
                    TemplateName = "Template 1",
                    Template = "Template content 1",
                    VisitType = "Medical",
                    IsDefault = true,
                    CreatedDate = DateTime.Now.AddDays(-30)
                },
                new TemplateData
                {
                    Id = Guid.NewGuid(),
                    TemplateName = "Template 2",
                    Template = "Template content 2",
                    VisitType = "Administrative",
                    IsDefault = true,
                    CreatedDate = DateTime.Now.AddDays(-15)
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedTemplates)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _predefinedTemplateService.GetTemplatesAsync();

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(expectedTemplates.Count));
            Assert.That(result[0].Id, Is.EqualTo(expectedTemplates[0].Id));
            Assert.That(result[0].TemplateName, Is.EqualTo(expectedTemplates[0].TemplateName));
            Assert.That(result[0].Template, Is.EqualTo(expectedTemplates[0].Template));
            Assert.That(result[0].VisitType, Is.EqualTo(expectedTemplates[0].VisitType));
            Assert.That(result[0].IsDefault, Is.EqualTo(expectedTemplates[0].IsDefault));
        }

        [Test]
        public async Task GetTemplatesAsync_WhenNotSuccessful_ReturnsNull()
        {
            // Arrange
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.NotFound,
                Content = new StringContent("Not found")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _predefinedTemplateService.GetTemplatesAsync();

            // Assert
            Assert.That(result, Is.Null);
        }

        [Test]
        public async Task CreateTemplatesAsync_WhenSuccessful_CompletesSuccessfully()
        {
            // Arrange
            var templates = new List<TemplateData>
            {
                new TemplateData
                {
                    Id = Guid.NewGuid(),
                    TemplateName = "New Template",
                    Template = "New template content",
                    VisitType = "Medical",
                    IsDefault = true,
                    CreatedDate = DateTime.Now
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.Created,
                Content = new StringContent("Success")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.DoesNotThrowAsync(async () => await _predefinedTemplateService.CreateTemplatesAsync(templates));
        }

        [Test]
        public void CreateTemplatesAsync_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var templates = new List<TemplateData>
            {
                new TemplateData
                {
                    Id = Guid.NewGuid(),
                    TemplateName = "New Template",
                    Template = "New template content",
                    VisitType = "Medical",
                    IsDefault = true,
                    CreatedDate = DateTime.Now
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _predefinedTemplateService.CreateTemplatesAsync(templates));

            Assert.That(exception.Message, Is.EqualTo("Template retrieval failure"));
        }

        [Test]
        public async Task UpdateTemplatesAsync_WhenSuccessful_CompletesSuccessfully()
        {
            // Arrange
            var template = new TemplateData
            {
                Id = Guid.NewGuid(),
                TemplateName = "Updated Template",
                Template = "Updated template content",
                VisitType = "Administrative",
                IsDefault = true,
                CreatedDate = DateTime.Now.AddDays(-10)
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent("Success")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.DoesNotThrowAsync(async () => await _predefinedTemplateService.UpdateTemplatesAsync(template));
        }

        [Test]
        public void UpdateTemplatesAsync_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var template = new TemplateData
            {
                Id = Guid.NewGuid(),
                TemplateName = "Updated Template",
                Template = "Updated template content",
                VisitType = "Administrative",
                IsDefault = true,
                CreatedDate = DateTime.Now.AddDays(-10)
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _predefinedTemplateService.UpdateTemplatesAsync(template));

            Assert.That(exception, Is.Not.Null);
        }

        [Test]
        public async Task DeleteTemplatesAsync_WhenSuccessful_CompletesSuccessfully()
        {
            // Arrange
            var templateId = Guid.NewGuid();

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.NoContent,
                Content = new StringContent("Success")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.DoesNotThrowAsync(async () => await _predefinedTemplateService.DeleteTemplatesAsync(templateId));
        }

        [Test]
        public void DeleteTemplatesAsync_WhenNotSuccessful_ThrowsException()
        {
            // Arrange
            var templateId = Guid.NewGuid();

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.NotFound,
                Content = new StringContent("Not found")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<Exception>(async () =>
                await _predefinedTemplateService.DeleteTemplatesAsync(templateId));

            Assert.That(exception.Message, Is.EqualTo("Error"));
        }
    }
}



