<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <!-- Application Identifier (optional, but sometimes required for legacy reasons) -->
    <key>application-identifier</key>
    <string>$(AppIdentifierPrefix)com.teyahealth.teyamobile</string>

    <!-- Keychain Access Groups (if your app uses keychain sharing) -->
    <key>keychain-access-groups</key>
    <array>
        <string>$(AppIdentifierPrefix)com.teyahealth.teyamobile</string>
        <string>$(AppIdentifierPrefix)com.microsoft.adalcache</string>
    </array>

    <!-- Push Notification Entitlement (if your app uses push notifications) -->
    <key>aps-environment</key>
    <string>production</string>
</dict>
</plist>
