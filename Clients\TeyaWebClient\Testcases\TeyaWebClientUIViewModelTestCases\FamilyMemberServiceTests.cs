using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using Moq;
using Moq.Protected;
using NUnit.Framework;
using TeyaUIModels.Model;
using TeyaUIViewModels.ViewModel;
using TeyaUIViewModels.TeyaUIViewModelResources;
using System.Net.Http.Json;
using System.Text;

namespace TeyaWebClientUIViewModelTestCases
{
    [TestFixture]
    public class FamilyMemberServiceTests
    {
        private Mock<HttpMessageHandler> _mockHttpMessageHandler;
        private HttpClient _httpClient;
        private Mock<IConfiguration> _mockConfiguration;
        private Mock<IStringLocalizer<FamilyMemberService>> _mockLocalizer;
        private Mock<ITokenService> _mockTokenService;
        private FamilyMemberService _familyMemberService;
        private readonly string _baseUrl = "http://test-api.com";
        private readonly string _accessToken = "test-token";

        [SetUp]
        public void Setup()
        {
            // Setup environment variable
            Environment.SetEnvironmentVariable("EncounterNotesURL", _baseUrl);

            // Setup mock HTTP handler
            _mockHttpMessageHandler = new Mock<HttpMessageHandler>();

            // Setup HttpClient with mock handler
            _httpClient = new HttpClient(_mockHttpMessageHandler.Object)
            {
                BaseAddress = new Uri(_baseUrl)
            };

            // Setup mock configuration
            _mockConfiguration = new Mock<IConfiguration>();

            // Setup mock localizer
            _mockLocalizer = new Mock<IStringLocalizer<FamilyMemberService>>();
            _mockLocalizer.Setup(l => l["AddressRetrievalFailure"])
                .Returns(new LocalizedString("AddressRetrievalFailure", "Failed to retrieve address"));
            _mockLocalizer.Setup(l => l["MemberRetrievalFailure"])
                .Returns(new LocalizedString("MemberRetrievalFailure", "Failed to retrieve member"));
            _mockLocalizer.Setup(l => l["DatabaseError"])
                .Returns(new LocalizedString("DatabaseError", "Database error"));
            _mockLocalizer.Setup(l => l["Error"])
                .Returns(new LocalizedString("Error", "An error occurred"));
            _mockLocalizer.Setup(l => l["UpdateFamilyMemberListFailure"])
                .Returns(new LocalizedString("UpdateFamilyMemberListFailure", "Failed to update family member list"));

            // Setup mock token service
            _mockTokenService = new Mock<ITokenService>();
            _mockTokenService.Setup(t => t.AccessToken).Returns(_accessToken);

            // Create FamilyMemberService with mocked dependencies
            _familyMemberService = new FamilyMemberService(_httpClient, _mockConfiguration.Object, _mockLocalizer.Object, _mockTokenService.Object);
        }

        [TearDown]
        public void TearDown()
        {
            _httpClient.Dispose();
            Environment.SetEnvironmentVariable("EncounterNotesURL", null);
        }

        [Test]
        public async Task GetMemberAsync_WhenSuccessful_ReturnsFamilyMembers()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var expectedMembers = new List<FamilyMember>
            {
                new FamilyMember
                {
                    RecordID = Guid.NewGuid(),
                    PatientId = patientId,
                    Relation = "Father",
                    Status = "Living",
                    Age = 65,
                    Notes = "Healthy",
                    IsActive = true
                },
                new FamilyMember
                {
                    RecordID = Guid.NewGuid(),
                    PatientId = patientId,
                    Relation = "Mother",
                    Status = "Living",
                    Age = 62,
                    Notes = "Hypertension",
                    IsActive = false
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedMembers)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _familyMemberService.GetMemberAsync(patientId, orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(2));
            Assert.That(result[0].Relation, Is.EqualTo("Father"));
            Assert.That(result[1].Relation, Is.EqualTo("Mother"));
        }

        [Test]
        public void GetMemberAsync_WhenHttpRequestFails_ThrowsException()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _familyMemberService.GetMemberAsync(patientId, orgId, subscription));

            Assert.That(exception.Message, Is.EqualTo("Failed to retrieve member"));
        }

        [Test]
        public async Task GetFamilyMemberByIdAsyncAndIsActive_WhenSuccessful_ReturnsActiveFamilyMembers()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var expectedMembers = new List<FamilyMember>
            {
                new FamilyMember
                {
                    RecordID = Guid.NewGuid(),
                    PatientId = patientId,
                    Relation = "Father",
                    Status = "Living",
                    Age = 65,
                    Notes = "Healthy",
                    IsActive = true
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedMembers)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _familyMemberService.GetFamilyMemberByIdAsyncAndIsActive(patientId, orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(1));
            Assert.That(result[0].Relation, Is.EqualTo("Father"));
            Assert.That(result[0].IsActive, Is.True);
        }

        [Test]
        public void GetFamilyMemberByIdAsyncAndIsActive_WhenHttpRequestFails_ThrowsException()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _familyMemberService.GetFamilyMemberByIdAsyncAndIsActive(patientId, orgId, subscription));

            Assert.That(exception.Message, Is.EqualTo("Failed to retrieve address"));
        }

        [Test]
        public async Task CreateMemberAsync_WhenSuccessful_CompletesWithoutException()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var familyMembers = new List<FamilyMember>
            {
                new FamilyMember
                {
                    RecordID = Guid.NewGuid(),
                    PatientId = patientId,
                    Relation = "Father",
                    Status = "Living",
                    Age = 65,
                    Notes = "Healthy",
                    IsActive = true
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            // No exception should be thrown
            await _familyMemberService.CreateMemberAsync(familyMembers, orgId, subscription);
        }

        [Test]
        public void CreateMemberAsync_WhenHttpRequestFails_ThrowsException()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var familyMembers = new List<FamilyMember>
            {
                new FamilyMember
                {
                    RecordID = Guid.NewGuid(),
                    PatientId = patientId,
                    Relation = "Father",
                    Status = "Living",
                    Age = 65,
                    Notes = "Healthy",
                    IsActive = true
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _familyMemberService.CreateMemberAsync(familyMembers, orgId, subscription));
        }

        [Test]
        public async Task UpdateMemberAsync_WhenSuccessful_CompletesWithoutException()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var recordId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var familyMember = new FamilyMember
            {
                RecordID = recordId,
                PatientId = patientId,
                Relation = "Father",
                Status = "Living",
                Age = 66, // Updated age
                Notes = "Healthy, regular checkups",
                IsActive = true
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            // No exception should be thrown
            await _familyMemberService.UpdateMemberAsync(familyMember, orgId, subscription);
        }

        [Test]
        public void UpdateMemberAsync_WhenHttpRequestFails_ThrowsException()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var recordId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var familyMember = new FamilyMember
            {
                RecordID = recordId,
                PatientId = patientId,
                Relation = "Father",
                Status = "Living",
                Age = 66, // Updated age
                Notes = "Healthy, regular checkups",
                IsActive = true
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _familyMemberService.UpdateMemberAsync(familyMember, orgId, subscription));
        }

        [Test]
        public async Task UpdateFamilyMemberList_WhenSuccessful_CompletesWithoutException()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var familyMembers = new List<FamilyMember>
            {
                new FamilyMember
                {
                    RecordID = Guid.NewGuid(),
                    PatientId = patientId,
                    Relation = "Father",
                    Status = "Living",
                    Age = 66,
                    Notes = "Updated notes",
                    IsActive = true
                },
                new FamilyMember
                {
                    RecordID = Guid.NewGuid(),
                    PatientId = patientId,
                    Relation = "Mother",
                    Status = "Living",
                    Age = 63,
                    Notes = "Updated notes",
                    IsActive = true
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            // No exception should be thrown
            await _familyMemberService.UpdateFamilyMemberList(familyMembers, orgId, subscription);
        }

        [Test]
        public void UpdateFamilyMemberList_WhenHttpRequestFails_ThrowsException()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var familyMembers = new List<FamilyMember>
            {
                new FamilyMember
                {
                    RecordID = Guid.NewGuid(),
                    PatientId = patientId,
                    Relation = "Father",
                    Status = "Living",
                    Age = 66,
                    Notes = "Updated notes",
                    IsActive = true
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _familyMemberService.UpdateFamilyMemberList(familyMembers, orgId, subscription));
        }

        [Test]
        public async Task DeleteMemberAsync_WhenSuccessful_CompletesWithoutException()
        {
            // Arrange
            var recordId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            // No exception should be thrown
            await _familyMemberService.DeleteMemberAsync(recordId, orgId, subscription);
        }

        [Test]
        public void DeleteMemberAsync_WhenHttpRequestFails_ThrowsException()
        {
            // Arrange
            var recordId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.ThrowsAsync<Exception>(async () =>
                await _familyMemberService.DeleteMemberAsync(recordId, orgId, subscription));
        }
    }
}



