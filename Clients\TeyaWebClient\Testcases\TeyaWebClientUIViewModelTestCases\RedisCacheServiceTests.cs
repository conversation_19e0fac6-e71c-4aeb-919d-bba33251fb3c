using System;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Moq;
using NUnit.Framework;
using StackExchange.Redis;
using TeyaUIViewModels.ViewModel;
using TeyaUIViewModels.TeyaUIViewModelResources;
using System.Text.Json;

namespace TeyaWebClientUIViewModelTestCases
{
    [TestFixture]
    public class RedisCacheServiceTests
    {
        private Mock<IConnectionMultiplexer> _mockConnectionMultiplexer;
        private Mock<IDatabase> _mockDatabase;
        private Mock<ILogger<RedisCacheService>> _mockLogger;
        private Mock<IConfiguration> _mockConfiguration;
        private Mock<IStringLocalizer<TeyaUIViewModelsStrings>> _mockLocalizer;
        private RedisCacheService _redisCacheService;
        private readonly string _instanceName = "TestInstance:";

        [SetUp]
        public void Setup()
        {
            // Setup mock connection multiplexer
            _mockConnectionMultiplexer = new Mock<IConnectionMultiplexer>();

            // Setup mock database
            _mockDatabase = new Mock<IDatabase>();
            _mockConnectionMultiplexer.Setup(c => c.GetDatabase(It.IsAny<int>(), It.IsAny<object>()))
                .Returns(_mockDatabase.Object);

            // Setup mock logger
            _mockLogger = new Mock<ILogger<RedisCacheService>>();

            // Setup mock configuration
            _mockConfiguration = new Mock<IConfiguration>();
            _mockConfiguration.Setup(c => c["Redis:InstanceName"]).Returns(_instanceName);

            // Setup mock localizer
            _mockLocalizer = new Mock<IStringLocalizer<TeyaUIViewModelsStrings>>();
            _mockLocalizer.Setup(l => l["CacheHit"])
                .Returns(new LocalizedString("CacheHit", "Cache hit for key: {0}"));
            _mockLocalizer.Setup(l => l["CacheMiss"])
                .Returns(new LocalizedString("CacheMiss", "Cache miss for key: {0}"));
            _mockLocalizer.Setup(l => l["ErrorCache"])
                .Returns(new LocalizedString("ErrorCache", "Error accessing cache for key: {0}"));

            // Create RedisCacheService with mocked dependencies
            _redisCacheService = new RedisCacheService(_mockConnectionMultiplexer.Object, _mockLogger.Object, _mockConfiguration.Object, _mockLocalizer.Object);
        }

        [Test]
        public async Task GetOrSetAsync_WhenCacheHit_ReturnsFromCache()
        {
            // Arrange
            var key = "test-key";
            var fullKey = $"{_instanceName}{key}";
            var cachedData = new TestData { Id = 1, Name = "Cached Data" };
            var serializedData = JsonSerializer.Serialize(cachedData);

            _mockDatabase.Setup(db => db.StringGetAsync(fullKey, CommandFlags.None))
                .ReturnsAsync(new RedisValue(serializedData));

            Func<Task<TestData>> getDataFunc = () => Task.FromResult(new TestData { Id = 2, Name = "Fresh Data" });

            // Act
            var result = await _redisCacheService.GetOrSetAsync(key, getDataFunc);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Id, Is.EqualTo(cachedData.Id));
            Assert.That(result.Name, Is.EqualTo(cachedData.Name));

            // Verify cache was accessed but not set
            _mockDatabase.Verify(db => db.StringGetAsync(fullKey, CommandFlags.None), Times.AtLeastOnce);
            _mockDatabase.Verify(db => db.StringSetAsync(It.IsAny<RedisKey>(), It.IsAny<RedisValue>(), It.IsAny<TimeSpan?>(), It.IsAny<When>(), It.IsAny<CommandFlags>()), Times.Never);

            // Cache hit verification completed
        }

        [Test]
        public async Task GetOrSetAsync_WhenCacheMiss_CallsFunctionAndSetsCache()
        {
            // Arrange
            var key = "test-key";
            var fullKey = $"{_instanceName}{key}";
            var freshData = new TestData { Id = 2, Name = "Fresh Data" };
            var serializedData = JsonSerializer.Serialize(freshData);

            _mockDatabase.Setup(db => db.StringGetAsync(fullKey, CommandFlags.None))
                .ReturnsAsync(RedisValue.Null);

            _mockDatabase.Setup(db => db.StringSetAsync(fullKey, It.IsAny<RedisValue>(), TimeSpan.FromMinutes(30), false, When.Always, CommandFlags.None))
                .ReturnsAsync(true);

            Func<Task<TestData>> getDataFunc = () => Task.FromResult(freshData);

            // Act
            var result = await _redisCacheService.GetOrSetAsync(key, getDataFunc);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Id, Is.EqualTo(freshData.Id));
            Assert.That(result.Name, Is.EqualTo(freshData.Name));

            // Verify cache was accessed and set
            _mockDatabase.Verify(db => db.StringGetAsync(fullKey, CommandFlags.None), Times.AtLeastOnce);
            _mockDatabase.Verify(db => db.StringSetAsync(fullKey, It.IsAny<RedisValue>(), TimeSpan.FromMinutes(30), false, When.Always, CommandFlags.None), Times.AtLeastOnce);

            // Cache miss verification completed
        }

        [Test]
        public async Task GetOrSetAsync_WhenCacheMissAndDataIsNull_DoesNotSetCache()
        {
            // Arrange
            var key = "test-key";
            var fullKey = $"{_instanceName}{key}";

            _mockDatabase.Setup(db => db.StringGetAsync(fullKey, CommandFlags.None))
                .ReturnsAsync(RedisValue.Null);

            Func<Task<TestData>> getDataFunc = () => Task.FromResult<TestData>(null);

            // Act
            var result = await _redisCacheService.GetOrSetAsync(key, getDataFunc);

            // Assert
            Assert.That(result, Is.Null);

            // Verify cache was accessed but not set
            _mockDatabase.Verify(db => db.StringGetAsync(fullKey, CommandFlags.None), Times.AtLeastOnce);
            _mockDatabase.Verify(db => db.StringSetAsync(It.IsAny<RedisKey>(), It.IsAny<RedisValue>(), It.IsAny<TimeSpan?>(), It.IsAny<When>(), It.IsAny<CommandFlags>()), Times.Never);
        }

        [Test]
        public async Task GetOrSetAsync_WhenCustomExpirationTime_UsesThatExpiration()
        {
            // Arrange
            var key = "test-key";
            var fullKey = $"{_instanceName}{key}";
            var freshData = new TestData { Id = 3, Name = "Custom Expiration Data" };
            var serializedData = JsonSerializer.Serialize(freshData);
            var customExpiration = TimeSpan.FromHours(2);

            _mockDatabase.Setup(db => db.StringGetAsync(fullKey, CommandFlags.None))
                .ReturnsAsync(RedisValue.Null);

            _mockDatabase.Setup(db => db.StringSetAsync(fullKey, It.IsAny<RedisValue>(), customExpiration, false, When.Always, CommandFlags.None))
                .ReturnsAsync(true);

            Func<Task<TestData>> getDataFunc = () => Task.FromResult(freshData);

            // Act
            var result = await _redisCacheService.GetOrSetAsync(key, getDataFunc, customExpiration);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Id, Is.EqualTo(freshData.Id));
            Assert.That(result.Name, Is.EqualTo(freshData.Name));

            // Verify cache was set with custom expiration
            _mockDatabase.Verify(db => db.StringSetAsync(fullKey, It.IsAny<RedisValue>(), customExpiration, false, When.Always, CommandFlags.None), Times.AtLeastOnce);
        }

        [Test]
        public async Task GetOrSetAsync_WhenExceptionThrown_CallsFunctionAndReturnsData()
        {
            // Arrange
            var key = "test-key";
            var fullKey = $"{_instanceName}{key}";
            var freshData = new TestData { Id = 4, Name = "Exception Fallback Data" };
            var expectedException = new RedisException("Redis connection failed");

            _mockDatabase.Setup(db => db.StringGetAsync(fullKey, CommandFlags.None))
                .ThrowsAsync(expectedException);

            Func<Task<TestData>> getDataFunc = () => Task.FromResult(freshData);

            // Act
            var result = await _redisCacheService.GetOrSetAsync(key, getDataFunc);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Id, Is.EqualTo(freshData.Id));
            Assert.That(result.Name, Is.EqualTo(freshData.Name));

            // Error handling verification completed
        }

        [Test]
        public async Task GetOrSetAsync_WhenDeserializationFails_CallsFunctionAndReturnsData()
        {
            // Arrange
            var key = "test-key";
            var fullKey = $"{_instanceName}{key}";
            var freshData = new TestData { Id = 5, Name = "Deserialization Fallback Data" };
            var invalidJson = "invalid json data";

            _mockDatabase.Setup(db => db.StringGetAsync(fullKey, CommandFlags.None))
                .ReturnsAsync(new RedisValue(invalidJson));

            Func<Task<TestData>> getDataFunc = () => Task.FromResult(freshData);

            // Act
            var result = await _redisCacheService.GetOrSetAsync(key, getDataFunc);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Id, Is.EqualTo(freshData.Id));
            Assert.That(result.Name, Is.EqualTo(freshData.Name));

            // Error handling verification completed
        }

        [Test]
        public async Task GetOrSetAsync_WhenSetCacheFails_StillReturnsData()
        {
            // Arrange
            var key = "test-key";
            var fullKey = $"{_instanceName}{key}";
            var freshData = new TestData { Id = 6, Name = "Set Cache Fail Data" };
            var serializedData = JsonSerializer.Serialize(freshData);

            _mockDatabase.Setup(db => db.StringGetAsync(fullKey, CommandFlags.None))
                .ReturnsAsync(RedisValue.Null);

            _mockDatabase.Setup(db => db.StringSetAsync(fullKey, It.IsAny<RedisValue>(), TimeSpan.FromMinutes(30), false, When.Always, CommandFlags.None))
                .ThrowsAsync(new RedisException("Set operation failed"));

            Func<Task<TestData>> getDataFunc = () => Task.FromResult(freshData);

            // Act
            var result = await _redisCacheService.GetOrSetAsync(key, getDataFunc);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Id, Is.EqualTo(freshData.Id));
            Assert.That(result.Name, Is.EqualTo(freshData.Name));

            // Error handling verification completed
        }

        [Test]
        public async Task GetOrSetAsync_WhenGetDataFuncThrowsException_PropagatesException()
        {
            // Arrange
            var key = "test-key";
            var fullKey = $"{_instanceName}{key}";
            var expectedException = new InvalidOperationException("Data function failed");

            _mockDatabase.Setup(db => db.StringGetAsync(fullKey, CommandFlags.None))
                .ReturnsAsync(RedisValue.Null);

            Func<Task<TestData>> getDataFunc = () => throw expectedException;

            // Act & Assert
            var exception = Assert.ThrowsAsync<InvalidOperationException>(async () =>
                await _redisCacheService.GetOrSetAsync(key, getDataFunc));

            Assert.That(exception, Is.EqualTo(expectedException));
        }

        [Test]
        public async Task GetOrSetAsync_WhenComplexObject_SerializesAndDeserializesCorrectly()
        {
            // Arrange
            var key = "complex-object-key";
            var fullKey = $"{_instanceName}{key}";
            var complexData = new ComplexTestData
            {
                Id = 7,
                Name = "Complex Object",
                CreatedDate = DateTime.Now,
                Tags = new[] { "tag1", "tag2", "tag3" },
                Metadata = new { Property1 = "Value1", Property2 = 42 }
            };
            var serializedData = JsonSerializer.Serialize(complexData);

            _mockDatabase.Setup(db => db.StringGetAsync(fullKey, CommandFlags.None))
                .ReturnsAsync(RedisValue.Null);

            _mockDatabase.Setup(db => db.StringSetAsync(fullKey, It.IsAny<RedisValue>(), TimeSpan.FromMinutes(30), false, When.Always, CommandFlags.None))
                .ReturnsAsync(true);

            Func<Task<ComplexTestData>> getDataFunc = () => Task.FromResult(complexData);

            // Act
            var result = await _redisCacheService.GetOrSetAsync(key, getDataFunc);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Id, Is.EqualTo(complexData.Id));
            Assert.That(result.Name, Is.EqualTo(complexData.Name));
            Assert.That(result.Tags, Is.EqualTo(complexData.Tags));
        }
    }

    // Test data classes for testing purposes
    public class TestData
    {
        public int Id { get; set; }
        public string Name { get; set; }
    }

    public class ComplexTestData
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public DateTime CreatedDate { get; set; }
        public string[] Tags { get; set; }
        public object Metadata { get; set; }
    }
}


