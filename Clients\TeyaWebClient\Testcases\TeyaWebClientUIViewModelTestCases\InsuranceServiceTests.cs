using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Http;
using System.Net.Http.Json;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Moq;
using Moq.Protected;
using NUnit.Framework;
using TeyaUIModels.Model;
using TeyaUIViewModels.ViewModel;

namespace TeyaWebClientUIViewModelTestCases
{
    [TestFixture]
    public class InsuranceServiceTests
    {
        private Mock<HttpMessageHandler> _mockHttpMessageHandler;
        private Mock<ILogger<InsuranceService>> _mockLogger;
        private Mock<IStringLocalizer<InsuranceService>> _mockLocalizer;
        private HttpClient _httpClient;
        private InsuranceService _insuranceService;

        private const string TestMemberServiceUrl = "https://test-memberservice.com";

        [SetUp]
        public void SetUp()
        {
            _mockHttpMessageHandler = new Mock<HttpMessageHandler>();
            _mockLogger = new Mock<ILogger<InsuranceService>>();
            _mockLocalizer = new Mock<IStringLocalizer<InsuranceService>>();

            _httpClient = new HttpClient(_mockHttpMessageHandler.Object);

            // Set up environment variable
            Environment.SetEnvironmentVariable("MemberServiceURL", TestMemberServiceUrl);

            // Set up localizer mock responses
            _mockLocalizer.Setup(l => l["Insurance not found"]).Returns(new LocalizedString("Insurance not found", "Insurance not found"));
            _mockLocalizer.Setup(l => l["Error fetching insurance by ID"]).Returns(new LocalizedString("Error fetching insurance by ID", "Error fetching insurance by ID"));
            _mockLocalizer.Setup(l => l["Error fetching all insurances"]).Returns(new LocalizedString("Error fetching all insurances", "Error fetching all insurances"));
            _mockLocalizer.Setup(l => l["Error adding insurance"]).Returns(new LocalizedString("Error adding insurance", "Error adding insurance"));
            _mockLocalizer.Setup(l => l["Error updating insurance"]).Returns(new LocalizedString("Error updating insurance", "Error updating insurance"));
            _mockLocalizer.Setup(l => l["Error deleting insurance"]).Returns(new LocalizedString("Error deleting insurance", "Error deleting insurance"));

            _insuranceService = new InsuranceService(_httpClient, _mockLogger.Object, _mockLocalizer.Object);
        }

        [TearDown]
        public void TearDown()
        {
            _httpClient?.Dispose();
            Environment.SetEnvironmentVariable("MemberServiceURL", null);
        }

        private Insurance CreateTestInsurance()
        {
            return new Insurance
            {
                InsuranceId = Guid.NewGuid(),
                PrimaryInsuranceProvider = "Blue Cross Blue Shield",
                PlanName = "Premium Health Plan",
                Subscriber = "John Doe",
                EffectiveDate = DateTime.UtcNow.AddYears(-1),
                Relationship = "Self",
                PolicyNumber = "POL123456789",
                GroupNumber = "GRP987654321",
                SocialSecurityNumber = "***********",
                SubscriberEmployer = "Tech Corp Inc",
                Sex = "Male",
                SubscriberAddressLine1 = "123 Main Street",
                SubscriberAddressLine2 = "Apt 4B",
                SubscriberCity = "New York",
                SubscriberState = "NY",
                SubscriberZipCode = "10001",
                SubscriberCountry = "USA",
                SubscriberPhone = "************",
                CoPay = 25.00m,
                AcceptAssignment = true,
                OrganizationId = Guid.NewGuid(),
                Subscription = false
            };
        }

        [Test]
        public async Task GetInsuranceByIdAsync_WhenSuccessful_ReturnsInsurance()
        {
            // Arrange
            var insuranceId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = false;
            var expectedInsurance = CreateTestInsurance();
            expectedInsurance.InsuranceId = insuranceId;

            var responseContent = JsonSerializer.Serialize(expectedInsurance);
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(responseContent, Encoding.UTF8, "application/json")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _insuranceService.GetInsuranceByIdAsync(insuranceId, orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.InsuranceId, Is.EqualTo(insuranceId));
            Assert.That(result.PrimaryInsuranceProvider, Is.EqualTo("Blue Cross Blue Shield"));
            Assert.That(result.PolicyNumber, Is.EqualTo("POL123456789"));
        }

        [Test]
        public async Task GetInsuranceByIdAsync_WhenInsuranceNotFound_ThrowsInvalidOperationException()
        {
            // Arrange
            var insuranceId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = false;

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent("null", Encoding.UTF8, "application/json")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<InvalidOperationException>(async () =>
                await _insuranceService.GetInsuranceByIdAsync(insuranceId, orgId, subscription));

            Assert.That(exception.Message, Does.Contain("Insurance not found"));
        }

        [Test]
        public async Task GetInsuranceByIdAsync_WhenHttpRequestFails_ThrowsExceptionAndLogsError()
        {
            // Arrange
            var insuranceId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = false;

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ThrowsAsync(new HttpRequestException("Network error"));

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _insuranceService.GetInsuranceByIdAsync(insuranceId, orgId, subscription));

            Assert.That(exception.Message, Does.Contain("Network error"));

            // Verify logging
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("Error fetching insurance by ID")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception, string>>()),
                Times.AtLeastOnce);
        }

        [Test]
        public async Task GetAllInsurancesAsync_WhenSuccessful_ReturnsInsuranceList()
        {
            // Arrange
            var expectedInsurances = new List<Insurance>
            {
                CreateTestInsurance(),
                CreateTestInsurance()
            };

            var responseContent = JsonSerializer.Serialize(expectedInsurances);
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(responseContent, Encoding.UTF8, "application/json")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _insuranceService.GetAllInsurancesAsync();

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(2));
            Assert.That(result[0].PrimaryInsuranceProvider, Is.EqualTo("Blue Cross Blue Shield"));
        }

        [Test]
        public async Task GetAllInsurancesAsync_WhenNoInsurancesFound_ReturnsEmptyList()
        {
            // Arrange
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent("null", Encoding.UTF8, "application/json")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _insuranceService.GetAllInsurancesAsync();

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(0));
        }

        [Test]
        public async Task GetAllInsurancesAsync_WhenHttpRequestFails_ThrowsExceptionAndLogsError()
        {
            // Arrange
            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ThrowsAsync(new HttpRequestException("Network error"));

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _insuranceService.GetAllInsurancesAsync());

            Assert.That(exception.Message, Does.Contain("Network error"));

            // Verify logging
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("Error fetching all insurances")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception, string>>()),
                Times.AtLeastOnce);
        }

        [Test]
        public async Task AddInsuranceAsync_WhenSuccessful_ReturnsTrue()
        {
            // Arrange
            var insurance = CreateTestInsurance();

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.Created
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _insuranceService.AddInsuranceAsync(insurance);

            // Assert
            Assert.That(result, Is.True);
        }

        [Test]
        public async Task AddInsuranceAsync_WhenHttpRequestFails_ReturnsFalseAndLogsError()
        {
            // Arrange
            var insurance = CreateTestInsurance();

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _insuranceService.AddInsuranceAsync(insurance);

            // Assert
            Assert.That(result, Is.False);
        }

        [Test]
        public async Task AddInsuranceAsync_WhenExceptionThrown_ReturnsFalseAndLogsError()
        {
            // Arrange
            var insurance = CreateTestInsurance();

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ThrowsAsync(new HttpRequestException("Network error"));

            // Act
            var result = await _insuranceService.AddInsuranceAsync(insurance);

            // Assert
            Assert.That(result, Is.False);

            // Verify logging
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("Error adding insurance")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception, string>>()),
                Times.AtLeastOnce);
        }

        [Test]
        public async Task UpdateInsuranceAsync_WhenSuccessful_ReturnsTrue()
        {
            // Arrange
            var insuranceId = Guid.NewGuid();
            var insurance = CreateTestInsurance();

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _insuranceService.UpdateInsuranceAsync(insuranceId, insurance);

            // Assert
            Assert.That(result, Is.True);
        }

        [Test]
        public async Task UpdateInsuranceAsync_WhenHttpRequestFails_ReturnsFalseAndLogsError()
        {
            // Arrange
            var insuranceId = Guid.NewGuid();
            var insurance = CreateTestInsurance();

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ThrowsAsync(new HttpRequestException("Network error"));

            // Act
            var result = await _insuranceService.UpdateInsuranceAsync(insuranceId, insurance);

            // Assert
            Assert.That(result, Is.False);

            // Verify logging
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("Error updating insurance")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception, string>>()),
                Times.AtLeastOnce);
        }

        [Test]
        public async Task DeleteInsuranceAsync_WhenSuccessful_ReturnsTrue()
        {
            // Arrange
            var insuranceId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = false;

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.NoContent
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _insuranceService.DeleteInsuranceAsync(insuranceId, orgId, subscription);

            // Assert
            Assert.That(result, Is.True);
        }

        [Test]
        public async Task DeleteInsuranceAsync_WhenHttpRequestFails_ReturnsFalseAndLogsError()
        {
            // Arrange
            var insuranceId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = false;

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ThrowsAsync(new HttpRequestException("Network error"));

            // Act
            var result = await _insuranceService.DeleteInsuranceAsync(insuranceId, orgId, subscription);

            // Assert
            Assert.That(result, Is.False);

            // Verify logging
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("Error deleting insurance")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception, string>>()),
                Times.AtLeastOnce);
        }
    }
}



