﻿using Markdig;
using Microsoft.AspNetCore.Components;
using MudBlazor;
using System.Text.Json;
using TeyaHealthMobileModel.Model;
using TeyaHealthMobileViewModel.ViewModel;
using TeyaMobile.Shared.Services;

namespace TeyaMobile.Shared.Pages
{
    public partial class SoapNotes
    {
        [Parameter] public Guid? PatientId { get; set; }
        [SupplyParameterFromQuery(Name = "orgId")]
        public Guid? OrganizationId { get; set; }
        [SupplyParameterFromQuery(Name = "sub")]
        public bool Subscription { get; set; }
        [SupplyParameterFromQuery(Name = "pcpId")]
        public Guid? PCPId { get; set; }

        [Inject] private StorageContainer Storage { get; set; }
        [Inject] private IOrganizationService OrganizationService { get; set; }

        private List<SoapRecord> soapRecords = new();
        private HashSet<string> expandedSections = new();
        private List<PatientInfo> availablePatients = new();
        private PatientInfo? _selectedPatient;
        private Guid? SelectedPatientId;

        private PatientInfo? selectedPatient
        {
            get => _selectedPatient;
            set
            {
                if (_selectedPatient != value)
                {
                    _selectedPatient = value;
                    _ = OnPatientSelectionChanged();
                }
            }
        }

        private bool isLoading = false;
        private bool isLoadingNotes = false;
        private bool isLoadingPatients = false;
        private bool _disposed = false;
        private string currentPatientName = "";

        // Check if this is provider view (no PatientId in route)
        private bool IsProviderView => !PatientId.HasValue;

        // Add Markdig pipeline for advanced markdown processing
        private static readonly MarkdownPipeline Pipeline = new MarkdownPipelineBuilder()
            .UseAdvancedExtensions()
            .Build();

        // Platform detection
        private bool IsWeb => OperatingSystem.IsBrowser();

        [Inject] private ActiveUser user { get; set; }

        public class SoapRecord
        {
            public Guid Id { get; set; }
            public Guid OrganizationId { get; set; }
            public Guid PCPId { get; set; }
            public Guid PatientId { get; set; }
            public string? PatientName { get; set; }
            public DateTime DateTime { get; set; }
            public string? Notes { get; set; }
            public string? Transcription { get; set; }
            public bool? isEditable { get; set; }
            public bool Subscription { get; set; }
            public Dictionary<string, Dictionary<string, string>>? ParsedNotes { get; set; }
            public Dictionary<string, Dictionary<string, MarkupString>>? ProcessedNotes { get; set; }
        }

        public class PatientInfo
        {
            public Guid PatientId { get; set; }
            public string? PatientName { get; set; }
        }

        protected override async Task OnInitializedAsync()
        {
            if (OrganizationId == null || OrganizationId == Guid.Empty)
            {
                // Try to load from storage if not provided
                OrganizationId = Storage.OrganizationId;
                if (OrganizationId == Guid.Empty)
                {
                    OrganizationId = await OrganizationService.GetOrganizationIdByNameAsync(user.OrganizationName);
                }
            }

            if (!PatientId.HasValue)
            {
                await LoadAvailablePatients();
            }
            else
            {
                await LoadSoapNotes();
            }
        }

        // Helper method to check if patient name is valid
        private bool IsValidPatientName(string? patientName)
        {
            if (string.IsNullOrWhiteSpace(patientName))
                return false;

            // List of placeholder texts to exclude
            var invalidPatientNames = new[]
            {
                "The patient name is not mentioned in the text provided.",
                "Patient name not mentioned",
                "No patient name provided",
                "Unknown patient",
                "Patient name unavailable",
                "Name not specified",
                "Not mentioned",
                "N/A",
                "NULL",
                "undefined"
            };

            // Check if the patient name contains any of the invalid patterns
            return !invalidPatientNames.Any(invalid =>
                patientName.Contains(invalid, StringComparison.OrdinalIgnoreCase));
        }

        private async Task LoadAvailablePatients()
        {
            if (_disposed || !PCPId.HasValue) return;

            try
            {
                isLoadingPatients = true;
                await InvokeAsync(StateHasChanged);

                var records = await NotesService.GetRecordsByPCPIdAsync(PCPId.Value, OrganizationId, Subscription);

                availablePatients = records
                    .Where(r => r.PatientId != Guid.Empty &&
                               !string.IsNullOrEmpty(r.PatientName) &&
                               IsValidPatientName(r.PatientName))
                    .GroupBy(r => r.PatientId)
                    .Select(g => new PatientInfo
                    {
                        PatientId = g.Key,
                        PatientName = g.First().PatientName
                    })
                    .OrderBy(p => p.PatientName)
                    .ToList();

                Console.WriteLine($"Loaded {availablePatients.Count} patients");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error loading patients: {ex.Message}");
                availablePatients = new List<PatientInfo>();
            }
            finally
            {
                isLoadingPatients = false;
                await InvokeAsync(StateHasChanged);
            }
        }

        // Search function for MudAutocomplete - MUST include CancellationToken
        private async Task<IEnumerable<PatientInfo>> SearchPatients(string searchTerm, CancellationToken cancellationToken)
        {
            // Check for cancellation
            if (cancellationToken.IsCancellationRequested)
                return Enumerable.Empty<PatientInfo>();

            // Return empty if no search term
            if (string.IsNullOrWhiteSpace(searchTerm))
                return availablePatients?.Take(10) ?? Enumerable.Empty<PatientInfo>();

            // Filter patients based on search term
            var filteredPatients = availablePatients?
                .Where(p => p.PatientName?.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) == true)
                .Take(10) ?? Enumerable.Empty<PatientInfo>();

            return await Task.FromResult(filteredPatients);
        }

        // Handle patient selection changes
        private async Task OnPatientSelectionChanged()
        {
            if (selectedPatient != null)
            {
                currentPatientName = selectedPatient.PatientName ?? "";
                await LoadSoapNotesForPatient(selectedPatient.PatientId);
            }
            else
            {
                soapRecords.Clear();
                currentPatientName = "";
                await InvokeAsync(StateHasChanged);
            }
        }

        private async Task LoadSoapNotesForPatient(Guid patientId)
        {
            if (_disposed) return;

            try
            {
                isLoading = true;
                await InvokeAsync(StateHasChanged);

                var records = await NotesService.GetRecordsByPatientIdAsync(patientId, OrganizationId, Subscription);
                ProcessRecords(records);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error loading patient notes: {ex.Message}");
            }
            finally
            {
                isLoading = false;
                await InvokeAsync(StateHasChanged);
            }
        }

        private async Task LoadSoapNotes()
        {
            if (_disposed) return;

            isLoading = true;
            StateHasChanged();

            try
            {
                List<Record> records = new();

                if (PatientId.HasValue)
                {
                    // Load for specific patient (from audio recorder)
                    records = await NotesService.GetRecordsByPatientIdAsync(PatientId.Value, OrganizationId, Subscription);
                }
                else if (selectedPatient?.PatientId != null)
                {
                    // Load for selected patient in provider view
                    records = await NotesService.GetRecordsByPatientIdAsync(selectedPatient.PatientId, OrganizationId, Subscription);
                }

                ProcessRecords(records);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error loading notes: {ex.Message}");
            }
            finally
            {
                isLoading = false;
                StateHasChanged();
            }
        }

        private void ProcessRecords(List<Record> records)
        {
            soapRecords = records
                .Where(r => !string.IsNullOrEmpty(r.Notes))
                .Select(r => new SoapRecord
                {
                    Id = r.Id,
                    OrganizationId = r.OrganizationId,
                    PCPId = r.PCPId,
                    PatientId = r.PatientId,
                    PatientName = r.PatientName,
                    DateTime = r.DateTime,
                    Notes = r.Notes,
                    Transcription = r.Transcription,
                    isEditable = r.isEditable,
                    Subscription = r.Subscription,
                    ParsedNotes = ParseNotesJson(r.Notes),
                    ProcessedNotes = ProcessMarkdownNotes(ParseNotesJson(r.Notes))
                })
                .OrderByDescending(r => r.DateTime)
                .ToList();

            if (soapRecords.Any() && !string.IsNullOrEmpty(soapRecords.First().PatientName))
            {
                currentPatientName = soapRecords.First().PatientName;
            }

            // Expand sections that have actual content
            expandedSections.Clear();
            foreach (var record in soapRecords)
            {
                if (record.ProcessedNotes?.Any() == true)
                {
                    foreach (var section in record.ProcessedNotes)
                    {
                        if (HasValidContent(section.Value))
                        {
                            expandedSections.Add($"{record.Id}_{section.Key}");
                        }
                    }
                }
            }
        }

        private Dictionary<string, Dictionary<string, string>>? ParseNotesJson(string? notesJson)
        {
            try
            {
                if (string.IsNullOrEmpty(notesJson))
                    return null;

                return JsonSerializer.Deserialize<Dictionary<string, Dictionary<string, string>>>(notesJson);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error parsing notes JSON: {ex.Message}");
                return null;
            }
        }

        private Dictionary<string, Dictionary<string, MarkupString>>? ProcessMarkdownNotes(
            Dictionary<string, Dictionary<string, string>>? parsedNotes)
        {
            if (parsedNotes == null)
                return null;

            var processedNotes = new Dictionary<string, Dictionary<string, MarkupString>>();

            foreach (var section in parsedNotes)
            {
                var processedSection = new Dictionary<string, MarkupString>();

                foreach (var field in section.Value)
                {
                    var cleanedContent = CleanContent(field.Value);

                    if (!string.IsNullOrEmpty(cleanedContent))
                    {
                        var htmlContent = Markdown.ToHtml(cleanedContent, Pipeline);
                        processedSection[field.Key] = new MarkupString(htmlContent);
                    }
                }

                if (processedSection.Any())
                {
                    processedNotes[section.Key] = processedSection;
                }
            }

            return processedNotes;
        }

        private string CleanContent(string? content)
        {
            if (string.IsNullOrEmpty(content))
                return string.Empty;

            // Remove 'Manual Content' (case insensitive)
            var cleaned = content.Replace("Manual Content", "", StringComparison.OrdinalIgnoreCase);

            // Remove double quotes at the beginning and end
            cleaned = cleaned.Trim('"', '\'', ' ');

            // Remove empty quotes patterns like "", '', " ", etc.
            cleaned = System.Text.RegularExpressions.Regex.Replace(cleaned, @"^[""'\s]*$", "");
            cleaned = System.Text.RegularExpressions.Regex.Replace(cleaned, @"[""']{2,}", "");

            return cleaned.Trim();
        }

        private bool HasValidContent(Dictionary<string, MarkupString> sectionContent)
        {
            return sectionContent.Any(kvp =>
                !string.IsNullOrEmpty(kvp.Value.Value) &&
                !string.IsNullOrWhiteSpace(kvp.Value.Value) &&
                kvp.Value.Value != "<p></p>" &&
                !kvp.Value.Value.Contains("No data available"));
        }

        private void ToggleSection(Guid recordId, string sectionKey)
        {
            var key = $"{recordId}_{sectionKey}";

            if (expandedSections.Contains(key))
            {
                expandedSections.Remove(key);
            }
            else
            {
                expandedSections.Add(key);
            }
        }

        private bool IsSectionExpanded(Guid recordId, string sectionKey)
        {
            var key = $"{recordId}_{sectionKey}";
            return expandedSections.Contains(key);
        }

        public void Dispose()
        {
            _disposed = true;
        }
    }
}
