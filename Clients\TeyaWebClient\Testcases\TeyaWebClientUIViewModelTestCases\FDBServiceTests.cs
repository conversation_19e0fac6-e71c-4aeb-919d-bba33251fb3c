using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using Moq;
using Moq.Protected;
using NUnit.Framework;
using TeyaUIModels.Model;
using TeyaUIViewModels.ViewModel;
using TeyaUIViewModels.TeyaUIViewModelResources;
using System.Net.Http.Json;
using System.Text;

namespace TeyaWebClientUIViewModelTestCases
{
    [TestFixture]
    public class FDBServiceTests
    {
        private Mock<HttpMessageHandler> _mockHttpMessageHandler;
        private HttpClient _httpClient;
        private Mock<IConfiguration> _mockConfiguration;
        private Mock<IStringLocalizer<TeyaUIViewModelsStrings>> _mockLocalizer;
        private Mock<ITokenService> _mockTokenService;
        private FDBService _fdbService;
        private readonly string _baseUrl = "http://test-api.com";
        private readonly string _accessToken = "test-token";

        [SetUp]
        public void Setup()
        {
            // Setup environment variable
            Environment.SetEnvironmentVariable("EncounterNotesURL", _baseUrl);

            // Setup mock HTTP handler
            _mockHttpMessageHandler = new Mock<HttpMessageHandler>();

            // Setup HttpClient with mock handler
            _httpClient = new HttpClient(_mockHttpMessageHandler.Object)
            {
                BaseAddress = new Uri(_baseUrl)
            };

            // Setup mock configuration
            _mockConfiguration = new Mock<IConfiguration>();

            // Setup mock localizer
            _mockLocalizer = new Mock<IStringLocalizer<TeyaUIViewModelsStrings>>();
            _mockLocalizer.Setup(l => l["AddressRetrievalFailure"])
                .Returns(new LocalizedString("AddressRetrievalFailure", "Failed to retrieve address"));
            _mockLocalizer.Setup(l => l["DatabaseError"])
                .Returns(new LocalizedString("DatabaseError", "Database error"));

            // Setup mock token service
            _mockTokenService = new Mock<ITokenService>();
            _mockTokenService.Setup(t => t.AccessToken).Returns(_accessToken);

            // Create FDBService with mocked dependencies
            _fdbService = new FDBService(_httpClient, _mockConfiguration.Object, _mockLocalizer.Object, _mockTokenService.Object);
        }

        [TearDown]
        public void TearDown()
        {
            _httpClient.Dispose();
            Environment.SetEnvironmentVariable("EncounterNotesURL", null);
        }

        [Test]
        public async Task GetAllFDBMedications_WhenSuccessful_ReturnsMedications()
        {
            // Arrange
            var expectedMedications = new List<FDBMedicationName>
            {
                new FDBMedicationName
                {
                    MED_NAME_ID = "123",
                    MED_NAME = "Aspirin"
                },
                new FDBMedicationName
                {
                    MED_NAME_ID = "456",
                    MED_NAME = "Ibuprofen"
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedMedications)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _fdbService.GetAllFDBMedications();

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(2));
            Assert.That(result[0].MED_NAME, Is.EqualTo("Aspirin"));
            Assert.That(result[1].MED_NAME, Is.EqualTo("Ibuprofen"));
        }

        [Test]
        public void GetAllFDBMedications_WhenHttpRequestFails_ThrowsException()
        {
            // Arrange
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _fdbService.GetAllFDBMedications());

            Assert.That(exception.Message, Is.EqualTo("Failed to retrieve address"));
        }

        [Test]
        public async Task GetFDBRoutedMedications_WhenSuccessful_ReturnsRoutedMedications()
        {
            // Arrange
            var medNameId = "123";
            var expectedMedications = new List<FDBRoutedMedication>
            {
                new FDBRoutedMedication
                {
                    ROUTED_MED_ID = "R123",
                    MED_NAME_ID = medNameId,
                    MED_ROUTED_MED_ID_DESC = "Oral"
                },
                new FDBRoutedMedication
                {
                    ROUTED_MED_ID = "R456",
                    MED_NAME_ID = medNameId,
                    MED_ROUTED_MED_ID_DESC = "Topical"
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedMedications)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _fdbService.GetFDBRoutedMedications(medNameId);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(2));
            Assert.That(result[0].MED_ROUTED_MED_ID_DESC, Is.EqualTo("Oral"));
            Assert.That(result[1].MED_ROUTED_MED_ID_DESC, Is.EqualTo("Topical"));
        }

        [Test]
        public void GetFDBRoutedMedications_WhenHttpRequestFails_ThrowsException()
        {
            // Arrange
            var medNameId = "123";
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _fdbService.GetFDBRoutedMedications(medNameId));

            Assert.That(exception.Message, Is.EqualTo("Failed to retrieve address"));
        }

        [Test]
        public async Task GetFDBRoutedDosageFormMedications_WhenSuccessful_ReturnsRoutedDosageFormMedications()
        {
            // Arrange
            var routedMedId = "R123";
            var expectedMedications = new List<FDBRoutedDosageFormMedication>
            {
                new FDBRoutedDosageFormMedication
                {
                    ROUTED_DOSAGE_FORM_MED_ID = "RD123",
                    ROUTED_MED_ID = routedMedId,
                    MED_ROUTED_DF_MED_ID_DESC = "Tablet"
                },
                new FDBRoutedDosageFormMedication
                {
                    ROUTED_DOSAGE_FORM_MED_ID = "RD456",
                    ROUTED_MED_ID = routedMedId,
                    MED_ROUTED_DF_MED_ID_DESC = "Capsule"
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedMedications)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _fdbService.GetFDBRoutedDosageFormMedications(routedMedId);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(2));
            Assert.That(result[0].MED_ROUTED_DF_MED_ID_DESC, Is.EqualTo("Tablet"));
            Assert.That(result[1].MED_ROUTED_DF_MED_ID_DESC, Is.EqualTo("Capsule"));
        }

        [Test]
        public void GetFDBRoutedDosageFormMedications_WhenHttpRequestFails_ThrowsException()
        {
            // Arrange
            var routedMedId = "R123";
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _fdbService.GetFDBRoutedDosageFormMedications(routedMedId));

            Assert.That(exception.Message, Is.EqualTo("Failed to retrieve address"));
        }

        [Test]
        public async Task GetFDBFinalMedications_WhenSuccessful_ReturnsFinalMedications()
        {
            // Arrange
            var routedDosageFormMedId = "RD123";
            var expectedMedications = new List<FDBMedication>
            {
                new FDBMedication
                {
                    MEDID = "M123",
                    ROUTED_DOSAGE_FORM_MED_ID = routedDosageFormMedId,
                    MED_MEDID_DESC = "Aspirin 100mg Tablet",
                    MED_STRENGTH = "100mg"
                },
                new FDBMedication
                {
                    MEDID = "M456",
                    ROUTED_DOSAGE_FORM_MED_ID = routedDosageFormMedId,
                    MED_MEDID_DESC = "Aspirin 200mg Tablet",
                    MED_STRENGTH = "200mg"
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedMedications)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _fdbService.GetFDBFinalMedications(routedDosageFormMedId);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(2));
            Assert.That(result[0].MED_MEDID_DESC, Is.EqualTo("Aspirin 100mg Tablet"));
            Assert.That(result[1].MED_MEDID_DESC, Is.EqualTo("Aspirin 200mg Tablet"));
        }

        [Test]
        public async Task GetFDBRouteLookUp_WhenSuccessful_ReturnsRouteLookups()
        {
            // Arrange
            var expectedRoutes = new List<FDBRouteLookUp>
            {
                new FDBRouteLookUp
                {
                    MED_ROUTE_ID = "1",
                    Route_Name = "Oral"
                },
                new FDBRouteLookUp
                {
                    MED_ROUTE_ID = "2",
                    Route_Name = "Topical"
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedRoutes)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _fdbService.GetFDBRouteLookUp();

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(2));
            Assert.That(result[0].Route_Name, Is.EqualTo("Oral"));
            Assert.That(result[1].Route_Name, Is.EqualTo("Topical"));
        }

        [Test]
        public async Task GetFDBTakeLookUp_WhenSuccessful_ReturnsTakeLookups()
        {
            // Arrange
            var expectedTakes = new List<FDBTakeLookUp>
            {
                new FDBTakeLookUp
                {
                    MED_DOSAGE_FORM_ID = "1",
                    Take_Name = "Once daily"
                },
                new FDBTakeLookUp
                {
                    MED_DOSAGE_FORM_ID = "2",
                    Take_Name = "Twice daily"
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedTakes)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _fdbService.GetFDBTakeLookUp();

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(2));
            Assert.That(result[0].Take_Name, Is.EqualTo("Once daily"));
            Assert.That(result[1].Take_Name, Is.EqualTo("Twice daily"));
        }

        [Test]
        public async Task GetAllergies_WhenSuccessful_ReturnsAllergies()
        {
            // Arrange
            var expectedAllergies = new List<FDBAllergies>
            {
                new FDBAllergies
                {
                    DAM_CONCEPT_ID = "1",
                    DAM_CONCEPT_ID_DESC = "Penicillin"
                },
                new FDBAllergies
                {
                    DAM_CONCEPT_ID = "2",
                    DAM_CONCEPT_ID_DESC = "Sulfa"
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedAllergies)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _fdbService.GetAllergies();

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(2));
            Assert.That(result[0].DAM_CONCEPT_ID_DESC, Is.EqualTo("Penicillin"));
            Assert.That(result[1].DAM_CONCEPT_ID_DESC, Is.EqualTo("Sulfa"));
        }

        [Test]
        public async Task GetICD_WhenSuccessful_ReturnsICDCodes()
        {
            // Arrange
            var expectedICDs = new List<FDB_ICD>
            {
                new FDB_ICD
                {
                    ICD_CD = "J01",
                    ICD_DESC = "Acute sinusitis"
                },
                new FDB_ICD
                {
                    ICD_CD = "J02",
                    ICD_DESC = "Acute pharyngitis"
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedICDs)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _fdbService.GetICD();

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(2));
            Assert.That(result[0].ICD_DESC, Is.EqualTo("Acute sinusitis"));
            Assert.That(result[1].ICD_DESC, Is.EqualTo("Acute pharyngitis"));
        }

        [Test]
        public async Task GetVaccines_WhenSuccessful_ReturnsVaccines()
        {
            // Arrange
            var expectedVaccines = new List<FDBVaccines>
            {
                new FDBVaccines
                {
                    EVD_CVX_CD = "03",
                    EVD_CVX_CD_DESC_SHORT = "MMR"
                },
                new FDBVaccines
                {
                    EVD_CVX_CD = "21",
                    EVD_CVX_CD_DESC_SHORT = "Varicella"
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedVaccines)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _fdbService.GetVaccines();

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(2));
            Assert.That(result[0].EVD_CVX_CD_DESC_SHORT, Is.EqualTo("MMR"));
            Assert.That(result[1].EVD_CVX_CD_DESC_SHORT, Is.EqualTo("Varicella"));
        }

        [Test]
        public async Task GetCPTForVaccine_WhenSuccessful_ReturnsCPTCode()
        {
            // Arrange
            var cvx = "03";
            var expectedVaccineCPT = new FDBVaccine_CPT_CVX
            {
                EVD_CVX_CD = cvx,
                EVD_CPT_CD = "90707",
                EVD_VACCINE_NAME = "MMR"
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedVaccineCPT)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _fdbService.GetCPTForVaccine(cvx);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.EVD_CPT_CD, Is.EqualTo("90707"));
            Assert.That(result.EVD_VACCINE_NAME, Is.EqualTo("MMR"));
        }
    }
}



