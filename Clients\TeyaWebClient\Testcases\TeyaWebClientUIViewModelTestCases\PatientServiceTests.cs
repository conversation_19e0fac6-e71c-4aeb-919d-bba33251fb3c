using System;
using NUnit.Framework;
using TeyaUIModels.Model;
using TeyaUIViewModels.ViewModel;

namespace TeyaWebClientUIViewModelTestCases
{
    [TestFixture]
    public class PatientServiceTests
    {
        private PatientService _patientService;

        [SetUp]
        public void Setup()
        {
            _patientService = new PatientService();
        }

        [Test]
        public void PatientData_WhenSet_ReturnsCorrectValue()
        {
            // Arrange
            var expectedPatient = new Patient
            {
                Id = Guid.NewGuid(),
                Name = "<PERSON>",
                DOB = new DateTime(1990, 1, 1),
                Sex = "Male",
                Email = "<EMAIL>",
                PhoneNumber = "************",
                OrganizationID = Guid.NewGuid(),
                PCPName = "Dr. <PERSON>"
            };

            // Act
            _patientService.PatientData = expectedPatient;

            // Assert
            Assert.That(_patientService.PatientData, Is.Not.Null);
            Assert.That(_patientService.PatientData.Id, Is.EqualTo(expectedPatient.Id));
            Assert.That(_patientService.PatientData.Name, Is.EqualTo(expectedPatient.Name));
            Assert.That(_patientService.PatientData.DOB, Is.EqualTo(expectedPatient.DOB));
            Assert.That(_patientService.PatientData.Sex, Is.EqualTo(expectedPatient.Sex));
            Assert.That(_patientService.PatientData.Email, Is.EqualTo(expectedPatient.Email));
            Assert.That(_patientService.PatientData.PhoneNumber, Is.EqualTo(expectedPatient.PhoneNumber));
            Assert.That(_patientService.PatientData.OrganizationID, Is.EqualTo(expectedPatient.OrganizationID));
            Assert.That(_patientService.PatientData.PCPName, Is.EqualTo(expectedPatient.PCPName));
        }

        [Test]
        public void PatientData_WhenSetToNull_ReturnsNull()
        {
            // Arrange
            _patientService.PatientData = new Patient { Id = Guid.NewGuid() };

            // Act
            _patientService.PatientData = null;

            // Assert
            Assert.That(_patientService.PatientData, Is.Null);
        }

        [Test]
        public void VisitStatus_WhenSet_ReturnsCorrectValue()
        {
            // Arrange
            var expectedVisitStatus = "In Progress";

            // Act
            _patientService.VisitStatus = expectedVisitStatus;

            // Assert
            Assert.That(_patientService.VisitStatus, Is.EqualTo(expectedVisitStatus));
        }

        [Test]
        public void VisitStatus_WhenSetToNull_ReturnsNull()
        {
            // Arrange
            _patientService.VisitStatus = "Completed";

            // Act
            _patientService.VisitStatus = null;

            // Assert
            Assert.That(_patientService.VisitStatus, Is.Null);
        }

        [Test]
        public void VisitStatus_WhenSetToEmptyString_ReturnsEmptyString()
        {
            // Arrange
            _patientService.VisitStatus = "Scheduled";

            // Act
            _patientService.VisitStatus = "";

            // Assert
            Assert.That(_patientService.VisitStatus, Is.EqualTo(""));
        }

        [Test]
        public void VisitType_WhenSet_ReturnsCorrectValue()
        {
            // Arrange
            var expectedVisitType = "Consultation";

            // Act
            _patientService.VisitType = expectedVisitType;

            // Assert
            Assert.That(_patientService.VisitType, Is.EqualTo(expectedVisitType));
        }

        [Test]
        public void VisitType_WhenSetToNull_ReturnsNull()
        {
            // Arrange
            _patientService.VisitType = "Follow-up";

            // Act
            _patientService.VisitType = null;

            // Assert
            Assert.That(_patientService.VisitType, Is.Null);
        }

        [Test]
        public void VisitType_WhenSetToEmptyString_ReturnsEmptyString()
        {
            // Arrange
            _patientService.VisitType = "Emergency";

            // Act
            _patientService.VisitType = "";

            // Assert
            Assert.That(_patientService.VisitType, Is.EqualTo(""));
        }

        [Test]
        public void PatientService_WhenInitialized_PropertiesAreNull()
        {
            // Arrange & Act
            var newPatientService = new PatientService();

            // Assert
            Assert.That(newPatientService.PatientData, Is.Null);
            Assert.That(newPatientService.VisitStatus, Is.Null);
            Assert.That(newPatientService.VisitType, Is.Null);
        }

        [Test]
        public void PatientService_WhenMultiplePropertiesSet_AllReturnCorrectValues()
        {
            // Arrange
            var expectedPatient = new Patient
            {
                Id = Guid.NewGuid(),
                Name = "Jane Smith",
                DOB = new DateTime(1985, 5, 15),
                Sex = "Female",
                Email = "<EMAIL>",
                PhoneNumber = "************",
                OrganizationID = Guid.NewGuid(),
                PCPName = "Dr. Johnson"
            };
            var expectedVisitStatus = "Completed";
            var expectedVisitType = "Annual Checkup";

            // Act
            _patientService.PatientData = expectedPatient;
            _patientService.VisitStatus = expectedVisitStatus;
            _patientService.VisitType = expectedVisitType;

            // Assert
            Assert.That(_patientService.PatientData, Is.EqualTo(expectedPatient));
            Assert.That(_patientService.VisitStatus, Is.EqualTo(expectedVisitStatus));
            Assert.That(_patientService.VisitType, Is.EqualTo(expectedVisitType));
        }

        [Test]
        public void PatientService_WhenPropertiesUpdatedMultipleTimes_ReturnsLatestValues()
        {
            // Arrange
            var firstPatient = new Patient { Id = Guid.NewGuid(), Name = "First Patient" };
            var secondPatient = new Patient { Id = Guid.NewGuid(), Name = "Second Patient" };
            var firstVisitStatus = "Initial";
            var secondVisitStatus = "Updated";
            var firstVisitType = "Type1";
            var secondVisitType = "Type2";

            // Act
            _patientService.PatientData = firstPatient;
            _patientService.VisitStatus = firstVisitStatus;
            _patientService.VisitType = firstVisitType;

            _patientService.PatientData = secondPatient;
            _patientService.VisitStatus = secondVisitStatus;
            _patientService.VisitType = secondVisitType;

            // Assert
            Assert.That(_patientService.PatientData, Is.EqualTo(secondPatient));
            Assert.That(_patientService.VisitStatus, Is.EqualTo(secondVisitStatus));
            Assert.That(_patientService.VisitType, Is.EqualTo(secondVisitType));
        }
    }
}


