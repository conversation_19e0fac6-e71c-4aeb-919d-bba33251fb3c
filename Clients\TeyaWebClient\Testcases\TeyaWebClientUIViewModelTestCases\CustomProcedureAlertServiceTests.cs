using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using Moq;
using Moq.Protected;
using NUnit.Framework;
using TeyaUIModels.Model;
using TeyaUIViewModels.ViewModel;
using TeyaUIViewModels.TeyaUIViewModelResources;
using System.Net.Http.Json;
using System.Text;

namespace TeyaWebClientUIViewModelTestCases
{
    [TestFixture]
    public class CustomProcedureAlertServiceTests
    {
        private Mock<HttpMessageHandler> _mockHttpMessageHandler;
        private HttpClient _httpClient;
        private Mock<IConfiguration> _mockConfiguration;
        private Mock<IStringLocalizer<TeyaUIViewModelsStrings>> _mockLocalizer;
        private Mock<ITokenService> _mockTokenService;
        private CustomProcedureAlertService _customProcedureAlertService;
        private readonly string _baseUrl = "http://test-api.com";
        private readonly string _accessToken = "test-token";

        [SetUp]
        public void Setup()
        {
            // Setup environment variable
            Environment.SetEnvironmentVariable("AlertsServiceURL", _baseUrl);

            // Setup mock HTTP handler
            _mockHttpMessageHandler = new Mock<HttpMessageHandler>();

            // Setup HttpClient with mock handler
            _httpClient = new HttpClient(_mockHttpMessageHandler.Object)
            {
                BaseAddress = new Uri(_baseUrl)
            };

            // Setup mock configuration
            _mockConfiguration = new Mock<IConfiguration>();

            // Setup mock localizer
            _mockLocalizer = new Mock<IStringLocalizer<TeyaUIViewModelsStrings>>();
            _mockLocalizer.Setup(l => l["AddressRetrievalFailure"])
                .Returns(new LocalizedString("AddressRetrievalFailure", "Failed to retrieve address"));
            _mockLocalizer.Setup(l => l["DatabaseError"])
                .Returns(new LocalizedString("DatabaseError", "Database error"));

            // Setup mock token service
            _mockTokenService = new Mock<ITokenService>();
            _mockTokenService.Setup(t => t.AccessToken).Returns(_accessToken);

            // Create CustomProcedureAlertService with mocked dependencies
            _customProcedureAlertService = new CustomProcedureAlertService(_httpClient, _mockConfiguration.Object, _mockLocalizer.Object, _mockTokenService.Object);
        }

        [TearDown]
        public void TearDown()
        {
            _httpClient.Dispose();
            Environment.SetEnvironmentVariable("AlertsServiceURL", null);
        }

        [Test]
        public async Task GetAllByIdAsync_WhenSuccessful_ReturnsAlerts()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var expectedAlerts = new List<CustomProcedureAlerts>
            {
                new CustomProcedureAlerts
                {
                    Id = Guid.NewGuid(),
                    Name = "Colonoscopy",
                    Description = "Routine colonoscopy",
                    IsActive = true
                },
                new CustomProcedureAlerts
                {
                    Id = Guid.NewGuid(),
                    Name = "Mammogram",
                    Description = "Annual mammogram",
                    IsActive = false
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedAlerts)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _customProcedureAlertService.GetAllByIdAsync(patientId, orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(2));
            Assert.That(result[0].Name, Is.EqualTo("Colonoscopy"));
            Assert.That(result[1].Name, Is.EqualTo("Mammogram"));
        }

        [Test]
        public void GetAllByIdAsync_WhenHttpRequestFails_ThrowsException()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _customProcedureAlertService.GetAllByIdAsync(patientId, orgId, subscription));
        }

        [Test]
        public async Task GetAllByIdAndIsActiveAsync_WhenSuccessful_ReturnsActiveAlerts()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var expectedAlerts = new List<CustomProcedureAlerts>
            {
                new CustomProcedureAlerts
                {
                    Id = Guid.NewGuid(),
                    Name = "Colonoscopy",
                    Description = "Routine colonoscopy",
                    IsActive = true
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedAlerts)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _customProcedureAlertService.GetAllByIdAsync(patientId, orgId, subscription); 

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(1));
            Assert.That(result[0].Name, Is.EqualTo("Colonoscopy"));
            Assert.That(result[0].IsActive, Is.True);
        }

        [Test]
        public void GetAllByIdAndIsActiveAsync_WhenHttpRequestFails_ThrowsException()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _customProcedureAlertService.GetAllByIdAsync(patientId, orgId, subscription));
        }

        [Test]
        public async Task AddCustomProcedureAlertsAsync_WhenSuccessful_CompletesWithoutException()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var alerts = new List<CustomProcedureAlerts>
            {
                new CustomProcedureAlerts
                {
                    Id = Guid.NewGuid(),
                    Name = "Colonoscopy",
                    Description = "Routine colonoscopy",
                    IsActive = true
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            // No exception should be thrown
            await _customProcedureAlertService.AddCustomProcedureAlertsAsync(alerts, orgId, subscription);
        }

        [Test]
        public void AddCustomProcedureAlertsAsync_WhenHttpRequestFails_ThrowsException()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var alerts = new List<CustomProcedureAlerts>
            {
                new CustomProcedureAlerts
                {
                    Id = Guid.NewGuid(),
                    Name = "Colonoscopy",
                    Description = "Routine colonoscopy",
                    IsActive = true
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _customProcedureAlertService.AddCustomProcedureAlertsAsync(alerts, orgId, subscription));
        }

        [Test]
        public async Task UpdateCustomProcedureAlertAsync_WhenSuccessful_CompletesWithoutException()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var alert = new CustomProcedureAlerts
            {
                Id = Guid.NewGuid(),
                Name = "Colonoscopy",
                Description = "Routine colonoscopy",
                IsActive = true
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            // No exception should be thrown
            await _customProcedureAlertService.UpdateCustomProcedureAlertAsync(alert, orgId, subscription);
        }

        [Test]
        public void UpdateCustomProcedureAlertAsync_WhenHttpRequestFails_ThrowsException()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var alert = new CustomProcedureAlerts
            {
                Id = Guid.NewGuid(),
                Name = "Colonoscopy",
                Description = "Routine colonoscopy",
                IsActive = true
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _customProcedureAlertService.UpdateCustomProcedureAlertAsync(alert, orgId, subscription));
        }

        [Test]
        public async Task UpdateCustomProcedureAlertsListAsync_WhenSuccessful_CompletesWithoutException()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var alerts = new List<CustomProcedureAlerts>
            {
                new CustomProcedureAlerts
                {
                    Id = Guid.NewGuid(),
                    Name = "Colonoscopy",
                    Description = "Routine colonoscopy",
                    IsActive = true
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            // No exception should be thrown
            await _customProcedureAlertService.UpdateCustomProcedureAlertsListAsync(alerts, orgId, subscription);
        }

        [Test]
        public void UpdateCustomProcedureAlertsListAsync_WhenHttpRequestFails_ThrowsException()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var alerts = new List<CustomProcedureAlerts>
            {
                new CustomProcedureAlerts
                {
                    Id = Guid.NewGuid(),
                    Name = "Colonoscopy",
                    Description = "Routine colonoscopy",
                    IsActive = true
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _customProcedureAlertService.UpdateCustomProcedureAlertsListAsync(alerts, orgId, subscription));
        }

        [Test]
        public async Task DeleteCustomProcedureAlertByEntityAsync_WhenSuccessful_CompletesWithoutException()
        {
            // Arrange
            var alertId = Guid.NewGuid();
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var alert = new CustomProcedureAlerts
            {
                Id = alertId,
                Name = "Colonoscopy",
                Description = "Routine colonoscopy",
                IsActive = true
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            // No exception should be thrown
            await _customProcedureAlertService.DeleteCustomProcedureAlertByEntityAsync(alert, orgId, subscription);
        }

        [Test]
        public void DeleteCustomProcedureAlertByEntityAsync_WhenHttpRequestFails_ThrowsException()
        {
            // Arrange
            var alertId = Guid.NewGuid();
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var alert = new CustomProcedureAlerts
            {
                Id = alertId,
                Name = "Colonoscopy",
                Description = "Routine colonoscopy",
                IsActive = true
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _customProcedureAlertService.DeleteCustomProcedureAlertByEntityAsync(alert, orgId, subscription));
        }
    }
}



