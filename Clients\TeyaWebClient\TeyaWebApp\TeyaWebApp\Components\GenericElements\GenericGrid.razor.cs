using Microsoft.AspNetCore.Components;
using Syncfusion.Blazor.Grids;
using System.Collections.Generic;
namespace TeyaWebApp.Components.GenericElements
{
    public partial class GenericGrid<TValue> : ComponentBase
    {
        [Parameter] public IEnumerable<TValue> DataSource { get; set; }
        [Parameter] public int PageSize { get; set; } = 10; // Default page size
        [Parameter] public RenderFragment ChildContent { get; set; }

        [Parameter] public GridLine GridLines { get; set; }


        private IReadOnlyDictionary<string, object> AdditionalAttributes { get; set; }

        public override Task SetParametersAsync(ParameterView parameters)
        {
            AdditionalAttributes = parameters.ToDictionary();
            return base.SetParametersAsync(parameters);
        }
    }
}