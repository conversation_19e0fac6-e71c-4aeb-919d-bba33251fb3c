using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using Moq;
using Moq.Protected;
using NUnit.Framework;
using TeyaUIModels.Model;
using TeyaUIViewModels.ViewModel;
using TeyaUIViewModels.TeyaUIViewModelResources;

namespace TeyaWebClientUIViewModelTestCases
{
    [TestFixture]
    public class LabTestServiceTests
    {
        private Mock<HttpMessageHandler> _mockHttpMessageHandler;
        private Mock<IConfiguration> _mockConfiguration;
        private Mock<IStringLocalizer<TeyaUIViewModelsStrings>> _mockLocalizer;
        private Mock<ITokenService> _mockTokenService;
        private HttpClient _httpClient;
        private LabTestService _labTestService;

        private const string TestEncounterNotesUrl = "https://test-encounternotes.com";
        private const string TestAccessToken = "test-access-token";

        [SetUp]
        public void SetUp()
        {
            _mockHttpMessageHandler = new Mock<HttpMessageHandler>();
            _mockConfiguration = new Mock<IConfiguration>();
            _mockLocalizer = new Mock<IStringLocalizer<TeyaUIViewModelsStrings>>();
            _mockTokenService = new Mock<ITokenService>();

            _httpClient = new HttpClient(_mockHttpMessageHandler.Object);

            // Set up environment variable
            Environment.SetEnvironmentVariable("EncounterNotesURL", TestEncounterNotesUrl);

            // Set up mock responses
            _mockTokenService.Setup(t => t.AccessToken).Returns(TestAccessToken);

            _labTestService = new LabTestService(
                _httpClient,
                _mockConfiguration.Object,
                _mockLocalizer.Object,
                _mockTokenService.Object);
        }

        [TearDown]
        public void TearDown()
        {
            _httpClient?.Dispose();
            Environment.SetEnvironmentVariable("EncounterNotesURL", null);
        }

        private LabTests CreateTestLabTest()
        {
            return new LabTests
            {
                LabTestsId = Guid.NewGuid(),
                PatientId = Guid.NewGuid(),
                OrganizationId = Guid.NewGuid(),
                PcpId = Guid.NewGuid(),
                AssessmentId = Guid.NewGuid(),
                CreatedDate = DateTime.UtcNow,
                UpdatedDate = DateTime.UtcNow,
                LabTest1 = "Complete Blood Count",
                LabTest2 = "Lipid Panel",
                TestOrganization = "Test Lab Corp",
                AssessmentData = "Normal ranges",
                IsActive = true
            };
        }

        [Test]
        public async Task GetAllByIdAsync_WhenSuccessful_ReturnsLabTestsList()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var expectedLabTests = new List<LabTests>
            {
                CreateTestLabTest(),
                CreateTestLabTest()
            };

            var responseContent = JsonSerializer.Serialize(expectedLabTests);
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(responseContent, Encoding.UTF8, "application/json")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _labTestService.GetAllByIdAsync(patientId);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(2));
            Assert.That(result[0].LabTest1, Is.EqualTo("Complete Blood Count"));
        }

        [Test]
        public async Task GetAllByIdAsync_WhenHttpRequestFails_ThrowsHttpRequestException()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.NotFound,
                Content = new StringContent("Not Found")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _labTestService.GetAllByIdAsync(patientId));
        }

        [Test]
        public async Task GetAllByIdAndIsActiveAsync_WhenSuccessful_ReturnsActiveLabTests()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var activeLabTest = CreateTestLabTest();
            activeLabTest.IsActive = true;

            var expectedLabTests = new List<LabTests> { activeLabTest };
            var responseContent = JsonSerializer.Serialize(expectedLabTests);
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(responseContent, Encoding.UTF8, "application/json")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _labTestService.GetAllByIdAndIsActiveAsync(patientId);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(1));
            Assert.That(result[0].IsActive, Is.True);
        }

        [Test]
        public async Task AddLabTestsAsync_WhenSuccessful_CompletesWithoutException()
        {
            // Arrange
            var labTests = new List<LabTests>
            {
                CreateTestLabTest(),
                CreateTestLabTest()
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.Created
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            // No exception should be thrown
            await _labTestService.AddLabTestsAsync(labTests);
        }

        [Test]
        public async Task AddLabTestsAsync_WhenHttpRequestFails_ThrowsHttpRequestException()
        {
            // Arrange
            var labTests = new List<LabTests> { CreateTestLabTest() };
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad Request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _labTestService.AddLabTestsAsync(labTests));
        }

        [Test]
        public async Task UpdateLabTestsAsync_WhenSuccessful_CompletesWithoutException()
        {
            // Arrange
            var labTest = CreateTestLabTest();
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            // No exception should be thrown
            await _labTestService.UpdateLabTestsAsync(labTest);
        }

        [Test]
        public async Task UpdateLabTestsListAsync_WhenSuccessful_CompletesWithoutException()
        {
            // Arrange
            var labTests = new List<LabTests>
            {
                CreateTestLabTest(),
                CreateTestLabTest()
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            // No exception should be thrown
            await _labTestService.UpdateLabTestsListAsync(labTests);
        }

        [Test]
        public async Task DeleteLabTestsByEntityAsync_WhenSuccessful_CompletesWithoutException()
        {
            // Arrange
            var labTest = CreateTestLabTest();
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.NoContent
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response)
                .Verifiable();

            // Act & Assert
            // No exception should be thrown
            await _labTestService.DeleteLabTestsByEntityAsync(labTest);

            // Verify the HTTP call was made
            _mockHttpMessageHandler.Protected().Verify(
                "SendAsync",
                Times.Once(),
                ItExpr.IsAny<HttpRequestMessage>(),
                ItExpr.IsAny<CancellationToken>());
        }
    }
}



