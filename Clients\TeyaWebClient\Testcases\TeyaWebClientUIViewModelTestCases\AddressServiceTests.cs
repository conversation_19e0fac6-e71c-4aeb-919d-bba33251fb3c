using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using Moq;
using Moq.Protected;
using NUnit.Framework;
using TeyaUIModels.Model;
using TeyaUIViewModels.ViewModel;
using System.Net.Http.Json;
using System.Text.Json;

namespace TeyaWebClientUIViewModelTestCases
{
    [TestFixture]
    public class AddressServiceTests
    {
        private Mock<HttpMessageHandler> _mockHttpMessageHandler;
        private HttpClient _httpClient;
        private Mock<ILogger<AddressService>> _mockLogger;
        private Mock<IStringLocalizer<AddressService>> _mockLocalizer;
        private AddressService _addressService;
        private readonly string _baseUrl = "http://test-api.com";

        [SetUp]
        public void Setup()
        {
            // Setup environment variable
            Environment.SetEnvironmentVariable("MemberServiceURL", _baseUrl);

            // Setup mock HTTP handler
            _mockHttpMessageHandler = new Mock<HttpMessageHandler>();

            // Setup HttpClient with mock handler
            _httpClient = new HttpClient(_mockHttpMessageHandler.Object)
            {
                BaseAddress = new Uri(_baseUrl)
            };

            // Setup mock logger
            _mockLogger = new Mock<ILogger<AddressService>>();

            // Setup mock localizer
            _mockLocalizer = new Mock<IStringLocalizer<AddressService>>();
            _mockLocalizer.Setup(l => l["Base URL not configured"])
                .Returns(new LocalizedString("Base URL not configured", "Base URL not configured"));
            _mockLocalizer.Setup(l => l["Failed to fetch address with ID {id}"])
                .Returns(new LocalizedString("Failed to fetch address with ID {id}", "Failed to fetch address with ID {id}"));
            _mockLocalizer.Setup(l => l["Error fetching address with ID {id}"])
                .Returns(new LocalizedString("Error fetching address with ID {id}", "Error fetching address with ID {id}"));
            _mockLocalizer.Setup(l => l["Error adding address"])
                .Returns(new LocalizedString("Error adding address", "Error adding address"));
            _mockLocalizer.Setup(l => l["Error updating address with ID {id}"])
                .Returns(new LocalizedString("Error updating address with ID {id}", "Error updating address with ID {id}"));
            _mockLocalizer.Setup(l => l["Error deleting address with ID {id}"])
                .Returns(new LocalizedString("Error deleting address with ID {id}", "Error deleting address with ID {id}"));
            _mockLocalizer.Setup(l => l["Failed to fetch addresses with name {name}"])
                .Returns(new LocalizedString("Failed to fetch addresses with name {name}", "Failed to fetch addresses with name {name}"));
            _mockLocalizer.Setup(l => l["Error fetching addresses with name {name}"])
                .Returns(new LocalizedString("Error fetching addresses with name {name}", "Error fetching addresses with name {name}"));
            _mockLocalizer.Setup(l => l["Failed to fetch all addresses"])
                .Returns(new LocalizedString("Failed to fetch all addresses", "Failed to fetch all addresses"));
            _mockLocalizer.Setup(l => l["Error fetching all addresses"])
                .Returns(new LocalizedString("Error fetching all addresses", "Error fetching all addresses"));

            // Create AddressService with mocked dependencies
            _addressService = new AddressService(_httpClient, _mockLogger.Object, _mockLocalizer.Object);
        }

        [TearDown]
        public void TearDown()
        {
            _httpClient.Dispose();
            Environment.SetEnvironmentVariable("MemberServiceURL", null);
        }

        [Test]
        public async Task GetAddressByIdAsync_WhenSuccessful_ReturnsAddress()
        {
            // Arrange
            var addressId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var expectedAddress = new Address
            {
                AddressId = addressId,
                AddressLine1 = "123 Test St",
                City = "Test City",
                State = "Test State",
                PostalCode = "12345",
                Country = "Test Country",
                OrganizationID = orgId,
                Subscription = subscription
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedAddress)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.Is<HttpRequestMessage>(req => req.RequestUri.ToString().Contains($"/api/Address/{addressId}/{orgId}/{subscription}")),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _addressService.GetAddressByIdAsync(addressId, orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.AddressId, Is.EqualTo(expectedAddress.AddressId));
            Assert.That(result.AddressLine1, Is.EqualTo(expectedAddress.AddressLine1));
            Assert.That(result.City, Is.EqualTo(expectedAddress.City));
            Assert.That(result.State, Is.EqualTo(expectedAddress.State));
            Assert.That(result.PostalCode, Is.EqualTo(expectedAddress.PostalCode));
            Assert.That(result.Country, Is.EqualTo(expectedAddress.Country));
            Assert.That(result.OrganizationID, Is.EqualTo(expectedAddress.OrganizationID));
            Assert.That(result.Subscription, Is.EqualTo(expectedAddress.Subscription));
        }

        [Test]
        public async Task GetAddressByIdAsync_WhenNotFound_ReturnsNull()
        {
            // Arrange
            var addressId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.NotFound
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.Is<HttpRequestMessage>(req => req.RequestUri.ToString().Contains($"/api/Address/{addressId}/{orgId}/{subscription}")),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _addressService.GetAddressByIdAsync(addressId, orgId, subscription);

            // Assert
            Assert.That(result, Is.Null);
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Warning,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.IsAny<Exception>(),
                    It.Is<Func<It.IsAnyType, Exception, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }

        [Test]
        public void GetAddressByIdAsync_WhenExceptionThrown_PropagatesException()
        {
            // Arrange
            var addressId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var expectedException = new HttpRequestException("Test exception");

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.Is<HttpRequestMessage>(req => req.RequestUri.ToString().Contains($"/api/Address/{addressId}/{orgId}/{subscription}")),
                    ItExpr.IsAny<CancellationToken>())
                .ThrowsAsync(expectedException);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _addressService.GetAddressByIdAsync(addressId, orgId, subscription));

            Assert.That(exception.Message, Is.EqualTo(expectedException.Message));
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.Is<Exception>(ex => ex.Message == expectedException.Message),
                    It.Is<Func<It.IsAnyType, Exception, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }

        [Test]
        public async Task AddAddressAsync_WhenSuccessful_ReturnsTrue()
        {
            // Arrange
            var address = new Address
            {
                AddressId = Guid.NewGuid(),
                AddressLine1 = "123 Test St",
                City = "Test City",
                State = "Test State",
                PostalCode = "12345",
                Country = "Test Country",
                OrganizationID = Guid.NewGuid(),
                Subscription = true
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.Created
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.Is<HttpRequestMessage>(req => req.RequestUri.ToString().Contains("/api/Address")),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _addressService.AddAddressAsync(address);

            // Assert
            Assert.That(result, Is.True);
        }

        [Test]
        public async Task AddAddressAsync_WhenUnsuccessful_ReturnsFalse()
        {
            // Arrange
            var address = new Address
            {
                AddressId = Guid.NewGuid(),
                AddressLine1 = "123 Test St",
                City = "Test City",
                State = "Test State",
                PostalCode = "12345",
                Country = "Test Country",
                OrganizationID = Guid.NewGuid(),
                Subscription = true
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.Is<HttpRequestMessage>(req => req.RequestUri.ToString().Contains("/api/Address")),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _addressService.AddAddressAsync(address);

            // Assert
            Assert.That(result, Is.False);
        }

        [Test]
        public void AddAddressAsync_WhenExceptionThrown_PropagatesException()
        {
            // Arrange
            var address = new Address
            {
                AddressId = Guid.NewGuid(),
                AddressLine1 = "123 Test St",
                City = "Test City",
                State = "Test State",
                PostalCode = "12345",
                Country = "Test Country",
                OrganizationID = Guid.NewGuid(),
                Subscription = true
            };

            var expectedException = new HttpRequestException("Test exception");

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.Is<HttpRequestMessage>(req => req.RequestUri.ToString().Contains("/api/Address")),
                    ItExpr.IsAny<CancellationToken>())
                .ThrowsAsync(expectedException);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _addressService.AddAddressAsync(address));

            Assert.That(exception.Message, Is.EqualTo(expectedException.Message));
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.Is<Exception>(ex => ex.Message == expectedException.Message),
                    It.Is<Func<It.IsAnyType, Exception, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }

        [Test]
        public async Task UpdateAddressAsync_WhenSuccessful_ReturnsTrue()
        {
            // Arrange
            var addressId = Guid.NewGuid();
            var address = new Address
            {
                AddressId = addressId,
                AddressLine1 = "123 Test St",
                City = "Test City",
                State = "Test State",
                PostalCode = "12345",
                Country = "Test Country",
                OrganizationID = Guid.NewGuid(),
                Subscription = true
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.Is<HttpRequestMessage>(req => req.RequestUri.ToString().Contains($"/api/Address/{addressId}")),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _addressService.UpdateAddressAsync(addressId, address);

            // Assert
            Assert.That(result, Is.True);
        }

        [Test]
        public async Task UpdateAddressAsync_WhenUnsuccessful_ReturnsFalse()
        {
            // Arrange
            var addressId = Guid.NewGuid();
            var address = new Address
            {
                AddressId = addressId,
                AddressLine1 = "123 Test St",
                City = "Test City",
                State = "Test State",
                PostalCode = "12345",
                Country = "Test Country",
                OrganizationID = Guid.NewGuid(),
                Subscription = true
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.Is<HttpRequestMessage>(req => req.RequestUri.ToString().Contains($"/api/Address/{addressId}")),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _addressService.UpdateAddressAsync(addressId, address);

            // Assert
            Assert.That(result, Is.False);
        }

        [Test]
        public void UpdateAddressAsync_WhenExceptionThrown_PropagatesException()
        {
            // Arrange
            var addressId = Guid.NewGuid();
            var address = new Address
            {
                AddressId = addressId,
                AddressLine1 = "123 Test St",
                City = "Test City",
                State = "Test State",
                PostalCode = "12345",
                Country = "Test Country",
                OrganizationID = Guid.NewGuid(),
                Subscription = true
            };

            var expectedException = new HttpRequestException("Test exception");

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.Is<HttpRequestMessage>(req => req.RequestUri.ToString().Contains($"/api/Address/{addressId}")),
                    ItExpr.IsAny<CancellationToken>())
                .ThrowsAsync(expectedException);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _addressService.UpdateAddressAsync(addressId, address));

            Assert.That(exception.Message, Is.EqualTo(expectedException.Message));
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.Is<Exception>(ex => ex.Message == expectedException.Message),
                    It.Is<Func<It.IsAnyType, Exception, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }

        [Test]
        public async Task DeleteAddressAsync_WhenSuccessful_ReturnsTrue()
        {
            // Arrange
            var addressId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.NoContent
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.Is<HttpRequestMessage>(req => req.RequestUri.ToString().Contains($"/api/Address/{addressId}/{orgId}/{subscription}")),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _addressService.DeleteAddressAsync(addressId, orgId, subscription);

            // Assert
            Assert.That(result, Is.True);
        }

        [Test]
        public async Task DeleteAddressAsync_WhenUnsuccessful_ReturnsFalse()
        {
            // Arrange
            var addressId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.NotFound
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.Is<HttpRequestMessage>(req => req.RequestUri.ToString().Contains($"/api/Address/{addressId}/{orgId}/{subscription}")),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _addressService.DeleteAddressAsync(addressId, orgId, subscription);

            // Assert
            Assert.That(result, Is.False);
        }

        [Test]
        public void DeleteAddressAsync_WhenExceptionThrown_PropagatesException()
        {
            // Arrange
            var addressId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var expectedException = new HttpRequestException("Test exception");

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.Is<HttpRequestMessage>(req => req.RequestUri.ToString().Contains($"/api/Address/{addressId}/{orgId}/{subscription}")),
                    ItExpr.IsAny<CancellationToken>())
                .ThrowsAsync(expectedException);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _addressService.DeleteAddressAsync(addressId, orgId, subscription));

            Assert.That(exception.Message, Is.EqualTo(expectedException.Message));
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.Is<Exception>(ex => ex.Message == expectedException.Message),
                    It.Is<Func<It.IsAnyType, Exception, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }

        [Test]
        public async Task GetAddressesByNameAsync_WhenSuccessful_ReturnsAddresses()
        {
            // Arrange
            var name = "Test";
            var expectedAddresses = new List<Address>
            {
                new Address
                {
                    AddressId = Guid.NewGuid(),
                    AddressLine1 = "123 Test St",
                    City = "Test City",
                    State = "Test State",
                    PostalCode = "12345",
                    Country = "Test Country",
                    OrganizationID = Guid.NewGuid(),
                    Subscription = true
                },
                new Address
                {
                    AddressId = Guid.NewGuid(),
                    AddressLine1 = "456 Test Ave",
                    City = "Test Town",
                    State = "Test State",
                    PostalCode = "67890",
                    Country = "Test Country",
                    OrganizationID = Guid.NewGuid(),
                    Subscription = false
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedAddresses)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.Is<HttpRequestMessage>(req => req.RequestUri.ToString().Contains($"/api/Address/search?name={name}")),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _addressService.GetAddressesByNameAsync(name);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(expectedAddresses.Count));
            Assert.That(result[0].AddressId, Is.EqualTo(expectedAddresses[0].AddressId));
            Assert.That(result[1].AddressId, Is.EqualTo(expectedAddresses[1].AddressId));
        }

        [Test]
        public async Task GetAddressesByNameAsync_WhenNotFound_ReturnsNull()
        {
            // Arrange
            var name = "NonExistent";

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.NotFound
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.Is<HttpRequestMessage>(req => req.RequestUri.ToString().Contains($"/api/Address/search?name={name}")),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _addressService.GetAddressesByNameAsync(name);

            // Assert
            Assert.That(result, Is.Null);
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Warning,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.IsAny<Exception>(),
                    It.Is<Func<It.IsAnyType, Exception, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }

        [Test]
        public void GetAddressesByNameAsync_WhenExceptionThrown_PropagatesException()
        {
            // Arrange
            var name = "Test";
            var expectedException = new HttpRequestException("Test exception");

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.Is<HttpRequestMessage>(req => req.RequestUri.ToString().Contains($"/api/Address/search?name={name}")),
                    ItExpr.IsAny<CancellationToken>())
                .ThrowsAsync(expectedException);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _addressService.GetAddressesByNameAsync(name));

            Assert.That(exception.Message, Is.EqualTo(expectedException.Message));
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.Is<Exception>(ex => ex.Message == expectedException.Message),
                    It.Is<Func<It.IsAnyType, Exception, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }

        [Test]
        public async Task GetAllAddressesAsync_WhenSuccessful_ReturnsAddresses()
        {
            // Arrange
            var expectedAddresses = new List<Address>
            {
                new Address
                {
                    AddressId = Guid.NewGuid(),
                    AddressLine1 = "123 Test St",
                    City = "Test City",
                    State = "Test State",
                    PostalCode = "12345",
                    Country = "Test Country",
                    OrganizationID = Guid.NewGuid(),
                    Subscription = true
                },
                new Address
                {
                    AddressId = Guid.NewGuid(),
                    AddressLine1 = "456 Test Ave",
                    City = "Test Town",
                    State = "Test State",
                    PostalCode = "67890",
                    Country = "Test Country",
                    OrganizationID = Guid.NewGuid(),
                    Subscription = false
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedAddresses)
            };

            var orgId = Guid.NewGuid();
            var subscription = true;

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.Is<HttpRequestMessage>(req => req.RequestUri.ToString().Contains($"/api/Address/{orgId}/{subscription}")),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _addressService.GetAllAddressesAsync(orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(expectedAddresses.Count));
            Assert.That(result[0].AddressId, Is.EqualTo(expectedAddresses[0].AddressId));
            Assert.That(result[1].AddressId, Is.EqualTo(expectedAddresses[1].AddressId));
        }

        [Test]
        public async Task GetAllAddressesAsync_WhenNotFound_ReturnsNull()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.NotFound
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.Is<HttpRequestMessage>(req => req.RequestUri.ToString().Contains($"/api/Address/{orgId}/{subscription}")),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _addressService.GetAllAddressesAsync(orgId, subscription);

            // Assert
            Assert.That(result, Is.Null);
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Warning,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.IsAny<Exception>(),
                    It.Is<Func<It.IsAnyType, Exception, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }

        [Test]
        public void GetAllAddressesAsync_WhenExceptionThrown_PropagatesException()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;
            var expectedException = new HttpRequestException("Test exception");

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.Is<HttpRequestMessage>(req => req.RequestUri.ToString().Contains($"/api/Address/{orgId}/{subscription}")),
                    ItExpr.IsAny<CancellationToken>())
                .ThrowsAsync(expectedException);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _addressService.GetAllAddressesAsync(orgId, subscription));

            Assert.That(exception.Message, Is.EqualTo(expectedException.Message));
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.Is<Exception>(ex => ex.Message == expectedException.Message),
                    It.Is<Func<It.IsAnyType, Exception, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }
    }
}

