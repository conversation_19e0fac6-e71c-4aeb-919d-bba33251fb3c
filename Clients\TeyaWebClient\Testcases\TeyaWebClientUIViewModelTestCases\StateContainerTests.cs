using System;
using NUnit.Framework;
using TeyaUIViewModels.ViewModel;

namespace TeyaWebClientUIViewModelTestCases
{
    [TestFixture]
    public class StateContainerTests
    {
        private StateContainer _stateContainer;
        private bool _onChangeEventFired;
        private Action _onChangeHandler;

        [SetUp]
        public void Setup()
        {
            _stateContainer = new StateContainer();
            _onChangeEventFired = false;

            // Create a reusable handler for proper unsubscription
            _onChangeHandler = () => _onChangeEventFired = true;

            // Subscribe to the OnChange event
            _stateContainer.OnChange += _onChangeHandler;
        }

        [TearDown]
        public void TearDown()
        {
            // Unsubscribe from events to prevent memory leaks
            if (_onChangeHandler != null)
            {
                _stateContainer.OnChange -= _onChangeHandler;
            }
        }

        [Test]
        public void Username_InitialValue_IsNull()
        {
            // Act & Assert
            Assert.That(_stateContainer.Username, Is.Null);
        }

        [Test]
        public void ExtractedName_InitialValue_IsNull()
        {
            // Act & Assert
            Assert.That(_stateContainer.ExtractedName, Is.Null);
        }

        [Test]
        public void SetUsername_WhenValidUsername_SetsUsernameAndFiresEvent()
        {
            // Arrange
            var username = "<EMAIL>";

            // Act
            _stateContainer.Username = username;

            // Assert
            Assert.That(_stateContainer.Username, Is.EqualTo(username));
            Assert.That(_onChangeEventFired, Is.True);
        }

        [Test]
        public void SetUsername_WhenNullUsername_SetsUsernameToNullAndFiresEvent()
        {
            // Arrange
            string username = null;

            // Act
            _stateContainer.Username = username;

            // Assert
            Assert.That(_stateContainer.Username, Is.Null);
            Assert.That(_onChangeEventFired, Is.True);
        }

        [Test]
        public void SetUsername_WhenEmptyUsername_SetsUsernameToEmptyAndFiresEvent()
        {
            // Arrange
            var username = "";

            // Act
            _stateContainer.Username = username;

            // Assert
            Assert.That(_stateContainer.Username, Is.EqualTo(""));
            Assert.That(_onChangeEventFired, Is.True);
        }

        [Test]
        public void SetUsername_WhenWhitespaceUsername_SetsUsernameAndFiresEvent()
        {
            // Arrange
            var username = "   ";

            // Act
            _stateContainer.Username = username;

            // Assert
            Assert.That(_stateContainer.Username, Is.EqualTo("   "));
            Assert.That(_onChangeEventFired, Is.True);
        }

        [Test]
        public void SetUsername_WhenCalledMultipleTimes_UpdatesUsernameAndFiresEventEachTime()
        {
            // Arrange
            var firstUsername = "<EMAIL>";
            var secondUsername = "<EMAIL>";

            // Act - First call
            _stateContainer.Username = firstUsername;
            Assert.That(_stateContainer.Username, Is.EqualTo(firstUsername));
            Assert.That(_onChangeEventFired, Is.True);

            // Reset event flag
            _onChangeEventFired = false;

            // Act - Second call
            _stateContainer.Username = secondUsername;

            // Assert
            Assert.That(_stateContainer.Username, Is.EqualTo(secondUsername));
            Assert.That(_onChangeEventFired, Is.True);
        }

        [Test]
        public void SetUsername_WhenSameValueSetTwice_StillFiresEvent()
        {
            // Arrange
            var username = "<EMAIL>";

            // Act - First call
            _stateContainer.Username = username;
            _onChangeEventFired = false; // Reset

            // Act - Second call with same value
            _stateContainer.Username = username;

            // Assert
            Assert.That(_stateContainer.Username, Is.EqualTo(username));
            Assert.That(_onChangeEventFired, Is.True);
        }

        [Test]
        public void SetExtractedName_WhenValidName_SetsExtractedNameAndFiresEvent()
        {
            // Arrange
            var extractedName = "John Doe";

            // Act
            _stateContainer.ExtractedName = extractedName;

            // Assert
            Assert.That(_stateContainer.ExtractedName, Is.EqualTo(extractedName));
            Assert.That(_onChangeEventFired, Is.True);
        }

        [Test]
        public void SetExtractedName_WhenNullName_SetsExtractedNameToNullAndFiresEvent()
        {
            // Arrange
            string extractedName = null;

            // Act
            _stateContainer.ExtractedName = extractedName;

            // Assert
            Assert.That(_stateContainer.ExtractedName, Is.Null);
            Assert.That(_onChangeEventFired, Is.True);
        }

        [Test]
        public void SetExtractedName_WhenEmptyName_SetsExtractedNameToEmptyAndFiresEvent()
        {
            // Arrange
            var extractedName = "";

            // Act
            _stateContainer.ExtractedName = extractedName;

            // Assert
            Assert.That(_stateContainer.ExtractedName, Is.EqualTo(""));
            Assert.That(_onChangeEventFired, Is.True);
        }

        [Test]
        public void SetExtractedName_WhenWhitespaceName_SetsExtractedNameAndFiresEvent()
        {
            // Arrange
            var extractedName = "   ";

            // Act
            _stateContainer.ExtractedName = extractedName;

            // Assert
            Assert.That(_stateContainer.ExtractedName, Is.EqualTo("   "));
            Assert.That(_onChangeEventFired, Is.True);
        }

        [Test]
        public void SetExtractedName_WhenCalledMultipleTimes_UpdatesExtractedNameAndFiresEventEachTime()
        {
            // Arrange
            var firstName = "John Doe";
            var secondName = "Jane Smith";

            // Act - First call
            _stateContainer.ExtractedName = firstName;
            Assert.That(_stateContainer.ExtractedName, Is.EqualTo(firstName));
            Assert.That(_onChangeEventFired, Is.True);

            // Reset event flag
            _onChangeEventFired = false;

            // Act - Second call
            _stateContainer.ExtractedName = secondName;

            // Assert
            Assert.That(_stateContainer.ExtractedName, Is.EqualTo(secondName));
            Assert.That(_onChangeEventFired, Is.True);
        }

        [Test]
        public void SetExtractedName_WhenSameValueSetTwice_StillFiresEvent()
        {
            // Arrange
            var extractedName = "Same Name";

            // Act - First call
            _stateContainer.ExtractedName = extractedName;
            _onChangeEventFired = false; // Reset

            // Act - Second call with same value
            _stateContainer.ExtractedName = extractedName;

            // Assert
            Assert.That(_stateContainer.ExtractedName, Is.EqualTo(extractedName));
            Assert.That(_onChangeEventFired, Is.True);
        }

        [Test]
        public void SetBothProperties_WhenCalledSequentially_BothPropertiesSetAndEventFiresTwice()
        {
            // Arrange
            var username = "<EMAIL>";
            var extractedName = "Test User";
            int eventFireCount = 0;

            // Use a custom event handler to count fires
            _stateContainer.OnChange -= _onChangeHandler;
            _stateContainer.OnChange += () => eventFireCount++;

            // Act
            _stateContainer.Username = username;
            _stateContainer.ExtractedName = extractedName;

            // Assert
            Assert.That(_stateContainer.Username, Is.EqualTo(username));
            Assert.That(_stateContainer.ExtractedName, Is.EqualTo(extractedName));
            Assert.That(eventFireCount, Is.EqualTo(2));
        }

        [Test]
        public void OnChangeEvent_WhenMultipleSubscribers_NotifiesAllSubscribers()
        {
            // Arrange
            bool firstSubscriberNotified = false;
            bool secondSubscriberNotified = false;

            _stateContainer.OnChange += () => firstSubscriberNotified = true;
            _stateContainer.OnChange += () => secondSubscriberNotified = true;

            // Act
            _stateContainer.Username = "<EMAIL>";

            // Assert
            Assert.That(firstSubscriberNotified, Is.True);
            Assert.That(secondSubscriberNotified, Is.True);
        }

        [Test]
        public void OnChangeEvent_WhenNoSubscribers_DoesNotThrowException()
        {
            // Arrange
            var stateContainerWithoutSubscribers = new StateContainer();

            // Act & Assert
            Assert.DoesNotThrow(() => stateContainerWithoutSubscribers.Username = "<EMAIL>");
            Assert.DoesNotThrow(() => stateContainerWithoutSubscribers.ExtractedName = "Test Name");
        }

        [Test]
        public void SetUsername_WithSpecialCharacters_HandlesCorrectly()
        {
            // Arrange
            var usernameWithSpecialChars = "<EMAIL>";

            // Act
            _stateContainer.Username = usernameWithSpecialChars;

            // Assert
            Assert.That(_stateContainer.Username, Is.EqualTo(usernameWithSpecialChars));
            Assert.That(_onChangeEventFired, Is.True);
        }

        [Test]
        public void SetExtractedName_WithSpecialCharacters_HandlesCorrectly()
        {
            // Arrange
            var nameWithSpecialChars = "José María García-López";

            // Act
            _stateContainer.ExtractedName = nameWithSpecialChars;

            // Assert
            Assert.That(_stateContainer.ExtractedName, Is.EqualTo(nameWithSpecialChars));
            Assert.That(_onChangeEventFired, Is.True);
        }

        [Test]
        public void SetUsername_WithVeryLongString_HandlesCorrectly()
        {
            // Arrange
            var longUsername = new string('a', 1000) + "@example.com";

            // Act
            _stateContainer.Username = longUsername;

            // Assert
            Assert.That(_stateContainer.Username, Is.EqualTo(longUsername));
            Assert.That(_onChangeEventFired, Is.True);
        }

        [Test]
        public void SetExtractedName_WithVeryLongString_HandlesCorrectly()
        {
            // Arrange
            var longName = new string('A', 1000);

            // Act
            _stateContainer.ExtractedName = longName;

            // Assert
            Assert.That(_stateContainer.ExtractedName, Is.EqualTo(longName));
            Assert.That(_onChangeEventFired, Is.True);
        }
    }
}
