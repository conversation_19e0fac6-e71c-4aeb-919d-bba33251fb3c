// Enhanced Blazor Server reconnection handling
window.blazorReconnect = {
    maxRetries: 8,
    retryIntervalMilliseconds: 2000,
    dialogId: 'components-reconnect-modal',
    
    showReconnectDialog: function() {
        const modal = document.getElementById(this.dialogId);
        if (modal) {
            modal.style.display = 'block';
            modal.style.position = 'fixed';
            modal.style.top = '0';
            modal.style.left = '0';
            modal.style.width = '100%';
            modal.style.height = '100%';
            modal.style.backgroundColor = 'rgba(0,0,0,0.5)';
            modal.style.zIndex = '9999';
        }
    },
    
    hideReconnectDialog: function() {
        const modal = document.getElementById(this.dialogId);
        if (modal) {
            modal.style.display = 'none';
        }
    },
    
    updateReconnectDialog: function(message) {
        const messageElement = document.querySelector('#' + this.dialogId + ' .reconnect-message');
        if (messageElement) {
            messageElement.textContent = message;
        }
    }
};

// Override default Blazor reconnection behavior
window.Blazor = window.Blazor || {};
window.Blazor.reconnect = async function() {
    let retryCount = 0;
    
    while (retryCount < window.blazorReconnect.maxRetries) {
        try {
            window.blazorReconnect.updateReconnectDialog(`Attempting to reconnect... (${retryCount + 1}/${window.blazorReconnect.maxRetries})`);
            
            // Try to reconnect
            const response = await fetch('/_blazor/negotiate', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });
            
            if (response.ok) {
                window.blazorReconnect.hideReconnectDialog();
                window.location.reload();
                return true;
            }
        } catch (error) {
            console.warn('Reconnection attempt failed:', error);
        }
        
        retryCount++;
        
        if (retryCount < window.blazorReconnect.maxRetries) {
            await new Promise(resolve => setTimeout(resolve, window.blazorReconnect.retryIntervalMilliseconds));
        }
    }
    
    // All retries failed
    window.blazorReconnect.updateReconnectDialog('Unable to reconnect to the server. Please refresh the page.');
    return false;
};

// Handle connection state changes
document.addEventListener('DOMContentLoaded', function() {
    // Show reconnect dialog when connection is lost
    window.addEventListener('beforeunload', function() {
        window.blazorReconnect.showReconnectDialog();
    });
    
    // Listen for Blazor connection events
    if (window.Blazor && window.Blazor.start) {
        const originalStart = window.Blazor.start;
        window.Blazor.start = function(options) {
            options = options || {};
            
            // Configure reconnection options
            options.reconnectionOptions = {
                maxRetries: window.blazorReconnect.maxRetries,
                retryIntervalMilliseconds: window.blazorReconnect.retryIntervalMilliseconds
            };
            
            return originalStart.call(this, options);
        };
    }
});
