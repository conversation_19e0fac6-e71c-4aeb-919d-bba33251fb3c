using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using Moq;
using Moq.Protected;
using NUnit.Framework;
using TeyaUIModels.Model;
using TeyaUIViewModels.ViewModel;
using TeyaUIViewModels.TeyaUIViewModelResources;
using System.Net.Http.Json;
using System.Text.Json;

namespace TeyaWebClientUIViewModelTestCases
{
    [TestFixture]
    public class PastResultServiceTests
    {
        private Mock<HttpMessageHandler> _mockHttpMessageHandler;
        private HttpClient _httpClient;
        private Mock<IConfiguration> _mockConfiguration;
        private Mock<IStringLocalizer<TeyaUIViewModelsStrings>> _mockLocalizer;
        private Mock<ITokenService> _mockTokenService;
        private PastResultService _pastResultService;
        private readonly string _baseUrl = "http://test-api.com";
        private readonly string _accessToken = "test-access-token";

        [SetUp]
        public void Setup()
        {
            // Setup environment variable
            Environment.SetEnvironmentVariable("EncounterNotesURL", _baseUrl);

            // Setup mock HTTP handler
            _mockHttpMessageHandler = new Mock<HttpMessageHandler>();

            // Setup HttpClient with mock handler
            _httpClient = new HttpClient(_mockHttpMessageHandler.Object)
            {
                BaseAddress = new Uri(_baseUrl)
            };

            // Setup mock configuration
            _mockConfiguration = new Mock<IConfiguration>();

            // Setup mock localizer
            _mockLocalizer = new Mock<IStringLocalizer<TeyaUIViewModelsStrings>>();
            _mockLocalizer.Setup(l => l["AddressRetrievalFailure"])
                .Returns(new LocalizedString("AddressRetrievalFailure", "Address retrieval failure"));
            _mockLocalizer.Setup(l => l["TasksRetrievalFailure"])
                .Returns(new LocalizedString("TasksRetrievalFailure", "Tasks retrieval failure"));

            // Setup mock token service
            _mockTokenService = new Mock<ITokenService>();
            _mockTokenService.Setup(t => t.AccessToken).Returns(_accessToken);
            _mockTokenService.Setup(t => t.GetValidatedAccessTokenAsync()).ReturnsAsync(_accessToken);

            // Create PastResultService with mocked dependencies
            _pastResultService = new PastResultService(_httpClient, _mockConfiguration.Object, _mockLocalizer.Object, _mockTokenService.Object);
        }

        [TearDown]
        public void TearDown()
        {
            _httpClient.Dispose();
            Environment.SetEnvironmentVariable("EncounterNotesURL", null);
        }

        [Test]
        public async Task GetResultsByIdAsync_WhenSuccessful_ReturnsPastResults()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var expectedResults = new List<PastResult>
            {
                new PastResult
                {
                    ResultId = Guid.NewGuid(),
                    PatientId = patientId,
                    OrganizationId = orgId,
                    OrderName = "Blood Test",
                    ViewResults = "Normal",
                    ResultDate = DateTime.Now.AddDays(-30),
                    IsActive = true,
                    Subscription = subscription,
                    CreatedBy = Guid.NewGuid(),
                    UpdatedBy = Guid.NewGuid(),
                    CreatedDate = DateTime.Now.AddDays(-30)
                },
                new PastResult
                {
                    ResultId = Guid.NewGuid(),
                    PatientId = patientId,
                    OrganizationId = orgId,
                    OrderName = "X-Ray",
                    ViewResults = "Clear",
                    ResultDate = DateTime.Now.AddDays(-15),
                    IsActive = true,
                    Subscription = subscription,
                    CreatedBy = Guid.NewGuid(),
                    UpdatedBy = Guid.NewGuid(),
                    CreatedDate = DateTime.Now.AddDays(-15)
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedResults)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _pastResultService.GetResultsByIdAsync(patientId, orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(expectedResults.Count));
            Assert.That(result[0].ResultId, Is.EqualTo(expectedResults[0].ResultId));
            Assert.That(result[0].PatientId, Is.EqualTo(expectedResults[0].PatientId));
            Assert.That(result[0].OrderName, Is.EqualTo(expectedResults[0].OrderName));
            Assert.That(result[0].ViewResults, Is.EqualTo(expectedResults[0].ViewResults));
            Assert.That(result[0].IsActive, Is.EqualTo(expectedResults[0].IsActive));
            Assert.That(result[0].OrganizationId, Is.EqualTo(expectedResults[0].OrganizationId));
        }

        [Test]
        public void GetResultsByIdAsync_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.NotFound,
                Content = new StringContent("Not found")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _pastResultService.GetResultsByIdAsync(patientId, orgId, subscription));

            Assert.That(exception.Message, Is.EqualTo("Address retrieval failure"));
        }

        [Test]
        public async Task GetResultByIdAsyncAndIsActive_WhenSuccessful_ReturnsPastResults()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var expectedResults = new List<PastResult>
            {
                new PastResult
                {
                    ResultId = Guid.NewGuid(),
                    PatientId = patientId,
                    OrganizationId = orgId,
                    OrderName = "Blood Test",
                    ViewResults = "Normal",
                    ResultDate = DateTime.Now.AddDays(-30),
                    IsActive = true,
                    Subscription = subscription,
                    CreatedBy = Guid.NewGuid(),
                    UpdatedBy = Guid.NewGuid(),
                    CreatedDate = DateTime.Now.AddDays(-30)
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedResults)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _pastResultService.GetResultByIdAsyncAndIsActive(patientId, orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(expectedResults.Count));
            Assert.That(result[0].IsActive, Is.True);
        }

        [Test]
        public void GetResultByIdAsyncAndIsActive_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.NotFound,
                Content = new StringContent("Not found")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _pastResultService.GetResultByIdAsyncAndIsActive(patientId, orgId, subscription));

            Assert.That(exception.Message, Is.EqualTo("Address retrieval failure"));
        }

        [Test]
        public async Task AddResultAsync_WhenSuccessful_CompletesSuccessfully()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;
            var pastResults = new List<PastResult>
            {
                new PastResult
                {
                    ResultId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    OrganizationId = orgId,
                    OrderName = "Blood Test",
                    ViewResults = "Normal",
                    ResultDate = DateTime.Now,
                    IsActive = true,
                    Subscription = subscription,
                    CreatedBy = Guid.NewGuid(),
                    UpdatedBy = Guid.NewGuid(),
                    CreatedDate = DateTime.Now
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.Created,
                Content = new StringContent("Success")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.DoesNotThrowAsync(async () => await _pastResultService.AddResultAsync(pastResults, orgId, subscription));
        }

        [Test]
        public void AddResultAsync_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;
            var pastResults = new List<PastResult>
            {
                new PastResult
                {
                    ResultId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    OrganizationId = orgId,
                    OrderName = "Blood Test",
                    ViewResults = "Normal",
                    ResultDate = DateTime.Now,
                    IsActive = true,
                    Subscription = subscription,
                    CreatedBy = Guid.NewGuid(),
                    UpdatedBy = Guid.NewGuid(),
                    CreatedDate = DateTime.Now
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _pastResultService.AddResultAsync(pastResults, orgId, subscription));

            Assert.That(exception.Message, Is.EqualTo("Tasks retrieval failure"));
        }

        [Test]
        public async Task UpdateResultAsync_WhenSuccessful_CompletesSuccessfully()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;
            var pastResult = new PastResult
            {
                ResultId = Guid.NewGuid(),
                PatientId = Guid.NewGuid(),
                OrganizationId = orgId,
                OrderName = "Blood Test Updated",
                ViewResults = "Abnormal",
                ResultDate = DateTime.Now,
                IsActive = true,
                Subscription = subscription,
                CreatedBy = Guid.NewGuid(),
                UpdatedBy = Guid.NewGuid(),
                CreatedDate = DateTime.Now
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent("Success")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.DoesNotThrowAsync(async () => await _pastResultService.UpdateResultAsync(pastResult, orgId, subscription));
        }

        [Test]
        public void UpdateResultAsync_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;
            var pastResult = new PastResult
            {
                ResultId = Guid.NewGuid(),
                PatientId = Guid.NewGuid(),
                OrganizationId = orgId,
                OrderName = "Blood Test Updated",
                ViewResults = "Abnormal",
                ResultDate = DateTime.Now,
                IsActive = true,
                Subscription = subscription,
                CreatedBy = Guid.NewGuid(),
                UpdatedBy = Guid.NewGuid(),
                CreatedDate = DateTime.Now
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _pastResultService.UpdateResultAsync(pastResult, orgId, subscription));

            Assert.That(exception, Is.Not.Null);
        }

        [Test]
        public async Task DeletePastResultAsync_WhenSuccessful_CompletesSuccessfully()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;
            var pastResult = new PastResult
            {
                ResultId = Guid.NewGuid(),
                PatientId = Guid.NewGuid(),
                OrganizationId = orgId,
                OrderName = "Blood Test",
                ViewResults = "Normal",
                ResultDate = DateTime.Now,
                IsActive = true,
                Subscription = subscription,
                CreatedBy = Guid.NewGuid(),
                UpdatedBy = Guid.NewGuid(),
                CreatedDate = DateTime.Now
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.NoContent,
                Content = new StringContent("Success")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.DoesNotThrowAsync(async () => await _pastResultService.DeletePastResultAsync(pastResult, orgId, subscription));
        }

        [Test]
        public void DeletePastResultAsync_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;
            var pastResult = new PastResult
            {
                ResultId = Guid.NewGuid(),
                PatientId = Guid.NewGuid(),
                OrganizationId = orgId,
                OrderName = "Blood Test",
                ViewResults = "Normal",
                ResultDate = DateTime.Now,
                IsActive = true,
                Subscription = subscription,
                CreatedBy = Guid.NewGuid(),
                UpdatedBy = Guid.NewGuid(),
                CreatedDate = DateTime.Now
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _pastResultService.DeletePastResultAsync(pastResult, orgId, subscription));

            Assert.That(exception.Message, Is.EqualTo("Tasks retrieval failure"));
        }

        [Test]
        public async Task UpdatePastResultListAsync_WhenSuccessful_CompletesSuccessfully()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;
            var pastResults = new List<PastResult>
            {
                new PastResult
                {
                    ResultId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    OrganizationId = orgId,
                    OrderName = "Blood Test 1",
                    ViewResults = "Normal",
                    ResultDate = DateTime.Now,
                    IsActive = true,
                    Subscription = subscription,
                    CreatedBy = Guid.NewGuid(),
                    UpdatedBy = Guid.NewGuid(),
                    CreatedDate = DateTime.Now
                },
                new PastResult
                {
                    ResultId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    OrganizationId = orgId,
                    OrderName = "Blood Test 2",
                    ViewResults = "Abnormal",
                    ResultDate = DateTime.Now,
                    IsActive = true,
                    Subscription = subscription,
                    CreatedBy = Guid.NewGuid(),
                    UpdatedBy = Guid.NewGuid(),
                    CreatedDate = DateTime.Now
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent("Success")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.DoesNotThrowAsync(async () => await _pastResultService.UpdatePastResultListAsync(pastResults, orgId, subscription));
        }

        [Test]
        public void UpdatePastResultListAsync_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;
            var pastResults = new List<PastResult>
            {
                new PastResult
                {
                    ResultId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    OrganizationId = orgId,
                    OrderName = "Blood Test 1",
                    ViewResults = "Normal",
                    ResultDate = DateTime.Now,
                    IsActive = true,
                    Subscription = subscription,
                    CreatedBy = Guid.NewGuid(),
                    UpdatedBy = Guid.NewGuid(),
                    CreatedDate = DateTime.Now
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _pastResultService.UpdatePastResultListAsync(pastResults, orgId, subscription));

            Assert.That(exception.Message, Is.EqualTo("Tasks retrieval failure"));
        }
    }
}



