using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Moq;
using Moq.Protected;
using NUnit.Framework;
using TeyaUIModels.Model;
using TeyaUIViewModels.ViewModel;
using TeyaUIViewModels.TeyaUIViewModelResources;
using System.Net.Http.Json;
using System.Text;

namespace TeyaWebClientUIViewModelTestCases
{
    [TestFixture]
    public class EmployerServiceTests
    {
        private Mock<HttpMessageHandler> _mockHttpMessageHandler;
        private HttpClient _httpClient;
        private Mock<IStringLocalizer<EmployerService>> _mockLocalizer;
        private Mock<ILogger<EmployerService>> _mockLogger;
        private EmployerService _employerService;
        private readonly string _baseUrl = "http://test-api.com";
        private readonly string _accessToken = "test-token";

        [SetUp]
        public void Setup()
        {
            // Setup environment variable
            Environment.SetEnvironmentVariable("MemberServiceURL", _baseUrl);

            // Setup mock HTTP handler
            _mockHttpMessageHandler = new Mock<HttpMessageHandler>();

            // Setup HttpClient with mock handler
            _httpClient = new HttpClient(_mockHttpMessageHandler.Object)
            {
                BaseAddress = new Uri(_baseUrl)
            };

            // Setup mock localizer
            _mockLocalizer = new Mock<IStringLocalizer<EmployerService>>();
            _mockLocalizer.Setup(l => l["Error adding Employer"])
                .Returns(new LocalizedString("Error adding Employer", "Error adding Employer"));
            _mockLocalizer.Setup(l => l["Error updating Employer with ID {id}"])
                .Returns(new LocalizedString("Error updating Employer with ID {id}", "Error updating Employer"));
            _mockLocalizer.Setup(l => l["Failed to fetch all Employeres"])
                .Returns(new LocalizedString("Failed to fetch all Employeres", "Failed to fetch all Employers"));
            _mockLocalizer.Setup(l => l["Error fetching all Employeres"])
                .Returns(new LocalizedString("Error fetching all Employeres", "Error fetching all Employers"));

            // Setup mock logger
            _mockLogger = new Mock<ILogger<EmployerService>>();

            // Create EmployerService with mocked dependencies
            _employerService = new EmployerService(_httpClient, _mockLogger.Object, _mockLocalizer.Object);
        }

        [TearDown]
        public void TearDown()
        {
            _httpClient.Dispose();
            Environment.SetEnvironmentVariable("MemberServiceURL", null);
        }

        [Test]
        public async Task GetEmployerByIdAsync_WhenSuccessful_ReturnsEmployer()
        {
            // Arrange
            var employerId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var expectedEmployer = new Employer
            {
                EmployerId = employerId,
                EmployerName = "Test Company",
                EmployerAddress = "123 Main St",
                EmployerCity = "Test City",
                EmployerState = "TS",
                EmployerPostalCode = "12345",
                EmployerCountry = "Test Country",
                Subscription = true
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedEmployer)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _employerService.GetEmployerByIdAsync(employerId, orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.EmployerId, Is.EqualTo(employerId));
            Assert.That(result.EmployerName, Is.EqualTo("Test Company"));
        }

        [Test]
        public async Task GetAllEmployerAsync_WhenSuccessful_ReturnsEmployers()
        {
            // Arrange
            var expectedEmployers = new List<Employer>
            {
                new Employer
                {
                    EmployerId = Guid.NewGuid(),
                    EmployerName = "Test Company 1",
                    EmployerAddress = "123 Main St",
                    EmployerCity = "Test City",
                    EmployerState = "TS",
                    EmployerPostalCode = "12345",
                    EmployerCountry = "Test Country",
                    Subscription = true
                },
                new Employer
                {
                    EmployerId = Guid.NewGuid(),
                    EmployerName = "Test Company 2",
                    EmployerAddress = "456 Oak St",
                    EmployerCity = "Test City",
                    EmployerState = "TS",
                    EmployerPostalCode = "67890",
                    EmployerCountry = "Test Country",
                    Subscription = true
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedEmployers)
            };

            // Act
            var orgId = Guid.NewGuid();
            var subscription = true;

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            var result = await _employerService.GetAllEmployerAsync(orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(2));
            Assert.That(result[0].EmployerName, Is.EqualTo("Test Company 1"));
            Assert.That(result[1].EmployerName, Is.EqualTo("Test Company 2"));
        }

        [Test]
        public async Task AddEmployerAsync_WhenSuccessful_ReturnsTrue()
        {
            // Arrange
            var employer = new Employer
            {
                EmployerId = Guid.NewGuid(),
                EmployerName = "Test Company",
                EmployerAddress = "123 Main St",
                EmployerCity = "Test City",
                EmployerState = "TS",
                EmployerPostalCode = "12345",
                EmployerCountry = "Test Country",
                Subscription = true
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _employerService.AddEmployerAsync(employer);

            // Assert
            Assert.That(result, Is.True);
        }

        [Test]
        public async Task AddEmployerAsync_WhenHttpRequestFails_ReturnsFalse()
        {
            // Arrange
            var employer = new Employer
            {
                EmployerId = Guid.NewGuid(),
                EmployerName = "Test Company",
                EmployerAddress = "123 Main St",
                EmployerCity = "Test City",
                EmployerState = "TS",
                EmployerPostalCode = "12345",
                EmployerCountry = "Test Country",
                Subscription = true
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _employerService.AddEmployerAsync(employer);

            // Assert
            Assert.That(result, Is.False);
        }

        [Test]
        public async Task UpdateEmployerAsync_WhenSuccessful_ReturnsTrue()
        {
            // Arrange
            var employerId = Guid.NewGuid();
            var employer = new Employer
            {
                EmployerId = employerId,
                EmployerName = "Updated Company",
                EmployerAddress = "123 Main St",
                EmployerCity = "Test City",
                EmployerState = "TS",
                EmployerPostalCode = "12345",
                EmployerCountry = "Test Country",
                Subscription = true
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _employerService.UpdateEmployerAsync(employerId, employer);

            // Assert
            Assert.That(result, Is.True);
        }

        [Test]
        public async Task UpdateEmployerAsync_WhenHttpRequestFails_ReturnsFalse()
        {
            // Arrange
            var employerId = Guid.NewGuid();
            var employer = new Employer
            {
                EmployerId = employerId,
                EmployerName = "Updated Company",
                EmployerAddress = "123 Main St",
                EmployerCity = "Test City",
                EmployerState = "TS",
                EmployerPostalCode = "12345",
                EmployerCountry = "Test Country",
                Subscription = true
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _employerService.UpdateEmployerAsync(employerId, employer);

            // Assert
            Assert.That(result, Is.False);
        }

        [Test]
        public async Task DeleteEmployerAsync_WhenSuccessful_ReturnsTrue()
        {
            // Arrange
            var employerId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _employerService.DeleteEmployerAsync(employerId, orgId, subscription);

            // Assert
            Assert.That(result, Is.True);
        }

        [Test]
        public async Task DeleteEmployerAsync_WhenHttpRequestFails_ReturnsFalse()
        {
            // Arrange
            var employerId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _employerService.DeleteEmployerAsync(employerId, orgId, subscription);

            // Assert
            Assert.That(result, Is.False);
        }

        [Test]
        public async Task GetEmployerByNameAsync_WhenSuccessful_ReturnsEmployers()
        {
            // Arrange
            var employerName = "Test Company";
            var expectedEmployers = new List<Employer>
            {
                new Employer
                {
                    EmployerId = Guid.NewGuid(),
                    EmployerName = "Test Company",
                    EmployerAddress = "123 Main St",
                    EmployerCity = "Test City",
                    EmployerState = "TS",
                    EmployerPostalCode = "12345",
                    EmployerCountry = "Test Country",
                    Subscription = true
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedEmployers)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _employerService.GetEmployerByNameAsync(employerName);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(1));
            Assert.That(result[0].EmployerName, Is.EqualTo("Test Company"));
        }
    }
}



