﻿using Microsoft.AspNetCore.Components;
using MudBlazor;
using TeyaUIModels.Model;
using TeyaUIViewModels.ViewModel;
using System.Timers;

namespace TeyaWebApp.Components.Pages
{
    /// <summary>
    /// Professional cosigning component for document signature workflow
    /// Implements the correct two-step process: Sign -> Choose (Lock or Request Cosigning)
    /// </summary>
    public partial class CosigningComponent : IDisposable
    {
        /// <summary>
        /// Current active user for authentication context
        /// </summary>
        [Inject] private ActiveUser CurrentUser { get; set; }
        [Inject] UserContext UserContext { get; set; }

        /// <summary>
        /// Navigation manager for page navigation
        /// </summary>
        [Inject] private NavigationManager NavigationManager { get; set; }

        /// <summary>
        /// Member service for getting provider list
        /// </summary>
        [Inject] private IMemberService MemberService { get; set; }

        /// <summary>
        /// Cosigning request service for review request workflow
        /// </summary>
        [Inject] private ICosigningRequestService CosigningRequestService { get; set; }

        /// <summary>
        /// The record ID for which cosigning is being performed
        /// </summary>
        [Parameter] public Guid RecordId { get; set; }

        /// <summary>
        /// The patient ID associated with the record
        /// </summary>
        [Parameter] public Guid PatientId { get; set; }

        /// <summary>
        /// The patient name to display in the component
        /// </summary>
        [Parameter] public string PatientName { get; set; } = string.Empty;

        /// <summary>
        /// The patient age to display in the component
        /// </summary>
        [Parameter] public string PatientAge { get; set; } = string.Empty;

        /// <summary>
        /// The patient gender to display in the component
        /// </summary>
        [Parameter] public string PatientGender { get; set; } = string.Empty;

        /// <summary>
        /// The organization ID for the cosigning workflow
        /// </summary>
        [Parameter] public Guid OrganizationId { get; set; }

        /// <summary>
        /// Whether to show the cosigning section
        /// </summary>
        [Parameter] public bool ShowCosigningSection { get; set; } = true;

        /// <summary>
        /// Whether the document requires a cosignature
        /// </summary>
        [Parameter] public bool RequiresCosignature { get; set; } = false;

        /// <summary>
        /// Event callback fired when signature is updated
        /// </summary>
        [Parameter] public EventCallback<Cosigning> OnSignatureUpdated { get; set; }

        /// <summary>
        /// Current cosigning state
        /// </summary>
        private Cosigning CurrentCosigning { get; set; } = new();

        /// <summary>
        /// Current active cosigning request (if any)
        /// </summary>
        private CosigningRequest? ActiveCosigningRequest { get; set; } = null;

        /// <summary>
        /// Flag to track if the current cosigning record exists in the database
        /// </summary>
        private bool IsNewRecord { get; set; } = true;

        /// <summary>
        /// Whether a signing operation is in progress
        /// </summary>
        private bool IsProcessing { get; set; } = false;
        private bool Subscription { get; set; } = false;

        // UI State Management
        private bool _showSigningForm = false;
        private bool _showPostSignActions = false;
        private bool _showCosigningRequestForm = false;
        private List<Member> _providerList = new List<Member>();
        private Member? _selectedReviewProvider = null;

        // Timer for periodic updates
        private System.Timers.Timer? _refreshTimer;
        private readonly int _refreshIntervalMs = 30000; // 30 seconds

        private readonly DialogOptions _dialogOptions = new()
        {
            CloseOnEscapeKey = true,
            MaxWidth = MaxWidth.Medium,
            FullWidth = true
        };

        /// <summary>
        /// Initialize the component and load cosigning status
        /// </summary>
        protected override async Task OnInitializedAsync()
        {
            try
            {
                await LoadCosigningStatus();
                await LoadCosigningRequestStatus();
                StartRefreshTimer();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error initializing cosigning component for record {RecordId}", RecordId);
            }
        }

        /// <summary>
        /// Handle parameter changes and reload cosigning status if needed
        /// </summary>
        protected override async Task OnParametersSetAsync()
        {
            if (RecordId != Guid.Empty)
            {
                await LoadCosigningStatus();
                await LoadCosigningRequestStatus();
            }
        }

        /// <summary>
        /// Load the current cosigning status from the backend
        /// </summary>
        private async Task LoadCosigningStatus()
        {
            try
            {
                Logger.LogInformation("Loading cosigning status for record {RecordId}", RecordId);

                var cosignings = await CosigningService.GetCosigningsByRecordId(RecordId, OrganizationId, Subscription);
                var existingCosigning = cosignings?.OrderByDescending(c => c.Date).FirstOrDefault();

                if (existingCosigning != null)
                {
                    CurrentCosigning = existingCosigning;
                    IsNewRecord = false;
                    Logger.LogInformation("Found existing cosigning record for {RecordId}. IsSigned: {IsSigned}, IsLocked: {IsLocked}",
                        RecordId, CurrentCosigning.IsSigned, CurrentCosigning.IsLocked);
                }
                else
                {
                    // Initialize with default values
                    CurrentCosigning = new Cosigning
                    {
                        RecordId = RecordId,
                        OrganizationId = OrganizationId,
                        IsSigned = false,
                        IsCosigned = false,
                        IsLocked = false,
                        Subscription = Subscription
                    };
                    IsNewRecord = true;
                    Logger.LogInformation("No existing cosigning record found for {RecordId}. Initialized new record.", RecordId);
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error loading cosigning status for record {RecordId}", RecordId);
                Snackbar.Add(Localizer["ErrorLoadingSignatureStatus"], Severity.Error);

                // Initialize with default values on error
                CurrentCosigning = new Cosigning
                {
                    RecordId = RecordId,
                    OrganizationId = OrganizationId,
                    IsSigned = false,
                    IsCosigned = false,
                    IsLocked = false,
                    Subscription = Subscription
                };
                IsNewRecord = true;
            }
        }

        /// <summary>
        /// Load the current cosigning request status
        /// </summary>
        private async Task LoadCosigningRequestStatus()
        {
            try
            {
                ActiveCosigningRequest = await CosigningRequestService.GetActiveRequestAsync(RecordId, OrganizationId, Subscription);
                Logger.LogInformation("Cosigning request status loaded for record {RecordId}. Active request: {HasRequest}",
                    RecordId, ActiveCosigningRequest != null);
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error loading cosigning request status for record {RecordId}", RecordId);
                ActiveCosigningRequest = null;
            }
        }

        /// <summary>
        /// Start the refresh timer for periodic updates
        /// </summary>
        private void StartRefreshTimer()
        {
            _refreshTimer = new System.Timers.Timer(_refreshIntervalMs);
            _refreshTimer.Elapsed += async (sender, e) => await RefreshStatus();
            _refreshTimer.AutoReset = true;
            _refreshTimer.Enabled = true;
        }

        /// <summary>
        /// Refresh the component status
        /// </summary>
        private async Task RefreshStatus()
        {
            try
            {
                await LoadCosigningStatus();
                await LoadCosigningRequestStatus();
                await InvokeAsync(StateHasChanged);
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error refreshing status for record {RecordId}", RecordId);
            }
        }

        /// <summary>
        /// Dispose of resources
        /// </summary>
        public void Dispose()
        {
            _refreshTimer?.Stop();
            _refreshTimer?.Dispose();
        }



        /// <summary>
        /// Get the appropriate color for the status chip
        /// </summary>
        private Color GetStatusColor()
        {
            if (CurrentCosigning.IsLocked) return Color.Warning;
            if (CurrentCosigning.IsCosigned) return Color.Success;
            if (CurrentCosigning.IsSigned && ActiveCosigningRequest != null)
            {
                return ActiveCosigningRequest.Status switch
                {
                    CosigningRequestStatus.Pending => Color.Info,
                    CosigningRequestStatus.Approved => Color.Success,
                    CosigningRequestStatus.ChangesRequested => Color.Warning,
                    _ => Color.Info
                };
            }
            if (CurrentCosigning.IsSigned) return Color.Info;
            return Color.Default;
        }

        /// <summary>
        /// Get the status text for the status chip
        /// </summary>
        private string GetStatusText()
        {
            if (CurrentCosigning.IsLocked) return Localizer["Locked"];
            if (CurrentCosigning.IsCosigned) return Localizer["Cosigned"];
            if (CurrentCosigning.IsSigned && ActiveCosigningRequest != null)
            {
                return ActiveCosigningRequest.Status switch
                {
                    CosigningRequestStatus.Pending => Localizer["CosigningRequested"],
                    CosigningRequestStatus.Approved => Localizer["CosigningApproved"],
                    CosigningRequestStatus.ChangesRequested => Localizer["ChangesRequested"],
                    _ => Localizer["CosigningRequested"]
                };
            }
            if (CurrentCosigning.IsSigned) return Localizer["Signed"];
            return Localizer["PendingSignature"];
        }

        /// <summary>
        /// Public method to refresh the signature status
        /// Can be called from parent components
        /// </summary>
        public async Task RefreshSignatureStatus()
        {
            await LoadCosigningStatus();
            await LoadCosigningRequestStatus();
            StateHasChanged();
        }

        /// <summary>
        /// Check if the component should be visible based on current state
        /// </summary>
        public bool ShouldShowComponent()
        {
            return ShowCosigningSection && RecordId != Guid.Empty;
        }

        /// <summary>
        /// Get the current signature completion percentage for progress indicators
        /// </summary>
        public int GetSignatureProgress()
        {
            if (CurrentCosigning.IsLocked) return 100;
            if (CurrentCosigning.IsCosigned) return 100;
            if (CurrentCosigning.IsSigned && ActiveCosigningRequest?.Status == CosigningRequestStatus.Approved) return 100;
            if (CurrentCosigning.IsSigned && ActiveCosigningRequest != null) return 75;
            if (CurrentCosigning.IsSigned && !RequiresCosignature) return 100;
            if (CurrentCosigning.IsSigned && RequiresCosignature) return 75;
            return 0;
        }

        /// <summary>
        /// Check if the user can perform signing actions
        /// </summary>
        private bool CanPerformSigningActions()
        {
            // Cannot perform actions if document is locked
            if (CurrentCosigning.IsLocked) return false;

            // Cannot perform actions if there's a pending cosigning request
            if (ActiveCosigningRequest?.Status == CosigningRequestStatus.Pending) return false;

            return true;
        }

        /// <summary>
        /// Check if post-sign actions should be shown
        /// </summary>
        private bool ShouldShowPostSignActions()
        {
            return CurrentCosigning.IsSigned &&
                   !CurrentCosigning.IsLocked &&
                   ActiveCosigningRequest?.Status != CosigningRequestStatus.Pending;
        }

        /// <summary>
        /// Show the signing form
        /// </summary>
        private async Task ShowSigningForm()
        {
            try
            {
                // Check if user can perform signing actions
                if (!CanPerformSigningActions())
                {
                    if (CurrentCosigning.IsLocked)
                    {
                        Snackbar.Add(Localizer["DocumentIsLocked"], Severity.Warning);
                    }
                    else if (ActiveCosigningRequest?.Status == CosigningRequestStatus.Pending)
                    {
                        Snackbar.Add(Localizer["CannotSignWithPendingRequest"], Severity.Warning);
                    }
                    return;
                }

                _showSigningForm = true;
                StateHasChanged();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error showing signing form for record {RecordId}", RecordId);
                Snackbar.Add(Localizer["ErrorShowingSigningForm"], Severity.Error);
            }
        }

        /// <summary>
        /// Cancel the signing process
        /// </summary>
        private void CancelSigning()
        {
            _showSigningForm = false;
            _showPostSignActions = false;
            StateHasChanged();
        }

        /// <summary>
        /// Process the signature - Step 1 of the workflow
        /// </summary>
        private async Task ProcessSignature()
        {
            try
            {
                IsProcessing = true;

                // Check if user can perform signing actions
                if (!CanPerformSigningActions())
                {
                    if (CurrentCosigning.IsLocked)
                    {
                        Snackbar.Add(Localizer["DocumentIsLocked"], Severity.Warning);
                    }
                    else if (ActiveCosigningRequest?.Status == CosigningRequestStatus.Pending)
                    {
                        Snackbar.Add(Localizer["CannotSignWithPendingRequest"], Severity.Warning);
                    }
                    return;
                }

                // Validate user information
                if (!Guid.TryParse(CurrentUser.id, out var userId) || userId == Guid.Empty)
                {
                    Logger.LogError("Invalid user ID: {UserId}", CurrentUser.id);
                    Snackbar.Add(Localizer["InvalidUserForSigning"], Severity.Error);
                    return;
                }

                var userName = CurrentUser.displayName ?? CurrentUser.givenName ?? "Unknown User";
                if (string.IsNullOrWhiteSpace(userName) || userName == "Unknown User")
                {
                    Logger.LogError("Invalid user name: {UserName}", userName);
                    Snackbar.Add(Localizer["InvalidUserNameForSigning"], Severity.Error);
                    return;
                }

                var now = DateTime.UtcNow;

                // Process signing
                CurrentCosigning.IsSigned = true;
                CurrentCosigning.SignerId = userId;
                CurrentCosigning.SignerName = userName;
                CurrentCosigning.Date = now;
                CurrentCosigning.LastUpdated = now;
                CurrentCosigning.OrganizationId = OrganizationId;
                CurrentCosigning.Subscription = Subscription;

                // Reset cosigning fields when re-signing
                CurrentCosigning.IsCosigned = false;
                CurrentCosigning.CosignerId = Guid.Empty;
                CurrentCosigning.CosignerName = string.Empty;

                // Save to database
                if (IsNewRecord)
                {
                    CurrentCosigning.Id = Guid.NewGuid();
                    await CosigningService.AddCosigning(new List<Cosigning> { CurrentCosigning });
                    IsNewRecord = false;
                }
                else
                {
                    await CosigningService.UpdateCosigning(CurrentCosigning);
                }

                Logger.LogInformation("Document signed successfully for record {RecordId}", RecordId);
                Snackbar.Add(Localizer["DocumentSignedSuccessfully"], Severity.Success);

                // Close signing form and show post-sign actions
                _showSigningForm = false;
                _showPostSignActions = true;

                await OnSignatureUpdated.InvokeAsync(CurrentCosigning);
                StateHasChanged();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error processing signature for record {RecordId}", RecordId);
                Snackbar.Add(Localizer["ErrorProcessingSignature"], Severity.Error);
                _showSigningForm = false;
            }
            finally
            {
                IsProcessing = false;
            }
        }



        /// <summary>
        /// Lock the document - Step 2A of the workflow
        /// </summary>
        private async Task LockDocument()
        {
            try
            {
                IsProcessing = true;

                // Validate that document is signed
                if (!CurrentCosigning.IsSigned)
                {
                    Snackbar.Add(Localizer["DocumentMustBeSignedFirst"], Severity.Warning);
                    return;
                }

                // Check if there's a pending cosigning request
                if (ActiveCosigningRequest?.Status == CosigningRequestStatus.Pending)
                {
                    Snackbar.Add(Localizer["CannotLockWithPendingRequest"], Severity.Warning);
                    return;
                }

                Logger.LogInformation("Locking document for record {RecordId}", RecordId);

                CurrentCosigning.IsLocked = true;
                CurrentCosigning.LastUpdated = DateTime.UtcNow;

                await CosigningService.UpdateCosigning(CurrentCosigning);

                Logger.LogInformation("Document locked successfully for record {RecordId}", RecordId);
                Snackbar.Add(Localizer["DocumentLockedSuccessfully"], Severity.Success);

                _showPostSignActions = false;
                await OnSignatureUpdated.InvokeAsync(CurrentCosigning);
                StateHasChanged();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error locking document for record {RecordId}", RecordId);
                Snackbar.Add(Localizer["ErrorLockingDocument"], Severity.Error);
            }
            finally
            {
                IsProcessing = false;
            }
        }

        /// <summary>
        /// Show the cosigning request form - Step 2B of the workflow
        /// </summary>
        private async Task ShowCosigningRequestForm()
        {
            try
            {
                // Validate that document is signed
                if (!CurrentCosigning.IsSigned)
                {
                    Snackbar.Add(Localizer["DocumentMustBeSignedFirst"], Severity.Warning);
                    return;
                }

                // Check if there's already a pending request
                if (ActiveCosigningRequest?.Status == CosigningRequestStatus.Pending)
                {
                    Snackbar.Add(Localizer["CosigningRequestAlreadyExists"], Severity.Warning);
                    return;
                }

                await LoadProviderList();
                _selectedReviewProvider = null;
                _showCosigningRequestForm = true;
                StateHasChanged();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error showing cosigning request form for record {RecordId}", RecordId);
                Snackbar.Add(Localizer["ErrorShowingRequestForm"], Severity.Error);
            }
        }

        /// <summary>
        /// Submit a cosigning request
        /// </summary>
        private async Task SubmitCosigningRequest()
        {
            try
            {
                if (_selectedReviewProvider == null)
                {
                    Snackbar.Add(Localizer["PleaseSelectProvider"], Severity.Warning);
                    return;
                }

                IsProcessing = true;

                var request = new CosigningRequest
                {
                    Id = Guid.NewGuid(),
                    RecordId = RecordId,
                    RequesterId = Guid.Parse(CurrentUser.id),
                    ReviewerId = _selectedReviewProvider.Id,
                    RequesterName = CurrentUser.displayName ?? CurrentUser.givenName ?? "Unknown User",
                    ReviewerName = _selectedReviewProvider.UserName,
                    Status = CosigningRequestStatus.Pending,
                    RequestedDate = DateTime.UtcNow,
                    OrganizationId = OrganizationId,
                    Subscription = Subscription,
                    PatientName = PatientName,
                    PatientAge = PatientAge,
                    PatientGender = PatientGender
                };

                await CosigningRequestService.CreateRequestAsync(request, OrganizationId, Subscription);

                Logger.LogInformation("Cosigning request created for record {RecordId} to provider {ProviderId}", RecordId, _selectedReviewProvider.Id);
                Snackbar.Add(Localizer["CosigningRequestSentSuccessfully"], Severity.Success);

                _showCosigningRequestForm = false;
                _showPostSignActions = false;
                await LoadCosigningRequestStatus();
                StateHasChanged();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error submitting cosigning request for record {RecordId}", RecordId);
                Snackbar.Add(Localizer["ErrorSubmittingCosigningRequest"], Severity.Error);
            }
            finally
            {
                IsProcessing = false;
            }
        }

        /// <summary>
        /// Cancel the cosigning request form
        /// </summary>
        private void CancelCosigningRequest()
        {
            _showCosigningRequestForm = false;
            _selectedReviewProvider = null;
            StateHasChanged();
        }

        /// <summary>
        /// Get the signature text for display in the primary signature field
        /// </summary>
        private string GetSignatureText()
        {
            var lines = new List<string>();

            if (CurrentCosigning.IsSigned)
            {
                lines.Add($"Electronically Signed by {CurrentCosigning.SignerName} on {CurrentCosigning.Date:MM/dd/yyyy}");
            }

            if (CurrentCosigning.IsCosigned)
            {
                lines.Add($"Electronically Cosigned by {CurrentCosigning.CosignerName} on {CurrentCosigning.LastUpdated:MM/dd/yyyy}");
            }

            if (CurrentCosigning.IsLocked)
            {
                lines.Add("Notes Locked!!");
            }

            return string.Join("\n", lines);
        }

        /// <summary>
        /// Get the appropriate text for the sign button based on current state
        /// </summary>
        private string GetSignButtonText()
        {
            if (CurrentCosigning.IsSigned)
            {
                return Localizer["Re-Sign"];
            }
            else
            {
                return Localizer["Sign"];
            }
        }

        /// <summary>
        /// Load the provider list for cosigning requests
        /// </summary>
        private async Task LoadProviderList()
        {
            try
            {
                // Clear existing list
                _providerList.Clear();
                _selectedReviewProvider = null;

                // Get providers from service
                var providers = await MemberService.GetProviderlistAsync(OrganizationId, Subscription);

                // Convert string providers to Member objects if needed
                if (providers != null && providers.Any())
                {
                    foreach (var provider in providers)
                    {
                        // Skip current user from provider list
                        if (provider == CurrentUser.displayName)
                            continue;

                        _providerList.Add(new Member
                        {
                            Id = Guid.NewGuid(), // This is just a placeholder for UI selection
                            UserName = provider
                        });
                    }
                }

                Logger.LogInformation("Loaded {Count} providers for cosigning requests", _providerList.Count);
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error loading provider list for organization {OrganizationId}", OrganizationId);
                Snackbar.Add(Localizer["ErrorLoadingProviders"], Severity.Error);
            }
        }

        /// <summary>
        /// Cancel the active cosigning request
        /// </summary>
        private async Task CancelActiveCosigningRequest()
        {
            try
            {
                if (ActiveCosigningRequest == null) return;

                IsProcessing = true;

                await CosigningRequestService.CancelRequestAsync(
                    ActiveCosigningRequest.Id,
                    OrganizationId,
                    Subscription);

                ActiveCosigningRequest = null;
                _showPostSignActions = true; // Show post-sign actions again

                Snackbar.Add(Localizer["CosigningRequestCancelled"], Severity.Info);
                StateHasChanged();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error cancelling cosigning request for record {RecordId}", RecordId);
                Snackbar.Add(Localizer["ErrorCancellingRequest"], Severity.Error);
            }
            finally
            {
                IsProcessing = false;
            }
        }

        /// <summary>
        /// Get the severity for the cosigning request alert
        /// </summary>
        private Severity GetCosigningRequestSeverity()
        {
            if (ActiveCosigningRequest == null) return Severity.Info;

            return ActiveCosigningRequest.Status switch
            {
                CosigningRequestStatus.Pending => Severity.Info,
                CosigningRequestStatus.Approved => Severity.Success,
                CosigningRequestStatus.ChangesRequested => Severity.Warning,
                _ => Severity.Info
            };
        }

        /// <summary>
        /// Get the icon for the cosigning request alert
        /// </summary>
        private string GetCosigningRequestIcon()
        {
            if (ActiveCosigningRequest == null) return Icons.Material.Filled.Info;

            return ActiveCosigningRequest.Status switch
            {
                CosigningRequestStatus.Pending => Icons.Material.Filled.Schedule,
                CosigningRequestStatus.Approved => Icons.Material.Filled.CheckCircle,
                CosigningRequestStatus.ChangesRequested => Icons.Material.Filled.Comment,
                _ => Icons.Material.Filled.Info
            };
        }

        /// <summary>
        /// Get the status text for the cosigning request
        /// </summary>
        private string GetCosigningRequestStatusText()
        {
            if (ActiveCosigningRequest == null) return string.Empty;

            return ActiveCosigningRequest.Status switch
            {
                CosigningRequestStatus.Pending => Localizer["CosigningRequestPending"],
                CosigningRequestStatus.Approved => Localizer["CosigningApproved"],
                CosigningRequestStatus.ChangesRequested => Localizer["ChangesRequested"],
                _ => ActiveCosigningRequest.Status.ToString()
            };
        }

        /*
        /// <summary>
        /// Cancel the cosigning request form
        /// </summary>
        private void CancelCosigningRequest()
        {
            _showCosigningRequestForm = false;
            _selectedReviewProvider = null;
            StateHasChanged();
        }

        /// <summary>
        /// Cancel the active cosigning request
        /// </summary>
        private async Task CancelActiveCosigningRequest()
        {
            try
            {
                if (ActiveCosigningRequest == null) return;

                IsProcessing = true;

                // Call API to cancel the request
                await CosigningRequestService.CancelRequestAsync(
                    ActiveCosigningRequest.Id,
                    OrganizationId,
                    Subscription);

                // Clear the active request
                ActiveCosigningRequest = null;
                _showPostSignActions = false;

                Snackbar.Add(Localizer["CosigningRequestCancelled"], Severity.Info);
                StateHasChanged();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error cancelling cosigning request for record {RecordId}", RecordId);
                Snackbar.Add(Localizer["ErrorCancellingRequest"], Severity.Error);
            }
            finally
            {
                IsProcessing = false;
            }
        }
        */
    }
}