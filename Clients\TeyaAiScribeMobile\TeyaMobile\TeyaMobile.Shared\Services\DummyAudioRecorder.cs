﻿namespace TeyaMobile.Shared.Services
{
    using TeyaHealthMobileModel.Model;
    using TeyaHealthMobileViewModel.ViewModel;

    namespace TeyaMobile.Shared.Services
    {
        public class DummyAudioRecorder : IAudioRecorder
        {
            public RecordingState RecordingState => RecordingState.Stopped;

            public event EventHandler<RecordingState>? RecordingStateChanged;
            public event EventHandler<Exception>? ErrorOccurred;

            public void SetNextRecordingId(Guid recordingId)
            {
                // No-op for web
            }

            public Task StartRecordingAsync()
            {
                // Web uses JavaScript, so this is a no-op
                return Task.CompletedTask;
            }

            public Task PauseRecordingAsync()
            {
                return Task.CompletedTask;
            }

            public Task ResumeRecordingAsync()
            {
                return Task.CompletedTask;
            }

            public Task<string> StopRecordingAsync()
            {
                // Web doesn't return file paths since upload is handled by JavaScript
                return Task.FromResult(string.Empty);
            }

            public double GetRecordingDuration()
            {
                return 0;
            }

            public bool SupportsPauseResume()
            {
                return true; // Web supports pause/resume via JavaScript
            }

            public string GetFileExtension()
            {
                return ".webm"; // Web typically uses WebM format
            }
        }
    }

}
