﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace TeyaMobile.TeyaMobileResource {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class TeyaMobileRes {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal TeyaMobileRes() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("TeyaMobile.TeyaMobileResource.TeyaMobileRes", typeof(TeyaMobileRes).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to def.
        /// </summary>
        internal static string abc {
            get {
                return ResourceManager.GetString("abc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Chunks merged into final WAV file..
        /// </summary>
        internal static string ChunksMerged {
            get {
                return ResourceManager.GetString("ChunksMerged", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Failed to initialize recorder: {0}.
        /// </summary>
        internal static string FailedToInitializeRecorder {
            get {
                return ResourceManager.GetString("FailedToInitializeRecorder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Failed to start recording: {0}.
        /// </summary>
        internal static string FailedToStartRecording {
            get {
                return ResourceManager.GetString("FailedToStartRecording", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Failed to stop recording: {0}.
        /// </summary>
        internal static string FailedToStopRecording {
            get {
                return ResourceManager.GetString("FailedToStopRecording", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Microphone permission denied.
        /// </summary>
        internal static string MicrophonePermissionDenied {
            get {
                return ResourceManager.GetString("MicrophonePermissionDenied", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pause.
        /// </summary>
        internal static string PauseButtonText {
            get {
                return ResourceManager.GetString("PauseButtonText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pause functionality is not supported on Android versions below 7.0 (API level 24)..
        /// </summary>
        internal static string PauseRecordingBLExcep {
            get {
                return ResourceManager.GetString("PauseRecordingBLExcep", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Recorded file is empty or incomplete..
        /// </summary>
        internal static string RecordedFileEmpty {
            get {
                return ResourceManager.GetString("RecordedFileEmpty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Recorded file does not exist..
        /// </summary>
        internal static string RecordedFileNotExist {
            get {
                return ResourceManager.GetString("RecordedFileNotExist", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Recorder is not currently recording..
        /// </summary>
        internal static string RecorderNotRecording {
            get {
                return ResourceManager.GetString("RecorderNotRecording", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Recorder is not recording or not paused.
        /// </summary>
        internal static string RecorderNotRecordingOrNotPaused {
            get {
                return ResourceManager.GetString("RecorderNotRecordingOrNotPaused", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Recording started successfully..
        /// </summary>
        internal static string RecordingStartedSuccessfully {
            get {
                return ResourceManager.GetString("RecordingStartedSuccessfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Recording stopped successfully. File path: {0}.
        /// </summary>
        internal static string RecordingStoppedSuccessfully {
            get {
                return ResourceManager.GetString("RecordingStoppedSuccessfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Resources cleaned up..
        /// </summary>
        internal static string ResClean {
            get {
                return ResourceManager.GetString("ResClean", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Resume functionality is not supported on Android versions below 7.0 (API level 24)..
        /// </summary>
        internal static string ResumeRecordingBLExcep {
            get {
                return ResourceManager.GetString("ResumeRecordingBLExcep", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Start.
        /// </summary>
        internal static string StartButtonText {
            get {
                return ResourceManager.GetString("StartButtonText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Starting recording....
        /// </summary>
        internal static string StartingRecording {
            get {
                return ResourceManager.GetString("StartingRecording", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Teya AI Scribe.
        /// </summary>
        internal static string StatusLabelText {
            get {
                return ResourceManager.GetString("StatusLabelText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Stop.
        /// </summary>
        internal static string StopButtonText {
            get {
                return ResourceManager.GetString("StopButtonText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 00:00.
        /// </summary>
        internal static string TimerLabelText {
            get {
                return ResourceManager.GetString("TimerLabelText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Teya AI Scribe.
        /// </summary>
        internal static string TitleTH {
            get {
                return ResourceManager.GetString("TitleTH", resourceCulture);
            }
        }
    }
}
