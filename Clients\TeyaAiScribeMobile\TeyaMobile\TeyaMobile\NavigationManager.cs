﻿namespace TeyaMobile
{
    public class NavigationManager
    {
        private INavigation _navigation;

        public void SetNavigation(INavigation navigation)
        {
            _navigation = navigation;
        }

        public async Task PushAsync(Page page)
        {
            await Shell.Current.Navigation.PushAsync(page);
        }

        public async Task PopAsync()
        {
            await Shell.Current.Navigation.PopAsync();
        }

        public async Task PopToRootAsync()
        {
            if (_navigation == null)
            {
                throw new InvalidOperationException("INavigation has not been set. Call SetNavigation first.");
            }

            if (_navigation.NavigationStack.Count > 1)
            {
                await _navigation.PopToRootAsync();
            }
        }
    }
}
