name: <PERSON><PERSON>-Appservice-Website-Dev

on:
  workflow_dispatch:
  schedule:
    # Runs at 00:00 UTC (midnight) every day
    - cron: '0 0 * * *'

env:
  APP_SERVICE_NAME: Teya-Website-Dev
  RESOURCE_GROUP: teyahealth-rg-dev-eastus-001
  CONTAINER_REGISTRY_LOGIN_SERVER: teyahealthdev.azurecr.io
  DOCKER_FILE_PATH: Clients/TeyaWebsite/TeyaWebsite/Dockerfile
  PROJECT_NAME_FOR_DOCKER: teyawebsite

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Manually log in to ACR (no auto env)
        uses: docker/login-action@v3
        with:
          registry: ${{ env.CONTAINER_REGISTRY_LOGIN_SERVER }}
          username: TeyaHealthDev
          password: ****************************************************

      - name: Build and push container image
        uses: docker/build-push-action@v5
        with:
          push: true
          tags: ${{ env.CONTAINER_REGISTRY_LOGIN_SERVER }}/${{ env.PROJECT_NAME_FOR_DOCKER }}:${{ github.sha }}
          file: ${{ env.DOCKER_FILE_PATH }}

      - name: Azure Login using Key Vault-based secret
        uses: azure/login@v1
        with:
          creds: ${{ secrets.DEVREPOSECRET }}

      - name: Set App Settings with Key Vault References
        run: |
          az webapp config appsettings set \
            --name ${{ env.APP_SERVICE_NAME }} \
            --resource-group ${{ env.RESOURCE_GROUP }} \
            --settings \
              KEYVAULT__URLS='@Microsoft.KeyVault(SecretUri=https://teyawebappvault-dev.vault.azure.net/secrets/keyvault-urls/bcfedda1d6d14293816c00807aefc2c6)' \
              KEYVAULT__TENANTID='@Microsoft.KeyVault(SecretUri=https://commonvault-dev.vault.azure.net/secrets/keyvault-tenantid/fb43f0f90b5941a28988a5d1d2f973bb)' \
              KEYVAULT__CLIENTID='@Microsoft.KeyVault(SecretUri=https://commonvault-dev.vault.azure.net/secrets/keyvault-clientid/903514adfd5f42b69e0080384ecf5aff)' \
              KEYVAULT__CLIENTSECRET='@Microsoft.KeyVault(SecretUri=https://commonvault-dev.vault.azure.net/secrets/keyvault-clientsecret/913c55db16a94b1981db54965e0096ca)' \
              ENVIRONMENT_KEY='@Microsoft.KeyVault(SecretUri=https://teyawebappvault-dev.vault.azure.net/secrets/environment-key/03447a630df641f5afaab9ece607840e)'

      - name: Deploy Docker image to Azure App Service via CLI
        run: |
          az webapp config container set \
            --name ${{ env.APP_SERVICE_NAME }} \
            --resource-group ${{ env.RESOURCE_GROUP }} \
            --docker-custom-image-name ${{ env.CONTAINER_REGISTRY_LOGIN_SERVER }}/${{ env.PROJECT_NAME_FOR_DOCKER }}:${{ github.sha }} \
            --docker-registry-server-url https://${{ env.CONTAINER_REGISTRY_LOGIN_SERVER }}

      - name: Logout from Azure
        run: az logout
