﻿using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Routing;
using Microsoft.Extensions.Logging;
using Microsoft.JSInterop;
using System.Text.Json;
using System.Timers;
using TeyaHealthMobileModel.Model;
using TeyaHealthMobileViewModel.ViewModel;
using TeyaMobile.Shared.Scripts;
using TeyaMobile.Shared.Services;

namespace TeyaMobile.Shared.Pages
{
    public partial class AudioRecorderComponent : ComponentBase, IAsyncDisposable
    {
        [Parameter]
        public Guid PatientId { get; set; }
        [SupplyParameterFromQuery(Name = "providerId")]
        public Guid ProviderId { get; set; }
        [SupplyParameterFromQuery(Name = "appointmentId")]
        public string AppointmentId { get; set; } = string.Empty;
        [SupplyParameterFromQuery(Name = "visitType")]
        public string VisitType { get; set; } = string.Empty;
        [SupplyParameterFromQuery(Name = "orgId")]

        public Guid? OrganizationId { get; set; }
        [SupplyParameterFromQuery(Name = "sub")]
        public bool Subscription { get; set; }
        [SupplyParameterFromQuery(Name = "ptname")]
        public string PatientName { get; set; } = string.Empty;

        [Inject] private IAudioRecorder? AudioRecorder { get; set; }
        [Inject] private ILogger<AudioRecorderComponent>? Logger { get; set; }
        [Inject] private IFormFactor FormFactor { get; set; } = default!;
        [Inject] protected IJSRuntime JSRuntime { get; set; } = default!;
        [Inject] protected NavigationManager Navigation { get; set; } = default!;
        [Inject] protected ISpeechService SpeechService { get; set; } = default!;
        [Inject] protected IPredefinedTemplateService PredefinedTemplateService { get; set; } = default!;
        [Inject] protected TeyaHealthMobileViewModel.ViewModel.IAuthenticationService AuthService { get; set; } = default!;
        [Inject] private IProgressNotesService ProgressNotesService { get; set; } = default!;

        // Platform detection properties using IFormFactor
        private string CurrentPlatform => FormFactor.GetFormFactor();
        private bool IsWeb => CurrentPlatform == "Web";
        private bool IsNativePlatform => CurrentPlatform == "Phone";

        private string LiveTranscriptionText = "";
        private ElementReference transcriptionEditor;

        private List<TemplateData> ProviderData = new();
        private bool IsRecording = false;
        private bool IsPaused = false;
        private bool IsProcessing = false;
        private int RecordingDuration = 0;
        private string TranscriptionText = "";
        private Guid CurrentRecordingId = Guid.Empty;
        private Guid currentRecordId { get; set; }
        private bool HasCompletedRecording = false;
        private string filepathtoprocess = "";
        private AudioRecorderInterop? _audioInterop;
        private System.Timers.Timer? _durationTimer;
        private System.Timers.Timer? _animationTimer;
        private Random _random = new Random();
        private DotNetObjectReference<AudioRecorderComponent>? _dotNetRef;
        private DateTime _recordingStartTime;
        private TimeSpan _totalPausedDuration = TimeSpan.Zero;
        private DateTime? _pauseStartTime;
        private bool _disposed = false;
        private List<Record> records = new();
        private Record record;
        private readonly CancellationTokenSource _componentCancellationToken = new();
        private string LoaderContext = "";

        protected override async Task OnInitializedAsync()
        {
            if (_disposed) return;

            try
            {
                _dotNetRef = DotNetObjectReference.Create(this);
                Logger?.LogInformation($"Initializing AudioRecorderComponent on platform: {CurrentPlatform}");

                if (Navigation != null)
                {
                    Navigation.LocationChanged += OnLocationChanged;
                }

                await InitializePlatformComponents();
            }
            catch (Exception ex)
            {
                Logger?.LogError(ex, "Error during AudioRecorderComponent initialization");
                await HandleError($"Initialization failed: {ex.Message}");
            }
        }

        private async Task InitializePlatformComponents()
        {
            if (_disposed) return;

            try
            {
                // Only register AudioRecorder events if it exists (native platforms)
                if (IsNativePlatform && AudioRecorder != null)
                {
                    AudioRecorder.RecordingStateChanged += OnRecordingStateChanged;
                    AudioRecorder.ErrorOccurred += OnErrorOccurred;
                    Logger?.LogInformation("AudioRecorder events registered for native platform");
                }

                if (SpeechService != null)
                {
                    SpeechService.OnPartialTranscription += OnPartialTranscription;
                    SpeechService.OnFinalTranscription += OnFinalTranscription;
                    SpeechService.OnError += OnSpeechError;
                    Logger?.LogInformation("SpeechService events registered");
                }
            }
            catch (Exception ex)
            {
                Logger?.LogError(ex, "Error initializing platform components");
                throw;
            }
        }

        private async void OnLocationChanged(object? sender, LocationChangedEventArgs e)
        {
            try
            {
                // If we're navigating away from this component, ensure cleanup
                if (!e.Location.Contains("audio-recorder") && !e.Location.Contains("recorder"))
                {
                    Logger?.LogInformation("Navigation detected, cleaning up AudioRecorderComponent");
                    await CleanupRecordingState();
                }
            }
            catch (Exception ex)
            {
                Logger?.LogError(ex, "Error during navigation cleanup");
            }
        }

        private async Task CleanupRecordingState()
        {
            if (_disposed) return;

            try
            {
                // Stop any active recording
                if (IsRecording)
                {
                    Logger?.LogInformation("Stopping active recording due to navigation");
                    await StopRecording();
                }

                // Stop timers
                StopTimers();

                // Reset component state
                ResetComponentState();
            }
            catch (Exception ex)
            {
                Logger?.LogError(ex, "Error during recording cleanup");
            }
        }

        private void ResetComponentState()
        {
            IsRecording = false;
            IsPaused = false;
            IsProcessing = false;
            RecordingDuration = 0;
            CurrentRecordingId = Guid.Empty;
            HasCompletedRecording = false;
            TranscriptionText = "";
            LiveTranscriptionText = "";
            _totalPausedDuration = TimeSpan.Zero;
            _pauseStartTime = null;
        }

        public async ValueTask DisposeAsync()
        {
            if (_disposed) return;

            try
            {
                _disposed = true;
                Logger?.LogInformation($"Disposing AudioRecorderComponent on {CurrentPlatform}");

                // Cancel any ongoing operations
                _componentCancellationToken.Cancel();

                // Cleanup recording state
                await CleanupRecordingState();

                // Unregister navigation events
                if (Navigation != null)
                {
                    Navigation.LocationChanged -= OnLocationChanged;
                }

                // Dispose platform-specific components
                await DisposePlatformComponents();

                // Dispose managed resources
                _dotNetRef?.Dispose();
                _componentCancellationToken?.Dispose();

                Logger?.LogInformation($"AudioRecorderComponent disposed successfully on {CurrentPlatform}");
            }
            catch (Exception ex)
            {
                Logger?.LogError(ex, "Error during AudioRecorderComponent disposal");
            }
        }

        private async Task DisposePlatformComponents()
        {
            try
            {
                // Unregister AudioRecorder events
                if (AudioRecorder != null)
                {
                    AudioRecorder.RecordingStateChanged -= OnRecordingStateChanged;
                    AudioRecorder.ErrorOccurred -= OnErrorOccurred;
                }

                // Unregister SpeechService events
                if (SpeechService != null)
                {
                    SpeechService.OnPartialTranscription -= OnPartialTranscription;
                    SpeechService.OnFinalTranscription -= OnFinalTranscription;
                    SpeechService.OnError -= OnSpeechError;

                    // Dispose SpeechService if it implements IDisposable
                    if (SpeechService is IDisposable disposableSpeechService)
                    {
                        disposableSpeechService.Dispose();
                    }
                }

                // Dispose web-specific components
                if (IsWeb && _audioInterop != null)
                {
                    _audioInterop.Dispose();
                    _audioInterop = null;
                }
            }
            catch (Exception ex)
            {
                Logger?.LogError(ex, "Error disposing platform components");
            }
        }

        private void OnPartialTranscription(object? sender, string text)
        {
            LiveTranscriptionText = text;
            TranscriptionText += text + " ";
            InvokeAsync(StateHasChanged);
        }

        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            if (firstRender && IsWeb && JSRuntime != null)
            {
                try
                {
                    _audioInterop = new AudioRecorderInterop(JSRuntime, this);
                    await _audioInterop.InitializeAsync();
                    Logger?.LogInformation("Web audio interop initialized");
                }
                catch (Exception ex)
                {
                    Logger?.LogError(ex, "Failed to initialize web audio interop");
                    await HandleError($"Web audio initialization failed: {ex.Message}");
                }
            }
        }

        // Fixed: Wave GIF visibility logic as requested
        private bool ShouldShowWaveGif()
        {
            return IsRecording && !IsPaused;
        }

        private async Task StartRecording()
        {
            if (_disposed)
            {
                Logger?.LogWarning("Attempted to start recording on disposed component");
                return;
            }

            try
            {
                Logger?.LogInformation($"Starting recording on platform: {CurrentPlatform}");
                IsProcessing = true;
                LoaderContext = "start";
                StateHasChanged();

                CurrentRecordingId = Guid.NewGuid();
                TranscriptionText = "";
                LiveTranscriptionText = "";
                _recordingStartTime = DateTime.Now;
                _totalPausedDuration = TimeSpan.Zero;
                _pauseStartTime = null;

                if (IsWeb)
                {
                    await StartWebRecording();
                }
                else if (IsNativePlatform)
                {
                    await StartNativeRecording();
                }

                StartTimers();
                IsRecording = true;
                IsPaused = false;
                IsProcessing = false;
                HasCompletedRecording = false;

                await StartSpeechRecognition();

                Logger?.LogInformation($"Recording started successfully with ID: {CurrentRecordingId} on {CurrentPlatform}");
                StateHasChanged();
            }
            catch (Exception ex)
            {
                Logger?.LogError(ex, "Failed to start recording");
                IsProcessing = false;
                await HandleError($"Failed to start recording: {ex.Message}");
            }
        }

        private async Task StartWebRecording()
        {
            if (_audioInterop != null)
            {
                await _audioInterop.StartRecordAsync();
                Logger?.LogInformation("Web recording started");
            }
            else
            {
                throw new InvalidOperationException("Web audio interop not initialized");
            }
        }

        private async Task StartNativeRecording()
        {
            if (AudioRecorder != null)
            {
                AudioRecorder.SetNextRecordingId(CurrentRecordingId);
                await AudioRecorder.StartRecordingAsync();
                Logger?.LogInformation("Native recording started on Phone platform");
            }
            else
            {
                throw new InvalidOperationException("AudioRecorder service not available");
            }
        }

        private async Task StartSpeechRecognition()
        {
            if (SpeechService != null)
            {
                if (IsWeb)
                {
                    await SpeechService.StartTranscriptionAsync(CurrentRecordingId);
                }
                else
                {
                    await SpeechService.StartContinuousRecognitionAsync();
                }
                Logger?.LogInformation($"Speech recognition started on {CurrentPlatform}");
            }
        }

        private async Task PauseResumeRecording()
        {
            try
            {
                if (IsPaused)
                {
                    await ResumeRecording();
                }
                else
                {
                    await PauseRecording();
                }
            }
            catch (Exception ex)
            {
                Logger?.LogError(ex, "Failed to pause/resume recording");
                await HandleError($"Failed to pause/resume recording: {ex.Message}");
            }
        }

        private async Task PauseRecording()
        {
            Logger?.LogInformation($"Pausing recording on {CurrentPlatform}");
            _pauseStartTime = DateTime.Now;

            if (IsWeb && _audioInterop != null)
            {
                await _audioInterop.PauseRecordAsync();
            }
            else if (IsNativePlatform && AudioRecorder != null)
            {
                await AudioRecorder.PauseRecordingAsync();
            }

            if (SpeechService != null && !IsWeb)
            {
                await SpeechService.StopContinuousRecognitionAsync();
            }

            IsPaused = true;
            _durationTimer?.Stop();
            Logger?.LogInformation("Recording paused");
            StateHasChanged();
        }

        private async Task ResumeRecording()
        {
            Logger?.LogInformation($"Resuming recording on {CurrentPlatform}");

            if (_pauseStartTime.HasValue)
            {
                _totalPausedDuration += DateTime.Now - _pauseStartTime.Value;
                _pauseStartTime = null;
            }

            if (IsWeb && _audioInterop != null)
            {
                await _audioInterop.ResumeRecordAsync();
            }
            else if (IsNativePlatform && AudioRecorder != null)
            {
                await AudioRecorder.ResumeRecordingAsync();
            }

            if (SpeechService != null && !IsWeb)
            {
                await SpeechService.StartContinuousRecognitionAsync();
            }

            IsPaused = false;
            _durationTimer?.Start();
            Logger?.LogInformation("Recording resumed");
            StateHasChanged();
        }

        private async Task StopRecording()
        {
            if (_disposed)
            {
                Logger?.LogWarning("Attempted to stop recording on disposed component");
                return;
            }

            try
            {
                Logger?.LogInformation($"Stopping recording on {CurrentPlatform}");
                IsProcessing = true;
                StateHasChanged();

                StopTimers();

                string filePath = "";

                if (IsWeb)
                {
                    await StopWebRecording();
                }
                else if (IsNativePlatform)
                {
                    filePath = await StopNativeRecording();
                }

                filepathtoprocess = filePath;
                // Set states for post-recording
                IsRecording = false;
                IsPaused = false;
                RecordingDuration = 0;
                HasCompletedRecording = true;
                IsProcessing = false;

                // Clear live transcription but keep complete transcription
                LiveTranscriptionText = "";

                Logger?.LogInformation("Recording stopped successfully");
                StateHasChanged();
            }
            catch (Exception ex)
            {
                Logger?.LogError(ex, "Failed to stop recording");
                IsProcessing = false;
                await HandleError($"Failed to stop recording: {ex.Message}");
            }
        }

        private async Task StopWebRecording()
        {
            if (_audioInterop != null)
            {
                await _audioInterop.StopRecordAsync(CurrentRecordingId);
                Logger?.LogInformation("Web recording stopped");
            }
        }

        private async Task<string> StopNativeRecording()
        {
            string filePath = "";

            if (AudioRecorder != null)
            {
                filePath = await AudioRecorder.StopRecordingAsync();
                Logger?.LogInformation($"Native recording stopped on Phone platform. File path: {filePath}");
            }
            else
            {
                Logger?.LogWarning("AudioRecorder not available for stopping native recording");
            }

            return filePath;
        }

        private async Task ProcessRecordingCompletion(string filePath)
        {
            if (SpeechService == null)
            {
                Logger?.LogWarning("SpeechService not available for processing recording completion");
                return;
            }

            try
            {
                await AddNewRecord();

                Logger?.LogInformation($"Processing recording completion on {CurrentPlatform}");

                if (IsWeb)
                {
                    await SpeechService.StopTranscriptionAsync(
                        currentRecordId,
                        ProviderId,
                        PatientId,
                        PatientName,
                        VisitType,
                        OrganizationId,
                        Subscription
                    );
                    Logger?.LogInformation("Web transcription stopped and processed");
                }
                else if (IsNativePlatform)
                {
                    await SpeechService.StopContinuousRecognitionAsync();

                    if (!string.IsNullOrEmpty(filePath) && System.IO.File.Exists(filePath))
                    {
                        Logger?.LogInformation($"Uploading audio file from Phone platform: {filePath}");
                        await SpeechService.UploadAudioToBackendAsync(filePath);
                        Logger?.LogInformation("Audio file uploaded successfully");

                        Logger?.LogInformation("Posting transcriptions to backend from Phone platform");
                        await SpeechService.PostTranscriptionsAsync(
                            currentRecordId,
                            ProviderId,
                            PatientId,
                            PatientName,
                            VisitType,
                            OrganizationId,
                            Subscription
                        );
                        Logger?.LogInformation("Transcriptions posted successfully");
                    }
                    else
                    {
                        Logger?.LogWarning($"Audio file not found or empty path: {filePath}. Posting transcriptions without file.");
                        await SpeechService.PostTranscriptionsAsync(
                            currentRecordId,
                            ProviderId,
                            PatientId,
                            PatientName,
                            VisitType,
                            OrganizationId,
                            Subscription
                        );
                    }
                }
            }
            catch (Exception ex)
            {
                Logger?.LogError(ex, "Failed to process recording completion");
                await HandleError($"Failed to process recording completion: {ex.Message}");
            }
        }

        private async Task AddNewRecord()
        {
            try
            {
                // Get the selected template
                TemplateData selectedTemplate = ProviderData.FirstOrDefault(t => t.IsDefault == true && t.VisitType == VisitType);
                if (selectedTemplate == null)
                {
                    var predefinedTemplates = await PredefinedTemplateService.GetTemplatesAsync();
                    selectedTemplate = predefinedTemplates.FirstOrDefault(t => t.VisitType == "Default");
                    if (selectedTemplate == null)
                    {
                        Logger?.LogError("Template Not Found");
                        return;
                    }
                }

                // Parse the template JSON
                var templateStructure = JsonSerializer.Deserialize<Dictionary<string, Dictionary<string, Dictionary<string, string>>>>(selectedTemplate.Template);

                // Transform template structure into record structure
                var recordNotes = new Dictionary<string, Dictionary<string, string>>();

                foreach (var section in templateStructure)
                {
                    var sectionName = section.Key;
                    var fields = section.Value;

                    var recordSection = new Dictionary<string, string>();

                    foreach (var field in fields)
                    {
                        // Initialize each field with empty content
                        recordSection[field.Key] = string.Empty;
                    }

                    recordNotes[sectionName] = recordSection;
                }

                currentRecordId = Guid.NewGuid();
                // Create a new record with the transformed structure
                var newRecord = new Record
                {
                    Id = currentRecordId,
                    PatientName = PatientName ?? "New Patient",
                    PatientId = PatientId,
                    PCPId = ProviderId,
                    DateTime = DateTime.Now,
                    Notes = JsonSerializer.Serialize(recordNotes), // Use the transformed structure
                    isEditable = true,
                    OrganizationId = (Guid)OrganizationId,
                    Transcription = string.Empty,
                    WordTimings = null,
                    Template = selectedTemplate.Template,
                };

                // Save the new record
                var response = await ProgressNotesService.UploadRecordAsync(newRecord, OrganizationId, Subscription);
                if (response.IsSuccessStatusCode)
                {
                    Logger?.LogInformation("RecordAddedSuccessfully");

                    records = await ProgressNotesService.GetRecordsByPatientIdAsync(PatientId, OrganizationId, Subscription);
                    // Fix for CS0266, CS1662, and CS8619
                    records = records.Where(record => record.isEditable == true)
                                     .OrderByDescending(record => record.DateTime)
                                     .ToList();
                    record = records.FirstOrDefault();

                    StateHasChanged();
                }
                else
                {
                    Logger?.LogInformation("ErrorAddingRecord");
                }
            }
            catch (Exception ex)
            {
                Logger?.LogWarning(ex, "ErrorAddingRecord");
            }
        }

        private async Task ResetAudioInterop()
        {
            if (IsWeb && _audioInterop != null)
            {
                try
                {
                    _audioInterop.Dispose();
                    _audioInterop = new AudioRecorderInterop(JSRuntime, this);
                    await _audioInterop.InitializeAsync();
                }
                catch (Exception ex)
                {
                    Logger?.LogWarning(ex, "Failed to reset audio interop");
                }
            }
        }

        private async Task ResetSpeechService()
        {
            if (SpeechService != null)
            {
                try
                {
                    if (IsWeb)
                    {
                        await SpeechService.StopTranscriptionAsync(CurrentRecordingId, ProviderId, PatientId, PatientName, VisitType, OrganizationId, Subscription);
                    }
                    else
                    {
                        await SpeechService.StopContinuousRecognitionAsync();
                    }
                }
                catch (Exception ex)
                {
                    Logger?.LogWarning(ex, "Failed to reset speech service");
                }
            }
        }

        private async Task ReRecord()
        {
            try
            {
                Logger?.LogInformation("Re-recording initiated - resetting component state");

                StopTimers();

                HasCompletedRecording = false;
                IsRecording = false;
                IsPaused = false;
                IsProcessing = false;
                RecordingDuration = 0;
                CurrentRecordingId = Guid.Empty;

                TranscriptionText = "";
                LiveTranscriptionText = "";

                _totalPausedDuration = TimeSpan.Zero;
                _pauseStartTime = null;
                _recordingStartTime = default;

                if (IsWeb && _audioInterop != null)
                {
                    try
                    {
                        await ResetAudioInterop();
                        Logger?.LogInformation("Web audio interop reset");
                    }
                    catch (Exception ex)
                    {
                        Logger?.LogWarning(ex, "Failed to reset web audio interop");
                    }
                }

                if (IsNativePlatform && AudioRecorder != null)
                {
                    try
                    {
                        await ResetAudioInterop();
                        Logger?.LogInformation("Native audio recorder reset");
                    }
                    catch (Exception ex)
                    {
                        Logger?.LogWarning(ex, "Failed to reset native audio recorder");
                    }
                }

                if (SpeechService != null)
                {
                    try
                    {
                        await ResetSpeechService();
                        Logger?.LogInformation("Speech service reset");
                    }
                    catch (Exception ex)
                    {
                        Logger?.LogWarning(ex, "Failed to reset speech service");
                    }
                }

                Logger?.LogInformation("Re-record completed - component reset to initial state");
                StateHasChanged();
            }
            catch (Exception ex)
            {
                Logger?.LogError(ex, "Failed to re-record");
                await HandleError($"Failed to re-record: {ex.Message}");
            }
        }

        private async Task ProcessRecording()
        {
            try
            {
                IsProcessing = true;
                LoaderContext = "process";
                StateHasChanged();

                await ProcessRecordingCompletion(filepathtoprocess);

                Logger?.LogInformation("Processing recording - navigating to SOAP notes");
                await InvokeAsync(() =>
                {
                    Navigation.NavigateTo($"/soapnotes/{PatientId}?orgId={OrganizationId}&sub={Subscription}");
                });
                // Do not set IsProcessing = false here, as navigation will dispose the component
            }
            catch (Exception ex)
            {
                Logger?.LogError(ex, "Failed to process recording");
                await HandleError($"Failed to process recording: {ex.Message}");
            }
        }

        private TimeSpan GetEffectiveRecordingDuration()
        {
            var totalDuration = DateTime.Now - _recordingStartTime;
            var effectiveDuration = totalDuration - _totalPausedDuration;

            if (_pauseStartTime.HasValue)
            {
                var currentPauseDuration = DateTime.Now - _pauseStartTime.Value;
                effectiveDuration -= currentPauseDuration;
            }

            return effectiveDuration;
        }

        private void StartTimers()
        {
            RecordingDuration = 0;
            _durationTimer = new System.Timers.Timer(1000);
            _durationTimer.Elapsed += OnDurationTimerElapsed;
            _durationTimer.Start();

            if (IsWeb)
            {
                _animationTimer = new System.Timers.Timer(100);
                _animationTimer.Elapsed += OnAnimationTimerElapsed;
                _animationTimer.Start();
            }
        }

        private void StopTimers()
        {
            _durationTimer?.Stop();
            _durationTimer?.Dispose();
            _durationTimer = null;

            _animationTimer?.Stop();
            _animationTimer?.Dispose();
            _animationTimer = null;
        }

        private void OnDurationTimerElapsed(object? sender, ElapsedEventArgs e)
        {
            RecordingDuration++;
            InvokeAsync(StateHasChanged);
        }

        private void OnAnimationTimerElapsed(object? sender, ElapsedEventArgs e)
        {
            InvokeAsync(StateHasChanged);
        }

        // Event handlers for native platforms
        private void OnRecordingStateChanged(object? sender, RecordingState state)
        {
            Logger?.LogInformation($"Recording state changed on {CurrentPlatform}: {state}");
            InvokeAsync(StateHasChanged);
        }

        private void OnErrorOccurred(object? sender, Exception ex)
        {
            Logger?.LogError(ex, $"AudioRecorder error occurred on {CurrentPlatform}");
            InvokeAsync(() => HandleError($"Recording error: {ex.Message}"));
        }

        private void OnFinalTranscription(object? sender, string text)
        {
            Logger?.LogInformation($"Final transcription received on {CurrentPlatform}: {text}");
        }

        private void OnSpeechError(object? sender, Exception ex)
        {
            Logger?.LogError(ex, $"Speech recognition error on {CurrentPlatform}");
            InvokeAsync(() => HandleError($"Speech recognition error: {ex.Message}"));
        }

        // JSInvokable methods for web platform
        [JSInvokable]
        public async Task ProcessAudioChunk(string base64AudioChunk)
        {
            if (SpeechService != null)
            {
                await SpeechService.ProcessAudioChunk(base64AudioChunk);
            }
        }

        [JSInvokable]
        public async Task OnRecordingComplete(string recordId)
        {
            Logger?.LogInformation($"Web recording completed: {recordId}");
        }

        [JSInvokable]
        public async Task OnAudioDetected(bool isDetected)
        {
            await InvokeAsync(StateHasChanged);
        }

        [JSInvokable]
        public async Task OnRecordingError(string error)
        {
            Logger?.LogError("Web recording error: {Error}", error);
            await HandleError($"Web recording error: {error}");
        }

        // Helper Methods
        private string GetStatusClass()
        {
            if (IsProcessing) return "status-processing";
            if (IsRecording && IsPaused) return "status-paused";
            if (IsRecording) return "status-recording";
            return "status-ready";
        }

        private string GetStatusText()
        {
            if (IsProcessing) return "Processing...";
            if (IsRecording && IsPaused) return "Paused";
            if (IsRecording) return "Recording...";
            return "AI Scribe";
        }

        private string FormatDuration(int seconds)
        {
            var timeSpan = TimeSpan.FromSeconds(seconds);
            return timeSpan.ToString(@"mm\:ss");
        }

        private async Task HandleError(string message)
        {
            Logger?.LogError("AudioRecorder Error on {Platform}: {Message}", CurrentPlatform, message);
            Console.WriteLine($"Audio Recorder Error on {CurrentPlatform}: {message}");
            IsProcessing = false;
            StateHasChanged();
        }

        public void Dispose()
        {
            try
            {
                StopTimers();

                if (IsNativePlatform && AudioRecorder != null)
                {
                    AudioRecorder.RecordingStateChanged -= OnRecordingStateChanged;
                    AudioRecorder.ErrorOccurred -= OnErrorOccurred;
                }

                if (SpeechService != null)
                {
                    SpeechService.OnPartialTranscription -= OnPartialTranscription;
                    SpeechService.OnFinalTranscription -= OnFinalTranscription;
                    SpeechService.OnError -= OnSpeechError;
                }

                _audioInterop?.Dispose();
                _dotNetRef?.Dispose();

                Logger?.LogInformation($"AudioRecorderComponent disposed successfully on {CurrentPlatform}");
            }
            catch (Exception ex)
            {
                Logger?.LogError(ex, "Error during AudioRecorderComponent disposal");
            }
        }
    }
}
