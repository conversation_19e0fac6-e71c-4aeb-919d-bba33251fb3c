using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Moq;
using Moq.Protected;
using NUnit.Framework;
using TeyaUIModels.Model;
using TeyaUIViewModels.ViewModel;
using TeyaUIViewModels.TeyaUIViewModelResources;
using System.Net.Http.Json;
using System.Text.Json;

namespace TeyaWebClientUIViewModelTestCases
{
    [TestFixture]
    public class SoapNotesComponentsServiceTests
    {
        private Mock<HttpMessageHandler> _mockHttpMessageHandler;
        private HttpClient _httpClient;
        private Mock<IConfiguration> _mockConfiguration;
        private Mock<IStringLocalizer<TeyaUIViewModelsStrings>> _mockLocalizer;
        private Mock<ILogger<SoapNotesComponent>> _mockLogger;
        private Mock<ITokenService> _mockTokenService;
        private SoapNotesComponentsService _soapNotesComponentsService;
        private readonly string _baseUrl = "http://test-api.com";
        private readonly string _accessToken = "test-access-token";

        [SetUp]
        public void Setup()
        {
            // Setup environment variable
            Environment.SetEnvironmentVariable("EncounterNotesURL", _baseUrl);

            // Setup mock HTTP handler
            _mockHttpMessageHandler = new Mock<HttpMessageHandler>();

            // Setup HttpClient with mock handler
            _httpClient = new HttpClient(_mockHttpMessageHandler.Object)
            {
                BaseAddress = new Uri(_baseUrl)
            };

            // Setup mock configuration
            _mockConfiguration = new Mock<IConfiguration>();

            // Setup mock localizer
            _mockLocalizer = new Mock<IStringLocalizer<TeyaUIViewModelsStrings>>();
            _mockLocalizer.Setup(l => l["AddressRetrievalFailure"])
                .Returns(new LocalizedString("AddressRetrievalFailure", "Address retrieval failure"));

            // Setup mock logger
            _mockLogger = new Mock<ILogger<SoapNotesComponent>>();

            // Setup mock token service
            _mockTokenService = new Mock<ITokenService>();
            _mockTokenService.Setup(t => t.AccessToken).Returns(_accessToken);

            // Create SoapNotesComponentsService with mocked dependencies
            _soapNotesComponentsService = new SoapNotesComponentsService(_httpClient, _mockConfiguration.Object, _mockLocalizer.Object, _mockLogger.Object, _mockTokenService.Object);
        }

        [TearDown]
        public void TearDown()
        {
            _httpClient.Dispose();
            Environment.SetEnvironmentVariable("EncounterNotesURL", null);
        }

        [Test]
        public async Task GetAllDetailsAsync_WhenSuccessful_ReturnsSoapNotesComponents()
        {
            // Arrange
            var expectedComponents = new List<SoapNotesComponent>
            {
                new SoapNotesComponent
                {
                    Id = Guid.NewGuid(),
                    Name = "Subjective"
                },
                new SoapNotesComponent
                {
                    Id = Guid.NewGuid(),
                    Name = "Objective"
                },
                new SoapNotesComponent
                {
                    Id = Guid.NewGuid(),
                    Name = "Assessment"
                },
                new SoapNotesComponent
                {
                    Id = Guid.NewGuid(),
                    Name = "Plan"
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedComponents)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _soapNotesComponentsService.GetAllDetailsAsync();

            // Assert
            Assert.That(result, Is.Not.Null);
            var resultList = new List<SoapNotesComponent>(result);
            Assert.That(resultList.Count, Is.EqualTo(expectedComponents.Count));
            Assert.That(resultList[0].Id, Is.EqualTo(expectedComponents[0].Id));
            Assert.That(resultList[0].Name, Is.EqualTo(expectedComponents[0].Name));

            // Verify all SOAP components are present
            Assert.That(resultList[0].Name, Is.EqualTo("Subjective"));
            Assert.That(resultList[1].Name, Is.EqualTo("Objective"));
            Assert.That(resultList[2].Name, Is.EqualTo("Assessment"));
            Assert.That(resultList[3].Name, Is.EqualTo("Plan"));
        }

        [Test]
        public void GetAllDetailsAsync_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.NotFound,
                Content = new StringContent("Not found")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _soapNotesComponentsService.GetAllDetailsAsync());

            Assert.That(exception.Message, Is.EqualTo("Address retrieval failure"));
        }

        [Test]
        public async Task GetAllDetailsAsync_WhenEmptyResponse_ReturnsEmptyCollection()
        {
            // Arrange
            var expectedComponents = new List<SoapNotesComponent>();

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedComponents)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _soapNotesComponentsService.GetAllDetailsAsync();

            // Assert
            Assert.That(result, Is.Not.Null);
            var resultList = new List<SoapNotesComponent>(result);
            Assert.That(resultList.Count, Is.EqualTo(0));
        }

        [Test]
        public void GetAllDetailsAsync_WhenExceptionThrown_PropagatesException()
        {
            // Arrange
            var expectedException = new HttpRequestException("Test exception");

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ThrowsAsync(expectedException);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _soapNotesComponentsService.GetAllDetailsAsync());

            Assert.That(exception, Is.Not.Null);
            Assert.That(exception.Message, Is.EqualTo("Test exception"));
        }

        [Test]
        public void GetAllDetailsAsync_WhenInternalServerError_ThrowsHttpRequestException()
        {
            // Arrange
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.InternalServerError,
                Content = new StringContent("Internal server error")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _soapNotesComponentsService.GetAllDetailsAsync());

            Assert.That(exception.Message, Is.EqualTo("Address retrieval failure"));
        }

        [Test]
        public void GetAllDetailsAsync_WhenBadRequest_ThrowsHttpRequestException()
        {
            // Arrange
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _soapNotesComponentsService.GetAllDetailsAsync());

            Assert.That(exception.Message, Is.EqualTo("Address retrieval failure"));
        }

        [Test]
        public async Task GetAllDetailsAsync_WhenSingleComponent_ReturnsSingleItem()
        {
            // Arrange
            var expectedComponents = new List<SoapNotesComponent>
            {
                new SoapNotesComponent
                {
                    Id = Guid.NewGuid(),
                    Name = "Custom Component"
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedComponents)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _soapNotesComponentsService.GetAllDetailsAsync();

            // Assert
            Assert.That(result, Is.Not.Null);
            var resultList = new List<SoapNotesComponent>(result);
            Assert.That(resultList.Count, Is.EqualTo(1));
            Assert.That(resultList[0].Name, Is.EqualTo("Custom Component"));
        }

        [Test]
        public async Task GetAllDetailsAsync_WhenComplexComponentData_PreservesAllProperties()
        {
            // Arrange
            var expectedComponents = new List<SoapNotesComponent>
            {
                new SoapNotesComponent
                {
                    Id = Guid.NewGuid(),
                    Name = "Complex Component with Special Characters: !@#$%^&*()"
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedComponents)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _soapNotesComponentsService.GetAllDetailsAsync();

            // Assert
            Assert.That(result, Is.Not.Null);
            var resultList = new List<SoapNotesComponent>(result);
            Assert.That(resultList.Count, Is.EqualTo(1));
            var component = resultList[0];
            Assert.That(component.Name, Is.EqualTo(expectedComponents[0].Name));
        }
    }
}



