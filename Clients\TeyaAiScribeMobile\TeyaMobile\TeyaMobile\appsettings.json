{
  "Graph_Auth_Scope": "https://graph.microsoft.com/.default",
  "GRAPH_API_BASE_URL": "https://graph.microsoft.com/v1.0/",
  "MemberServiceURL": "************************************/test/",
  "EXTENSION_ID_ORGANIZATION_NAME": "extension_8a2d87f30a864e7e8b70f49083a6ff68_OrganizationName",
  "EXTENSION_ID_ADDRESS": "extension_8a2d87f30a864e7e8b70f49083a6ff68_Address",
  "ServicePrincipleClientId": "d20b72c2-619c-4b74-bb31-2194e8e5a137",
  "ServicePrincipleTenantId": "03a052f6-4a19-4ae7-8ed7-47b794e0e597",
  "ServicePrincipleSecret": "****************************************",
  "AzureAd": {
    "Instance": "https://TeyaHealthDevAuth.ciamlogin.com/",
    "Domain": "TeyaHealthDevAuth.onmicrosoft.com",
    "TenantId": "03a052f6-4a19-4ae7-8ed7-47b794e0e597",
    "ClientId": "e21369d6-92b3-446b-b981-0291bcb29b1b",
    "ClientSecret": "****************************************",
    "CallbackPath": "/signin-oidc",
    "SignedOutCallbackPath": "/signout-callback-oidc",
    "Scopes": [
      "openid",
      "profile",
      "email",
      "offline_access",
      "api://e21369d6-92b3-446b-b981-0291bcb29b1b/access_as_user",
      "User.Read"
    ]
  },
  "EncounterNotesURL": "************************************/encounternotes-api-dev",
  //"EncounterNotesURL": "http://********/EncounterNotesService",
  "AZURE_BLOB_CONNECTION_STRING": "DefaultEndpointsProtocol=https;AccountName=teyarecordingsdev;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net",
  "AZURE_BLOB_CONTAINER_NAME": "audiofiles",
  "AZURE_SPEECH_API_KEY": "91SxUNRBf4Q5jx9PUNcFCAXwDjymNH7yoqudOgaYX9vYtWGP0JIZJQQJ99ALACYeBjFXJ3w3AAAYACOGwBTm",
  "AZURE_REGION": "eastus",
  "AZURE_LANGUAGE": "en-US",
  "AudioUrl": "https://teyarecording.blob.core.windows.net/audiofiles",
  "AUTH_CLIENT_ID": "e21369d6-92b3-446b-b981-0291bcb29b1b",
  "AUTH_AUTHORITY": "https://TeyaHealthDevAuth.ciamlogin.com/03a052f6-4a19-4ae7-8ed7-47b794e0e597/v2.0",
  "AUTH_CLIENT_SECRET": "****************************************",
  "AUTH_RESPONSE_TYPE": "code",
  "AUTH_SAVE_TOKENS": "true",
  "AUTH_CALLBACK_PATH": "/signin-oidc",
  "AUTH_SCOPE_0": "api://e21369d6-92b3-446b-b981-0291bcb29b1b/access_as_user",
  "AUTH_SCOPE_1": "openid",
  "AUTH_SCOPE_2": "profile",
  "AUTH_SCOPE_3": "api://e21369d6-92b3-446b-b981-0291bcb29b1b/access_as_user",
  "AUTH_SCOPE_4": "https://graph.microsoft.com/.default",
  "AzureBusConnectionString": "Endpoint=sb://teyahealth-dev.servicebus.windows.net/;SharedAccessKeyName=Task;SharedAccessKey=ZEy2iTOVqSjOxSEWSyiqN/8cRPDS+ASbOYB2DM=;EntityPath=tasks",
  "TopicName": "tasks",
  "PrimaryDomain": "TeyaHealthDevAuth.onmicrosoft.com",
  "ROLE_SEARCH_URL": "************************************/test/Roles/search",
  "ORGANIZATIONS_API_URL": "************************************/test/Organizations/search",
  "AUTH_LOGOUT_URI": "https://TeyaHealthDevAuth.ciamlogin.com/0319-4ae7-8ed7-47b79597/oauth2/v2.0/logout",
  "AUTH_POST_LOGOUT_URI": "https://localhost:7170/",
  "RegistrationUrl": "************************************/test/registration",
  "OrganizationsUrl": "************************************/test/Organizations",
  "AppointmentsUrl": "************************************/testap",
  "SyncfusionKey": "Mzg4OTIzM0AzMjM5MmUzMDJlMzAzYjMyMzkzYlJwa09TaVM0bUVZdnhmOGFWR0plYWd0TFFTQzFSNjRmV0JSTkpJTlBQMlk9;Mgo+DSMBMAY9C3t2XFhhQlJHfVldX2pWfFN0QHNYf1R0dV9EYUwgOX1dQl9mSXhTdEVgWH5beX1dQ2VXU00=;Mgo+DSMBPh8sVXJ9S0d+X1JPcEBAVHxLflFzVWJZdVpxfldHcC0sT3RfQFhjT35RdkZiW39ecHdXRWteWA==;Mzg4OTIzNkAzMjM5MmUzMDJlMzAzYjMyMzkzYlMvRHlQMUFCOVV6VU1BVE0rMjdIUjFYallkQXhoY1lVQmVVNVpHNUZXblU9;NRAiBiAaIQQuGjN/V09+XU9HdVREQmFAYVF2R2ZJfl56cFRMYl1BNQtUQF1hTH5VdkdjWn5ccHRURGdaWkZ/;ORg4AjUWIQA/Gnt2XFhhQlJHfVldX2pWfFN0QHNYf1R0dV9EYUwgOX1dQl9mSXhTdEVgWH1ecHVRRWJXU00=;Mzg4OTIzOUAzMjM5MmUzMDJlMzAzYjMyMzkzYlJwa09TaVM0bUVZdnhmOGFWR0plYWd0TFFTQzFSNjRmV0JSTkpJTlBQMlk9",
  "RedisConnectionString": "TeyaHealthRedisCache.redis.cache.windows.net:6380,password=lyvUJ8XxAnKfamb8vvRKfl2tSwZreIrRAAzCaPaM4w0=,ssl=True,abortConnect=False"
}