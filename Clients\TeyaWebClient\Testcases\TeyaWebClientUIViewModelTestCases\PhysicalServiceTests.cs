using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using Moq;
using Moq.Protected;
using NUnit.Framework;
using TeyaUIModels.Model;
using TeyaUIViewModels.ViewModel;
using TeyaUIViewModels.TeyaUIViewModelResources;
using System.Net.Http.Json;
using System.Text.Json;

namespace TeyaWebClientUIViewModelTestCases
{
    [TestFixture]
    public class PhysicalServiceTests
    {
        private Mock<HttpMessageHandler> _mockHttpMessageHandler;
        private HttpClient _httpClient;
        private Mock<IConfiguration> _mockConfiguration;
        private Mock<IStringLocalizer<TeyaUIViewModelsStrings>> _mockLocalizer;
        private Mock<ITokenService> _mockTokenService;
        private PhysicalService _physicalService;
        private readonly string _baseUrl = "http://test-api.com";
        private readonly string _accessToken = "test-access-token";

        [SetUp]
        public void Setup()
        {
            // Setup environment variable
            Environment.SetEnvironmentVariable("EncounterNotesURL", _baseUrl);

            // Setup mock HTTP handler
            _mockHttpMessageHandler = new Mock<HttpMessageHandler>();

            // Setup HttpClient with mock handler
            _httpClient = new HttpClient(_mockHttpMessageHandler.Object)
            {
                BaseAddress = new Uri(_baseUrl)
            };

            // Setup mock configuration
            _mockConfiguration = new Mock<IConfiguration>();

            // Setup mock localizer
            _mockLocalizer = new Mock<IStringLocalizer<TeyaUIViewModelsStrings>>();
            _mockLocalizer.Setup(l => l["AddressRetrievalFailure"])
                .Returns(new LocalizedString("AddressRetrievalFailure", "Address retrieval failure"));
            _mockLocalizer.Setup(l => l["TasksRetrievalFailure"])
                .Returns(new LocalizedString("TasksRetrievalFailure", "Tasks retrieval failure"));

            // Setup mock token service
            _mockTokenService = new Mock<ITokenService>();
            _mockTokenService.Setup(t => t.AccessToken).Returns(_accessToken);
            _mockTokenService.Setup(t => t.GetValidatedAccessTokenAsync()).ReturnsAsync(_accessToken);

            // Create PhysicalService with mocked dependencies
            _physicalService = new PhysicalService(_httpClient, _mockConfiguration.Object, _mockLocalizer.Object, _mockTokenService.Object);
        }

        [TearDown]
        public void TearDown()
        {
            _httpClient.Dispose();
            Environment.SetEnvironmentVariable("EncounterNotesURL", null);
        }

        [Test]
        public async Task GetExaminationsByIdAsync_WhenSuccessful_ReturnsExaminations()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var expectedExaminations = new List<Physicalexamination>
            {
                new Physicalexamination
                {
                    ExaminationId = Guid.NewGuid(),
                    PatientId = patientId,
                    Skin = "Normal",
                    Rash = "None",
                    Tester = "Dr. Smith",
                    Moles = "None observed",
                    IsActive = true
                },
                new Physicalexamination
                {
                    ExaminationId = Guid.NewGuid(),
                    PatientId = patientId,
                    Skin = "Dry",
                    Rash = "Mild eczema",
                    Tester = "Dr. Johnson",
                    Moles = "Two small moles",
                    IsActive = true
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedExaminations)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.Is<HttpRequestMessage>(req =>
                        req.Method == HttpMethod.Get &&
                        req.RequestUri.ToString() == $"{_baseUrl}/api/PhysicalExamination/{patientId}/{orgId}/{subscription}" &&
                        req.Headers.Authorization.Scheme == "Bearer" &&
                        req.Headers.Authorization.Parameter == _accessToken),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _physicalService.GetExaminationsByIdAsync(patientId, orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(expectedExaminations.Count));
            Assert.That(result[0].ExaminationId, Is.EqualTo(expectedExaminations[0].ExaminationId));
            Assert.That(result[0].PatientId, Is.EqualTo(expectedExaminations[0].PatientId));
            Assert.That(result[0].Skin, Is.EqualTo(expectedExaminations[0].Skin));
            Assert.That(result[0].Rash, Is.EqualTo(expectedExaminations[0].Rash));
            Assert.That(result[0].Tester, Is.EqualTo(expectedExaminations[0].Tester));
            Assert.That(result[0].Moles, Is.EqualTo(expectedExaminations[0].Moles));
            Assert.That(result[0].IsActive, Is.EqualTo(expectedExaminations[0].IsActive));
        }

        [Test]
        public void GetExaminationsByIdAsync_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.NotFound,
                Content = new StringContent("Not found")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.Is<HttpRequestMessage>(req =>
                        req.Method == HttpMethod.Get &&
                        req.RequestUri.ToString() == $"{_baseUrl}/api/PhysicalExamination/{patientId}/{orgId}/{subscription}"),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _physicalService.GetExaminationsByIdAsync(patientId, orgId, subscription));

            Assert.That(exception.Message, Is.EqualTo("Address retrieval failure"));
        }

        [Test]
        public async Task GetExaminationByIdAsyncAndIsActive_WhenSuccessful_ReturnsActiveExaminations()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var expectedExaminations = new List<Physicalexamination>
            {
                new Physicalexamination
                {
                    ExaminationId = Guid.NewGuid(),
                    PatientId = patientId,
                    Skin = "Normal",
                    Rash = "None",
                    Tester = "Dr. Smith",
                    Moles = "None observed",
                    IsActive = true
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedExaminations)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.Is<HttpRequestMessage>(req =>
                        req.Method == HttpMethod.Get &&
                        req.RequestUri.ToString() == $"{_baseUrl}/api/PhysicalExamination/{patientId}/IsActive/{orgId}/{subscription}" &&
                        req.Headers.Authorization.Scheme == "Bearer" &&
                        req.Headers.Authorization.Parameter == _accessToken),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _physicalService.GetExaminationByIdAsyncAndIsActive(patientId, orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(expectedExaminations.Count));
            Assert.That(result[0].IsActive, Is.True);
        }

        [Test]
        public void GetExaminationByIdAsyncAndIsActive_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.NotFound,
                Content = new StringContent("Not found")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.Is<HttpRequestMessage>(req =>
                        req.Method == HttpMethod.Get &&
                        req.RequestUri.ToString() == $"{_baseUrl}/api/PhysicalExamination/{patientId}/IsActive/{orgId}/{subscription}"),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _physicalService.GetExaminationByIdAsyncAndIsActive(patientId, orgId, subscription));

            Assert.That(exception.Message, Is.EqualTo("Address retrieval failure"));
        }

        [Test]
        public async Task AddExaminationAsync_WhenSuccessful_CompletesSuccessfully()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;
            var examinations = new List<Physicalexamination>
            {
                new Physicalexamination
                {
                    ExaminationId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    Skin = "Normal",
                    Rash = "None",
                    Tester = "Dr. Smith",
                    Moles = "None observed",
                    IsActive = true
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.Created,
                Content = new StringContent("Success")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.Is<HttpRequestMessage>(req =>
                        req.Method == HttpMethod.Post &&
                        req.RequestUri.ToString() == $"{_baseUrl}/api/PhysicalExamination/AddExamination/{orgId}/{subscription}" &&
                        req.Headers.Authorization.Scheme == "Bearer" &&
                        req.Headers.Authorization.Parameter == _accessToken),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.DoesNotThrowAsync(async () => await _physicalService.AddExaminationAsync(examinations, orgId, subscription));
        }

        [Test]
        public void AddExaminationAsync_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;
            var examinations = new List<Physicalexamination>
            {
                new Physicalexamination
                {
                    ExaminationId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    Skin = "Normal",
                    Rash = "None",
                    Tester = "Dr. Smith",
                    Moles = "None observed",
                    IsActive = true
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.Is<HttpRequestMessage>(req =>
                        req.Method == HttpMethod.Post &&
                        req.RequestUri.ToString() == $"{_baseUrl}/api/PhysicalExamination/AddExamination/{orgId}/{subscription}"),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _physicalService.AddExaminationAsync(examinations, orgId, subscription));

            Assert.That(exception.Message, Is.EqualTo("Tasks retrieval failure"));
        }

        [Test]
        public async Task UpdateExaminationAsync_WhenSuccessful_CompletesSuccessfully()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;
            var examination = new Physicalexamination
            {
                ExaminationId = Guid.NewGuid(),
                PatientId = Guid.NewGuid(),
                Skin = "Normal Updated",
                Rash = "None Updated",
                Tester = "Dr. Smith Updated",
                Moles = "None observed Updated",
                IsActive = true
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent("Success")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.Is<HttpRequestMessage>(req =>
                        req.Method == HttpMethod.Put &&
                        req.RequestUri.ToString() == $"{_baseUrl}/api/PhysicalExamination/{examination.PatientId}/{orgId}/{subscription}" &&
                        req.Headers.Authorization.Scheme == "Bearer" &&
                        req.Headers.Authorization.Parameter == _accessToken),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.DoesNotThrowAsync(async () => await _physicalService.UpdateExaminationAsync(examination, orgId, subscription));
        }

        [Test]
        public void UpdateExaminationAsync_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;
            var examination = new Physicalexamination
            {
                ExaminationId = Guid.NewGuid(),
                PatientId = Guid.NewGuid(),
                Skin = "Normal Updated",
                Rash = "None Updated",
                Tester = "Dr. Smith Updated",
                Moles = "None observed Updated",
                IsActive = true
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.Is<HttpRequestMessage>(req =>
                        req.Method == HttpMethod.Put &&
                        req.RequestUri.ToString() == $"{_baseUrl}/api/PhysicalExamination/{examination.PatientId}/{orgId}/{subscription}"),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _physicalService.UpdateExaminationAsync(examination, orgId, subscription));

            Assert.That(exception, Is.Not.Null);
        }

        [Test]
        public async Task DeleteExaminationAsync_WhenSuccessful_CompletesSuccessfully()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;
            var examination = new Physicalexamination
            {
                ExaminationId = Guid.NewGuid(),
                PatientId = Guid.NewGuid(),
                Skin = "Normal",
                Rash = "None",
                Tester = "Dr. Smith",
                Moles = "None observed",
                IsActive = true
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.NoContent,
                Content = new StringContent("Success")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.Is<HttpRequestMessage>(req =>
                        req.Method == HttpMethod.Delete &&
                        req.RequestUri.ToString() == $"{_baseUrl}/{orgId}/{subscription}" &&
                        req.Headers.Authorization.Scheme == "Bearer" &&
                        req.Headers.Authorization.Parameter == _accessToken),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.DoesNotThrowAsync(async () => await _physicalService.DeleteExaminationAsync(examination, orgId, subscription));
        }

        [Test]
        public void DeleteExaminationAsync_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;
            var examination = new Physicalexamination
            {
                ExaminationId = Guid.NewGuid(),
                PatientId = Guid.NewGuid(),
                Skin = "Normal",
                Rash = "None",
                Tester = "Dr. Smith",
                Moles = "None observed",
                IsActive = true
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.Is<HttpRequestMessage>(req =>
                        req.Method == HttpMethod.Delete &&
                        req.RequestUri.ToString() == $"{_baseUrl}/{orgId}/{subscription}"),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _physicalService.DeleteExaminationAsync(examination, orgId, subscription));

            Assert.That(exception.Message, Is.EqualTo("Tasks retrieval failure"));
        }

        [Test]
        public async Task UpdateExaminationListAsync_WhenSuccessful_CompletesSuccessfully()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;
            var examinations = new List<Physicalexamination>
            {
                new Physicalexamination
                {
                    ExaminationId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    Skin = "Normal 1",
                    Rash = "None 1",
                    Tester = "Dr. Smith 1",
                    Moles = "None observed 1",
                    IsActive = true
                },
                new Physicalexamination
                {
                    ExaminationId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    Skin = "Normal 2",
                    Rash = "None 2",
                    Tester = "Dr. Smith 2",
                    Moles = "None observed 2",
                    IsActive = true
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent("Success")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.Is<HttpRequestMessage>(req =>
                        req.Method == HttpMethod.Put &&
                        req.RequestUri.ToString() == $"{_baseUrl}/api/PhysicalExamination/UpdateExaminationList/{orgId}/{subscription}" &&
                        req.Headers.Authorization.Scheme == "Bearer" &&
                        req.Headers.Authorization.Parameter == _accessToken),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.DoesNotThrowAsync(async () => await _physicalService.UpdateExaminationListAsync(examinations, orgId, subscription));
        }

        [Test]
        public void UpdateExaminationListAsync_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;
            var examinations = new List<Physicalexamination>
            {
                new Physicalexamination
                {
                    ExaminationId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    Skin = "Normal 1",
                    Rash = "None 1",
                    Tester = "Dr. Smith 1",
                    Moles = "None observed 1",
                    IsActive = true
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.Is<HttpRequestMessage>(req =>
                        req.Method == HttpMethod.Put &&
                        req.RequestUri.ToString() == $"{_baseUrl}/api/PhysicalExamination/UpdateExaminationList/{orgId}/{subscription}"),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _physicalService.UpdateExaminationListAsync(examinations, orgId, subscription));

            Assert.That(exception.Message, Is.EqualTo("Tasks retrieval failure"));
        }
    }
}
