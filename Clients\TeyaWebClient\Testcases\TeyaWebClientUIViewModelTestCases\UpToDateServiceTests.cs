using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Moq;
using Moq.Protected;
using NUnit.Framework;
using TeyaUIModels.Model;
using TeyaUIViewModels.ViewModel;
using System.Net.Http.Json;
using System.Text.Json;

namespace TeyaWebClientUIViewModelTestCases
{
    [TestFixture]
    public class UpToDateServiceTests
    {
        private Mock<HttpMessageHandler> _mockHttpMessageHandler;
        private HttpClient _httpClient;
        private Mock<ILogger<UpToDateService>> _mockLogger;
        private Mock<IStringLocalizer<UpToDateService>> _mockLocalizer;
        private Mock<ITokenService> _mockTokenService;
        private UpToDateService _upToDateService;
        private readonly string _baseUrl = "http://test-api.com";
        private readonly string _accessToken = "test-access-token";

        [SetUp]
        public void Setup()
        {
            // Setup environment variable
            Environment.SetEnvironmentVariable("MemberServiceURL", _baseUrl);

            // Setup mock HTTP handler
            _mockHttpMessageHandler = new Mock<HttpMessageHandler>();

            // Setup HttpClient with mock handler
            _httpClient = new HttpClient(_mockHttpMessageHandler.Object)
            {
                BaseAddress = new Uri(_baseUrl)
            };

            // Setup mock logger
            _mockLogger = new Mock<ILogger<UpToDateService>>();

            // Setup mock localizer
            _mockLocalizer = new Mock<IStringLocalizer<UpToDateService>>();
            _mockLocalizer.Setup(l => l["Base URL not configured"])
                .Returns(new LocalizedString("Base URL not configured", "Base URL not configured"));
            _mockLocalizer.Setup(l => l["Access token not available"])
                .Returns(new LocalizedString("Access token not available", "Access token not available"));
            _mockLocalizer.Setup(l => l["Failed to fetch UpToDate with ID {id}"])
                .Returns(new LocalizedString("Failed to fetch UpToDate with ID {id}", "Failed to fetch UpToDate with ID {id}"));
            _mockLocalizer.Setup(l => l["Error fetching UpToDate with ID {id}"])
                .Returns(new LocalizedString("Error fetching UpToDate with ID {id}", "Error fetching UpToDate with ID {id}"));
            _mockLocalizer.Setup(l => l["Error adding UpToDate"])
                .Returns(new LocalizedString("Error adding UpToDate", "Error adding UpToDate"));
            _mockLocalizer.Setup(l => l["Error updating UpToDate with ID {id}"])
                .Returns(new LocalizedString("Error updating UpToDate with ID {id}", "Error updating UpToDate with ID {id}"));
            _mockLocalizer.Setup(l => l["Error deleting UpToDate with ID {id}"])
                .Returns(new LocalizedString("Error deleting UpToDate with ID {id}", "Error deleting UpToDate with ID {id}"));
            _mockLocalizer.Setup(l => l["Failed to fetch all UpToDates"])
                .Returns(new LocalizedString("Failed to fetch all UpToDates", "Failed to fetch all UpToDates"));
            _mockLocalizer.Setup(l => l["Error fetching all UpToDates"])
                .Returns(new LocalizedString("Error fetching all UpToDates", "Error fetching all UpToDates"));
            _mockLocalizer.Setup(l => l["Failed to fetch UpToDates with name {name}"])
                .Returns(new LocalizedString("Failed to fetch UpToDates with name {name}", "Failed to fetch UpToDates with name {name}"));
            _mockLocalizer.Setup(l => l["Error fetching UpToDates with name {name}"])
                .Returns(new LocalizedString("Error fetching UpToDates with name {name}", "Error fetching UpToDates with name {name}"));

            // Setup mock token service
            _mockTokenService = new Mock<ITokenService>();
            _mockTokenService.Setup(t => t.AccessToken).Returns(_accessToken);

            // Create UpToDateService with mocked dependencies
            _upToDateService = new UpToDateService(_httpClient, _mockLogger.Object, _mockLocalizer.Object, _mockTokenService.Object);
        }

        [TearDown]
        public void TearDown()
        {
            _httpClient.Dispose();
            Environment.SetEnvironmentVariable("MemberServiceURL", null);
        }

        [Test]
        public void Constructor_WhenHttpClientIsNull_ThrowsArgumentNullException()
        {
            // Act & Assert
            var exception = Assert.Throws<ArgumentNullException>(() =>
                new UpToDateService(null, _mockLogger.Object, _mockLocalizer.Object, _mockTokenService.Object));

            Assert.That(exception.ParamName, Is.EqualTo("httpClient"));
        }

        [Test]
        public void Constructor_WhenLoggerIsNull_ThrowsArgumentNullException()
        {
            // Act & Assert
            var exception = Assert.Throws<ArgumentNullException>(() =>
                new UpToDateService(_httpClient, null, _mockLocalizer.Object, _mockTokenService.Object));

            Assert.That(exception.ParamName, Is.EqualTo("logger"));
        }

        [Test]
        public void Constructor_WhenLocalizerIsNull_ThrowsArgumentNullException()
        {
            // Act & Assert
            var exception = Assert.Throws<ArgumentNullException>(() =>
                new UpToDateService(_httpClient, _mockLogger.Object, null, _mockTokenService.Object));

            Assert.That(exception.ParamName, Is.EqualTo("localizer"));
        }

        [Test]
        public void Constructor_WhenTokenServiceIsNull_ThrowsArgumentNullException()
        {
            // Act & Assert
            var exception = Assert.Throws<ArgumentNullException>(() =>
                new UpToDateService(_httpClient, _mockLogger.Object, _mockLocalizer.Object, null));

            Assert.That(exception.ParamName, Is.EqualTo("tokenService"));
        }

        [Test]
        public void Constructor_WhenMemberServiceURLNotConfigured_ThrowsInvalidOperationException()
        {
            // Arrange
            Environment.SetEnvironmentVariable("MemberServiceURL", null);

            // Act & Assert
            var exception = Assert.Throws<InvalidOperationException>(() =>
                new UpToDateService(_httpClient, _mockLogger.Object, _mockLocalizer.Object, _mockTokenService.Object));

            Assert.That(exception.Message, Is.EqualTo("Base URL not configured"));
        }

        [Test]
        public void Constructor_WhenAccessTokenNotAvailable_ThrowsInvalidOperationException()
        {
            // Arrange
            _mockTokenService.Setup(t => t.AccessToken).Returns((string)null);

            // Act & Assert
            var exception = Assert.Throws<InvalidOperationException>(() =>
                new UpToDateService(_httpClient, _mockLogger.Object, _mockLocalizer.Object, _mockTokenService.Object));

            Assert.That(exception.Message, Is.EqualTo("Access token not available"));
        }

        [Test]
        public async Task GetUpToDateByIdAsync_WhenSuccessful_ReturnsUpToDate()
        {
            // Arrange
            var upToDateId = Guid.NewGuid();
            var expectedUpToDate = new UpToDate
            {
                Id = upToDateId,
                ProviderId = Guid.NewGuid(),
                ProviderName = "UpToDate Medical",
                SearchName = "Cardiology Research",
                URL = "https://uptodate.com/cardiology",
                UserName = "<EMAIL>",
                Password = "SecurePassword123",
                OrganizationId = Guid.NewGuid()
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedUpToDate)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _upToDateService.GetUpToDateByIdAsync(upToDateId);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Id, Is.EqualTo(expectedUpToDate.Id));
            Assert.That(result.ProviderId, Is.EqualTo(expectedUpToDate.ProviderId));
            Assert.That(result.ProviderName, Is.EqualTo(expectedUpToDate.ProviderName));
            Assert.That(result.SearchName, Is.EqualTo(expectedUpToDate.SearchName));
            Assert.That(result.URL, Is.EqualTo(expectedUpToDate.URL));
            Assert.That(result.UserName, Is.EqualTo(expectedUpToDate.UserName));
            Assert.That(result.Password, Is.EqualTo(expectedUpToDate.Password));
            Assert.That(result.OrganizationId, Is.EqualTo(expectedUpToDate.OrganizationId));
        }

        [Test]
        public async Task GetUpToDateByIdAsync_WhenNotSuccessful_ReturnsNull()
        {
            // Arrange
            var upToDateId = Guid.NewGuid();

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.NotFound,
                Content = new StringContent("Not found")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _upToDateService.GetUpToDateByIdAsync(upToDateId);

            // Assert
            Assert.That(result, Is.Null);

            // Verify warning was logged
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Warning,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.IsAny<Exception>(),
                    It.Is<Func<It.IsAnyType, Exception, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }

        [Test]
        public void GetUpToDateByIdAsync_WhenExceptionThrown_PropagatesException()
        {
            // Arrange
            var upToDateId = Guid.NewGuid();
            var expectedException = new HttpRequestException("Test exception");

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ThrowsAsync(expectedException);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _upToDateService.GetUpToDateByIdAsync(upToDateId));

            Assert.That(exception, Is.EqualTo(expectedException));

            // Verify error was logged
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.Is<Exception>(ex => ex == expectedException),
                    It.Is<Func<It.IsAnyType, Exception, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }

        [Test]
        public async Task AddUpToDateAsync_WhenSuccessful_ReturnsTrue()
        {
            // Arrange
            var upToDate = new UpToDate
            {
                Id = Guid.NewGuid(),
                ProviderId = Guid.NewGuid(),
                ProviderName = "Medical Research Database",
                SearchName = "Neurology Research",
                URL = "https://uptodate.com/neurology",
                UserName = "<EMAIL>",
                Password = "SecurePassword789",
                OrganizationId = Guid.NewGuid()
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.Created,
                Content = new StringContent("Success")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _upToDateService.AddUpToDateAsync(upToDate);

            // Assert
            Assert.That(result, Is.True);
        }

        [Test]
        public async Task AddUpToDateAsync_WhenNotSuccessful_ReturnsFalse()
        {
            // Arrange
            var upToDate = new UpToDate
            {
                Id = Guid.NewGuid(),
                ProviderId = Guid.NewGuid(),
                ProviderName = "Failed Provider",
                SearchName = "Failed Search",
                URL = "https://failed.com",
                UserName = "<EMAIL>",
                Password = "FailedPassword",
                OrganizationId = Guid.NewGuid()
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _upToDateService.AddUpToDateAsync(upToDate);

            // Assert
            Assert.That(result, Is.False);
        }

        [Test]
        public void AddUpToDateAsync_WhenExceptionThrown_PropagatesException()
        {
            // Arrange
            var upToDate = new UpToDate
            {
                Id = Guid.NewGuid(),
                ProviderId = Guid.NewGuid(),
                ProviderName = "Exception Provider",
                SearchName = "Exception Search",
                URL = "https://exception.com",
                UserName = "<EMAIL>",
                Password = "ExceptionPassword",
                OrganizationId = Guid.NewGuid()
            };

            var expectedException = new HttpRequestException("Test exception");

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ThrowsAsync(expectedException);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _upToDateService.AddUpToDateAsync(upToDate));

            Assert.That(exception, Is.EqualTo(expectedException));

            // Verify error was logged
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.Is<Exception>(ex => ex == expectedException),
                    It.Is<Func<It.IsAnyType, Exception, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }

        [Test]
        public async Task UpdateUpToDateAsync_WhenSuccessful_ReturnsTrue()
        {
            // Arrange
            var upToDateId = Guid.NewGuid();
            var upToDate = new UpToDate
            {
                Id = upToDateId,
                ProviderId = Guid.NewGuid(),
                ProviderName = "Updated Provider",
                SearchName = "Updated Research",
                URL = "https://updated.com/research",
                UserName = "<EMAIL>",
                Password = "UpdatedPassword123",
                OrganizationId = Guid.NewGuid()
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent("Success")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _upToDateService.UpdateUpToDateAsync(upToDateId, upToDate);

            // Assert
            Assert.That(result, Is.True);
        }

        [Test]
        public async Task UpdateUpToDateAsync_WhenNotSuccessful_ReturnsFalse()
        {
            // Arrange
            var upToDateId = Guid.NewGuid();
            var upToDate = new UpToDate
            {
                Id = upToDateId,
                ProviderId = Guid.NewGuid(),
                ProviderName = "Failed Update Provider",
                SearchName = "Failed Update Search",
                URL = "https://failed-update.com",
                UserName = "<EMAIL>",
                Password = "FailedUpdatePassword",
                OrganizationId = Guid.NewGuid()
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _upToDateService.UpdateUpToDateAsync(upToDateId, upToDate);

            // Assert
            Assert.That(result, Is.False);
        }

        [Test]
        public async Task DeleteUpToDateAsync_WhenSuccessful_ReturnsTrue()
        {
            // Arrange
            var upToDateId = Guid.NewGuid();

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent("Success")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _upToDateService.DeleteUpToDateAsync(upToDateId);

            // Assert
            Assert.That(result, Is.True);
        }

        [Test]
        public async Task DeleteUpToDateAsync_WhenNotSuccessful_ReturnsFalse()
        {
            // Arrange
            var upToDateId = Guid.NewGuid();

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.NotFound,
                Content = new StringContent("Not found")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _upToDateService.DeleteUpToDateAsync(upToDateId);

            // Assert
            Assert.That(result, Is.False);
        }

        [Test]
        public async Task GetAllUpToDatesAsync_WhenSuccessful_ReturnsUpToDates()
        {
            // Arrange
            var expectedUpToDates = new List<UpToDate>
            {
                new UpToDate
                {
                    Id = Guid.NewGuid(),
                    ProviderId = Guid.NewGuid(),
                    ProviderName = "First Provider",
                    SearchName = "First Research",
                    URL = "https://first.com/research",
                    UserName = "<EMAIL>",
                    Password = "FirstPassword123",
                    OrganizationId = Guid.NewGuid()
                },
                new UpToDate
                {
                    Id = Guid.NewGuid(),
                    ProviderId = Guid.NewGuid(),
                    ProviderName = "Second Provider",
                    SearchName = "Second Research",
                    URL = "https://second.com/research",
                    UserName = "<EMAIL>",
                    Password = "SecondPassword456",
                    OrganizationId = Guid.NewGuid()
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedUpToDates)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _upToDateService.GetAllUpToDatesAsync();

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(expectedUpToDates.Count));
            Assert.That(result[0].ProviderName, Is.EqualTo("First Provider"));
            Assert.That(result[1].ProviderName, Is.EqualTo("Second Provider"));
        }

        [Test]
        public async Task GetAllUpToDatesAsync_WhenNotSuccessful_ReturnsNull()
        {
            // Arrange
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.InternalServerError,
                Content = new StringContent("Internal server error")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _upToDateService.GetAllUpToDatesAsync();

            // Assert
            Assert.That(result, Is.Null);

            // Verify warning was logged
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Warning,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.IsAny<Exception>(),
                    It.Is<Func<It.IsAnyType, Exception, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }

        [Test]
        public async Task GetUpToDatesByNameAsync_WhenSuccessful_ReturnsUpToDates()
        {
            // Arrange
            var searchName = "cardiology";
            var expectedUpToDates = new List<UpToDate>
            {
                new UpToDate
                {
                    Id = Guid.NewGuid(),
                    ProviderId = Guid.NewGuid(),
                    ProviderName = "Cardiology Provider",
                    SearchName = "cardiology",
                    URL = "https://cardiology.com/research",
                    UserName = "<EMAIL>",
                    Password = "CardioPassword123",
                    OrganizationId = Guid.NewGuid()
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedUpToDates)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _upToDateService.GetUpToDatesByNameAsync(searchName);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(1));
            Assert.That(result[0].SearchName, Is.EqualTo("cardiology"));
            Assert.That(result[0].ProviderName, Is.EqualTo("Cardiology Provider"));
        }

        [Test]
        public async Task GetUpToDatesByNameAsync_WhenNotSuccessful_ReturnsNull()
        {
            // Arrange
            var searchName = "nonexistent";

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.NotFound,
                Content = new StringContent("Not found")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _upToDateService.GetUpToDatesByNameAsync(searchName);

            // Assert
            Assert.That(result, Is.Null);

            // Verify warning was logged
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Warning,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.IsAny<Exception>(),
                    It.Is<Func<It.IsAnyType, Exception, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }
    }
}



