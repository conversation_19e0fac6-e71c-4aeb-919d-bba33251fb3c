﻿/* typography-theme.css */

:root {
    /* Font Families */
    --font-family-default: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
    --font-family-heading: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
    --font-family-monospace: 'SFMono-Regular', Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace;
    /* Font Sizes */
    --font-size-xs: 0.75rem; /* 12px */
    --font-size-sm: 0.875rem; /* 14px */
    --font-size-md: 1rem; /* 16px */
    --font-size-lg: 1.25rem; /* 20px */
    --font-size-xl: 1.5rem; /* 24px */
    --font-size-2xl: 2rem; /* 32px */
    --font-size-3xl: 3rem; /* 48px */
    /* Font Weights */
    --font-weight-light: 300;
    --font-weight-regular: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    /* Line Heights */
    --line-height-tight: 1.2;
    --line-height-snug: 1.375;
    --line-height-normal: 1.5;
    --line-height-relaxed: 1.625;
    --line-height-loose: 2;
    /* Text Colors */
    --text-color-primary: #333333;
    --text-color-secondary: #666666;
    --text-color-muted: #888888;
    --text-color-accent: #0077cc;
    --text-color-error: #d32f2f;
    --text-color-success: #2e7d32;
    --text-color-warning: #ed6c02;
    --text-color-info: #0288d1;
    /* Letter Spacing */
    --letter-spacing-tight: -0.025em;
    --letter-spacing-normal: 0em;
    --letter-spacing-wide: 0.025em;
    --letter-spacing-wider: 0.05em;
    --letter-spacing-widest: 0.1em;
}

/* Dark Theme */
.theme-dark {
    --text-color-primary: #e0e0e0;
    --text-color-secondary: #b0b0b0;
    --text-color-muted: #909090;
    --text-color-accent: #4da6ff;
    --text-color-error: #f44336;
    --text-color-success: #4caf50;
    --text-color-warning: #ff9800;
    --text-color-info: #29b6f6;
}

/* Jira-inspired Theme */
.theme-jira {
    --font-family-default: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
    --font-family-heading: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
    --text-color-primary: #172B4D;
    --text-color-secondary: #505F79;
    --text-color-muted: #6B778C;
    --text-color-accent: #0052CC;
    --text-color-error: #DE350B;
    --text-color-success: #36B37E;
    --text-color-warning: #FFC400;
    --text-color-info: #00B8D9;
}

/* High Contrast Theme for Accessibility */
.theme-high-contrast {
    --text-color-primary: #000000;
    --text-color-secondary: #222222;
    --text-color-muted: #444444;
    --text-color-accent: #004e99;
    --text-color-error: #990000;
    --text-color-success: #006600;
    --text-color-warning: #b36200;
    --text-color-info: #004c66;
}

/* Responsive Typography - adjust font sizes for different screen sizes */
@media (max-width: 768px) {
    :root {
        --font-size-xs: 0.75rem; /* Stays the same */
        --font-size-sm: 0.813rem; /* Slightly smaller */
        --font-size-md: 0.938rem; /* Slightly smaller */
        --font-size-lg: 1.125rem; /* Smaller */
        --font-size-xl: 1.25rem; /* Smaller */
        --font-size-2xl: 1.5rem; /* Smaller */
        --font-size-3xl: 2rem; /* Smaller */
    }
}

/* Global Styles for common HTML elements */
body {
    font-family: var(--font-family-default);
    font-size: var(--font-size-md);
    font-weight: var(--font-weight-regular);
    line-height: var(--line-height-normal);
    color: var(--text-color-primary);
}

h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-family-heading);
    margin-top: 0;
    line-height: var(--line-height-tight);
    color: var(--text-color-primary);
}

h1 {
    font-size: var(--font-size-3xl);
    font-weight: var(--font-weight-bold);
}

h2 {
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-semibold);
}

h3 {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-medium);
}

h4 {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-medium);
}

h5 {
    font-size: var(--font-size-md);
    font-weight: var(--font-weight-semibold);
}

h6 {
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-semibold);
    text-transform: uppercase;
}

p {
    margin-top: 0;
    margin-bottom: 1rem;
}

code, pre, kbd, samp {
    font-family: var(--font-family-monospace);
    font-size: var(--font-size-sm);
}
