using NUnit.Framework;
using Moq;
using Moq.Protected;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using TeyaUIViewModels.ViewModel;
using TeyaUIModels.Model;
using TeyaUIViewModels.TeyaUIViewModelResources;
using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Threading.Tasks;
using System.Threading;
using System.Net;
using System.Text.Json;
using System.Text;
using System.Linq;

namespace TeyaWebClientUIViewModelTestCases
{
    [TestFixture]
    public class GynHistoryServiceTests
    {
        private Mock<HttpMessageHandler> _mockHttpMessageHandler;
        private Mock<ITokenService> _mockTokenService;
        private Mock<ILogger<GynHistoryService>> _mockLogger;
        private Mock<IConfiguration> _mockConfiguration;
        private Mock<IStringLocalizer<TeyaUIViewModelsStrings>> _mockLocalizer;
        private HttpClient _httpClient;
        private GynHistoryService _gynHistoryService;
        private const string TestGynHistoryUrl = "https://test-gynhistory.com";
        private const string TestAccessToken = "test-access-token";

        [SetUp]
        public void SetUp()
        {
            _mockHttpMessageHandler = new Mock<HttpMessageHandler>();
            _mockTokenService = new Mock<ITokenService>();
            _mockLogger = new Mock<ILogger<GynHistoryService>>();
            _mockConfiguration = new Mock<IConfiguration>();
            _mockLocalizer = new Mock<IStringLocalizer<TeyaUIViewModelsStrings>>();
            _httpClient = new HttpClient(_mockHttpMessageHandler.Object);

            // Set up environment variable
            Environment.SetEnvironmentVariable("EncounterNotesURL", TestGynHistoryUrl);

            // Set up token service mock
            _mockTokenService.Setup(t => t.AccessToken).Returns(TestAccessToken);

            _gynHistoryService = new GynHistoryService(_httpClient, _mockConfiguration.Object, _mockLocalizer.Object, _mockTokenService.Object, _mockLogger.Object);
        }

        [TearDown]
        public void TearDown()
        {
            Environment.SetEnvironmentVariable("EncounterNotesURL", null);
            _httpClient?.Dispose();
        }

        [Test]
        public async Task AddAsync_WhenSuccessful_CompletesWithoutException()
        {
            // Arrange
            var gynHistoryDto = new GynHistoryDTO
            {
                gynId = Guid.NewGuid(),
                PatientId = Guid.NewGuid(),
                Symptoms = "Test symptoms",
                Notes = "Test notes",
                DateOfHistory = DateTime.UtcNow,
                OrganizationId = Guid.NewGuid(),
                PcpId = Guid.NewGuid(),
                Subscription = false
            };
            var orgId = Guid.NewGuid();
            var subscription = false;

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.Created
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            // No exception should be thrown
            await _gynHistoryService.AddAsync(gynHistoryDto, orgId, subscription);
        }

        [Test]
        public void AddAsync_WhenHttpRequestFails_ThrowsException()
        {
            // Arrange
            var gynHistoryDto = new GynHistoryDTO
            {
                gynId = Guid.NewGuid(),
                PatientId = Guid.NewGuid(),
                Symptoms = "Test symptoms",
                Notes = "Test notes",
                DateOfHistory = DateTime.UtcNow,
                OrganizationId = Guid.NewGuid(),
                Subscription = false
            };
            var orgId = Guid.NewGuid();
            var subscription = false;

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<Exception>(async () =>
                await _gynHistoryService.AddAsync(gynHistoryDto, orgId, subscription));

            Assert.That(exception.Message, Does.Contain("Error adding GYN history"));

            // Verify error was logged
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.IsAny<Exception>(),
                    It.Is<Func<It.IsAnyType, Exception?, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }

        [Test]
        public async Task UpdateGynHistoryAsync_WhenSuccessful_CompletesWithoutException()
        {
            // Arrange
            var gynHistoryId = Guid.NewGuid();
            var gynHistoryDto = new GynHistoryDTO
            {
                gynId = gynHistoryId,
                PatientId = Guid.NewGuid(),
                Symptoms = "Updated symptoms",
                Notes = "Updated notes",
                DateOfHistory = DateTime.UtcNow,
                OrganizationId = Guid.NewGuid(),
                Subscription = false
            };
            var orgId = Guid.NewGuid();
            var subscription = false;

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            // No exception should be thrown
            await _gynHistoryService.UpdateGynHistoryAsync(gynHistoryId, gynHistoryDto, orgId, subscription);
        }

        [Test]
        public void UpdateGynHistoryAsync_WhenHttpRequestFails_ThrowsException()
        {
            // Arrange
            var gynHistoryId = Guid.NewGuid();
            var gynHistoryDto = new GynHistoryDTO();
            var orgId = Guid.NewGuid();
            var subscription = false;

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<Exception>(async () =>
                await _gynHistoryService.UpdateGynHistoryAsync(gynHistoryId, gynHistoryDto, orgId, subscription));

            Assert.That(exception.Message, Does.Contain("Error updating GYN history"));

            // Verify error was logged
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.IsAny<Exception>(),
                    It.Is<Func<It.IsAnyType, Exception?, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }

        [Test]
        public async Task DeleteGynHistoryAsync_WhenSuccessful_CompletesWithoutException()
        {
            // Arrange
            var gynHistoryId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = false;

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.NoContent
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response)
                .Verifiable();

            // Act & Assert
            // No exception should be thrown
            await _gynHistoryService.DeleteGynHistoryAsync(gynHistoryId, orgId, subscription);

            // Verify the HTTP call was made
            _mockHttpMessageHandler.Protected().Verify(
                "SendAsync",
                Times.Once(),
                ItExpr.IsAny<HttpRequestMessage>(),
                ItExpr.IsAny<CancellationToken>());
        }

        [Test]
        public async Task GetByPatientIdAsync_WhenSuccessful_ReturnsGynHistories()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = false;
            var expectedHistories = new List<GynHistoryDTO>
            {
                new GynHistoryDTO
                {
                    gynId = Guid.NewGuid(),
                    PatientId = patientId,
                    Symptoms = "Symptoms 1",
                    Notes = "Notes 1",
                    DateOfHistory = DateTime.UtcNow.AddDays(-30),
                    OrganizationId = orgId,
                    Subscription = subscription
                },
                new GynHistoryDTO
                {
                    gynId = Guid.NewGuid(),
                    PatientId = patientId,
                    Symptoms = "Symptoms 2",
                    Notes = "Notes 2",
                    DateOfHistory = DateTime.UtcNow.AddDays(-60),
                    OrganizationId = orgId,
                    Subscription = subscription
                }
            };

            var responseContent = JsonSerializer.Serialize(expectedHistories);
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(responseContent, Encoding.UTF8, "application/json")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _gynHistoryService.GetByPatientIdAsync(patientId, orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count(), Is.EqualTo(2));
            Assert.That(result.All(h => h.PatientId == patientId), Is.True);
        }

        [Test]
        public void GetByPatientIdAsync_WhenHttpRequestFails_ThrowsException()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = false;

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<Exception>(async () =>
                await _gynHistoryService.GetByPatientIdAsync(patientId, orgId, subscription));

            Assert.That(exception.Message, Does.Contain($"Error retrieving GYN histories for PatientId {patientId}"));

            // Verify error was logged
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.IsAny<Exception>(),
                    It.Is<Func<It.IsAnyType, Exception?, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }

        [Test]
        public async Task UpdateGynHistoryListAsync_WhenSuccessful_CompletesWithoutException()
        {
            // Arrange
            var gynHistories = new List<GynHistoryDTO>
            {
                new GynHistoryDTO
                {
                    gynId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    Symptoms = "Symptoms 1",
                    Notes = "Notes 1",
                    DateOfHistory = DateTime.UtcNow
                },
                new GynHistoryDTO
                {
                    gynId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    Symptoms = "Symptoms 2",
                    Notes = "Notes 2",
                    DateOfHistory = DateTime.UtcNow
                }
            };
            var orgId = Guid.NewGuid();
            var subscription = false;

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            // No exception should be thrown
            await _gynHistoryService.UpdateGynHistoryListAsync(gynHistories, orgId, subscription);
        }

        [Test]
        public void UpdateGynHistoryListAsync_WhenListIsNull_ThrowsException()
        {
            // Arrange
            List<GynHistoryDTO> gynHistories = null;
            var orgId = Guid.NewGuid();
            var subscription = false;

            // Act & Assert
            var exception = Assert.ThrowsAsync<Exception>(async () =>
                await _gynHistoryService.UpdateGynHistoryListAsync(gynHistories, orgId, subscription));

            Assert.That(exception.Message, Does.Contain("OB history list is empty"));
        }

        [Test]
        public void UpdateGynHistoryListAsync_WhenListIsEmpty_ThrowsException()
        {
            // Arrange
            var gynHistories = new List<GynHistoryDTO>();
            var orgId = Guid.NewGuid();
            var subscription = false;

            // Act & Assert
            var exception = Assert.ThrowsAsync<Exception>(async () =>
                await _gynHistoryService.UpdateGynHistoryListAsync(gynHistories, orgId, subscription));

            Assert.That(exception.Message, Does.Contain("OB history list is empty"));
        }

        [Test]
        public async Task LoadGynHistoriesAsync_WhenSuccessful_ReturnsTransformedHistories()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = false;
            var sourceHistories = new List<GynHistoryDTO>
            {
                new GynHistoryDTO
                {
                    gynId = Guid.NewGuid(),
                    PatientId = patientId,
                    Symptoms = "Original symptoms",
                    Notes = "Original notes",
                    DateOfHistory = DateTime.UtcNow,
                    OrganizationId = orgId,
                    PcpId = Guid.NewGuid(),
                    IsDeleted = false
                }
            };

            var responseContent = JsonSerializer.Serialize(sourceHistories);
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(responseContent, Encoding.UTF8, "application/json")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _gynHistoryService.LoadGynHistoriesAsync(patientId, orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(1));
            Assert.That(result[0].PatientId, Is.EqualTo(patientId));
            Assert.That(result[0].Symptoms, Is.EqualTo("Original symptoms"));
            Assert.That(result[0].Notes, Is.EqualTo("Original notes"));
        }

        [Test]
        public void LoadGynHistoriesAsync_WhenGetByPatientIdFails_ThrowsException()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = false;

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<Exception>(async () =>
                await _gynHistoryService.LoadGynHistoriesAsync(patientId, orgId, subscription));

            Assert.That(exception.Message, Does.Contain("Error loading OB histories"));
        }
    }
}



