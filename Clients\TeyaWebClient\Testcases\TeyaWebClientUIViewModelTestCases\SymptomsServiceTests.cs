using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Moq;
using Moq.Protected;
using NUnit.Framework;
using TeyaUIModels.Model;
using TeyaUIViewModels.ViewModel;
using System.Net.Http.Json;
using System.Text.Json;

namespace TeyaWebClientUIViewModelTestCases
{
    [TestFixture]
    public class SymptomsServiceTests
    {
        private Mock<HttpMessageHandler> _mockHttpMessageHandler;
        private HttpClient _httpClient;
        private Mock<IConfiguration> _mockConfiguration;
        private Mock<IStringLocalizer<SymptomsService>> _mockLocalizer;
        private Mock<ILogger<SymptomsService>> _mockLogger;
        private Mock<ITokenService> _mockTokenService;
        private SymptomsService _symptomsService;
        private readonly string _baseUrl = "http://test-api.com";
        private readonly string _accessToken = "test-access-token";

        [SetUp]
        public void Setup()
        {
            // Setup environment variable
            Environment.SetEnvironmentVariable("EncounterNotesURL", _baseUrl);

            // Setup mock HTTP handler
            _mockHttpMessageHandler = new Mock<HttpMessageHandler>();

            // Setup HttpClient with mock handler
            _httpClient = new HttpClient(_mockHttpMessageHandler.Object)
            {
                BaseAddress = new Uri(_baseUrl)
            };

            // Setup mock configuration
            _mockConfiguration = new Mock<IConfiguration>();

            // Setup mock localizer
            _mockLocalizer = new Mock<IStringLocalizer<SymptomsService>>();
            _mockLocalizer.Setup(l => l["SymptomsRetrievalFailure"])
                .Returns(new LocalizedString("SymptomsRetrievalFailure", "Symptoms retrieval failure"));
            _mockLocalizer.Setup(l => l["SymptomsAdditionFailure"])
                .Returns(new LocalizedString("SymptomsAdditionFailure", "Symptoms addition failure"));

            // Setup mock logger
            _mockLogger = new Mock<ILogger<SymptomsService>>();

            // Setup mock token service
            _mockTokenService = new Mock<ITokenService>();
            _mockTokenService.Setup(t => t.AccessToken).Returns(_accessToken);

            // Create SymptomsService with mocked dependencies
            _symptomsService = new SymptomsService(_httpClient, _mockConfiguration.Object, _mockLocalizer.Object, _mockTokenService.Object, _mockLogger.Object);
        }

        [TearDown]
        public void TearDown()
        {
            _httpClient.Dispose();
            Environment.SetEnvironmentVariable("EncounterNotesURL", null);
        }

        [Test]
        public async Task GetSymptomsAsync_WhenSuccessful_ReturnsSymptoms()
        {
            // Arrange
            var expectedSymptoms = new List<Symptoms>
            {
                new Symptoms
                {
                    SymptomId = Guid.NewGuid(),
                    Symptom = "Headache - Severe headache with throbbing pain, high severity, 2 hours duration, frontal region location"
                },
                new Symptoms
                {
                    SymptomId = Guid.NewGuid(),
                    Symptom = "Nausea - Feeling of sickness with urge to vomit, medium severity, 30 minutes duration, stomach location"
                },
                new Symptoms
                {
                    SymptomId = Guid.NewGuid(),
                    Symptom = "Fatigue - Extreme tiredness and lack of energy, medium severity, 3 days duration, general location"
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedSymptoms)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _symptomsService.GetSymptomsAsync();

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(expectedSymptoms.Count));
            Assert.That(result[0].SymptomId, Is.EqualTo(expectedSymptoms[0].SymptomId));
            Assert.That(result[0].Symptom, Is.EqualTo(expectedSymptoms[0].Symptom));

            // Verify all symptoms
            Assert.That(result[1].Symptom, Does.Contain("Nausea"));
            Assert.That(result[2].Symptom, Does.Contain("Fatigue"));
        }

        [Test]
        public async Task GetSymptomsAsync_WhenNotSuccessful_ReturnsEmptyList()
        {
            // Arrange
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.NotFound,
                Content = new StringContent("Not found")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _symptomsService.GetSymptomsAsync();

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(0));
        }

        [Test]
        public async Task GetSymptomsAsync_WhenEmptyResponse_ReturnsEmptyList()
        {
            // Arrange
            var expectedSymptoms = new List<Symptoms>();

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedSymptoms)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _symptomsService.GetSymptomsAsync();

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(0));
        }

        [Test]
        public async Task GetSymptomsAsync_WhenExceptionThrown_ReturnsEmptyList()
        {
            // Arrange
            var expectedException = new HttpRequestException("Test exception");

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ThrowsAsync(expectedException);

            // Act
            var result = await _symptomsService.GetSymptomsAsync();

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(0));
        }

        [Test]
        public async Task AddSymptomsAsync_WhenSuccessful_ReturnsTrue()
        {
            // Arrange
            var symptoms = new List<Symptoms>
            {
                new Symptoms
                {
                    SymptomId = Guid.NewGuid(),
                    Symptom = "Chest Pain - Sharp pain in chest area, high severity, 1 hour duration, left chest location"
                },
                new Symptoms
                {
                    SymptomId = Guid.NewGuid(),
                    Symptom = "Shortness of Breath - Difficulty breathing during activity, medium severity, 30 minutes duration, respiratory location"
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.Created,
                Content = new StringContent("Success")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _symptomsService.AddSymptomsAsync(symptoms);

            // Assert
            Assert.That(result, Is.True);

            // Verify logging
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Information,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.IsAny<Exception>(),
                    It.Is<Func<It.IsAnyType, Exception, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }

        [Test]
        public async Task AddSymptomsAsync_WhenNotSuccessful_ReturnsFalse()
        {
            // Arrange
            var symptoms = new List<Symptoms>
            {
                new Symptoms
                {
                    SymptomId = Guid.NewGuid(),
                    Symptom = "Failed Symptom - This symptom will fail to add, low severity, 1 minute duration, unknown location"
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _symptomsService.AddSymptomsAsync(symptoms);

            // Assert
            Assert.That(result, Is.False);

            // Verify error logging
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.IsAny<Exception>(),
                    It.Is<Func<It.IsAnyType, Exception, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }

        [Test]
        public async Task AddSymptomsAsync_WhenNullSymptoms_ReturnsFalse()
        {
            // Arrange
            List<Symptoms> symptoms = null;

            // Act
            var result = await _symptomsService.AddSymptomsAsync(symptoms);

            // Assert
            Assert.That(result, Is.False);

            // Verify error logging
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.IsAny<Exception>(),
                    It.Is<Func<It.IsAnyType, Exception, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }

        [Test]
        public async Task AddSymptomsAsync_WhenEmptySymptoms_ReturnsFalse()
        {
            // Arrange
            var symptoms = new List<Symptoms>();

            // Act
            var result = await _symptomsService.AddSymptomsAsync(symptoms);

            // Assert
            Assert.That(result, Is.False);

            // Verify error logging
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.IsAny<Exception>(),
                    It.Is<Func<It.IsAnyType, Exception, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }

        [Test]
        public async Task AddSymptomsAsync_WhenExceptionThrown_ReturnsFalse()
        {
            // Arrange
            var symptoms = new List<Symptoms>
            {
                new Symptoms
                {
                    SymptomId = Guid.NewGuid(),
                    Symptom = "Exception Symptom - This will cause an exception, high severity, unknown duration, unknown location"
                }
            };

            var expectedException = new HttpRequestException("Test exception");

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ThrowsAsync(expectedException);

            // Act
            var result = await _symptomsService.AddSymptomsAsync(symptoms);

            // Assert
            Assert.That(result, Is.False);

            // Verify error logging - the service logs the exception message, not the exception itself
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.IsAny<Exception>(),
                    It.Is<Func<It.IsAnyType, Exception, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }

        [Test]
        public async Task GetSymptomsAsync_WhenInternalServerError_ReturnsEmptyList()
        {
            // Arrange
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.InternalServerError,
                Content = new StringContent("Internal server error")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _symptomsService.GetSymptomsAsync();

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(0));
        }

        [Test]
        public async Task GetSymptomsAsync_WhenBadRequest_ReturnsEmptyList()
        {
            // Arrange
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _symptomsService.GetSymptomsAsync();

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(0));
        }

        [Test]
        public async Task GetSymptomsAsync_WhenComplexSymptomData_ReturnsCorrectData()
        {
            // Arrange
            var expectedSymptoms = new List<Symptoms>
            {
                new Symptoms
                {
                    SymptomId = Guid.NewGuid(),
                    Symptom = "Complex symptom with special characters: !@#$%^&*() - Multi-line description with line breaks and special characters: àáâãäåæçèéêë, critical severity, 2 weeks and 3 days duration, multiple locations: head, neck, shoulders"
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedSymptoms)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _symptomsService.GetSymptomsAsync();

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(1));
            var symptom = result[0];
            Assert.That(symptom.SymptomId, Is.EqualTo(expectedSymptoms[0].SymptomId));
            Assert.That(symptom.Symptom, Is.EqualTo(expectedSymptoms[0].Symptom));
        }

        [Test]
        public async Task AddSymptomsAsync_WhenSingleSymptom_ReturnsTrue()
        {
            // Arrange
            var symptoms = new List<Symptoms>
            {
                new Symptoms
                {
                    SymptomId = Guid.NewGuid(),
                    Symptom = "Single Symptom - A single symptom for testing, low severity, 5 minutes duration, test location"
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.Created,
                Content = new StringContent("Success")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _symptomsService.AddSymptomsAsync(symptoms);

            // Assert
            Assert.That(result, Is.True);
        }
    }
}



