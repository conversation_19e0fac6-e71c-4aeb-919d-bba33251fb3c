<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <Version>1.0.50702.3</Version>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>

    <IsPackable>false</IsPackable>
    <IsTestProject>true</IsTestProject>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="coverlet.collector" Version="6.0.0" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.8.0" />
    <PackageReference Include="Microsoft.Identity.Client" Version="4.61.3" />
    <PackageReference Include="Moq" Version="4.20.70" />
    <PackageReference Include="NUnit" Version="3.14.0" />
    <PackageReference Include="NUnit.Analyzers" Version="3.9.0" />
    <PackageReference Include="NUnit3TestAdapter" Version="4.5.0" />
    <PackageReference Include="DotNetEnv" Version="3.1.1" />
    <PackageReference Include="Azure.Storage.Blobs" Version="12.22.2" />
    <PackageReference Include="Microsoft.AspNetCore.Components.Forms" Version="8.0.10" />
  </ItemGroup>

  <ItemGroup>
    <Using Include="NUnit.Framework" />
    <Using Include="Moq" />
    <Using Include="System.Threading.Tasks" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\TeyaWebApp\TeyaWebApp\TeyaUIViewModels\TeyaUIViewModels.csproj" />
    <ProjectReference Include="..\..\TeyaWebApp\TeyaWebApp\TeyaUIModels\TeyaUIModels.csproj" />
  </ItemGroup>

</Project>
