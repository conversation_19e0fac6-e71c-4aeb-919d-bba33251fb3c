﻿using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using MudBlazor;
using Syncfusion.Blazor.Popups;
using Syncfusion.Blazor.Schedule;
using TeyaHealthMobileModel.Model;
using TeyaHealthMobileViewModel.ViewModel;
using TeyaMobile.Shared.Services;

namespace TeyaMobile.Shared.Pages
{
    public partial class Appointments : ComponentBase, IDisposable
    {
        private List<AppointmentData> appointments = new();
        private List<AppointmentData> allAppointments = new();
        private List<AppointmentData> filteredAppointments = new();
        private DateTime selectedDate = DateTime.Today;
        private bool isLoading = false;
        private string errorMessage = "";
        private string schedulerHeight = "calc(100vh - 200px)";
        [Inject] private ActiveUser user { get; set; }
        [Inject] private StorageContainer StorageContainer { get; set; }
        [Inject] private IOrganizationService OrganizationService { get; set; }
        private Syncfusion.Blazor.Schedule.SfSchedule<AppointmentData>? ScheduleRef;
        private Syncfusion.Blazor.Popups.SfDialog? calendarDialogRef;
        private bool isCalendarDialogVisible = false;
        private DialogPositionData dialogPosition = new DialogPositionData() { X = "center", Y = "center" };

        private CancellationTokenSource? _cancellationTokenSource;
        private DateTime currentTime = DateTime.Now;
        private Timer? _statusTimer;

        // Filter and stats
        private enum FilterType { Total, Completed, Pending }
        private FilterType activeFilter = FilterType.Total;
        private bool _disposed = false;
        private Guid providerId = Guid.Empty;
        private Guid orgId = Guid.Empty;
        private int totalAppointments = 0;
        private int completedAppointments = 0;
        private int pendingAppointments = 0;
        private Guid? OrganizationId = Guid.Empty;

        protected override async Task OnInitializedAsync()
        {
            try
            {
                _cancellationTokenSource = new CancellationTokenSource();

                OrganizationId = StorageContainer.OrganizationId;
                if (OrganizationId == Guid.Empty)
                {
                    OrganizationId = await OrganizationService.GetOrganizationIdByNameAsync(user.OrganizationName);
                }

                await LoadAppointments();
                await SetSchedulerHeight();
            }
            catch (Exception ex)
            {
                errorMessage = $"Initialization error: {ex.Message}";
                Console.WriteLine($"Error in OnInitializedAsync: {ex.Message}");
            }
        }

        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            if (firstRender)
            {
                _statusTimer = new Timer(_ =>
                {
                    InvokeAsync(() =>
                    {
                        UpdateSummary();
                        StateHasChanged();
                    });
                }, null, TimeSpan.Zero, TimeSpan.FromMinutes(1));
            }
        }

        [JSInvokable]
        public async Task OnWindowResize()
        {
            await SetSchedulerHeight();
            StateHasChanged();
        }

        private async Task SetSchedulerHeight()
        {
            try
            {
                var windowHeight = await JSRuntime.InvokeAsync<int>("eval", "window.innerHeight");
                var headerHeight = 200;
                schedulerHeight = $"{Math.Max(400, windowHeight - headerHeight)}px";
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error setting scheduler height: {ex.Message}");
                schedulerHeight = "calc(100vh - 200px)";
            }
        }


        private async Task LoadAppointments()
        {
            if (_disposed) return;

            isLoading = true;
            errorMessage = "";
            StateHasChanged();

            try
            {
                List<Appointment> appointmentList = await AppointmentService.GetAppointmentsAsync(
                    selectedDate,
                    OrganizationId,
                    false);

                if (_disposed) return;

                appointments = appointmentList
                    .Where(a => a.AppointmentDate.Date == selectedDate.Date)
                    .Select(a => new AppointmentData
                    {
                        Id = a.Id,
                        Subject = a.PatientName ?? "Unknown Patient",
                        PatientName = a.PatientName ?? "Unknown Patient",
                        StartTime = a.StartTime ?? a.AppointmentDate,
                        EndTime = a.EndTime ?? a.AppointmentDate.AddMinutes(30),
                        Provider = a.Provider ?? "Unknown Provider",
                        VisitType = a.VisitType ?? "Consultation",
                        VisitStatus = a.VisitStatus ?? "Scheduled",
                        Reason = a.Reason ?? "",
                        Notes = a.Notes ?? "",
                        RoomNumber = a.RoomNumber ?? "00",
                        PatientId = a.PatientId,
                        Subscription = a.Subscription,
                        OrganisationId = a.OrganisationId,
                    }).ToList();

                allAppointments = appointments;
                filteredAppointments = appointments;

                UpdateSummary();
                ApplyFilter(activeFilter);
            }
            catch (Exception ex)
            {
                if (!_disposed)
                {
                    errorMessage = $"Error loading appointments: {ex.Message}";
                    Console.WriteLine($"Error loading appointments: {ex.Message}");
                }
            }
            finally
            {
                if (!_disposed)
                {
                    isLoading = false;
                    StateHasChanged();
                }
            }
        }

        private void UpdateSummary()
        {
            currentTime = DateTime.Now;
            totalAppointments = appointments.Count;

            completedAppointments = appointments.Count(a =>
                a.EndTime < currentTime ||
                a.VisitStatus.Equals("Completed", StringComparison.OrdinalIgnoreCase));

            pendingAppointments = appointments.Count(a =>
                a.EndTime >= currentTime &&
                !a.VisitStatus.Equals("Completed", StringComparison.OrdinalIgnoreCase) &&
                !a.VisitStatus.Equals("Cancelled", StringComparison.OrdinalIgnoreCase));
        }

        // Fixed: Corrected filter logic to match original working version
        private void ApplyFilter(FilterType filterType)
        {
            activeFilter = filterType;

            filteredAppointments = filterType switch
            {
                FilterType.Completed => allAppointments
                    .Where(a => a.EndTime < currentTime ||
                               a.VisitStatus.Equals("Completed", StringComparison.OrdinalIgnoreCase))
                    .ToList(),
                FilterType.Pending => allAppointments
                    .Where(a => a.EndTime >= currentTime &&
                               !a.VisitStatus.Equals("Completed", StringComparison.OrdinalIgnoreCase) &&
                               !a.VisitStatus.Equals("Cancelled", StringComparison.OrdinalIgnoreCase))
                    .ToList(),
                _ => allAppointments.ToList()
            };

            StateHasChanged();
        }

        // Fixed: Simplified date picker opening
        private void OpenDatePicker()
        {
            isCalendarDialogVisible = true;
            StateHasChanged();
        }

        // Fixed: Proper date selection handling
        //private async Task OnCalendarDateSelected(DateTime selectedDateValue)
        //{
        //    if (selectedDateValue != default(DateTime))
        //    {
        //        // Always close the dialog first
        //        isCalendarDialogVisible = false;

        //        // Only reload appointments if the date actually changed
        //        if (selectedDate.Date != selectedDateValue.Date)
        //        {
        //            selectedDate = selectedDateValue;
        //            await LoadAppointments();
        //        }

        //        StateHasChanged();
        //    }
        //}

        private async Task OnCalendarDateSelected(DateTime selectedDateValue)
        {
            // Always close dialog immediately
            isCalendarDialogVisible = false;
            StateHasChanged(); // Update UI to close dialog immediately

            // Only reload if date actually changed
            if (selectedDate.Date != selectedDateValue.Date)
            {
                selectedDate = selectedDateValue;
                await LoadAppointments();
                StateHasChanged();
            }
        }

        private void CloseCalendarDialog()
        {
            isCalendarDialogVisible = false;
            StateHasChanged();
        }

        private async Task OnAppointmentClick(EventClickArgs<AppointmentData> args)
        {
            if (_disposed || args.Event == null) return;
            await OnAppointmentCardClick(args.Event);
        }

        private async Task OnAppointmentCardClick(AppointmentData appointment)
        {
            if (_disposed || appointment == null) return;

            try
            {
                Navigation.NavigateTo($"/recorder/{appointment.PatientId}?appointmentId={appointment.Id}&orgId={OrganizationId}&sub={appointment.Subscription}&visitType={appointment.VisitType}&ptname={appointment.PatientName}&providerId={Guid.Parse(user.id)}");
            }
            catch (Exception ex)
            {
                if (!_disposed)
                {
                    Console.WriteLine($"Error handling appointment click: {ex.Message}");
                }
            }
        }

        private string GetAppointmentStatusClass(AppointmentData appointment)
        {
            return appointment.VisitStatus.ToLower().Replace(" ", "-") switch
            {
                "completed" => "completed",
                "cancelled" => "cancelled",
                "confirmed" => "confirmed",
                "pending" => "pending",
                _ => "scheduled"
            };
        }

        private async Task RetryLoadAppointments()
        {
            if (_disposed) return;
            await LoadAppointments();
        }

        public void Dispose()
        {
            _disposed = true;
            _statusTimer?.Dispose();
            _cancellationTokenSource?.Cancel();
            _cancellationTokenSource?.Dispose();
        }
    }
}
