﻿@using MudBlazor;
@inject ActiveUser user
@inject IFormFactor FormFactor

<MudNavMenu>
    @if (!IsHomePage)
    {
        <MudIconButton Icon="@Icons.Material.Filled.ArrowBack" Color="Color.Primary" Size="Size.Large" Style="margin: 12px 0 18px 0; font-size: 2rem; border-radius: 12px; background: rgba(80,80,80,0.04); transition: background 0.2s;" OnClick="GoBack" />
    }
    <MudNavLink Href="/" Match="NavLinkMatch.All" Icon="@Icons.Material.Filled.Dashboard" OnClick="OnNavClick" Style="@(IsTablet ? "font-size:1.2em; min-height:60px; padding:18px 0;" : null)">
        Dashboard
    </MudNavLink>

    <MudNavLink Href="/appointments" Match="NavLinkMatch.Prefix" Icon="@Icons.Material.Filled.CalendarToday" OnClick="OnNavClick" Style="@(IsTablet ? "font-size:1.2em; min-height:60px; padding:18px 0;" : null)">
        Appointments
    </MudNavLink>

    <MudNavLink Href="/audio-recorder" Match="NavLinkMatch.Prefix" Icon="@Icons.Material.Filled.Mic" OnClick="OnNavClick" Style="@(IsTablet ? "font-size:1.2em; min-height:60px; padding:18px 0;" : null)">
        AI Scribe
    </MudNavLink>

    <MudNavLink Href="@GetSoapNotesUrl()" Match="NavLinkMatch.Prefix" Icon="@Icons.Material.Filled.Description" OnClick="OnNavClick" Style="@(IsTablet ? "font-size:1.2em; min-height:60px; padding:18px 0;" : null)">
        SOAP Notes
    </MudNavLink>

    <MudNavGroup Title="Settings" Icon="@Icons.Material.Filled.Settings" Expanded="false" Style="@(IsTablet ? "font-size:1.2em; min-height:60px; padding:18px 0;" : null)">
        <MudNavLink Href="/users" Match="NavLinkMatch.Prefix" Icon="@Icons.Material.Filled.People" OnClick="OnNavClick" Style="@(IsTablet ? "font-size:1.1em; min-height:54px; padding:14px 0;" : null)">
            Users
        </MudNavLink>
        <MudNavLink Href="/security" Match="NavLinkMatch.Prefix" Icon="@Icons.Material.Filled.Security" OnClick="OnNavClick" Style="@(IsTablet ? "font-size:1.1em; min-height:54px; padding:14px 0;" : null)">
            Security
        </MudNavLink>
    </MudNavGroup>

    <MudNavLink Href="/secure" Match="NavLinkMatch.Prefix" Icon="@Icons.Material.Filled.Shield" OnClick="OnNavClick" Style="@(IsTablet ? "font-size:1.2em; min-height:60px; padding:18px 0;" : null)">
        Secure Area
    </MudNavLink>

    <MudNavLink Href="/about" Match="NavLinkMatch.Prefix" Icon="@Icons.Material.Filled.Info" OnClick="OnNavClick" Style="@(IsTablet ? "font-size:1.2em; min-height:60px; padding:18px 0;" : null)">
        About
    </MudNavLink>
</MudNavMenu>

@code {
    [Parameter] public EventCallback OnNavigationClick { get; set; }
    [Inject] NavigationManager Navigation { get; set; }

    private async Task OnNavClick()
    {
        await OnNavigationClick.InvokeAsync();
    }

    private string GetSoapNotesUrl()
    {
        bool Sub = false;
        return $"/soapnotes?pcpId={user.id}&sub={Sub}";
    }

    private bool IsTablet => FormFactor.GetFormFactor() == "Tablet";
    private bool IsHomePage => Navigation.Uri.EndsWith("/") || Navigation.Uri.EndsWith("/home", StringComparison.OrdinalIgnoreCase);
    private void GoBack() => Navigation.NavigateTo(Navigation.BaseUri, forceLoad: false, replace: false);
}
