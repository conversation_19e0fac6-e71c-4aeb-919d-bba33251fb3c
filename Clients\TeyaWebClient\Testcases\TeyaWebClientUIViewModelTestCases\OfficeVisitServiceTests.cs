using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Moq;
using Moq.Protected;
using NUnit.Framework;
using TeyaUIModels.Model;
using TeyaUIViewModels.ViewModel;
using TeyaUIViewModels.TeyaUIViewModelResources;

namespace TeyaWebClientUIViewModelTestCases
{
    [TestFixture]
    public class OfficeVisitServiceTests
    {
        private Mock<HttpMessageHandler> _mockHttpMessageHandler;
        private Mock<IConfiguration> _mockConfiguration;
        private Mock<IStringLocalizer<TeyaUIViewModelsStrings>> _mockLocalizer;
        private Mock<ITokenService> _mockTokenService;
        private Mock<ILogger<OfficeVisitService>> _mockLogger;
        private HttpClient _httpClient;
        private OfficeVisitService _officeVisitService;

        private const string TestAppointmentsUrl = "https://test-appointments.com";
        private const string TestMemberServiceUrl = "https://test-memberservice.com";
        private const string TestAccessToken = "test-access-token";

        [SetUp]
        public void SetUp()
        {
            _mockHttpMessageHandler = new Mock<HttpMessageHandler>();
            _mockConfiguration = new Mock<IConfiguration>();
            _mockLocalizer = new Mock<IStringLocalizer<TeyaUIViewModelsStrings>>();
            _mockTokenService = new Mock<ITokenService>();
            _mockLogger = new Mock<ILogger<OfficeVisitService>>();

            _httpClient = new HttpClient(_mockHttpMessageHandler.Object);

            // Set up environment variables
            Environment.SetEnvironmentVariable("AppointmentsURL", TestAppointmentsUrl);
            Environment.SetEnvironmentVariable("MemberServiceURL", TestMemberServiceUrl);

            // Set up mock responses
            _mockTokenService.Setup(t => t.AccessToken).Returns(TestAccessToken);
            _mockTokenService.Setup(t => t.GetValidatedAccessTokenAsync()).ReturnsAsync(TestAccessToken);
            _mockLocalizer.Setup(l => l["AddressRetrievalFailure"]).Returns(new LocalizedString("AddressRetrievalFailure", "Address retrieval failed"));
            _mockLocalizer.Setup(l => l["InvalidPatientIds"]).Returns(new LocalizedString("InvalidPatientIds", "Invalid patient IDs"));
            _mockLocalizer.Setup(l => l["Minutes"]).Returns(new LocalizedString("Minutes", "minutes"));
            _mockLocalizer.Setup(l => l["Unknown"]).Returns(new LocalizedString("Unknown", "Unknown"));
            _mockLocalizer.Setup(l => l["R-1111"]).Returns(new LocalizedString("R-1111", "R-1111"));

            _officeVisitService = new OfficeVisitService(
                _httpClient,
                _mockConfiguration.Object,
                _mockLocalizer.Object,
                _mockTokenService.Object);
        }

        [TearDown]
        public void TearDown()
        {
            _httpClient?.Dispose();
            Environment.SetEnvironmentVariable("AppointmentsURL", null);
            Environment.SetEnvironmentVariable("MemberServiceURL", null);
        }

        private Appointment CreateTestAppointment()
        {
            return new Appointment
            {
                Id = Guid.NewGuid(),
                UserId = Guid.NewGuid(),
                ProviderId = Guid.NewGuid(),
                PatientId = Guid.NewGuid(),
                FacilityId = Guid.NewGuid(),
                OrganizationId = Guid.NewGuid(),
                StartTime = DateTime.UtcNow.AddHours(1),
                EndTime = DateTime.UtcNow.AddHours(2),
                AppointmentDate = DateTime.UtcNow.Date,
                CreatedDate = DateTime.UtcNow,
                UpdatedDate = DateTime.UtcNow,
                Subscription = false,
                Facility = "Main Clinic",
                PatientName = "John Doe",
                Provider = "Dr. Smith",
                VisitType = "Consultation",
                VisitStatus = "Scheduled",
                Reason = "Regular checkup",
                Notes = "Patient is feeling well"
            };
        }

        private Office_visit_members CreateTestOfficeMember()
        {
            return new Office_visit_members
            {
                PatientId = Guid.NewGuid(),
                UserName = "John Doe",
                DateOfBirth = DateTime.UtcNow.AddYears(-30),
                SexualOrientation = "Male"
            };
        }

        [Test]
        public async Task GetAppointmentsByuserIdAsync_WhenSuccessful_ReturnsAppointmentList()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = false;
            var expectedAppointments = new List<Appointment>
            {
                CreateTestAppointment(),
                CreateTestAppointment()
            };

            var responseContent = JsonSerializer.Serialize(expectedAppointments);
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(responseContent, Encoding.UTF8, "application/json")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _officeVisitService.GetAppointmentsByuserIdAsync(userId, orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(2));
            Assert.That(result[0].VisitType, Is.EqualTo("Consultation"));
        }

        [Test]
        public async Task GetAppointmentsByuserIdAsync_WhenHttpRequestFails_ThrowsHttpRequestException()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = false;
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.InternalServerError,
                Content = new StringContent("Internal Server Error")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _officeVisitService.GetAppointmentsByuserIdAsync(userId, orgId, subscription));

            Assert.That(exception.Message, Does.Contain("Address retrieval failed"));
        }

        [Test]
        public async Task GetMembersByUserIdsAsync_WhenSuccessful_ReturnsOfficeMembersList()
        {
            // Arrange
            var patientIds = new List<Guid> { Guid.NewGuid(), Guid.NewGuid() };
            var orgId = Guid.NewGuid();
            var subscription = false;
            var expectedMembers = new List<Office_visit_members>
            {
                CreateTestOfficeMember(),
                CreateTestOfficeMember()
            };

            var queryString = string.Join("&", patientIds.Select(id => $"patientIds={Uri.EscapeDataString(id.ToString())}"));
            var expectedUrl = $"{TestMemberServiceUrl}/api/Registration/patientlistbyid/{orgId}/{subscription}?{queryString}";

            var responseContent = JsonSerializer.Serialize(expectedMembers);
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(responseContent, Encoding.UTF8, "application/json")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.Is<HttpRequestMessage>(req =>
                        req.RequestUri.ToString().Contains(expectedUrl) &&
                        req.Headers.Authorization != null &&
                        req.Headers.Authorization.Scheme == "Bearer" &&
                        req.Headers.Authorization.Parameter == TestAccessToken),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _officeVisitService.GetMembersByUserIdsAsync(patientIds, orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(2));
            Assert.That(result[0].UserName, Is.EqualTo("John Doe"));
        }

        [Test]
        public async Task GetMembersByUserIdsAsync_WhenPatientIdsIsNull_ThrowsArgumentException()
        {
            // Arrange
            List<Guid> patientIds = null;
            var orgId = Guid.NewGuid();
            var subscription = false;

            // Act & Assert
            var exception = Assert.ThrowsAsync<ArgumentException>(async () =>
                await _officeVisitService.GetMembersByUserIdsAsync(patientIds, orgId, subscription));

            Assert.That(exception.Message, Does.Contain("Invalid patient IDs"));
        }

        [Test]
        public async Task GetMembersByUserIdsAsync_WhenPatientIdsIsEmpty_ThrowsArgumentException()
        {
            // Arrange
            var patientIds = new List<Guid>();
            var orgId = Guid.NewGuid();
            var subscription = false;

            // Act & Assert
            var exception = Assert.ThrowsAsync<ArgumentException>(async () =>
                await _officeVisitService.GetMembersByUserIdsAsync(patientIds, orgId, subscription));

            Assert.That(exception.Message, Does.Contain("Invalid patient IDs"));
        }

        [Test]
        public async Task GetMembersByUserIdsAsync_WhenHttpRequestFails_ThrowsHttpRequestException()
        {
            // Arrange
            var patientIds = new List<Guid> { Guid.NewGuid() };
            var orgId = Guid.NewGuid();
            var subscription = false;
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad Request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _officeVisitService.GetMembersByUserIdsAsync(patientIds, orgId, subscription));

            Assert.That(exception.Message, Does.Contain("Address retrieval failed"));
        }

        [Test]
        public async Task GetPatientListByIdAsync_WhenSuccessful_ReturnsOfficeVisitModelList()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = false;
            var patientId = Guid.NewGuid();
            var role = "Provider";


            var appointment = CreateTestAppointment();
            appointment.PatientId = patientId;
            appointment.ProviderId = userId;
            appointment.OrganizationId = orgId;
            appointment.VisitType = "Consultation";
            appointment.StartTime = DateTime.Now;
            appointment.EndTime = appointment.StartTime.Value.AddMinutes(30);

            var appointments = new List<Appointment> { appointment };

            var member = CreateTestOfficeMember();
            member.PatientId = patientId;
            member.UserName = "John Doe";
            member.SexualOrientation = "Male";
            member.DateOfBirth = new DateTime(1990, 1, 1);

            var members = new List<Office_visit_members> { member };

            // Mock appointments response
            var appointmentsResponse = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(JsonSerializer.Serialize(appointments), Encoding.UTF8, "application/json")
            };

            // Mock members response
            var membersResponse = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(JsonSerializer.Serialize(members), Encoding.UTF8, "application/json")
            };

            _mockHttpMessageHandler
                .Protected()
                .SetupSequence<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(appointmentsResponse)
                .ReturnsAsync(membersResponse);

            // Act
            var result = await _officeVisitService.GetPatientListByIdAsync(userId, orgId, subscription, role);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(1));
            Assert.That(result[0].PatientName, Is.EqualTo("John Doe"));
            Assert.That(result[0].VisitType, Is.EqualTo("Consultation"));
            Assert.That(result[0].Sex, Is.EqualTo("Male"));
        }

        [Test]
        public async Task GetPatientListByIdAsync_WhenNoAppointments_ReturnsNull()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = false;
            var emptyAppointments = new List<Appointment>();
            var role = "Provider";

            var appointmentsResponse = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(JsonSerializer.Serialize(emptyAppointments), Encoding.UTF8, "application/json")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(appointmentsResponse);

            // Act
            var result = await _officeVisitService.GetPatientListByIdAsync(userId, orgId, subscription,role);

            // Assert
            Assert.That(result, Is.Null);
        }

        /// <summary>
        /// Get all the appointments from the service.
        /// </summary>
        /// <returns></returns>
        
        [Test]
        public async Task GetAllAppointmentsAsync_WhenSuccessful_ReturnsAppointmentList()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = false;

            var expectedAppointments = new List<Appointment>
    {
        new Appointment
            {
                Id = Guid.NewGuid(),
                UserId = Guid.NewGuid(),
                ProviderId = Guid.NewGuid(),
                PatientId = Guid.NewGuid(),
                FacilityId = Guid.NewGuid(),
                OrganizationId =orgId,
                StartTime = DateTime.UtcNow.AddHours(1),
                EndTime = DateTime.UtcNow.AddHours(2),
                AppointmentDate = DateTime.UtcNow.Date,
                CreatedDate = DateTime.UtcNow,
                UpdatedDate = DateTime.UtcNow,
                Subscription = false,
                Facility = "Main Clinic",
                PatientName = "John Doe",
                Provider = "Dr. Smith",
                VisitType = "Consultation",
                VisitStatus = "Scheduled",
                Reason = "Regular checkup",
                Notes = "Patient is feeling well"
            },
       new Appointment
            {
                Id = Guid.NewGuid(),
                UserId = Guid.NewGuid(),
                ProviderId = Guid.NewGuid(),
                PatientId = Guid.NewGuid(),
                FacilityId = Guid.NewGuid(),
                OrganizationId = orgId,
                StartTime = DateTime.UtcNow.AddHours(1),
                EndTime = DateTime.UtcNow.AddHours(2),
                AppointmentDate = DateTime.UtcNow.Date,
                CreatedDate = DateTime.UtcNow,
                UpdatedDate = DateTime.UtcNow,
                Subscription = false,
                Facility = "Main Clinic",
                PatientName = "John Doe",
                Provider = "Dr. Smith",
                VisitType = "Consultation",
                VisitStatus = "Scheduled",
                Reason = "Regular checkup",
                Notes = "Patient is feeling well"
            }
    };

            var responseContent = JsonSerializer.Serialize(expectedAppointments);
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(responseContent, Encoding.UTF8, "application/json")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _officeVisitService.GetAllAppointmentsAsync(orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(2));
            Assert.That(result[0].VisitType, Is.EqualTo("Consultation"));
        }


        [Test]
        public void GetAllAppointmentsAsync_WhenHttpFails_ThrowsHttpRequestException()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = false;

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad Request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _officeVisitService.GetAllAppointmentsAsync(orgId, subscription));

            Assert.That(exception.Message, Does.Contain("Address retrieval failed"));
        }

    }
}



