name: Versioning

on:
  pull_request:
    types: [closed]
    branches: [main]
    

permissions:
  id-token: write
  contents: write
  actions: read
  pull-requests: write
  issues: write

jobs:
  versioning:   
    runs-on: ubuntu-latest
    name: Versioning
    if: github.event.pull_request.merged == true && !contains(github.event.pull_request.labels.*.name, 'auto-version')
    steps:
      - name: Checkout main branch
        uses: actions/checkout@v4
        with:
          ref: main
          token: ${{ secrets.GITHUB_TOKEN }}
          fetch-depth: 0

      - name: Set up Git config
        run: |
          git config user.name "${{ github.actor }}"
          git config user.email "${{ github.actor }}@users.noreply.github.com"
      - name: Install Azure CLI
        run: |
          curl -sL https://aka.ms/InstallAzureCLIDeb | sudo bash
      - name: Download existing revision.json from Azure Blob
        run: |
          mkdir -p revision-store
          az storage blob download \
            --container-name revisions \
            --name revision.json \
            --file revision-store/revision.json \
            --account-name teyarecordingsdev \
            --sas-token "sp=rw&st=2025-06-12T11:54:16Z&se=2026-06-12T19:54:16Z&spr=https&sv=2024-11-04&sr=c&sig=13rDJrmldw%2FIn%2B0JSNzh4QopkEqRQD%2BJSGUkE1Ec69A%3D" || echo '{"date":"", "revision":0}' > revision-store/revision.json
      - name: Calculate version
        id: version
        run: |
          mkdir -p revision-store
          FILE=revision-store/revision.json
          raw=$(date +'%y%m%d%H%M')
          last_digit="${raw:1:1}"
          TODAY="${last_digit}${raw:2:4}"
          TIME="${raw:6:4}"
          if [ -f "$FILE" ]; then
            LAST_DATE=$(jq -r '.date' "$FILE")
            LAST_REV=$(jq -r '.revision' "$FILE")
            if [ "$LAST_DATE" = "$TODAY" ]; then
              REVISION=$((LAST_REV + 1))
            else
              REVISION=1
            fi
          else
            REVISION=1
          fi
          echo "{\"date\":\"$TODAY\",\"revision\":$REVISION}" > "$FILE"
          VERSION="1.0.$TODAY.$REVISION"
          BRANCH_NAME="VER-${TODAY}${TIME}-version-bump"
          echo "VERSION=$VERSION" >> $GITHUB_OUTPUT
          echo "BRANCH_NAME=$BRANCH_NAME" >> $GITHUB_OUTPUT
          echo "VERSION=$VERSION" >> $GITHUB_ENV
          echo "BRANCH_NAME=$BRANCH_NAME" >> $GITHUB_ENV
      - name: Create new branch
        run: |
          git checkout -b "$BRANCH_NAME"
      - name: Update csproj and .sqlproj versions
        run: |
          CSPROJ_FILES=$(find . -type f -name '*.csproj')
          for file in $CSPROJ_FILES; do
            if grep -q "<Version>" "$file"; then
              sed -i "s|<Version>.*</Version>|<Version>$VERSION</Version>|g" "$file"
            else
              sed -i "0,/<PropertyGroup>/s|<PropertyGroup>|<PropertyGroup>\n    <Version>$VERSION</Version>|" "$file"
            fi
          done
          SQLPROJ_FILES=$(find . -type f -name '*.sqlproj')
          for file in $SQLPROJ_FILES; do
            if grep -q "<Version>" "$file"; then
              sed -i "s|<Version>.*</Version>|<Version>$VERSION</Version>|g" "$file"
            else
              sed -i "0,/<PropertyGroup>/s|<PropertyGroup>|<PropertyGroup>\n    <Version>$VERSION</Version>|" "$file"
            fi
            if grep -q "<DacVersion>" "$file"; then
              sed -i "s|<DacVersion>.*</DacVersion>|<DacVersion>$VERSION</DacVersion>|g" "$file"
            else
              sed -i "0,/<PropertyGroup>/s|<PropertyGroup>|<PropertyGroup>\n    <DacVersion>$VERSION</DacVersion>|" "$file"
            fi
          done
          
      - name: Upload new revision.json to Azure Blob
        run: |
          az storage blob upload \
            --container-name revisions \
            --name revision.json \
            --file revision-store/revision.json \
            --account-name teyarecordingsdev \
            --sas-token "sp=rw&st=2025-06-12T11:54:16Z&se=2026-06-12T19:54:16Z&spr=https&sv=2024-11-04&sr=c&sig=13rDJrmldw%2FIn%2B0JSNzh4QopkEqRQD%2BJSGUkE1Ec69A%3D" \
            --overwrite
          
      - name: Commit and push to new branch
        run: |
          git add .
          git commit -m "ci: version bump to $VERSION" || echo "No changes to commit"
          git push origin "$BRANCH_NAME"
      

      - name: Install GitHub CLI
        run: sudo apt-get install gh -y
      
      
      - name: Ensure 'auto-version' label exists
        run: |
          gh label list | grep -q "^auto-version" || gh label create "auto-version" --description "Auto-created label for versioning" --color "#ededed"
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Authenticate GitHub CLI
        run: echo "****************************************" | gh auth login --with-token

      - name: Create Pull Request via CLI
        run: |
          gh pr create \
            --title "chore: version bump to ${{ env.VERSION }}" \
            --body "Automated version bump to ${{ env.VERSION }}" \
            --base main \
            --head ${{ env.BRANCH_NAME }} \
            --label "auto-version"
        env:
          GITHUB_TOKEN: ****************************************
          
      
      - name: Wait for checks to start
        run: |
          pr_number=$(gh pr view "${{ env.BRANCH_NAME }}" --json number -q '.number')
          echo "Waiting for PR #$pr_number checks to pass..."
      
          sha=$(gh pr view "$pr_number" --json commits -q '.commits[-1].oid')
      
          for i in {1..30}; do
            echo "Fetching check run status for commit: $sha"
            response=$(gh api repos/${{ github.repository }}/commits/$sha/check-runs)
            conclusion=$(echo "$response" | jq -r '.check_runs[] | select(.status == "completed") | .conclusion' | uniq)
      
            if [[ -n "$conclusion" ]]; then
              echo "Conclusion: $conclusion"
              if [[ "$conclusion" == "success" ]]; then
                echo "All checks started."
                break
              else
                echo "Checks failed with conclusion: $conclusion"
                exit 1
              fi
            fi
      
            echo "Checks not started yet. Retrying in 10s..."
            sleep 10
          done
        env:
          GITHUB_TOKEN: ****************************************

      - name: Check for merge conflicts
        run: |
          pr_number=$(gh pr view "${{ env.BRANCH_NAME }}" --json number -q '.number')
          mergeable_state=$(gh pr view "$pr_number" --json mergeable -q '.mergeable')

          echo "PR #$pr_number mergeable state: $mergeable_state"

          if [[ "$mergeable_state" == "CONFLICTING" ]]; then
            echo "❌ Merge conflict detected. Closing PR and deleting branch."

            # Close PR and delete the remote branch
            gh pr close "$pr_number" --delete-branch

            # Delete local branch (if needed)
            git branch -D "${{ env.BRANCH_NAME }}"

            # Optional: Send failure notification to MS Teams
            curl -H "Content-Type: application/json" \
              -d "{\"text\": \"❌ Merge conflict in PR #$pr_number. Closed and branch deleted.\nTriggered by: '${{ github.actor }}'\"}" \
              ${{ secrets.TEAMS_WEBHOOK_URL }}

            exit 1
          fi
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Approve PR with Bot 1
        run: |
          pr_number=$(gh pr list --head "${{ env.BRANCH_NAME }}" --json number -q '.[0].number')
          pr_author=$(gh pr view "$pr_number" --json author -q '.author.login')
      
          echo "PR #$pr_number created by $pr_author"
      
          if [[ "$pr_author" == "GaurangAgrawal03" ]]; then
            echo "Bot1 cannot approve user1's PR. Using fallback."
            GH_TOKEN=**************************************** gh pr review "$pr_number" --approve
          else
            echo "Using Bot1 to approve."
            GH_TOKEN=**************************************** gh pr review "$pr_number" --approve
          fi
        env:
          GH_TOKEN: ****************************************
      
      - name: Approve PR with Bot 2
        run: |
          pr_number=$(gh pr list --head "${{ env.BRANCH_NAME }}" --json number -q '.[0].number')
          pr_author=$(gh pr view "$pr_number" --json author -q '.author.login')
      
          echo "PR #$pr_number created by $pr_author"
      
          if [[ "$pr_author" == "sourav11teya" ]]; then
            echo "Bot2 cannot approve user2's PR. Using fallback."
            GH_TOKEN=**************************************** gh pr review "$pr_number" --approve
          else
            echo "Using Bot2 to approve."
            GH_TOKEN=**************************************** gh pr review "$pr_number" --approve
          fi
        env:
          GH_TOKEN: ****************************************


      - name: Merge Pull Request (wait until checks pass)
        run: |
          pr_number=$(gh pr view "${{ env.BRANCH_NAME }}" --json number -q '.number')
          echo "Waiting for all checks to pass on PR #$pr_number..."
      
          sha=$(gh pr view "$pr_number" --json commits -q '.commits[-1].oid')
      
          while true; do
            echo "Checking status of commit: $sha"
            response=$(gh api repos/${{ github.repository }}/commits/$sha/check-runs)
            total=$(echo "$response" | jq '.total_count')
            completed=$(echo "$response" | jq '[.check_runs[] | select(.status == "completed")] | length')
            failed=$(echo "$response" | jq '[.check_runs[] | select(.conclusion != "success" and .conclusion != null)] | length')
      
            echo "Checks completed: $completed / $total | Failed: $failed"
      
            if [ "$total" -eq "$completed" ]; then
              if [ "$failed" -eq 0 ]; then
                echo "✅ All checks passed. Proceeding to merge."
                break
              else
                echo "❌ Some checks failed. Exiting."
                exit 1
              fi
            fi
      
            echo "⏳ Checks still running... waiting 30s"
            sleep 30
          done
          
          # Merge after all checks passed
          gh pr merge "$pr_number" --merge --delete-branch
        env:
          GITHUB_TOKEN: ****************************************

     
      - name: Notify MS Teams Success
        run: |
          curl -H "Content-Type: application/json" \
          -d "{\"text\": \"✅ Version bumped to: '${{ env.VERSION }}' and pushed to '${{ env.BRANCH_NAME }}'\nTriggered by: '${{ github.actor }}'\"}" \
          ${{ secrets.TEAMS_WEBHOOK_URL }}
