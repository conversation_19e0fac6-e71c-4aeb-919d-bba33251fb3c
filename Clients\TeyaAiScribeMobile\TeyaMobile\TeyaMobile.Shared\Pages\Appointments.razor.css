﻿.appointments-container {
    padding: 8px;
    min-height: calc(100vh - 120px);
}

.stats-header-section {
    padding: 12px;
    margin-bottom: 12px;
    border-radius: var(--mud-default-borderradius);
    background-color: var(--mud-palette-surface);
    border: 1px solid var(--mud-palette-lines-default);
    min-height: 80px;
    display: flex;
    align-items: center;
}

.stats-grid-container {
    display: flex !important;
    flex-direction: row !important;
    flex-wrap: nowrap !important;
    align-items: center !important;
    justify-content: space-between !important;
    gap: 8px !important;
    width: 100% !important;
}

.stats-cards-section {
    display: flex !important;
    flex-direction: row !important;
    flex-wrap: nowrap !important;
    gap: 8px !important;
    flex: 1;
    min-width: 0;
    overflow-x: auto;
}

.stat-card {
    cursor: pointer;
    transition: all 0.2s ease;
    border-radius: var(--mud-default-borderradius);
    background-color: var(--mud-palette-surface);
    border: 1px solid var(--mud-palette-lines-default);
    height: 60px;
    min-width: 80px;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}

    .stat-card:hover {
        transform: translateY(-1px);
        box-shadow: var(--mud-elevation-4);
        border-color: var(--mud-palette-primary);
    }

    .stat-card.active {
        background-color: var(--mud-palette-primary);
        color: var(--mud-palette-primary-text);
        border-color: var(--mud-palette-primary);
        transform: scale(1.02);
    }

    .stat-card.completed.active {
        background-color: var(--mud-palette-success);
        color: var(--mud-palette-success-text);
        border-color: var(--mud-palette-success);
    }

    .stat-card.pending.active {
        background-color: var(--mud-palette-warning);
        color: var(--mud-palette-warning-text);
        border-color: var(--mud-palette-warning);
    }

.stat-content {
    padding: 8px !important;
    text-align: center;
    width: 100%;
}

.stat-count {
    font-weight: 700;
    line-height: 1;
    margin-bottom: 2px;
}

.stat-label {
    font-weight: 600;
    letter-spacing: 0.5px;
    opacity: 0.8;
}

.calendar-icon-btn {
    flex-shrink: 0 !important;
    width: 60px !important;
    height: 60px !important;
}

/* Mobile Responsive Styles */
@media (max-width: 768px) {
    .stats-header-section {
        padding: 8px;
        min-height: 60px;
    }

    .stats-grid-container {
        gap: 6px !important;
    }

    .stats-cards-section {
        gap: 4px !important;
    }

    .stat-card {
        min-width: 60px;
        height: 50px;
    }

    .stat-count {
        font-size: 0.9rem;
    }

    .stat-label {
        font-size: 0.6rem;
    }

    .calendar-icon-btn {
        width: 50px !important;
        height: 50px !important;
    }
}

@media (max-width: 480px) {
    .stats-header-section {
        padding: 6px;
        min-height: 50px;
    }

    .stats-grid-container {
        gap: 4px !important;
    }

    .stats-cards-section {
        gap: 2px !important;
    }

    .stat-card {
        min-width: 50px;
        height: 45px;
    }

    .stat-count {
        font-size: 0.8rem;
    }

    .stat-label {
        font-size: 0.55rem;
    }

    .calendar-icon-btn {
        width: 45px !important;
        height: 45px !important;
    }
}

@media (max-width: 360px) {
    .stat-card {
        min-width: 45px;
        height: 40px;
    }

    .stat-count {
        font-size: 0.7rem;
    }

    .stat-label {
        font-size: 0.5rem;
    }

    .calendar-icon-btn {
        width: 40px !important;
        height: 40px !important;
    }
}

/* Loading Section */
.loading-section {
    padding: 32px;
    margin-bottom: 12px;
    border-radius: var(--mud-default-borderradius);
    background-color: var(--mud-palette-surface);
}

/* Scheduler Section */
.scheduler-section {
    border-radius: var(--mud-default-borderradius);
    overflow: hidden;
    margin-bottom: 12px;
    background-color: var(--mud-palette-surface);
}

/* Error Alert */
.error-alert {
    margin-top: 12px;
    border-radius: var(--mud-default-borderradius);
}

/* Appointment Cards */
.appointment-card {
    margin: 2px;
    border-radius: var(--mud-default-borderradius);
    cursor: pointer;
    transition: all 0.3s ease;
    border-left: 4px solid var(--mud-palette-primary);
    background-color: var(--mud-palette-surface);
    overflow: hidden;
    max-width: 100%;
    min-width: 0;
}

    .appointment-card:hover {
        transform: translateY(-1px);
        box-shadow: var(--mud-elevation-4);
    }

    .appointment-card.completed {
        border-left-color: var(--mud-palette-success);
    }

    .appointment-card.pending {
        border-left-color: var(--mud-palette-warning);
    }

    .appointment-card.confirmed {
        border-left-color: var(--mud-palette-info);
    }

.appointment-content {
    padding: 12px !important;
}

.appointment-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.name-row {
    display: flex;
    flex-direction: row;
    align-items: baseline;
    gap: 8px;
    width: 100%;
    overflow: hidden;
}

.patient-name {
    font-weight: 600;
    color: var(--mud-palette-text-primary);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 100px;
}

.visit-type-inline {
    font-size: 0.85em;
    color: var(--mud-palette-text-secondary);
    font-weight: 500;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 70px;
}

.room-number-sub {
    font-size: 0.75em;
    color: var(--mud-palette-text-disabled);
    margin-left: 2px;
    margin-top: 0px;
    margin-bottom: 2px;
    display: block;
    line-height: 1.1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.reason {
    color: var(--mud-palette-text-secondary);
    font-style: italic;
    font-size: 0.85em;
    margin-top: 4px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 100%;
}

.time-status {
    display: flex;
    align-items: center;
    gap: 8px;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--mud-palette-primary);
}

    .status-dot.completed {
        background: var(--mud-palette-success);
    }

    .status-dot.pending {
        background: var(--mud-palette-warning);
    }

    .status-dot.confirmed {
        background: var(--mud-palette-info);
    }

.appointment-details {
    font-size: 0.875rem;
}

.detail-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 4px;
}

.visit-type {
    font-weight: 500;
    color: var(--mud-palette-text-primary);
    max-width: 80px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.room-chip {
    font-size: 0.7rem;
    max-width: 80px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* Syncfusion Integration */
.mud-themed-scheduler {
    font-family: var(--mud-typography-default-family);
    background-color: var(--mud-palette-surface);
}

    .mud-themed-scheduler .e-schedule .e-time-cells-wrap {
        width: 60px !important;
        background-color: var(--mud-palette-surface);
        border-color: var(--mud-palette-lines-default);
    }

    .mud-themed-scheduler .e-schedule .e-content-wrap {
        overflow-y: auto !important;
        max-height: calc(100vh - 200px) !important;
        background-color: var(--mud-palette-surface);
    }

    .mud-themed-scheduler .e-schedule .e-day-view .e-appointment {
        padding: 0 !important;
        margin: 2px !important;
        border-radius: var(--mud-default-borderradius) !important;
        border: none !important;
    }

/* Calendar Dialog */
.compact-date-picker-dialog .e-dialog {
    border-radius: var(--mud-default-borderradius);
}

.compact-calendar-container {
    padding: 16px;
}

.appointments-container {
    padding: 8px !important;
    max-width: 100% !important;
}

.stats-header-section {
    border-radius: 12px !important;
    background: var(--mud-palette-surface) !important;
}

.stat-card {
    border-radius: 8px !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    border: 2px solid transparent !important;
}

.stat-card:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15) !important;
}

.stat-card.active {
    border-color: var(--mud-palette-primary) !important;
    background: var(--mud-palette-primary-lighten) !important;
}

.appointment-details {
    margin-top: 8px !important;
}

.detail-row {
    display: flex !important;
    align-items: center !important;
    flex-wrap: wrap !important;
    gap: 8px !important;
}

.visit-type {
    flex-shrink: 0 !important;
    min-width: fit-content !important;
}

.room-chip {
    flex-shrink: 0 !important;
}

.calendar-fab-btn {
    touch-action: manipulation !important;
    -webkit-tap-highlight-color: transparent !important;
}

/* Mobile-specific adjustments */
@media (max-width: 768px) {
    .stats-header-section {
        padding: 7px !important;
    }

    .stat-content {
        padding: 5px 5px !important;
    }

    .appointment-content {
        padding: 5px !important;
    }
}

/* iOS Safari specific fixes */
@supports (-webkit-touch-callout: none) {
    .stat-card, .calendar-fab-btn {
        -webkit-tap-highlight-color: rgba(0,0,0,0) !important;
    }
}

/* Tablet/iPad Responsive Enhancements */
@media (min-width: 768px) and (max-width: 1200px) {
    .appointments-container {
        padding: 24px !important;
        max-width: 1100px !important;
    }

    .stats-header-section {
        padding: 24px !important;
        min-height: 100px;
    }

    .stat-card {
        min-width: 120px;
        height: 80px;
        font-size: 1.1rem;
    }

    .stat-content {
        padding: 16px !important;
    }

    .calendar-fab-btn {
        width: 56px !important;
        height: 56px !important;
    }

    .scheduler-section {
        padding: 24px 16px !important;
    }

    .appointment-content {
        padding: 20px !important;
    }
}

@media (min-width: 1200px) {
    .appointments-container {
        padding: 40px !important;
        max-width: 1400px !important;
    }

    .stats-header-section {
        padding: 36px !important;
        min-height: 120px;
    }

    .stat-card {
        min-width: 160px;
        height: 100px;
        font-size: 1.2rem;
    }

    .stat-content {
        padding: 24px !important;
    }

    .calendar-fab-btn {
        width: 64px !important;
        height: 64px !important;
    }

    .scheduler-section {
        padding: 32px 24px !important;
    }

    .appointment-content {
        padding: 28px !important;
    }
}

@media (max-width: 600px) {
    .appointment-card {
        font-size: 0.95em;
        min-height: 50px;
    }
    .patient-name, .room-chip, .visit-type {
        max-width: 60px;
        font-size: 0.85em;
    }
}

.appointment-details-vertical {
    display: flex;
    flex-direction: column;
    gap: 2px;
    align-items: flex-start;
    justify-content: center;
    width: 100%;
    max-width: 100%;
    overflow: hidden;
    padding: 0;
}

.appointment-details-vertical .patient-name,
.appointment-details-vertical .visit-type,
.appointment-details-vertical .room-chip,
.appointment-details-vertical .reason {
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    padding: 0;
    margin: 0;
}

.appointment-details-vertical .room-chip {
    margin-top: 2px;
    margin-bottom: 2px;
}

.appointment-details-vertical .reason {
    color: var(--mud-palette-text-secondary);
    font-style: italic;
    font-size: 0.85em;
    margin-top: 2px;
}

@media (max-width: 600px) {
    .appointment-details-vertical .patient-name,
    .appointment-details-vertical .visit-type,
    .appointment-details-vertical .room-chip,
    .appointment-details-vertical .reason {
        font-size: 0.85em;
        max-width: 90vw;
    }
}

.name-row-responsive {
    display: flex;
    flex-direction: row;
    align-items: baseline;
    gap: 8px;
    flex-wrap: wrap;
    width: 100%;
    min-width: 0;
    max-width: 100%;
}

.patient-name {
    font-weight: 600;
    color: var(--mud-palette-text-primary);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    flex-shrink: 1;
    min-width: 0;
    max-width: 60vw;
    font-size: 1.1em;
}

.room-number-inline {
    font-size: 0.85em;
    color: var(--mud-palette-text-secondary);
    font-weight: 500;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    flex-shrink: 1;
    min-width: 0;
    max-width: 30vw;
}

.visit-type-block {
    font-size: 1em;
    color: var(--mud-palette-primary);
    font-weight: 500;
    margin-top: 2px;
    margin-bottom: 2px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 100%;
    max-width: 100%;
}

.reason {
    color: var(--mud-palette-text-secondary);
    font-style: italic;
    font-size: 0.95em;
    margin-top: 4px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 100%;
}

@media (max-width: 600px) {
    .patient-name {
        font-size: 1em;
        max-width: 45vw;
    }
    .room-number-inline {
        font-size: 0.8em;
        max-width: 40vw;
    }
    .visit-type-block {
        font-size: 0.95em;
    }
    .reason {
        font-size: 0.85em;
    }
}

@media (min-width: 768px) {
    .appointment-card {
        width: 100% !important;
        max-width: 100% !important;
        min-width: 0 !important;
        height: 100% !important;
        min-height: 70px !important;
        display: flex;
        flex-direction: column;
        justify-content: stretch;
        align-items: stretch;
        box-sizing: border-box;
    }
    .appointment-content {
        width: 100% !important;
        max-width: 100% !important;
        min-width: 0 !important;
        height: 100% !important;
        min-height: 70px !important;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: flex-start;
        padding: 20px 18px !important;
        box-sizing: border-box;
    }
    .appointment-details-vertical {
        width: 100% !important;
        max-width: 100% !important;
        min-width: 0 !important;
        height: 100% !important;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: flex-start;
        box-sizing: border-box;
    }
    .name-row-responsive {
        width: 100% !important;
        max-width: 100% !important;
        min-width: 0 !important;
    }
    .patient-name {
        font-size: 1.3em !important;
        max-width: 100% !important;
        min-width: 0 !important;
    }
    .room-number-inline {
        font-size: 1.1em !important;
        max-width: 100% !important;
        min-width: 0 !important;
    }
    .visit-type-block {
        font-size: 1.15em !important;
        max-width: 100% !important;
        min-width: 0 !important;
    }
    .reason {
        font-size: 1em !important;
        max-width: 100% !important;
        min-width: 0 !important;
    }
}
