using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Moq;
using Moq.Protected;
using NUnit.Framework;
using TeyaUIModels.Model;
using TeyaUIModels.ViewModel;
using TeyaUIViewModels.ViewModel;

namespace TeyaWebClientUIViewModelTestCases
{
    [TestFixture]
    public class MemberServiceTests
    {
        private Mock<HttpMessageHandler> _mockHttpMessageHandler;
        private Mock<IStringLocalizer<MemberService>> _mockLocalizer;
        private Mock<ILogger<MemberService>> _mockLogger;
        private Mock<ITokenService> _mockTokenService;
        private HttpClient _httpClient;
        private MemberService _memberService;

        private const string TestMemberServiceUrl = "https://test-memberservice.com";
        private const string TestAccessToken = "test-access-token";

        [SetUp]
        public void SetUp()
        {
            _mockHttpMessageHandler = new Mock<HttpMessageHandler>();
            _mockLocalizer = new Mock<IStringLocalizer<MemberService>>();
            _mockLogger = new Mock<ILogger<MemberService>>();
            _mockTokenService = new Mock<ITokenService>();

            _httpClient = new HttpClient(_mockHttpMessageHandler.Object);

            // Set up environment variable
            Environment.SetEnvironmentVariable("MemberServiceURL", TestMemberServiceUrl);

            // Set up mock responses
            _mockTokenService.Setup(t => t.GetValidatedAccessTokenAsync()).ReturnsAsync(TestAccessToken);

            _memberService = new MemberService(
                _httpClient,
                _mockLocalizer.Object,
                _mockLogger.Object,
                _mockTokenService.Object);
        }

        [TearDown]
        public void TearDown()
        {
            _httpClient?.Dispose();
            Environment.SetEnvironmentVariable("MemberServiceURL", null);
        }

        private Member CreateTestMember()
        {
            return new Member
            {
                Id = Guid.NewGuid(),
                UserName = "<EMAIL>",
                Email = "<EMAIL>",
                FirstName = "John",
                LastName = "Doe",
                PhoneNumber = "555-1234",
                DateOfBirth = new DateTime(1980, 1, 1),
                IsActive = true,
                OrganizationID = Guid.NewGuid(),
                RoleID = Guid.NewGuid(),
                ProviderType = "Physician",
                NPI = "**********",
                Subscription = false
            };
        }

        private ProviderPatient CreateTestProviderPatient()
        {
            return new ProviderPatient
            {
                Id = Guid.NewGuid(),
                PCPId = Guid.NewGuid(),
                SSN = "***********",
                Username = "testuser"
            };
        }

        [Test]
        public async Task GetAllMembersAsync_WhenSuccessful_ReturnsMembersList()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;

            var expectedMembers = new List<Member>
            {
                CreateTestMember(),
                CreateTestMember()
            };

            var responseContent = JsonSerializer.Serialize(expectedMembers);
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(responseContent, Encoding.UTF8, "application/json")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _memberService.GetAllMembersAsync(orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(2));
            Assert.That(result[0].FirstName, Is.EqualTo("John"));
        }

        [Test]
        public async Task GetAllMembersAsync_WhenAccessTokenMissing_ThrowsUnauthorizedAccessException()
        {
            // Arrange
            _mockTokenService.Setup(t => t.GetValidatedAccessTokenAsync()).ReturnsAsync((string)null);

            // Act & Assert
            // The actual implementation throws NullReferenceException when accessing null token
            var orgId = Guid.NewGuid();
            var subscription = true;
            Assert.ThrowsAsync<NullReferenceException>(async () =>
                await _memberService.GetAllMembersAsync(orgId, subscription));
        }

        [Test]
        public async Task SearchMembersAsync_WhenSuccessful_ReturnsMembersList()
        {
            // Arrange
            var query = "<EMAIL>";
            var orgId = Guid.NewGuid();
            var subscription = false;
            var expectedMembers = new List<Member> { CreateTestMember() };

            var responseContent = JsonSerializer.Serialize(expectedMembers);
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(responseContent, Encoding.UTF8, "application/json")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.Is<HttpRequestMessage>(req =>
                        req.RequestUri.ToString().Contains($"/api/Registration/search/{orgId}/{subscription}?searchTerm=") &&
                        req.Headers.Authorization != null &&
                        req.Headers.Authorization.Scheme == "Bearer" &&
                        req.Headers.Authorization.Parameter == TestAccessToken),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _memberService.SearchMembersAsync(query, orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(1));
            Assert.That(result[0].Email, Is.EqualTo("<EMAIL>"));
        }

        [Test]
        public async Task SearchMembersAsync_WhenHttpRequestFails_ThrowsHttpRequestException()
        {
            // Arrange
            var query = "<EMAIL>";
            var orgId = Guid.NewGuid();
            var subscription = false;
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad Request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _memberService.SearchMembersAsync(query, orgId, subscription));
        }

        [Test]
        public async Task RegisterMembersAsync_WhenSuccessful_ReturnsMember()
        {
            // Arrange
            var members = new List<Member> { CreateTestMember() };
            var expectedMember = CreateTestMember();

            var responseContent = JsonSerializer.Serialize(expectedMember);
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.Created,
                Content = new StringContent(responseContent, Encoding.UTF8, "application/json")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _memberService.RegisterMembersAsync(members);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.FirstName, Is.EqualTo("John"));
        }

        [Test]
        public async Task RegisterMembersAsync_WhenAccessTokenMissing_ThrowsUnauthorizedAccessException()
        {
            // Arrange
            var members = new List<Member> { CreateTestMember() };
            _mockTokenService.Setup(t => t.GetValidatedAccessTokenAsync()).ReturnsAsync((string)null);

            // Act & Assert
            // The actual implementation throws UnauthorizedAccessException when token is null
            Assert.ThrowsAsync<UnauthorizedAccessException>(async () =>
                await _memberService.RegisterMembersAsync(members));
        }

        [Test]
        public async Task GetMemberByIdAsync_WhenSuccessful_ReturnsMember()
        {
            // Arrange
            var memberId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = false;
            var expectedMember = CreateTestMember();

            var responseContent = JsonSerializer.Serialize(expectedMember);
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(responseContent, Encoding.UTF8, "application/json")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _memberService.GetMemberByIdAsync(memberId, orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.FirstName, Is.EqualTo("John"));
        }

        [Test]
        public async Task GetMemberByIdAsync_WhenNotFound_ThrowsHttpRequestException()
        {
            // Arrange
            var memberId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = false;
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.NotFound,
                Content = new StringContent("Not Found")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _memberService.GetMemberByIdAsync(memberId, orgId, subscription));
        }

        [Test]
        public async Task GetMembersForProductAsync_WhenSuccessful_ReturnsMembersList()
        {
            // Arrange
            var productId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = false;
            var expectedMembers = new List<Member> { CreateTestMember() };

            var responseContent = JsonSerializer.Serialize(expectedMembers);
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(responseContent, Encoding.UTF8, "application/json")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _memberService.GetMembersForProductAsync(productId, orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(1));
        }

        [Test]
        public async Task DeleteMemberByIdAsync_WhenSuccessful_CompletesWithoutException()
        {
            // Arrange
            var memberId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = false;

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.NoContent
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            // No exception should be thrown
            await _memberService.DeleteMemberByIdAsync(memberId, orgId, subscription);
        }

        [Test]
        public async Task UpdateMemberByIdAsync_WhenSuccessful_CompletesWithoutException()
        {
            // Arrange
            var memberId = Guid.NewGuid();
            var member = CreateTestMember();
            member.Id = memberId; // Ensure the member ID matches the parameter

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            // No exception should be thrown
            await _memberService.UpdateMemberByIdAsync(memberId, member);
        }

        [Test]
        public async Task GetProviderlistAsync_WhenSuccessful_ReturnsProviderUsernames()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var members = new List<Member>
    {
        new Member { OrganizationID = orgId, UserName = "<EMAIL>", RoleName = "Provider" },
        new Member { OrganizationID = orgId, UserName = "<EMAIL>", RoleName = "Provider" },
        new Member { OrganizationID = orgId, UserName = "<EMAIL>", RoleName = "Admin" }, // Should be filtered out
        new Member { OrganizationID = Guid.NewGuid(), UserName = "<EMAIL>", RoleName = "Provider" } // Should be filtered out
    };

            var responseContent = JsonSerializer.Serialize(members);
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(responseContent, Encoding.UTF8, "application/json")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            var subscription = true;

            // Act
            var result = await _memberService.GetProviderlistAsync(orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(2));
            Assert.That(result, Does.Contain("<EMAIL>"));
            Assert.That(result, Does.Contain("<EMAIL>"));
            Assert.That(result, Does.Not.Contain("<EMAIL>"));
            Assert.That(result, Does.Not.Contain("<EMAIL>"));
        }


        [Test]
        public async Task SearchMembersEmailAsync_WhenMembersFound_ReturnsTrue()
        {
            // Arrange
            var query = "<EMAIL>";
            var orgId = Guid.NewGuid();
            var subscription = false;
            var members = new List<Member> { CreateTestMember() };

            var responseContent = JsonSerializer.Serialize(members);
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(responseContent, Encoding.UTF8, "application/json")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _memberService.SearchMembersEmailAsync(query, orgId, subscription);

            // Assert
            Assert.That(result, Is.True);
        }

        [Test]
        public async Task SearchMembersEmailAsync_WhenNoMembersFound_ReturnsFalse()
        {
            // Arrange
            var query = "<EMAIL>";
            var orgId = Guid.NewGuid();
            var subscription = false;
            var members = new List<Member>();

            var responseContent = JsonSerializer.Serialize(members);
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(responseContent, Encoding.UTF8, "application/json")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _memberService.SearchMembersEmailAsync(query, orgId, subscription);

            // Assert
            Assert.That(result, Is.False);
        }

        [Test]
        public async Task HasProductAccess_WhenAccessGranted_ReturnsTrue()
        {
            // Arrange
            var memberId = Guid.NewGuid();
            var productId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = false;

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent("{\"Message\": \"AccessGranted\"}", Encoding.UTF8, "application/json")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _memberService.HasProductAccess(memberId, productId, orgId, subscription);

            // Assert
            Assert.That(result, Is.True);
        }

        [Test]
        public async Task GetProviderPatientByOrganizationIdAsync_WhenSuccessful_ReturnsProviderPatientList()
        {
            // Arrange
            var organizationId = Guid.NewGuid();
            var subscription = false;
            var expectedProviderPatients = new List<ProviderPatient>
            {
                CreateTestProviderPatient(),
                CreateTestProviderPatient()
            };

            var responseContent = JsonSerializer.Serialize(expectedProviderPatients);
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(responseContent, Encoding.UTF8, "application/json")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _memberService.GetProviderPatientByOrganizationIdAsync(organizationId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(2));
        }

        [Test]
        public async Task GetPatientsByOrganizationIdAsync_WhenSuccessful_ReturnsProviderPatientList()
        {
            // Arrange
            var organizationId = Guid.NewGuid();
            var subscription = false;
            var expectedProviderPatients = new List<ProviderPatient>
            {
                CreateTestProviderPatient()
            };

            var responseContent = JsonSerializer.Serialize(expectedProviderPatients);
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(responseContent, Encoding.UTF8, "application/json")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _memberService.GetPatientsByOrganizationIdAsync(organizationId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(1));
        }

        [Test]
        public async Task RegisterMembersContentAsync_WhenSuccessful_ReturnsContent()
        {
            // Arrange
            var members = new List<Member> { CreateTestMember() };
            var expectedContent = "Registration successful";

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(expectedContent, Encoding.UTF8, "text/plain")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _memberService.RegisterMembersContentAsync(members);

            // Assert
            Assert.That(result, Is.EqualTo(expectedContent));
        }
    }
}



