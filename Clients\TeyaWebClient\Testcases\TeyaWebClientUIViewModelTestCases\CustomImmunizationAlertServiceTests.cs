using System;

using System.Collections.Generic;

using System.Net;

using System.Net.Http;

using System.Threading;

using System.Threading.Tasks;

using Microsoft.Extensions.Configuration;

using Microsoft.Extensions.Localization;

using Moq;

using Moq.Protected;

using NUnit.Framework;

using TeyaUIModels.Model;

using TeyaUIViewModels.ViewModel;

using TeyaUIViewModels.TeyaUIViewModelResources;

using System.Net.Http.Json;

using System.Text;

namespace TeyaWebClientUIViewModelTestCases

{

    [TestFixture]

    public class CustomImmunizationAlertServiceTests

    {

        private Mock<HttpMessageHandler> _mockHttpMessageHandler;

        private HttpClient _httpClient;

        private Mock<IConfiguration> _mockConfiguration;

        private Mock<IStringLocalizer<TeyaUIViewModelsStrings>> _mockLocalizer;

        private Mock<ITokenService> _mockTokenService;

        private CustomImmunizationAlertService _customImmunizationAlertService;

        private readonly string _baseUrl = "http://test-api.com";

        private readonly string _accessToken = "test-token";

        [SetUp]

        public void Setup()

        {

            // Setup environment variable

            Environment.SetEnvironmentVariable("AlertsServiceURL", _baseUrl);

            // Setup mock HTTP handler

            _mockHttpMessageHandler = new Mock<HttpMessageHandler>();

            // Setup HttpClient with mock handler

            _httpClient = new HttpClient(_mockHttpMessageHandler.Object)

            {

                BaseAddress = new Uri(_baseUrl)

            };

            // Setup mock configuration

            _mockConfiguration = new Mock<IConfiguration>();

            // Setup mock localizer

            _mockLocalizer = new Mock<IStringLocalizer<TeyaUIViewModelsStrings>>();

            _mockLocalizer.Setup(l => l["AddressRetrievalFailure"])

                .Returns(new LocalizedString("AddressRetrievalFailure", "Failed to retrieve address"));

            // Setup mock token service

            _mockTokenService = new Mock<ITokenService>();

            _mockTokenService.Setup(t => t.AccessToken).Returns(_accessToken);

            // Create CustomImmunizationAlertService with mocked dependencies

            _customImmunizationAlertService = new CustomImmunizationAlertService(_httpClient, _mockConfiguration.Object, _mockLocalizer.Object, _mockTokenService.Object);

        }

        [TearDown]

        public void TearDown()

        {

            _httpClient.Dispose();

            Environment.SetEnvironmentVariable("AlertsServiceURL", null);

        }

        [Test]

        public async Task GetAllByIdAsync_WhenSuccessful_ReturnsAlerts()

        {

            // Arrange

            var patientId = Guid.NewGuid();

            var orgId = Guid.NewGuid();

            var subscription = true;

            var expectedAlerts = new List<CustomImmunizationAlerts>

            {

                new CustomImmunizationAlerts

                {

                    Id = Guid.NewGuid(),

                    Name = "Flu Vaccine",

                    Description = "Annual flu vaccine",

                    IsActive = true

                },

                new CustomImmunizationAlerts

                {

                    Id = Guid.NewGuid(),

                    Name = "COVID-19 Vaccine",

                    Description = "COVID-19 vaccine",

                    IsActive = false

                }

            };

            var response = new HttpResponseMessage

            {

                StatusCode = HttpStatusCode.OK,

                Content = JsonContent.Create(expectedAlerts)

            };

            _mockHttpMessageHandler

                .Protected()

                .Setup<Task<HttpResponseMessage>>(

                    "SendAsync",

                    ItExpr.IsAny<HttpRequestMessage>(),

                    ItExpr.IsAny<CancellationToken>())

                .ReturnsAsync(response);

            // Act

            var result = await _customImmunizationAlertService.GetAllByIdAsync(patientId, orgId, subscription);

            // Assert

            Assert.That(result, Is.Not.Null);

            Assert.That(result.Count, Is.EqualTo(2));

            Assert.That(result[0].Name, Is.EqualTo("Flu Vaccine"));

            Assert.That(result[1].Name, Is.EqualTo("COVID-19 Vaccine"));

        }

        [Test]

        public void GetAllByIdAsync_WhenHttpRequestFails_ThrowsException()

        {

            // Arrange

            var patientId = Guid.NewGuid();

            var orgId = Guid.NewGuid();

            var subscription = true;

            var response = new HttpResponseMessage

            {

                StatusCode = HttpStatusCode.BadRequest,

                Content = new StringContent("Bad request")

            };

            _mockHttpMessageHandler

                .Protected()

                .Setup<Task<HttpResponseMessage>>(

                    "SendAsync",

                    ItExpr.IsAny<HttpRequestMessage>(),

                    ItExpr.IsAny<CancellationToken>())

                .ReturnsAsync(response);

            // Act & Assert

            Assert.ThrowsAsync<HttpRequestException>(async () =>

                await _customImmunizationAlertService.GetAllByIdAsync(patientId, orgId, subscription));

        }

        [Test]

        public async Task GetAllByIdAndIsActiveAsync_WhenSuccessful_ReturnsActiveAlerts()

        {

            // Arrange

            var patientId = Guid.NewGuid();

            var orgId = Guid.NewGuid();

            var subscription = true;

            var expectedAlerts = new List<CustomImmunizationAlerts>

            {

                new CustomImmunizationAlerts

                {

                    Id = Guid.NewGuid(),

                    Name = "Flu Vaccine",

                    Description = "Annual flu vaccine",

                    IsActive = true

                }

            };

            var response = new HttpResponseMessage

            {

                StatusCode = HttpStatusCode.OK,

                Content = JsonContent.Create(expectedAlerts)

            };

            _mockHttpMessageHandler

                .Protected()

                .Setup<Task<HttpResponseMessage>>(

                    "SendAsync",

                    ItExpr.IsAny<HttpRequestMessage>(),

                    ItExpr.IsAny<CancellationToken>())

                .ReturnsAsync(response);

            // Act

            var result = await _customImmunizationAlertService.GetActiveCustomImmunizationAlertsByOrganizationIdAsync(patientId, orgId, subscription);

            // Assert

            Assert.That(result, Is.Not.Null);

            Assert.That(result.Count, Is.EqualTo(1));

            Assert.That(result[0].Name, Is.EqualTo("Flu Vaccine"));

            Assert.That(result[0].IsActive, Is.True);

        }

        [Test]

        public void GetAllByIdAndIsActiveAsync_WhenHttpRequestFails_ThrowsException()

        {

            // Arrange

            var patientId = Guid.NewGuid();

            var orgId = Guid.NewGuid();

            var subscription = true;

            var response = new HttpResponseMessage

            {

                StatusCode = HttpStatusCode.BadRequest,

                Content = new StringContent("Bad request")

            };

            _mockHttpMessageHandler

                .Protected()

                .Setup<Task<HttpResponseMessage>>(

                    "SendAsync",

                    ItExpr.IsAny<HttpRequestMessage>(),

                    ItExpr.IsAny<CancellationToken>())

                .ReturnsAsync(response);

            // Act & Assert

            Assert.ThrowsAsync<HttpRequestException>(async () =>

                await _customImmunizationAlertService.GetActiveCustomImmunizationAlertsByOrganizationIdAsync(patientId, orgId, subscription));

        }

        [Test]

        public async Task AddCustomImmunizationAlertsAsync_WhenSuccessful_CompletesWithoutException()

        {

            // Arrange

            var patientId = Guid.NewGuid();

            var orgId = Guid.NewGuid();

            var subscription = true;

            var alerts = new List<CustomImmunizationAlerts>

            {

                new CustomImmunizationAlerts

                {

                    Id = Guid.NewGuid(),

                    Name = "Flu Vaccine",

                    Description = "Annual flu vaccine",

                    IsActive = true

                }

            };

            var response = new HttpResponseMessage

            {

                StatusCode = HttpStatusCode.OK

            };

            _mockHttpMessageHandler

                .Protected()

                .Setup<Task<HttpResponseMessage>>(

                    "SendAsync",

                    ItExpr.IsAny<HttpRequestMessage>(),

                    ItExpr.IsAny<CancellationToken>())

                .ReturnsAsync(response);

            // Act & Assert

            // No exception should be thrown

            await _customImmunizationAlertService.AddCustomImmunizationAlertsAsync(alerts, orgId, subscription);

        }


        [Test]

        public void AddCustomImmunizationAlertsAsync_WhenHttpRequestFails_ThrowsException()

        {

            // Arrange

            var patientId = Guid.NewGuid();

            var orgId = Guid.NewGuid();

            var subscription = true;

            var alerts = new List<CustomImmunizationAlerts>

            {

                new CustomImmunizationAlerts

                {

                    Id = Guid.NewGuid(),

                    Name = "Flu Vaccine",

                    Description = "Annual flu vaccine",

                    IsActive = true

                }

            };

            var response = new HttpResponseMessage

            {

                StatusCode = HttpStatusCode.BadRequest,

                Content = new StringContent("Bad request")

            };

            _mockHttpMessageHandler

                .Protected()

                .Setup<Task<HttpResponseMessage>>(

                    "SendAsync",

                    ItExpr.IsAny<HttpRequestMessage>(),

                    ItExpr.IsAny<CancellationToken>())

                .ReturnsAsync(response);

            // Act & Assert

            Assert.ThrowsAsync<HttpRequestException>(async () =>

                await _customImmunizationAlertService.AddCustomImmunizationAlertsAsync(alerts, orgId, subscription));

        }

        [Test]

        public async Task UpdateCustomImmunizationAlertAsync_WhenSuccessful_CompletesWithoutException()

        {

            // Arrange

            var patientId = Guid.NewGuid();

            var orgId = Guid.NewGuid();

            var subscription = true;

            var alert = new CustomImmunizationAlerts

            {

                Id = Guid.NewGuid(),

                Name = "Flu Vaccine",

                Description = "Annual flu vaccine",

                IsActive = true

            };

            var response = new HttpResponseMessage

            {

                StatusCode = HttpStatusCode.OK

            };

            _mockHttpMessageHandler

                .Protected()

                .Setup<Task<HttpResponseMessage>>(

                    "SendAsync",

                    ItExpr.IsAny<HttpRequestMessage>(),

                    ItExpr.IsAny<CancellationToken>())

                .ReturnsAsync(response);

            // Act & Assert

            // No exception should be thrown

            await _customImmunizationAlertService.UpdateCustomImmunizationAlertAsync(alert, orgId, subscription);

        }

        [Test]

        public void UpdateCustomImmunizationAlertAsync_WhenHttpRequestFails_ThrowsException()

        {

            // Arrange

            var patientId = Guid.NewGuid();

            var orgId = Guid.NewGuid();

            var subscription = true;

            var alert = new CustomImmunizationAlerts

            {

                Id = Guid.NewGuid(),

                Name = "Flu Vaccine",

                Description = "Annual flu vaccine",

                IsActive = true

            };

            var response = new HttpResponseMessage

            {

                StatusCode = HttpStatusCode.BadRequest,

                Content = new StringContent("Bad request")

            };

            _mockHttpMessageHandler

                .Protected()

                .Setup<Task<HttpResponseMessage>>(

                    "SendAsync",

                    ItExpr.IsAny<HttpRequestMessage>(),

                    ItExpr.IsAny<CancellationToken>())

                .ReturnsAsync(response);

            // Act & Assert

            Assert.ThrowsAsync<HttpRequestException>(async () =>

                await _customImmunizationAlertService.UpdateCustomImmunizationAlertAsync(alert, orgId, subscription));

        }

        [Test]

        public async Task UpdateCustomImmunizationAlertsListAsync_WhenSuccessful_CompletesWithoutException()

        {

            // Arrange

            var patientId = Guid.NewGuid();

            var orgId = Guid.NewGuid();

            var subscription = true;

            var alerts = new List<CustomImmunizationAlerts>

            {

                new CustomImmunizationAlerts

                {

                    Id = Guid.NewGuid(),

                    Name = "Flu Vaccine",

                    Description = "Annual flu vaccine",

                    IsActive = true

                }

            };

            var response = new HttpResponseMessage

            {

                StatusCode = HttpStatusCode.OK

            };

            _mockHttpMessageHandler

                .Protected()

                .Setup<Task<HttpResponseMessage>>(

                    "SendAsync",

                    ItExpr.IsAny<HttpRequestMessage>(),

                    ItExpr.IsAny<CancellationToken>())

                .ReturnsAsync(response);

            // Act & Assert

            // No exception should be thrown

            await _customImmunizationAlertService.UpdateCustomImmunizationAlertsListAsync(alerts, orgId, subscription);

        }

        [Test]

        public void UpdateCustomImmunizationAlertsListAsync_WhenHttpRequestFails_ThrowsException()

        {

            // Arrange

            var patientId = Guid.NewGuid();

            var orgId = Guid.NewGuid();

            var subscription = true;

            var alerts = new List<CustomImmunizationAlerts>

            {

                new CustomImmunizationAlerts

                {

                    Id = Guid.NewGuid(),

                    Name = "Flu Vaccine",

                    Description = "Annual flu vaccine",

                    IsActive = true

                }

            };

            var response = new HttpResponseMessage

            {

                StatusCode = HttpStatusCode.BadRequest,

                Content = new StringContent("Bad request")

            };

            _mockHttpMessageHandler

                .Protected()

                .Setup<Task<HttpResponseMessage>>(

                    "SendAsync",

                    ItExpr.IsAny<HttpRequestMessage>(),

                    ItExpr.IsAny<CancellationToken>())

                .ReturnsAsync(response);

            // Act & Assert

            Assert.ThrowsAsync<HttpRequestException>(async () =>

                await _customImmunizationAlertService.UpdateCustomImmunizationAlertsListAsync(alerts, orgId, subscription));

        }

        [Test]

        public async Task DeleteCustomImmunizationAlertByEntityAsync_WhenSuccessful_CompletesWithoutException()

        {

            // Arrange

            var alertId = Guid.NewGuid();

            var patientId = Guid.NewGuid();

            var orgId = Guid.NewGuid();

            var subscription = true;

            var alert = new CustomImmunizationAlerts

            {

                Id = alertId,

                Name = "Flu Vaccine",

                Description = "Annual flu vaccine",

                IsActive = true

            };

            var response = new HttpResponseMessage

            {

                StatusCode = HttpStatusCode.OK

            };

            _mockHttpMessageHandler

                .Protected()

                .Setup<Task<HttpResponseMessage>>(

                    "SendAsync",

                    ItExpr.IsAny<HttpRequestMessage>(),

                    ItExpr.IsAny<CancellationToken>())

                .ReturnsAsync(response);

            // Act & Assert

            // No exception should be thrown

            await _customImmunizationAlertService.DeleteCustomImmunizationAlertByEntityAsync(alert, orgId, subscription);

        }

        [Test]

        public void DeleteCustomImmunizationAlertByEntityAsync_WhenHttpRequestFails_ThrowsException()

        {

            // Arrange

            var alertId = Guid.NewGuid();

            var patientId = Guid.NewGuid();

            var orgId = Guid.NewGuid();

            var subscription = true;

            var alert = new CustomImmunizationAlerts

            {

                Id = alertId,

                Name = "Flu Vaccine",

                Description = "Annual flu vaccine",

                IsActive = true

            };

            var response = new HttpResponseMessage

            {

                StatusCode = HttpStatusCode.BadRequest,

                Content = new StringContent("Bad request")

            };

            _mockHttpMessageHandler

                .Protected()

                .Setup<Task<HttpResponseMessage>>(

                    "SendAsync",

                    ItExpr.IsAny<HttpRequestMessage>(),

                    ItExpr.IsAny<CancellationToken>())

                .ReturnsAsync(response);

            // Act & Assert

            Assert.ThrowsAsync<HttpRequestException>(async () =>

                await _customImmunizationAlertService.DeleteCustomImmunizationAlertByEntityAsync(alert, orgId, subscription));

        }

    }

}


