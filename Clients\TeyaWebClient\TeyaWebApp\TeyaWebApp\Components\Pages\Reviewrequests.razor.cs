﻿using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using MudBlazor;
using TeyaUIModels.Model;
using TeyaUIViewModels.ViewModel;
using TeyaWebApp.TeyaAIScribeResource;
using System.Text.Json;
using System.Text.RegularExpressions;

namespace TeyaWebApp.Components.Pages
{
    /// <summary>
    /// Review Requests page for viewing and managing incoming co-signing requests
    /// </summary>
    public partial class Reviewrequests : ComponentBase
    {
        /// <summary>
        /// Current active user for authentication context
        /// </summary>
        [Inject] private ActiveUser CurrentUser { get; set; }
        [Inject] private UserContext UserContext { get; set; }
        [Inject] private ICosigningRequestService CosigningRequestService { get; set; }
        [Inject] private ICosigningCommentHelper CommentHelper { get; set; }
        [Inject] private IProgressNotesService ProgressNotesService { get; set; }
        [Inject] private ILogger<Reviewrequests> Logger { get; set; }
        [Inject] private ISnackbar Snackbar { get; set; }
        [Inject] private IDialogService DialogService { get; set; }
        [Inject] private NavigationManager Navigation { get; set; }

        /// <summary>
        /// List of incoming co-signing requests
        /// </summary>
        private List<CosigningRequest> _reviewRequests = new List<CosigningRequest>();

        /// <summary>
        /// Loading state indicator
        /// </summary>
        private bool _isLoading = true;

        /// <summary>
        /// GitHub-style review form state
        /// </summary>
        private bool _showReviewForm = false;
        private CosigningRequest? _selectedRequest = null;
        private Record? _selectedRecord = null;
        private bool _isLoadingRecord = false;
        private bool _isProcessing = false;

        /// <summary>
        /// Comment dialog state
        /// </summary>
        private bool _showCommentDialog = false;
        private string _newComment = string.Empty;
        private string _commentContext = string.Empty;
        private string _commentSectionKey = string.Empty;
        private string _commentSubsectionKey = string.Empty;

        /// <summary>
        /// Request changes dialog state
        /// </summary>
        private bool _showRequestChangesDialog = false;
        private string _requestChangesComment = string.Empty;

        /// <summary>
        /// Comments storage for sections
        /// </summary>
        private Dictionary<string, List<CosigningComment>> _sectionComments = new();

        /// <summary>
        /// Dialog options for comment dialog
        /// </summary>
        private DialogOptions _commentDialogOptions = new()
        {
            CloseOnEscapeKey = true,
            MaxWidth = MaxWidth.Medium,
            FullWidth = true
        };

        /// <summary>
        /// Component initialization
        /// </summary>
        protected override async Task OnInitializedAsync()
        {
            try
            {
                await LoadReviewRequests();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error initializing ReviewRequests page");
                Snackbar.Add(Localizer["ErrorLoadingRequests"], Severity.Error);

            }
            finally
            {
                _isLoading = false;
                StateHasChanged();
            }
        }

        /// <summary>
        /// Load incoming co-signing requests for the current user
        /// </summary>
        private async Task LoadReviewRequests()
        {
            try
            {
                if (!Guid.TryParse(CurrentUser.id, out var userId))
                {
                    Logger.LogError("Invalid user ID: {UserId}", CurrentUser.id);
                    return;
                }

                var requests = await CosigningRequestService.GetByReviewerIdAsync(
                    userId,
                    UserContext.ActiveUserOrganizationID,
                    UserContext.ActiveUserSubscription
                );
                _reviewRequests = requests.ToList();

                Logger.LogInformation("Loaded {Count} incoming requests for user {UserId}", _reviewRequests.Count, userId);
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error loading incoming requests for user {UserId}", CurrentUser.id);
                Snackbar.Add(Localizer["ErrorLoadingRequests"], Severity.Error);
            }
        }

        /// <summary>
        /// Get status color for display
        /// </summary>
        private Color GetStatusColor(CosigningRequestStatus status)
        {
            return status switch
            {
                CosigningRequestStatus.Pending => Color.Warning,
                CosigningRequestStatus.Approved => Color.Success,
                CosigningRequestStatus.ChangesRequested => Color.Error,
                _ => Color.Default
            };
        }

        /// <summary>
        /// Get status icon for display
        /// </summary>
        private string GetStatusIcon(CosigningRequestStatus status)
        {
            return status switch
            {
                CosigningRequestStatus.Pending => Icons.Material.Filled.Schedule,
                CosigningRequestStatus.Approved => Icons.Material.Filled.CheckCircle,
                CosigningRequestStatus.ChangesRequested => Icons.Material.Filled.Comment,
                _ => Icons.Material.Filled.Help
            };
        }

        /// <summary>
        /// Get status text for display
        /// </summary>
        private string GetStatusText(CosigningRequestStatus status)
        {
            return status switch
            {
                CosigningRequestStatus.Pending => Localizer["Pending"],
                CosigningRequestStatus.Approved => Localizer["Approved"],
                CosigningRequestStatus.ChangesRequested => Localizer["ChangesRequested"],
                _ => status.ToString()
            };
        }

        /// <summary>
        /// Get CSS class for status indicator
        /// </summary>
        private string GetStatusClass(CosigningRequestStatus status)
        {
            return status switch
            {
                CosigningRequestStatus.Pending => "pending",
                CosigningRequestStatus.Approved => "approved",
                CosigningRequestStatus.ChangesRequested => "changes-requested",
                _ => "pending"
            };
        }

        /// <summary>
        /// Get request card style based on status
        /// </summary>
        private string GetRequestCardStyle(CosigningRequest request)
        {
            var baseStyle = "border-radius: 12px; transition: all 0.2s ease; cursor: pointer;";

            return request.Status switch
            {
                CosigningRequestStatus.Pending => $"{baseStyle} border-left: 4px solid #ff9800;",
                CosigningRequestStatus.Approved => $"{baseStyle} border-left: 4px solid #4caf50;",
                CosigningRequestStatus.ChangesRequested => $"{baseStyle} border-left: 4px solid #f44336;",
                _ => baseStyle
            };
        }

        /// <summary>
        /// Get comments preview text
        /// </summary>
        private string GetCommentsPreview(string commentsJson)
        {
            try
            {
                if (string.IsNullOrEmpty(commentsJson) || commentsJson == "[]")
                    return string.Empty;

                var comments = JsonSerializer.Deserialize<List<CosigningComment>>(commentsJson);
                if (comments?.Any() == true)
                {
                    var latestComment = comments.OrderByDescending(c => c.CommentDate).First();
                    var preview = latestComment.Comment.Length > 60
                        ? latestComment.Comment.Substring(0, 60) + "..."
                        : latestComment.Comment;
                    return $"{latestComment.CommenterName}: {preview}";
                }
            }
            catch (Exception ex)
            {
                Logger.LogWarning(ex, "Error parsing comments JSON: {CommentsJson}", commentsJson);
            }

            return string.Empty;
        }

        /// <summary>
        /// Get section comment count
        /// </summary>
        private int GetSectionCommentCount(string sectionKey)
        {
            return _sectionComments.Where(kvp => kvp.Key.StartsWith($"{sectionKey}|")).Sum(kvp => kvp.Value.Count);
        }

        /// <summary>
        /// Get subsection comment count
        /// </summary>
        private int GetSubsectionCommentCount(string sectionKey, string subsectionKey)
        {
            var key = $"{sectionKey}|{subsectionKey}";
            return _sectionComments.ContainsKey(key) ? _sectionComments[key].Count : 0;
        }

        /// <summary>
        /// Open GitHub-style review form for a request
        /// </summary>
        private async Task OpenReviewForm(CosigningRequest request)
        {
            try
            {
                _selectedRequest = request;
                _isLoadingRecord = true;
                _showReviewForm = true;
                _sectionComments.Clear();
                StateHasChanged();

                // Load the record data
                _selectedRecord = await ProgressNotesService.GetRecordByIdAsync(
                    request.RecordId,
                    UserContext.ActiveUserOrganizationID,
                    UserContext.ActiveUserSubscription
                );

                // Load existing comments for this request
                await LoadExistingComments(request);

                _isLoadingRecord = false;
                StateHasChanged();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error opening review form for request {RequestId}", request.Id);
                Snackbar.Add(Localizer["ErrorLoadingRecord"], Severity.Error);
                _isLoadingRecord = false;
                _showReviewForm = false;
                StateHasChanged();
            }
        }

        /// <summary>
        /// Close review form
        /// </summary>
        private void CloseReviewForm()
        {
            _showReviewForm = false;
            _selectedRequest = null;
            _selectedRecord = null;
            _sectionComments.Clear();
            StateHasChanged();
        }

        /// <summary>
        /// Approve the selected request
        /// </summary>
        private async Task ApproveRequest()
        {
            if (_selectedRequest == null) return;

            try
            {
                _isProcessing = true;
                StateHasChanged();

                await CosigningRequestService.ApproveRequestAsync(
                    _selectedRequest.Id,
                    Guid.Parse(CurrentUser.id),
                    CurrentUser.displayName ?? CurrentUser.givenName ?? "Unknown User",
                    UserContext.ActiveUserOrganizationID,
                    UserContext.ActiveUserSubscription
                );

                Snackbar.Add(Localizer["RequestApprovedSuccessfully"], Severity.Success);
                CloseReviewForm();
                await LoadReviewRequests(); // Refresh the list
                StateHasChanged();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error approving request {RequestId}", _selectedRequest.Id);
                Snackbar.Add(Localizer["ErrorApprovingRequest"], Severity.Error);
            }
            finally
            {
                _isProcessing = false;
                StateHasChanged();
            }
        }

        /// <summary>
        /// Add comment to a section
        /// </summary>
        private void AddSectionComment(string sectionKey)
        {
            _commentSectionKey = sectionKey;
            _commentSubsectionKey = string.Empty;
            _commentContext = sectionKey;
            _newComment = string.Empty;
            _showCommentDialog = true;
            StateHasChanged();
        }

        /// <summary>
        /// Add comment to a subsection
        /// </summary>
        private void AddSubsectionComment(string sectionKey, string subsectionKey)
        {
            _commentSectionKey = sectionKey;
            _commentSubsectionKey = subsectionKey;
            _commentContext = $"{sectionKey} > {subsectionKey}";
            _newComment = string.Empty;
            _showCommentDialog = true;
            StateHasChanged();
        }

        /// <summary>
        /// Close comment dialog
        /// </summary>
        private void CloseCommentDialog()
        {
            _showCommentDialog = false;
            _newComment = string.Empty;
            _commentContext = string.Empty;
            _commentSectionKey = string.Empty;
            _commentSubsectionKey = string.Empty;
            StateHasChanged();
        }

        /// <summary>
        /// Submit a new comment
        /// </summary>
        private async Task SubmitComment()
        {
            if (_selectedRequest == null || string.IsNullOrWhiteSpace(_newComment)) return;

            try
            {
                _isProcessing = true;
                StateHasChanged();

                // Create comment with section context
                var commentText = string.IsNullOrEmpty(_commentSubsectionKey)
                    ? $"[{_commentSectionKey}] {_newComment}"
                    : $"[{_commentSectionKey} > {_commentSubsectionKey}] {_newComment}";

                await CosigningRequestService.AddCommentAsync(
                    _selectedRequest.Id,
                    Guid.Parse(CurrentUser.id),
                    CurrentUser.displayName ?? CurrentUser.givenName ?? "Unknown User",
                    commentText,
                    UserContext.ActiveUserOrganizationID,
                    UserContext.ActiveUserSubscription
                );

                // Add to local comments for immediate display
                var commentKey = string.IsNullOrEmpty(_commentSubsectionKey)
                    ? _commentSectionKey
                    : $"{_commentSectionKey}|{_commentSubsectionKey}";

                if (!_sectionComments.ContainsKey(commentKey))
                {
                    _sectionComments[commentKey] = new List<CosigningComment>();
                }

                _sectionComments[commentKey].Add(new CosigningComment
                {
                    Id = Guid.NewGuid(),
                    Comment = _newComment,
                    CommenterName = CurrentUser.displayName ?? CurrentUser.givenName ?? "Unknown User",
                    CommentDate = DateTime.UtcNow
                });

                Snackbar.Add(Localizer["CommentAddedSuccessfully"], Severity.Success);
                CloseCommentDialog();
                StateHasChanged();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error adding comment to request {RequestId}", _selectedRequest.Id);
                Snackbar.Add(Localizer["ErrorAddingComment"], Severity.Error);
            }
            finally
            {
                _isProcessing = false;
                StateHasChanged();
            }
        }

        /// <summary>
        /// View comments for a request with changes requested
        /// </summary>
        private async Task ViewComments(CosigningRequest request)
        {
            try
            {
                // Get comments from the request
                var comments = CommentHelper.GetComments(request.CommentsJson);

                if (comments.Any())
                {
                    var commentsText = string.Join("\n\n", comments.Select(c =>
                        $"[{c.CommentDate:MM/dd/yyyy HH:mm}] {c.CommenterName}: {c.Comment}"));

                    await DialogService.ShowMessageBox(
                        Localizer["Comments"],
                        commentsText,
                        yesText: Localizer["Close"]
                    );
                }
                else
                {
                    Snackbar.Add(Localizer["NoCommentsFound"], Severity.Info);
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error viewing comments for request {RequestId}", request.Id);
                Snackbar.Add(Localizer["ErrorViewingComments"], Severity.Error);
            }
        }

        /// <summary>
        /// Show request changes dialog
        /// </summary>
        private void ShowRequestChangesDialog()
        {
            _requestChangesComment = string.Empty;
            _showRequestChangesDialog = true;
        }

        /// <summary>
        /// Close request changes dialog
        /// </summary>
        private void CloseRequestChangesDialog()
        {
            _showRequestChangesDialog = false;
            _requestChangesComment = string.Empty;
        }

        /// <summary>
        /// Submit request changes
        /// </summary>
        private async Task SubmitRequestChanges()
        {
            if (string.IsNullOrWhiteSpace(_requestChangesComment) || _selectedRequest == null)
                return;

            try
            {
                _isProcessing = true;
                StateHasChanged();
                await CosigningRequestService.RequestChangesAsync(
                    _selectedRequest.Id,
                    _selectedRequest.ReviewerId,
                    _selectedRequest.ReviewerName,
                    _requestChangesComment,
                    UserContext.ActiveUserOrganizationID,
                    UserContext.ActiveUserSubscription
                );

                Snackbar.Add(Localizer["ChangesRequestedSuccessfully"], Severity.Success);

                // Update the request status locally
                _selectedRequest.Status = CosigningRequestStatus.ChangesRequested;

                // Update the request in the list
                var requestInList = _reviewRequests.FirstOrDefault(r => r.Id == _selectedRequest.Id);
                if (requestInList != null)
                {
                    requestInList.Status = CosigningRequestStatus.ChangesRequested;
                }

                CloseRequestChangesDialog();
                CloseReviewForm();
                await LoadReviewRequests(); // Refresh the list
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error requesting changes for request {RequestId}", _selectedRequest.Id);
                Snackbar.Add(Localizer["ErrorRequestingChanges"], Severity.Error);
            }
            finally
            {
                _isProcessing = false;
                StateHasChanged();
            }
        }

        /// <summary>
        /// Load existing comments for the request and organize by section
        /// </summary>
        private async Task LoadExistingComments(CosigningRequest request)
        {
            try
            {
                var comments = CommentHelper.GetComments(request.CommentsJson);
                _sectionComments.Clear();

                foreach (var comment in comments)
                {
                    // Parse section context from comment text
                    var match = Regex.Match(comment.Comment, @"^\[([^\]]+)\]\s*(.*)");
                    if (match.Success)
                    {
                        var sectionPath = match.Groups[1].Value;
                        var commentText = match.Groups[2].Value;

                        // Convert section path to key format
                        var commentKey = sectionPath.Contains(" > ")
                            ? sectionPath.Replace(" > ", "|")
                            : sectionPath;

                        if (!_sectionComments.ContainsKey(commentKey))
                        {
                            _sectionComments[commentKey] = new List<CosigningComment>();
                        }

                        _sectionComments[commentKey].Add(new CosigningComment
                        {
                            Id = comment.Id,
                            Comment = commentText,
                            CommenterName = comment.CommenterName,
                            CommentDate = comment.CommentDate
                        });
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error loading existing comments for request {RequestId}", request.Id);
            }
        }

        /// <summary>
        /// Extract notes data from JSON string (similar to Notes page)
        /// </summary>
        private List<Dictionary<string, Dictionary<string, string>>> ExtractNotesData(string notesJson)
        {
            if (string.IsNullOrWhiteSpace(notesJson))
                return new List<Dictionary<string, Dictionary<string, string>>>();

            try
            {
                var cleanedJson = notesJson.Replace("\\\"", "\"").Trim('"');
                return JsonSerializer.Deserialize<List<Dictionary<string, Dictionary<string, string>>>>(cleanedJson)
                       ?? new List<Dictionary<string, Dictionary<string, string>>>();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error parsing notes JSON");
                return new List<Dictionary<string, Dictionary<string, string>>>();
            }
        }

        /// <summary>
        /// Get editor content for a specific field (similar to Notes page)
        /// </summary>
        private string GetEditorContent(Record record, string sectionKey, string fieldKey)
        {
            try
            {
                if (record?.Template == null) return string.Empty;

                var templateData = JsonSerializer.Deserialize<Dictionary<string, object>>(record.Template);
                if (templateData?.ContainsKey(fieldKey) == true)
                {
                    return templateData[fieldKey]?.ToString() ?? string.Empty;
                }
                return string.Empty;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error getting editor content for {SectionKey}.{FieldKey}", sectionKey, fieldKey);
                return string.Empty;
            }
        }

        /// <summary>
        /// Clean HTML content for display
        /// </summary>
        private string CleanHtml(string content)
        {
            if (string.IsNullOrWhiteSpace(content))
                return string.Empty;

            // Basic HTML cleaning - remove script tags and dangerous content
            content = Regex.Replace(content, @"<script[^>]*>.*?</script>", "", RegexOptions.IgnoreCase | RegexOptions.Singleline);
            content = Regex.Replace(content, @"<style[^>]*>.*?</style>", "", RegexOptions.IgnoreCase | RegexOptions.Singleline);

            return content;
        }
    }
}