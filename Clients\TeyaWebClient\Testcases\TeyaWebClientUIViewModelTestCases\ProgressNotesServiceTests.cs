using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using Moq;
using Moq.Protected;
using NUnit.Framework;
using TeyaUIModels.Model;
using TeyaUIViewModels.ViewModel;
using TeyaUIViewModels.TeyaUIViewModelResources;
using System.Net.Http.Json;
using System.Text.Json;

namespace TeyaWebClientUIViewModelTestCases
{
    [TestFixture]
    public class ProgressNotesServiceTests
    {
        private Mock<HttpMessageHandler> _mockHttpMessageHandler;
        private HttpClient _httpClient;
        private Mock<IConfiguration> _mockConfiguration;
        private Mock<IStringLocalizer<TeyaUIViewModelsStrings>> _mockLocalizer;
        private Mock<ITokenService> _mockTokenService;
        private ProgressNotesService _progressNotesService;
        private readonly string _baseUrl = "http://test-api.com";
        private readonly string _accessToken = "test-access-token";

        [SetUp]
        public void Setup()
        {
            // Setup environment variable
            Environment.SetEnvironmentVariable("EncounterNotesURL", _baseUrl);

            // Setup mock HTTP handler
            _mockHttpMessageHandler = new Mock<HttpMessageHandler>();

            // Setup HttpClient with mock handler
            _httpClient = new HttpClient(_mockHttpMessageHandler.Object)
            {
                BaseAddress = new Uri(_baseUrl)
            };

            // Setup mock configuration
            _mockConfiguration = new Mock<IConfiguration>();
            _mockConfiguration.Setup(c => c["EncounterNotesURL"]).Returns(_baseUrl);

            // Setup mock localizer
            _mockLocalizer = new Mock<IStringLocalizer<TeyaUIViewModelsStrings>>();

            // Setup mock token service
            _mockTokenService = new Mock<ITokenService>();
            _mockTokenService.Setup(t => t.AccessToken).Returns(_accessToken);

            // Create ProgressNotesService with mocked dependencies
            _progressNotesService = new ProgressNotesService(_httpClient, _mockConfiguration.Object, _mockLocalizer.Object, _mockTokenService.Object);
        }

        [TearDown]
        public void TearDown()
        {
            _httpClient.Dispose();
            Environment.SetEnvironmentVariable("EncounterNotesURL", null);
        }

        [Test]
        public async Task GetRecordsByPatientIdAsync_WhenSuccessful_ReturnsRecords()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var expectedRecords = new List<Record>
            {
                new Record
                {
                    Id = Guid.NewGuid(),
                    PatientId = patientId,
                    PatientName = "John Doe",
                    PCPId = Guid.NewGuid(),
                    DateTime = DateTime.Now.AddDays(-7),
                    Notes = "Patient progress notes 1",
                    isEditable = true,
                    Transcription = "Transcription 1"
                },
                new Record
                {
                    Id = Guid.NewGuid(),
                    PatientId = patientId,
                    PatientName = "John Doe",
                    PCPId = Guid.NewGuid(),
                    DateTime = DateTime.Now.AddDays(-3),
                    Notes = "Patient progress notes 2",
                    isEditable = true,
                    Transcription = "Transcription 2"
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(JsonSerializer.Serialize(expectedRecords))
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _progressNotesService.GetRecordsByPatientIdAsync(patientId, orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(expectedRecords.Count));
            Assert.That(result[0].Id, Is.EqualTo(expectedRecords[0].Id));
            Assert.That(result[0].PatientId, Is.EqualTo(expectedRecords[0].PatientId));
            Assert.That(result[0].PatientName, Is.EqualTo(expectedRecords[0].PatientName));
            Assert.That(result[0].Notes, Is.EqualTo(expectedRecords[0].Notes));
            Assert.That(result[0].isEditable, Is.EqualTo(expectedRecords[0].isEditable));
            Assert.That(result[0].Transcription, Is.EqualTo(expectedRecords[0].Transcription));
        }

        [Test]
        public async Task GetRecordsByPatientIdAsync_WhenEmptyResponse_ReturnsEmptyList()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent("[]")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _progressNotesService.GetRecordsByPatientIdAsync(patientId, orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(0));
        }

        [Test]
        public void GetRecordsByPatientIdAsync_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.NotFound,
                Content = new StringContent("Not found")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<Exception>(async () =>
                await _progressNotesService.GetRecordsByPatientIdAsync(patientId, orgId, subscription));

            Assert.That(exception.Message, Does.Contain("Error fetching records"));
        }

        [Test]
        public void GetRecordsByPatientIdAsync_WhenExceptionThrown_WrapsException()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var expectedException = new HttpRequestException("Test exception");

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ThrowsAsync(expectedException);

            // Act & Assert
            var exception = Assert.ThrowsAsync<Exception>(async () =>
                await _progressNotesService.GetRecordsByPatientIdAsync(patientId, orgId, subscription));

            Assert.That(exception.Message, Does.Contain("An error occurred while retrieving records"));
            Assert.That(exception.InnerException, Is.EqualTo(expectedException));
        }

        [Test]
        public async Task GetRecordsByPCPIdAsync_WhenSuccessful_ReturnsRecords()
        {
            // Arrange
            var pcpId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var expectedRecords = new List<Record>
            {
                new Record
                {
                    Id = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    PatientName = "Patient 1",
                    PCPId = pcpId,
                    DateTime = DateTime.Now.AddDays(-5),
                    Notes = "PCP notes 1",
                    isEditable = true,
                    Transcription = "PCP Transcription 1"
                },
                new Record
                {
                    Id = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    PatientName = "Patient 2",
                    PCPId = pcpId,
                    DateTime = DateTime.Now.AddDays(-2),
                    Notes = "PCP notes 2",
                    isEditable = true,
                    Transcription = "PCP Transcription 2"
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(JsonSerializer.Serialize(expectedRecords))
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _progressNotesService.GetRecordsByPCPIdAsync(pcpId, orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(expectedRecords.Count));
            Assert.That(result[0].PCPId, Is.EqualTo(pcpId));
            Assert.That(result[1].PCPId, Is.EqualTo(pcpId));
            Assert.That(result[0].PatientName, Is.EqualTo("Patient 1"));
            Assert.That(result[1].PatientName, Is.EqualTo("Patient 2"));
        }

        [Test]
        public void GetRecordsByPCPIdAsync_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var pcpId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<Exception>(async () =>
                await _progressNotesService.GetRecordsByPCPIdAsync(pcpId, orgId, subscription));

            Assert.That(exception.Message, Does.Contain("Error fetching records"));
        }

        [Test]
        public async Task SaveRecordAsync_WhenSuccessful_ReturnsSuccessfulResponse()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;
            var updatedRecord = new Record
            {
                Id = Guid.NewGuid(),
                PatientId = Guid.NewGuid(),
                OrganizationId = orgId,
                PatientName = "Updated Patient",
                PCPId = Guid.NewGuid(),
                DateTime = DateTime.Now,
                Notes = "Updated progress notes",
                isEditable = false,
                Transcription = "Updated transcription"
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent("Success")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _progressNotesService.SaveRecordAsync(updatedRecord, orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.StatusCode, Is.EqualTo(HttpStatusCode.OK));
        }

        [Test]
        public void SaveRecordAsync_WhenExceptionThrown_WrapsException()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;
            var updatedRecord = new Record
            {
                Id = Guid.NewGuid(),
                PatientId = Guid.NewGuid(),
                OrganizationId = orgId,
                PatientName = "Updated Patient",
                PCPId = Guid.NewGuid(),
                DateTime = DateTime.Now,
                Notes = "Updated progress notes",
                isEditable = false,
                Transcription = "Updated transcription"
            };

            var expectedException = new HttpRequestException("Test exception");

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ThrowsAsync(expectedException);

            // Act & Assert
            var exception = Assert.ThrowsAsync<Exception>(async () =>
                await _progressNotesService.SaveRecordAsync(updatedRecord, orgId, subscription));

            Assert.That(exception.Message, Does.Contain("An error occurred while Updating record"));
            Assert.That(exception.InnerException, Is.EqualTo(expectedException));
        }

        [Test]
        public async Task SaveRecordAsync_WhenNotSuccessful_ReturnsFailureResponse()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;
            var updatedRecord = new Record
            {
                Id = Guid.NewGuid(),
                PatientId = Guid.NewGuid(),
                OrganizationId = orgId,
                PatientName = "Updated Patient",
                PCPId = Guid.NewGuid(),
                DateTime = DateTime.Now,
                Notes = "Updated progress notes",
                isEditable = false,
                Transcription = "Updated transcription"
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _progressNotesService.SaveRecordAsync(updatedRecord, orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.StatusCode, Is.EqualTo(HttpStatusCode.BadRequest));
        }

        [Test]
        public async Task GetRecordsByPatientIdAsync_WhenWhitespaceResponse_ReturnsEmptyList()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent("   ")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _progressNotesService.GetRecordsByPatientIdAsync(patientId, orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(0));
        }

        [Test]
        public async Task GetRecordsByPCPIdAsync_WhenEmptyResponse_ReturnsEmptyList()
        {
            // Arrange
            var pcpId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent("[]")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _progressNotesService.GetRecordsByPCPIdAsync(pcpId, orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(0));
        }
    }
}



