<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <!-- Bundle Information -->
        <key>CFBundleIdentifier</key>
        <string>com.teyahealth.teyamobile</string>
        <key>CFBundleName</key>
        <string>TeyaMobile</string>
        <key>CFBundleDisplayName</key>
        <string>TeyaMobile</string>
        <key>CFBundleVersion</key>
        <string>1.0.1</string>
        <key>CFBundleShortVersionString</key>
        <string>1.0</string>
        <key>CFBundleExecutable</key>
        <string>TeyaMobile</string>
        <key>CFBundlePackageType</key>
        <string>APPL</string>
        <key>CFBundleSignature</key>
        <string>????</string>

        <!-- iOS Requirements -->
        <key>LSRequiresIPhoneOS</key>
        <true />
        <key>UIDeviceFamily</key>
        <array>
            <integer>1</integer>
            <integer>2</integer>
        </array>
        <key>UIRequiredDeviceCapabilities</key>
        <array>
            <string>arm64</string>
        </array>
        <key>MinimumOSVersion</key>
        <string>15.0</string>

        <!-- Interface Orientations -->
        <key>UISupportedInterfaceOrientations</key>
        <array>
            <string>UIInterfaceOrientationPortrait</string>
            <string>UIInterfaceOrientationLandscapeLeft</string>
            <string>UIInterfaceOrientationLandscapeRight</string>
        </array>
        <key>UISupportedInterfaceOrientations~ipad</key>
        <array>
            <string>UIInterfaceOrientationPortrait</string>
            <string>UIInterfaceOrientationPortraitUpsideDown</string>
            <string>UIInterfaceOrientationLandscapeLeft</string>
            <string>UIInterfaceOrientationLandscapeRight</string>
        </array>

        <!-- App Icon -->
        <key>XSAppIconAssets</key>
        <string>Assets.xcassets/appicon.appiconset</string>

        <!-- Privacy Permissions -->
        <key>NSMicrophoneUsageDescription</key>
        <string>This app needs access to the microphone to record audio</string>
        <key>NSSpeechRecognitionUsageDescription</key>
        <string>This app uses speech recognition to transcribe your voice in real-time.</string>
        <key>NSLocationWhenInUseUsageDescription</key>
        <string>This app uses your location to provide location-based features.</string>
        <key>NSLocationAlwaysUsageDescription</key>
        <string>This app needs location access even when not in use for background services.</string>
        <key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
        <string>This app needs location access for background services and location-based features.</string>

        <!-- Background Modes - CORRECTED -->
        <key>UIBackgroundModes</key>
        <array>
            <string>audio</string>
            <string>fetch</string>
            <string>remote-notification</string>
        </array>

        <!-- File Sharing -->
        <key>UIFileSharingEnabled</key>
        <true />
    </dict>
</plist>
