using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Localization;
using Azure.Communication.Email;
using Azure;
using TeyaUIViewModels.ViewModel;
using TeyaUIViewModels.TeyaUIViewModelResource;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace TeyaWebClientUIViewModelTestCases
{
    [TestFixture]
    public class CommunicationServiceTests
    {
        private Mock<IStringLocalizer<TeyaUIViewModelsResource>> _mockLocalizer;
        private CommunicationService _communicationService;
        private const string TestConnectionString = "endpoint=https://test.communication.azure.com/;accesskey=testkey";

        [SetUp]
        public void SetUp()
        {
            _mockLocalizer = new Mock<IStringLocalizer<TeyaUIViewModelsResource>>();

            // Set up environment variable for connection string
            Environment.SetEnvironmentVariable("EMAIL-SERVICE-CONNECTION-STRING", TestConnectionString);

            // Set up localizer mock responses
            _mockLocalizer.Setup(l => l["EmailConnectionStringNotSet"]).Returns(new LocalizedString("EmailConnectionStringNotSet", "Email connection string not set"));
            _mockLocalizer.Setup(l => l["SenderAddressEmpty"]).Returns(new LocalizedString("SenderAddressEmpty", "Sender address is empty"));
            _mockLocalizer.Setup(l => l["RecipientAddressesEmpty"]).Returns(new LocalizedString("RecipientAddressesEmpty", "Recipient addresses are empty"));
            _mockLocalizer.Setup(l => l["SubjectEmpty"]).Returns(new LocalizedString("SubjectEmpty", "Subject is empty"));
            _mockLocalizer.Setup(l => l["PlainTextContentEmpty"]).Returns(new LocalizedString("PlainTextContentEmpty", "Plain text content is empty"));
            _mockLocalizer.Setup(l => l["HtmlContentEmpty"]).Returns(new LocalizedString("HtmlContentEmpty", "HTML content is empty"));
            _mockLocalizer.Setup(l => l["FailedToSendEmail", It.IsAny<object>()])
                .Returns((string key, object[] args) => new LocalizedString(key, $"Failed to send email: {args[0]}"));

            _communicationService = new CommunicationService(_mockLocalizer.Object);
        }

        [TearDown]
        public void TearDown()
        {
            Environment.SetEnvironmentVariable("EMAIL-SERVICE-CONNECTION-STRING", null);
        }

        [Test]
        public void Constructor_WhenConnectionStringIsNull_ThrowsInvalidOperationException()
        {
            // Arrange
            Environment.SetEnvironmentVariable("EMAIL-SERVICE-CONNECTION-STRING", null);

            // Act & Assert
            Assert.Throws<InvalidOperationException>(() => new CommunicationService(_mockLocalizer.Object));
        }

        [Test]
        public void Constructor_WhenConnectionStringIsEmpty_ThrowsInvalidOperationException()
        {
            // Arrange
            Environment.SetEnvironmentVariable("EMAIL-SERVICE-CONNECTION-STRING", "");

            // Act & Assert
            Assert.Throws<InvalidOperationException>(() => new CommunicationService(_mockLocalizer.Object));
        }

        [Test]
        public void Constructor_WhenConnectionStringIsWhitespace_ThrowsInvalidOperationException()
        {
            // Arrange
            Environment.SetEnvironmentVariable("EMAIL-SERVICE-CONNECTION-STRING", "   ");

            // Act & Assert
            Assert.Throws<InvalidOperationException>(() => new CommunicationService(_mockLocalizer.Object));
        }

        [Test]
        public void MailService_WhenSenderAddressIsNull_ThrowsArgumentException()
        {
            // Arrange
            string senderAddress = null!; // Use null-forgiving operator for testing null scenarios
            string subject = "Test Subject";
            string plainTextContent = "Test Content";
            var recipientAddresses = new List<string> { "<EMAIL>" };

            // Act & Assert
            var exception = Assert.ThrowsAsync<ArgumentException>(async () =>
                await _communicationService.MailService(senderAddress, subject, plainTextContent, recipientAddresses));

            Assert.That(exception.ParamName, Is.EqualTo("senderAddress"));
        }

        [Test]
        public void MailService_WhenSenderAddressIsEmpty_ThrowsArgumentException()
        {
            // Arrange
            string senderAddress = "";
            string subject = "Test Subject";
            string plainTextContent = "Test Content";
            var recipientAddresses = new List<string> { "<EMAIL>" };

            // Act & Assert
            var exception = Assert.ThrowsAsync<ArgumentException>(async () =>
                await _communicationService.MailService(senderAddress, subject, plainTextContent, recipientAddresses));

            Assert.That(exception.ParamName, Is.EqualTo("senderAddress"));
        }

        [Test]
        public void MailService_WhenRecipientAddressesIsNull_ThrowsArgumentException()
        {
            // Arrange
            string senderAddress = "<EMAIL>";
            string subject = "Test Subject";
            string plainTextContent = "Test Content";
            List<string> recipientAddresses = null!; // Use null-forgiving operator for testing null scenarios

            // Act & Assert
            var exception = Assert.ThrowsAsync<ArgumentException>(async () =>
                await _communicationService.MailService(senderAddress, subject, plainTextContent, recipientAddresses));

            Assert.That(exception.ParamName, Is.EqualTo("recipientAddresses"));
        }

        [Test]
        public void MailService_WhenRecipientAddressesIsEmpty_ThrowsArgumentException()
        {
            // Arrange
            string senderAddress = "<EMAIL>";
            string subject = "Test Subject";
            string plainTextContent = "Test Content";
            var recipientAddresses = new List<string>();

            // Act & Assert
            var exception = Assert.ThrowsAsync<ArgumentException>(async () =>
                await _communicationService.MailService(senderAddress, subject, plainTextContent, recipientAddresses));

            Assert.That(exception.ParamName, Is.EqualTo("recipientAddresses"));
        }

        [Test]
        public void MailService_WhenSubjectIsNull_ThrowsArgumentException()
        {
            // Arrange
            string senderAddress = "<EMAIL>";
            string subject = null!; // Use null-forgiving operator for testing null scenarios
            string plainTextContent = "Test Content";
            var recipientAddresses = new List<string> { "<EMAIL>" };

            // Act & Assert
            var exception = Assert.ThrowsAsync<ArgumentException>(async () =>
                await _communicationService.MailService(senderAddress, subject, plainTextContent, recipientAddresses));

            Assert.That(exception.ParamName, Is.EqualTo("subject"));
        }

        [Test]
        public void MailService_WhenPlainTextContentIsNull_ThrowsArgumentException()
        {
            // Arrange
            string senderAddress = "<EMAIL>";
            string subject = "Test Subject";
            string plainTextContent = null!; // Use null-forgiving operator for testing null scenarios
            var recipientAddresses = new List<string> { "<EMAIL>" };

            // Act & Assert
            var exception = Assert.ThrowsAsync<ArgumentException>(async () =>
                await _communicationService.MailService(senderAddress, subject, plainTextContent, recipientAddresses));

            Assert.That(exception.ParamName, Is.EqualTo("plainTextContent"));
        }

        [Test]
        public void SendHtmlEmailService_WhenSenderAddressIsNull_ThrowsArgumentException()
        {
            // Arrange
            string senderAddress = null!; // Use null-forgiving operator for testing null scenarios
            string subject = "Test Subject";
            string htmlContent = "<h1>Test Content</h1>";
            var recipientAddresses = new List<string> { "<EMAIL>" };

            // Act & Assert
            var exception = Assert.ThrowsAsync<ArgumentException>(async () =>
                await _communicationService.SendHtmlEmailService(senderAddress, subject, htmlContent, recipientAddresses));

            Assert.That(exception.ParamName, Is.EqualTo("senderAddress"));
        }

        [Test]
        public void SendHtmlEmailService_WhenRecipientAddressesIsNull_ThrowsArgumentException()
        {
            // Arrange
            string senderAddress = "<EMAIL>";
            string subject = "Test Subject";
            string htmlContent = "<h1>Test Content</h1>";
            List<string> recipientAddresses = null!; // Use null-forgiving operator for testing null scenarios

            // Act & Assert
            var exception = Assert.ThrowsAsync<ArgumentException>(async () =>
                await _communicationService.SendHtmlEmailService(senderAddress, subject, htmlContent, recipientAddresses));

            Assert.That(exception.ParamName, Is.EqualTo("recipientAddresses"));
        }

        [Test]
        public void SendHtmlEmailService_WhenSubjectIsEmpty_ThrowsArgumentException()
        {
            // Arrange
            string senderAddress = "<EMAIL>";
            string subject = "";
            string htmlContent = "<h1>Test Content</h1>";
            var recipientAddresses = new List<string> { "<EMAIL>" };

            // Act & Assert
            var exception = Assert.ThrowsAsync<ArgumentException>(async () =>
                await _communicationService.SendHtmlEmailService(senderAddress, subject, htmlContent, recipientAddresses));

            Assert.That(exception.ParamName, Is.EqualTo("subject"));
        }

        [Test]
        public void SendHtmlEmailService_WhenHtmlContentIsWhitespace_ThrowsArgumentException()
        {
            // Arrange
            string senderAddress = "<EMAIL>";
            string subject = "Test Subject";
            string htmlContent = "   ";
            var recipientAddresses = new List<string> { "<EMAIL>" };

            // Act & Assert
            var exception = Assert.ThrowsAsync<ArgumentException>(async () =>
                await _communicationService.SendHtmlEmailService(senderAddress, subject, htmlContent, recipientAddresses));

            Assert.That(exception.ParamName, Is.EqualTo("htmlContent"));
        }
    }
}


