using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using Moq;
using Moq.Protected;
using NUnit.Framework;
using TeyaUIModels.Model;
using TeyaWebApp.Services;
using System.Net.Http.Json;
using System.Text.Json;
using TeyaUIViewModels.ViewModel;
using System.Linq;

namespace TeyaWebClientUIViewModelTestCases
{
    [TestFixture]
    public class PagePathServiceTests
    {
        private Mock<HttpMessageHandler> _mockHttpMessageHandler;
        private HttpClient _httpClient;
        private Mock<ILogger<PagePathService>> _mockLogger;
        private Mock<IStringLocalizer<PagePathService>> _mockLocalizer;
        private Mock<ITokenService> _mockTokenService;
        private PagePathService _pagePathService;
        private readonly string _baseUrl = "http://test-api.com";
        private readonly string _accessToken = "test-access-token";

        [SetUp]
        public void Setup()
        {
            // Setup environment variable
            Environment.SetEnvironmentVariable("MemberServiceURL", _baseUrl);

            // Setup mock HTTP handler
            _mockHttpMessageHandler = new Mock<HttpMessageHandler>();

            // Setup HttpClient with mock handler
            _httpClient = new HttpClient(_mockHttpMessageHandler.Object)
            {
                BaseAddress = new Uri(_baseUrl)
            };

            // Setup mock logger
            _mockLogger = new Mock<ILogger<PagePathService>>();

            // Setup mock localizer
            _mockLocalizer = new Mock<IStringLocalizer<PagePathService>>();
            _mockLocalizer.Setup(l => l["ErrorFetchingAllPagePaths"])
                .Returns(new LocalizedString("ErrorFetchingAllPagePaths", "Error fetching all page paths"));
            _mockLocalizer.Setup(l => l["ErrorFetchingPagePathById"])
                .Returns(new LocalizedString("ErrorFetchingPagePathById", "Error fetching page path by ID"));
            _mockLocalizer.Setup(l => l["ErrorAddingPagePath"])
                .Returns(new LocalizedString("ErrorAddingPagePath", "Error adding page path"));
            _mockLocalizer.Setup(l => l["ErrorUpdatingPagePath"])
                .Returns(new LocalizedString("ErrorUpdatingPagePath", "Error updating page path"));
            _mockLocalizer.Setup(l => l["ErrorDeletingPagePath"])
                .Returns(new LocalizedString("ErrorDeletingPagePath", "Error deleting page path"));
            _mockLocalizer.Setup(l => l["API Response: {ResponseData}"])
                .Returns(new LocalizedString("API Response: {ResponseData}", "API Response: {ResponseData}"));

            // Setup mock token service
            _mockTokenService = new Mock<ITokenService>();
            _mockTokenService.Setup(t => t.AccessToken).Returns(_accessToken);
            _mockTokenService.Setup(t => t.GetValidatedAccessTokenAsync()).ReturnsAsync(_accessToken);

            // Create PagePathService with mocked dependencies
            _pagePathService = new PagePathService(_httpClient, _mockLocalizer.Object, _mockLogger.Object, _mockTokenService.Object);
        }

        [TearDown]
        public void TearDown()
        {
            _httpClient.Dispose();
            Environment.SetEnvironmentVariable("MemberServiceURL", null);
        }

        [Test]
        public async Task GetPagePathsAsync_WhenSuccessful_ReturnsPagePaths()
        {
            // Arrange
            var expectedPagePaths = new List<PagePath>
            {
                new PagePath
                {
                    PageId = Guid.NewGuid(),
                    PagePathValue = "/test-path-1",
                    CreatedBy = Guid.NewGuid(),
                    CreatedDate = DateTime.Now,
                    IsActive = true
                },
                new PagePath
                {
                    PageId = Guid.NewGuid(),
                    PagePathValue = "/test-path-2",
                    CreatedBy = Guid.NewGuid(),
                    CreatedDate = DateTime.Now,
                    IsActive = true
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedPagePaths)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _pagePathService.GetPagePathsAsync();

            // Assert
            Assert.That(result, Is.Not.Null);
            var resultList = result.ToList();
            Assert.That(resultList.Count, Is.EqualTo(expectedPagePaths.Count));
            Assert.That(resultList[0].PageId, Is.EqualTo(expectedPagePaths[0].PageId));
            Assert.That(resultList[0].PagePathValue, Is.EqualTo(expectedPagePaths[0].PagePathValue));
            Assert.That(resultList[0].IsActive, Is.EqualTo(expectedPagePaths[0].IsActive));
            Assert.That(resultList[1].PageId, Is.EqualTo(expectedPagePaths[1].PageId));
            Assert.That(resultList[1].PagePathValue, Is.EqualTo(expectedPagePaths[1].PagePathValue));
        }

        [Test]
        public void GetPagePathsAsync_WhenExceptionThrown_PropagatesException()
        {
            // Arrange
            var expectedException = new HttpRequestException("Test exception");

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ThrowsAsync(expectedException);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _pagePathService.GetPagePathsAsync());

            Assert.That(exception.Message, Is.EqualTo(expectedException.Message));
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.Is<Exception>(ex => ex.Message == expectedException.Message),
                    It.Is<Func<It.IsAnyType, Exception, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }

        [Test]
        public async Task GetPagePathByIdAsync_WhenSuccessful_ReturnsPagePath()
        {
            // Arrange
            var pagePathId = Guid.NewGuid();
            var expectedPagePath = new PagePath
            {
                PageId = pagePathId,
                PagePathValue = "/test-path",
                CreatedBy = Guid.NewGuid(),
                CreatedDate = DateTime.Now,
                IsActive = true
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedPagePath)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _pagePathService.GetPagePathByIdAsync(pagePathId);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.PageId, Is.EqualTo(expectedPagePath.PageId));
            Assert.That(result.PagePathValue, Is.EqualTo(expectedPagePath.PagePathValue));
            Assert.That(result.IsActive, Is.EqualTo(expectedPagePath.IsActive));
        }

        [Test]
        public void GetPagePathByIdAsync_WhenExceptionThrown_PropagatesException()
        {
            // Arrange
            var pagePathId = Guid.NewGuid();
            var expectedException = new HttpRequestException("Test exception");

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ThrowsAsync(expectedException);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _pagePathService.GetPagePathByIdAsync(pagePathId));

            Assert.That(exception.Message, Is.EqualTo(expectedException.Message));
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.Is<Exception>(ex => ex.Message == expectedException.Message),
                    It.Is<Func<It.IsAnyType, Exception, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }

        [Test]
        public async Task AddPagePathAsync_WhenSuccessful_CompletesSuccessfully()
        {
            // Arrange
            var pagePath = new PagePath
            {
                PageId = Guid.NewGuid(),
                PagePathValue = "/test-path",
                CreatedBy = Guid.NewGuid(),
                CreatedDate = DateTime.Now,
                IsActive = true
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.Created,
                Content = new StringContent("Success")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.DoesNotThrowAsync(async () => await _pagePathService.AddPagePathAsync(pagePath));

            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Information,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.IsAny<Exception>(),
                    It.Is<Func<It.IsAnyType, Exception, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }

        [Test]
        public void AddPagePathAsync_WhenExceptionThrown_PropagatesException()
        {
            // Arrange
            var pagePath = new PagePath
            {
                PageId = Guid.NewGuid(),
                PagePathValue = "/test-path",
                CreatedBy = Guid.NewGuid(),
                CreatedDate = DateTime.Now,
                IsActive = true
            };

            var expectedException = new HttpRequestException("Test exception");

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ThrowsAsync(expectedException);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _pagePathService.AddPagePathAsync(pagePath));

            Assert.That(exception.Message, Is.EqualTo(expectedException.Message));
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.Is<Exception>(ex => ex.Message == expectedException.Message),
                    It.Is<Func<It.IsAnyType, Exception, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }

        [Test]
        public async Task UpdatePagePathAsync_WhenSuccessful_CompletesSuccessfully()
        {
            // Arrange
            var pagePath = new PagePath
            {
                PageId = Guid.NewGuid(),
                PagePathValue = "/test-path-updated",
                CreatedBy = Guid.NewGuid(),
                CreatedDate = DateTime.Now,
                IsActive = true
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent("Success")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.DoesNotThrowAsync(async () => await _pagePathService.UpdatePagePathAsync(pagePath));

            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Information,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.IsAny<Exception>(),
                    It.Is<Func<It.IsAnyType, Exception, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }

        [Test]
        public void UpdatePagePathAsync_WhenExceptionThrown_PropagatesException()
        {
            // Arrange
            var pagePath = new PagePath
            {
                PageId = Guid.NewGuid(),
                PagePathValue = "/test-path-updated",
                CreatedBy = Guid.NewGuid(),
                CreatedDate = DateTime.Now,
                IsActive = true
            };

            var expectedException = new HttpRequestException("Test exception");

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ThrowsAsync(expectedException);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _pagePathService.UpdatePagePathAsync(pagePath));

            Assert.That(exception.Message, Is.EqualTo(expectedException.Message));
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.Is<Exception>(ex => ex.Message == expectedException.Message),
                    It.Is<Func<It.IsAnyType, Exception, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }

        [Test]
        public async Task DeletePagePathByIdAsync_WhenSuccessful_CompletesSuccessfully()
        {
            // Arrange
            var pagePathId = Guid.NewGuid();

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.NoContent
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.DoesNotThrowAsync(async () => await _pagePathService.DeletePagePathByIdAsync(pagePathId));

            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Information,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.IsAny<Exception>(),
                    It.Is<Func<It.IsAnyType, Exception, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }

        [Test]
        public void DeletePagePathByIdAsync_WhenExceptionThrown_PropagatesException()
        {
            // Arrange
            var pagePathId = Guid.NewGuid();
            var expectedException = new HttpRequestException("Test exception");

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ThrowsAsync(expectedException);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _pagePathService.DeletePagePathByIdAsync(pagePathId));

            Assert.That(exception.Message, Is.EqualTo(expectedException.Message));
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.Is<Exception>(ex => ex.Message == expectedException.Message),
                    It.Is<Func<It.IsAnyType, Exception, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }
    }
}



