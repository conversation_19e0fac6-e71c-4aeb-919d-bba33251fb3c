using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Moq;
using Moq.Protected;
using NUnit.Framework;
using TeyaUIModels.Model;
using TeyaWebApp.Services;
using TeyaUIViewModels.ViewModel;
using System.Net.Http.Json;
using System.Text.Json;

namespace TeyaWebClientUIViewModelTestCases
{
    [TestFixture]
    public class UserLicenseServiceTests
    {
        private Mock<HttpMessageHandler> _mockHttpMessageHandler;
        private HttpClient _httpClient;
        private Mock<IStringLocalizer<UserLicenseService>> _mockLocalizer;
        private Mock<ILogger<UserLicenseService>> _mockLogger;
        private Mock<ITokenService> _mockTokenService;
        private UserLicenseService _userLicenseService;
        private readonly string _baseUrl = "http://test-api.com";
        private readonly string _accessToken = "test-access-token";

        [SetUp]
        public void Setup()
        {
            // Setup environment variable
            Environment.SetEnvironmentVariable("MemberServiceURL", _baseUrl);

            // Setup mock HTTP handler
            _mockHttpMessageHandler = new Mock<HttpMessageHandler>();

            // Setup HttpClient with mock handler
            _httpClient = new HttpClient(_mockHttpMessageHandler.Object)
            {
                BaseAddress = new Uri(_baseUrl)
            };

            // Setup mock localizer
            _mockLocalizer = new Mock<IStringLocalizer<UserLicenseService>>();
            _mockLocalizer.Setup(l => l["ErrorFetchingAllUserLicenses"])
                .Returns(new LocalizedString("ErrorFetchingAllUserLicenses", "Error fetching all user licenses"));
            _mockLocalizer.Setup(l => l["ErrorFetchingUserLicenseById"])
                .Returns(new LocalizedString("ErrorFetchingUserLicenseById", "Error fetching user license by ID"));
            _mockLocalizer.Setup(l => l["ErrorFetchingUserLicenseByOrganizationId"])
                .Returns(new LocalizedString("ErrorFetchingUserLicenseByOrganizationId", "Error fetching user license by organization ID"));
            _mockLocalizer.Setup(l => l["ErrorAddingUserLicense"])
                .Returns(new LocalizedString("ErrorAddingUserLicense", "Error adding user license"));
            _mockLocalizer.Setup(l => l["ErrorUpdatingUserLicense"])
                .Returns(new LocalizedString("ErrorUpdatingUserLicense", "Error updating user license"));
            _mockLocalizer.Setup(l => l["ErrorDeletingUserLicense"])
                .Returns(new LocalizedString("ErrorDeletingUserLicense", "Error deleting user license"));

            // Setup mock logger
            _mockLogger = new Mock<ILogger<UserLicenseService>>();

            // Setup mock token service
            _mockTokenService = new Mock<ITokenService>();
            _mockTokenService.Setup(t => t.AccessToken).Returns(_accessToken);
            _mockTokenService.Setup(t => t.GetValidatedAccessTokenAsync()).ReturnsAsync(_accessToken);

            // Create UserLicenseService with mocked dependencies
            _userLicenseService = new UserLicenseService(_httpClient, _mockLocalizer.Object, _mockLogger.Object, _mockTokenService.Object);
        }

        [TearDown]
        public void TearDown()
        {
            _httpClient.Dispose();
            Environment.SetEnvironmentVariable("MemberServiceURL", null);
        }

        [Test]
        public async Task GetAllUserLicensesAsync_WhenSuccessful_ReturnsUserLicenses()
        {
            // Arrange
            var expectedLicenses = new List<UserLicense>
            {
                new UserLicense
                {
                    Id = Guid.NewGuid(),
                    PlanId = Guid.NewGuid(),
                    OrganizationId = Guid.NewGuid(),
                    ProductId = Guid.NewGuid(),
                    Seats = 100,
                    ActiveUsers = 45,
                    ExpiryDate = DateTime.Now.AddYears(1),
                    Status = true,
                    CreatedDate = DateTime.Now.AddDays(-30),
                    CreatedBy = Guid.NewGuid(),
                    UpdatedDate = DateTime.Now.AddDays(-25),
                    UpdatedBy = Guid.NewGuid()
                },
                new UserLicense
                {
                    Id = Guid.NewGuid(),
                    PlanId = Guid.NewGuid(),
                    OrganizationId = Guid.NewGuid(),
                    ProductId = Guid.NewGuid(),
                    Seats = 50,
                    ActiveUsers = 25,
                    ExpiryDate = DateTime.Now.AddMonths(6),
                    Status = true,
                    CreatedDate = DateTime.Now.AddDays(-15),
                    CreatedBy = Guid.NewGuid(),
                    UpdatedDate = DateTime.Now.AddDays(-10),
                    UpdatedBy = Guid.NewGuid()
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedLicenses)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _userLicenseService.GetAllUserLicensesAsync();

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count(), Is.EqualTo(expectedLicenses.Count));
            var resultList = result.ToList();
            Assert.That(resultList[0].Id, Is.EqualTo(expectedLicenses[0].Id));
            Assert.That(resultList[0].OrganizationId, Is.EqualTo(expectedLicenses[0].OrganizationId));
            Assert.That(resultList[0].PlanId, Is.EqualTo(expectedLicenses[0].PlanId));
            Assert.That(resultList[0].Seats, Is.EqualTo(expectedLicenses[0].Seats));
            Assert.That(resultList[0].ActiveUsers, Is.EqualTo(expectedLicenses[0].ActiveUsers));
            Assert.That(resultList[0].ExpiryDate, Is.EqualTo(expectedLicenses[0].ExpiryDate));
            Assert.That(resultList[0].Status, Is.EqualTo(expectedLicenses[0].Status));
            Assert.That(resultList[0].CreatedBy, Is.EqualTo(expectedLicenses[0].CreatedBy));
        }

        [Test]
        public void GetAllUserLicensesAsync_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.InternalServerError,
                Content = new StringContent("Internal server error")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _userLicenseService.GetAllUserLicensesAsync());

            Assert.That(exception, Is.Not.Null);

            // Verify error was logged
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.IsAny<Exception>(),
                    It.Is<Func<It.IsAnyType, Exception, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }

        [Test]
        public async Task GetUserLicenseByIdAsync_WhenSuccessful_ReturnsUserLicense()
        {
            // Arrange
            var licenseId = Guid.NewGuid();
            var expectedLicense = new UserLicense
            {
                Id = licenseId,
                PlanId = Guid.NewGuid(),
                OrganizationId = Guid.NewGuid(),
                ProductId = Guid.NewGuid(),
                Seats = 500,
                ActiveUsers = 250,
                ExpiryDate = DateTime.Now.AddYears(2),
                Status = true,
                CreatedDate = DateTime.Now.AddDays(-60),
                CreatedBy = Guid.NewGuid(),
                UpdatedDate = DateTime.Now.AddDays(-55),
                UpdatedBy = Guid.NewGuid()
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedLicense)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _userLicenseService.GetUserLicenseByIdAsync(licenseId);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Id, Is.EqualTo(expectedLicense.Id));
            Assert.That(result.OrganizationId, Is.EqualTo(expectedLicense.OrganizationId));
            Assert.That(result.PlanId, Is.EqualTo(expectedLicense.PlanId));
            Assert.That(result.Seats, Is.EqualTo(expectedLicense.Seats));
            Assert.That(result.ActiveUsers, Is.EqualTo(expectedLicense.ActiveUsers));
            Assert.That(result.ExpiryDate, Is.EqualTo(expectedLicense.ExpiryDate));
            Assert.That(result.Status, Is.EqualTo(expectedLicense.Status));
            Assert.That(result.CreatedBy, Is.EqualTo(expectedLicense.CreatedBy));
        }

        [Test]
        public void GetUserLicenseByIdAsync_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var licenseId = Guid.NewGuid();

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.NotFound,
                Content = new StringContent("Not found")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _userLicenseService.GetUserLicenseByIdAsync(licenseId));

            Assert.That(exception, Is.Not.Null);

            // Verify error was logged
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.IsAny<Exception>(),
                    It.Is<Func<It.IsAnyType, Exception, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }

        [Test]
        public async Task GetUserLicenseByOrganizationIdAsync_WhenSuccessful_ReturnsUserLicense()
        {
            // Arrange
            var organizationId = Guid.NewGuid();
            var expectedLicense = new UserLicense
            {
                Id = Guid.NewGuid(),
                PlanId = Guid.NewGuid(),
                OrganizationId = organizationId,
                ProductId = Guid.NewGuid(),
                Seats = 200,
                ActiveUsers = 150,
                ExpiryDate = DateTime.Now.AddMonths(8),
                Status = true,
                CreatedDate = DateTime.Now.AddDays(-45),
                CreatedBy = Guid.NewGuid(),
                UpdatedDate = DateTime.Now.AddDays(-40),
                UpdatedBy = Guid.NewGuid()
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedLicense)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _userLicenseService.GetUserLicenseByOrganizationIdAsync(organizationId);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Id, Is.EqualTo(expectedLicense.Id));
            Assert.That(result.OrganizationId, Is.EqualTo(expectedLicense.OrganizationId));
            Assert.That(result.PlanId, Is.EqualTo(expectedLicense.PlanId));
            Assert.That(result.Seats, Is.EqualTo(expectedLicense.Seats));
            Assert.That(result.ActiveUsers, Is.EqualTo(expectedLicense.ActiveUsers));
            Assert.That(result.ExpiryDate, Is.EqualTo(expectedLicense.ExpiryDate));
            Assert.That(result.Status, Is.EqualTo(expectedLicense.Status));
            Assert.That(result.CreatedBy, Is.EqualTo(expectedLicense.CreatedBy));
        }

        [Test]
        public void GetUserLicenseByOrganizationIdAsync_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var organizationId = Guid.NewGuid();

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _userLicenseService.GetUserLicenseByOrganizationIdAsync(organizationId));

            Assert.That(exception, Is.Not.Null);

            // Verify error was logged
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.IsAny<Exception>(),
                    It.Is<Func<It.IsAnyType, Exception, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }

        [Test]
        public async Task AddUserLicenseAsync_WhenSuccessful_CompletesSuccessfully()
        {
            // Arrange
            var license = new UserLicense
            {
                Id = Guid.NewGuid(),
                PlanId = Guid.NewGuid(),
                OrganizationId = Guid.NewGuid(),
                ProductId = Guid.NewGuid(),
                Seats = 75,
                ActiveUsers = 0,
                ExpiryDate = DateTime.Now.AddYears(1),
                Status = true,
                CreatedDate = DateTime.Now,
                CreatedBy = Guid.NewGuid(),
                UpdatedDate = DateTime.Now,
                UpdatedBy = Guid.NewGuid()
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.Created,
                Content = new StringContent("Success")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.DoesNotThrowAsync(async () => await _userLicenseService.AddUserLicenseAsync(license));
        }

        [Test]
        public void AddUserLicenseAsync_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var license = new UserLicense
            {
                Id = Guid.NewGuid(),
                PlanId = Guid.NewGuid(),
                OrganizationId = Guid.NewGuid(),
                ProductId = Guid.NewGuid(),
                Seats = 75,
                ActiveUsers = 0,
                ExpiryDate = DateTime.Now.AddYears(1),
                Status = true,
                CreatedDate = DateTime.Now,
                CreatedBy = Guid.NewGuid(),
                UpdatedDate = DateTime.Now,
                UpdatedBy = Guid.NewGuid()
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _userLicenseService.AddUserLicenseAsync(license));

            Assert.That(exception, Is.Not.Null);

            // Verify error was logged
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.IsAny<Exception>(),
                    It.Is<Func<It.IsAnyType, Exception, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }

        [Test]
        public async Task UpdateUserLicenseAsync_WhenSuccessful_CompletesSuccessfully()
        {
            // Arrange
            var license = new UserLicense
            {
                Id = Guid.NewGuid(),
                PlanId = Guid.NewGuid(),
                OrganizationId = Guid.NewGuid(),
                ProductId = Guid.NewGuid(),
                Seats = 150,
                ActiveUsers = 75,
                ExpiryDate = DateTime.Now.AddYears(2),
                Status = true,
                CreatedDate = DateTime.Now.AddDays(-30),
                CreatedBy = Guid.NewGuid(),
                UpdatedDate = DateTime.Now.AddDays(-25),
                UpdatedBy = Guid.NewGuid()
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent("Success")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.DoesNotThrowAsync(async () => await _userLicenseService.UpdateUserLicenseAsync(license));
        }

        [Test]
        public void UpdateUserLicenseAsync_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var license = new UserLicense
            {
                Id = Guid.NewGuid(),
                PlanId = Guid.NewGuid(),
                OrganizationId = Guid.NewGuid(),
                ProductId = Guid.NewGuid(),
                Seats = 150,
                ActiveUsers = 75,
                ExpiryDate = DateTime.Now.AddYears(2),
                Status = true,
                CreatedDate = DateTime.Now.AddDays(-30),
                CreatedBy = Guid.NewGuid(),
                UpdatedDate = DateTime.Now.AddDays(-25),
                UpdatedBy = Guid.NewGuid()
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _userLicenseService.UpdateUserLicenseAsync(license));

            Assert.That(exception, Is.Not.Null);

            // Verify error was logged
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.IsAny<Exception>(),
                    It.Is<Func<It.IsAnyType, Exception, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }

        [Test]
        public async Task DeleteUserLicenseByIdAsync_WhenSuccessful_CompletesSuccessfully()
        {
            // Arrange
            var licenseId = Guid.NewGuid();

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent("Success")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.DoesNotThrowAsync(async () => await _userLicenseService.DeleteUserLicenseByIdAsync(licenseId));
        }

        [Test]
        public void DeleteUserLicenseByIdAsync_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var licenseId = Guid.NewGuid();

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.NotFound,
                Content = new StringContent("Not found")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _userLicenseService.DeleteUserLicenseByIdAsync(licenseId));

            Assert.That(exception, Is.Not.Null);

            // Verify error was logged
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.IsAny<Exception>(),
                    It.Is<Func<It.IsAnyType, Exception, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }

        [Test]
        public async Task IncrementActiveUsersAsync_WhenSuccessful_CompletesSuccessfully()
        {
            // Arrange
            var organizationId = Guid.NewGuid();

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent("Success")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.DoesNotThrowAsync(async () => await _userLicenseService.IncrementActiveUsersAsync(organizationId));
        }

        [Test]
        public async Task ResetActiveUsersAsync_WhenSuccessful_CompletesSuccessfully()
        {
            // Arrange
            var organizationId = Guid.NewGuid();

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent("Success")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.DoesNotThrowAsync(async () => await _userLicenseService.ResetActiveUsersAsync(organizationId));
        }

        [Test]
        public async Task SetLicenseStatusAsync_WhenSuccessful_CompletesSuccessfully()
        {
            // Arrange
            var organizationId = Guid.NewGuid();
            var status = false;

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent("Success")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.DoesNotThrowAsync(async () => await _userLicenseService.SetLicenseStatusAsync(organizationId, status));
        }

        [Test]
        public async Task CheckLicenseExpiryAsync_WhenSuccessful_ReturnsExpiryStatus()
        {
            // Arrange
            var organizationId = Guid.NewGuid();
            var expectedResult = true;
            var licenseExpiryResponse = new { Expired = expectedResult, Message = "License expired" };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(licenseExpiryResponse)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _userLicenseService.CheckLicenseExpiryAsync(organizationId);

            // Assert
            Assert.That(result, Is.EqualTo(expectedResult));
        }
    }
}



