using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Moq;
using Moq.Protected;
using NUnit.Framework;
using TeyaWebApp.Model;
using TeyaWebApp.ViewModel;
using System.Net.Http.Json;
using System.Text.Json;
using System.Linq;

namespace TeyaWebClientUIViewModelTestCases
{
    [TestFixture]
    public class UserTypeServiceTests
    {
        private Mock<HttpMessageHandler> _mockHttpMessageHandler;
        private HttpClient _httpClient;
        private UserTypeService _userTypeService;
        private readonly string _baseUrl = "http://test-api.com";

        [SetUp]
        public void Setup()
        {
            // Setup environment variable
            Environment.SetEnvironmentVariable("MemberServiceURL", _baseUrl);

            // Setup mock HTTP handler
            _mockHttpMessageHandler = new Mock<HttpMessageHandler>();

            // Setup HttpClient with mock handler
            _httpClient = new HttpClient(_mockHttpMessageHandler.Object)
            {
                BaseAddress = new Uri(_baseUrl)
            };

            // Create UserTypeService with mocked dependencies
            _userTypeService = new UserTypeService(_httpClient);
        }

        [TearDown]
        public void TearDown()
        {
            _httpClient.Dispose();
            Environment.SetEnvironmentVariable("MemberServiceURL", null);
        }

        [Test]
        public async Task GetUserTypeByIdAsync_WhenSuccessful_ReturnsUserType()
        {
            // Arrange
            var userTypeId = Guid.NewGuid();
            var expectedUserType = new UserType
            {
                Id = userTypeId,
                Username = "testuser",
                Password = "testpassword",
                FirstName = "John",
                MiddleName = "M",
                LastName = "Doe",
                Suffix = "MD",
                FederalTaxId = "*********",
                DEANumber = "DEA123456",
                UPIN = "UPIN123",
                NPI = "**********",
                ProviderType = "Physician",
                MainMenuRole = "Doctor",
                PatientMenuRole = "Primary",
                Supervisor = "Jane Smith",
                JobDescription = "Primary Care Physician",
                Taxonomy = "207Q00000X",
                NewCropERxRole = "Prescriber",
                AccessControl = "Full",
                GoogleEmail = "<EMAIL>",
                AdditionalInfo = "Board certified",
                DefaultBillingFacility = "Main Clinic"
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedUserType)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _userTypeService.GetUserTypeByIdAsync(userTypeId);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Id, Is.EqualTo(expectedUserType.Id));
            Assert.That(result.Username, Is.EqualTo(expectedUserType.Username));
            Assert.That(result.FirstName, Is.EqualTo(expectedUserType.FirstName));
            Assert.That(result.LastName, Is.EqualTo(expectedUserType.LastName));
            Assert.That(result.ProviderType, Is.EqualTo(expectedUserType.ProviderType));
            Assert.That(result.MainMenuRole, Is.EqualTo(expectedUserType.MainMenuRole));
            Assert.That(result.NPI, Is.EqualTo(expectedUserType.NPI));
            Assert.That(result.DEANumber, Is.EqualTo(expectedUserType.DEANumber));
        }

        [Test]
        public void GetUserTypeByIdAsync_WhenNotSuccessful_ThrowsException()
        {
            // Arrange
            var userTypeId = Guid.NewGuid();
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.NotFound,
                Content = new StringContent("Not found")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<Exception>(async () =>
                await _userTypeService.GetUserTypeByIdAsync(userTypeId));

            Assert.That(exception, Is.Not.Null);
            Assert.That(exception.Message, Does.Contain("Error retrieving UserType"));
        }

        [Test]
        public async Task AddUserTypeAsync_WhenSuccessful_CompletesSuccessfully()
        {
            // Arrange
            var userType = new UserType
            {
                Id = Guid.NewGuid(),
                Username = "newuser",
                Password = "newpassword",
                FirstName = "Jane",
                LastName = "Smith",
                ProviderType = "Nurse",
                MainMenuRole = "Staff",
                NPI = "**********"
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent("User type added successfully")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.Is<HttpRequestMessage>(req => req.RequestUri.ToString().Contains("/api/UserType")),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.DoesNotThrowAsync(async () => await _userTypeService.AddUserTypeAsync(userType));
        }

        [Test]
        public void AddUserTypeAsync_WhenNotSuccessful_ThrowsException()
        {
            // Arrange
            var userType = new UserType
            {
                Id = Guid.NewGuid(),
                Username = "failuser",
                FirstName = "Failed",
                LastName = "User"
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.Is<HttpRequestMessage>(req => req.RequestUri.ToString().Contains("/api/UserType")),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<Exception>(async () =>
                await _userTypeService.AddUserTypeAsync(userType));

            Assert.That(exception, Is.Not.Null);
            Assert.That(exception.Message, Does.Contain("Error adding UserType"));
        }

        [Test]
        public async Task UpdateUserTypeAsync_WhenSuccessful_CompletesSuccessfully()
        {
            // Arrange
            var userTypeId = Guid.NewGuid();
            var userType = new UserType
            {
                Id = userTypeId,
                Username = "updateduser",
                FirstName = "Updated",
                LastName = "User",
                ProviderType = "Updated Provider",
                MainMenuRole = "Updated Role"
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent("User type updated successfully")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.DoesNotThrowAsync(async () => await _userTypeService.UpdateUserTypeAsync(userTypeId, userType));
        }

        [Test]
        public void UpdateUserTypeAsync_WhenNotSuccessful_ThrowsException()
        {
            // Arrange
            var userTypeId = Guid.NewGuid();
            var userType = new UserType
            {
                Id = userTypeId,
                Username = "faileduser",
                FirstName = "Failed",
                LastName = "Update"
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<Exception>(async () =>
                await _userTypeService.UpdateUserTypeAsync(userTypeId, userType));

            Assert.That(exception, Is.Not.Null);
            Assert.That(exception.Message, Does.Contain("Error updating UserType"));
        }

        [Test]
        public async Task DeleteUserTypeAsync_WhenSuccessful_CompletesSuccessfully()
        {
            // Arrange
            var userTypeId = Guid.NewGuid();

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent("User type deleted successfully")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.DoesNotThrowAsync(async () => await _userTypeService.DeleteUserTypeAsync(userTypeId));
        }

        [Test]
        public void DeleteUserTypeAsync_WhenNotSuccessful_ThrowsException()
        {
            // Arrange
            var userTypeId = Guid.NewGuid();

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.NotFound,
                Content = new StringContent("Not found")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<Exception>(async () =>
                await _userTypeService.DeleteUserTypeAsync(userTypeId));

            Assert.That(exception, Is.Not.Null);
            Assert.That(exception.Message, Does.Contain("Error deleting UserType"));
        }
    }
}



