using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Moq;
using Moq.Protected;
using NUnit.Framework;
using TeyaUIModels.Model;
using TeyaUIViewModels.ViewModels;
using TeyaUIViewModels.ViewModel;
using System.Net.Http.Json;
using System.Text.Json;

namespace TeyaWebClientUIViewModelTestCases
{
    [TestFixture]
    public class VisitTypeServiceTests
    {
        private Mock<HttpMessageHandler> _mockHttpMessageHandler;
        private HttpClient _httpClient;
        private Mock<IStringLocalizer<VisitTypeService>> _mockLocalizer;
        private Mock<ILogger<VisitTypeService>> _mockLogger;
        private Mock<ITokenService> _mockTokenService;
        private VisitTypeService _visitTypeService;
        private readonly string _baseUrl = "http://test-api.com";
        private readonly string _accessToken = "test-access-token";

        [SetUp]
        public void Setup()
        {
            // Setup environment variable
            Environment.SetEnvironmentVariable("MemberServiceURL", _baseUrl);

            // Setup mock HTTP handler
            _mockHttpMessageHandler = new Mock<HttpMessageHandler>();

            // Setup HttpClient with mock handler
            _httpClient = new HttpClient(_mockHttpMessageHandler.Object)
            {
                BaseAddress = new Uri(_baseUrl)
            };

            // Setup mock localizer
            _mockLocalizer = new Mock<IStringLocalizer<VisitTypeService>>();
            _mockLocalizer.Setup(l => l["ErrorFetchingVisitTypes"])
                .Returns(new LocalizedString("ErrorFetchingVisitTypes", "Error fetching visit types"));
            _mockLocalizer.Setup(l => l["ErrorAddingVisitType"])
                .Returns(new LocalizedString("ErrorAddingVisitType", "Error adding visit type"));
            _mockLocalizer.Setup(l => l["ErrorUpdatingCptCode"])
                .Returns(new LocalizedString("ErrorUpdatingCptCode", "Error updating CPT code"));
            _mockLocalizer.Setup(l => l["ErrorDeletingVisitType"])
                .Returns(new LocalizedString("ErrorDeletingVisitType", "Error deleting visit type"));
            _mockLocalizer.Setup(l => l["AccessTokenNotFound"])
                .Returns(new LocalizedString("AccessTokenNotFound", "Access token not found"));
            _mockLocalizer.Setup(l => l["AccessTokenMissing"])
                .Returns(new LocalizedString("AccessTokenMissing", "Access token missing"));

            // Setup mock logger
            _mockLogger = new Mock<ILogger<VisitTypeService>>();

            // Setup mock token service
            _mockTokenService = new Mock<ITokenService>();
            _mockTokenService.Setup(t => t.AccessToken).Returns(_accessToken);
            _mockTokenService.Setup(t => t.GetValidatedAccessTokenAsync()).ReturnsAsync(_accessToken);
            _mockTokenService.Setup(t => t.GetValidatedAccessTokenAsync()).ReturnsAsync(_accessToken);

            // Create VisitTypeService with mocked dependencies
            _visitTypeService = new VisitTypeService(_httpClient, _mockLocalizer.Object, _mockLogger.Object, _mockTokenService.Object);
        }

        [TearDown]
        public void TearDown()
        {
            _httpClient.Dispose();
            Environment.SetEnvironmentVariable("MemberServiceURL", null);
        }

        [Test]
        public async Task GetAllVisitTypesAsync_WhenSuccessful_ReturnsVisitTypes()
        {
            // Arrange
            var expectedVisitTypes = new List<VisitType>
            {
                new VisitType
                {
                    ID = Guid.NewGuid(),
                    VisitName = "Routine Checkup",
                    CPTCode = "99213",
                    OrganizationId = Guid.NewGuid(),
                    IsActive = true,
                    Subscription = true
                },
                new VisitType
                {
                    ID = Guid.NewGuid(),
                    VisitName = "Emergency Visit",
                    CPTCode = "99281",
                    OrganizationId = Guid.NewGuid(),
                    IsActive = true,
                    Subscription = true
                },
                new VisitType
                {
                    ID = Guid.NewGuid(),
                    VisitName = "Consultation",
                    CPTCode = "99242",
                    OrganizationId = Guid.NewGuid(),
                    IsActive = true,
                    Subscription = false
                },
                new VisitType
                {
                    ID = Guid.NewGuid(),
                    VisitName = "Follow-up",
                    CPTCode = "99214",
                    OrganizationId = Guid.NewGuid(),
                    IsActive = true,
                    Subscription = true
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedVisitTypes)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.Is<HttpRequestMessage>(req =>
                        req.Method == HttpMethod.Get &&
                        req.RequestUri.ToString() == $"{_baseUrl}/api/VisitType" &&
                        req.Headers.Authorization.Scheme == "Bearer" &&
                        req.Headers.Authorization.Parameter == _accessToken),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _visitTypeService.GetAllVisitTypesAsync();

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(expectedVisitTypes.Count));
            Assert.That(result[0].ID, Is.EqualTo(expectedVisitTypes[0].ID));
            Assert.That(result[0].VisitName, Is.EqualTo(expectedVisitTypes[0].VisitName));
            Assert.That(result[0].CPTCode, Is.EqualTo(expectedVisitTypes[0].CPTCode));
            Assert.That(result[0].OrganizationId, Is.EqualTo(expectedVisitTypes[0].OrganizationId));
            Assert.That(result[0].IsActive, Is.EqualTo(expectedVisitTypes[0].IsActive));
            Assert.That(result[0].Subscription, Is.EqualTo(expectedVisitTypes[0].Subscription));

            // Verify all visit type names
            Assert.That(result[1].VisitName, Is.EqualTo("Emergency Visit"));
            Assert.That(result[2].VisitName, Is.EqualTo("Consultation"));
            Assert.That(result[3].VisitName, Is.EqualTo("Follow-up"));
        }

        [Test]
        public void GetAllVisitTypesAsync_WhenNotSuccessful_ThrowsException()
        {
            // Arrange
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.InternalServerError,
                Content = new StringContent("Internal server error")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.Is<HttpRequestMessage>(req =>
                        req.Method == HttpMethod.Get &&
                        req.RequestUri.ToString() == $"{_baseUrl}/api/VisitType"),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _visitTypeService.GetAllVisitTypesAsync());

            Assert.That(exception, Is.Not.Null);

            // Verify that error was logged
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.IsAny<Exception>(),
                    It.Is<Func<It.IsAnyType, Exception?, string>>((v, t) => true)),
                Times.Once);
        }

        [Test]
        public async Task GetVisitTypesByOrganizationIdAsync_WhenSuccessful_ReturnsVisitTypes()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;
            var expectedVisitTypes = new List<VisitType>
            {
                new VisitType
                {
                    ID = Guid.NewGuid(),
                    VisitName = "Organization Visit Type 1",
                    CPTCode = "99213",
                    OrganizationId = orgId,
                    IsActive = true,
                    Subscription = subscription
                },
                new VisitType
                {
                    ID = Guid.NewGuid(),
                    VisitName = "Organization Visit Type 2",
                    CPTCode = "99214",
                    OrganizationId = orgId,
                    IsActive = true,
                    Subscription = subscription
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedVisitTypes)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.Is<HttpRequestMessage>(req =>
                        req.Method == HttpMethod.Get &&
                        req.RequestUri.ToString() == $"{_baseUrl}/api/VisitType/by-organization/{orgId}/{subscription}" &&
                        req.Headers.Authorization.Scheme == "Bearer" &&
                        req.Headers.Authorization.Parameter == _accessToken),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _visitTypeService.GetVisitTypesByOrganizationIdAsync(orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(expectedVisitTypes.Count));
            Assert.That(result[0].OrganizationId, Is.EqualTo(orgId));
            Assert.That(result[1].OrganizationId, Is.EqualTo(orgId));
            Assert.That(result[0].VisitName, Is.EqualTo("Organization Visit Type 1"));
            Assert.That(result[1].VisitName, Is.EqualTo("Organization Visit Type 2"));
        }

        [Test]
        public async Task AddVisitTypeAsync_WhenSuccessful_ReturnsTrue()
        {
            // Arrange
            var visitType = new VisitType
            {
                ID = Guid.NewGuid(),
                VisitName = "New Visit Type",
                CPTCode = "99999",
                OrganizationId = Guid.NewGuid(),
                IsActive = true,
                Subscription = true
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent("Success")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.Is<HttpRequestMessage>(req =>
                        req.Method == HttpMethod.Post &&
                        req.RequestUri.ToString() == $"{_baseUrl}/api/VisitType" &&
                        req.Headers.Authorization.Scheme == "Bearer" &&
                        req.Headers.Authorization.Parameter == _accessToken),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _visitTypeService.AddVisitTypeAsync(visitType);

            // Assert
            Assert.That(result, Is.True);
        }

        [Test]
        public void AddVisitTypeAsync_WhenNotSuccessful_ThrowsException()
        {
            // Arrange
            var visitType = new VisitType
            {
                ID = Guid.NewGuid(),
                VisitName = "Failed Visit Type",
                CPTCode = "FAIL",
                OrganizationId = Guid.NewGuid(),
                IsActive = true,
                Subscription = true
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.Is<HttpRequestMessage>(req =>
                        req.Method == HttpMethod.Post &&
                        req.RequestUri.ToString() == $"{_baseUrl}/api/VisitType"),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _visitTypeService.AddVisitTypeAsync(visitType));

            Assert.That(exception, Is.Not.Null);

            // Verify that error was logged
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.IsAny<Exception>(),
                    It.Is<Func<It.IsAnyType, Exception?, string>>((v, t) => true)),
                Times.Once);
        }

        [Test]
        public async Task UpdateCptCodeAsync_WhenSuccessful_ReturnsTrue()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var visitName = "Test Visit";
            var newCptCode = "99999";
            var subscription = true;

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent("Success")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.Is<HttpRequestMessage>(req =>
                        req.Method == HttpMethod.Put &&
                        req.RequestUri.ToString().Contains($"/api/VisitType/update-cpt-code/{orgId}") &&
                        req.Headers.Authorization.Scheme == "Bearer" &&
                        req.Headers.Authorization.Parameter == _accessToken),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _visitTypeService.UpdateCptCodeAsync(orgId, visitName, newCptCode, subscription);

            // Assert
            Assert.That(result, Is.True);
        }

        [Test]
        public async Task DeleteVisitTypeAsync_WhenSuccessful_ReturnsTrue()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var visitName = "Test Visit";
            var subscription = true;

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent("Success")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.Is<HttpRequestMessage>(req =>
                        req.Method == HttpMethod.Delete &&
                        req.RequestUri.ToString().Contains($"/api/VisitType/delete/{orgId}") &&
                        req.Headers.Authorization.Scheme == "Bearer" &&
                        req.Headers.Authorization.Parameter == _accessToken),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _visitTypeService.DeleteVisitTypeAsync(orgId, visitName, subscription);

            // Assert
            Assert.That(result, Is.True);
        }

        [Test]
        public void GetAllVisitTypesAsync_WhenAccessTokenIsNull_ThrowsUnauthorizedAccessException()
        {
            // Arrange
            _mockTokenService.Setup(t => t.AccessToken).Returns((string)null);
            _mockTokenService.Setup(t => t.GetValidatedAccessTokenAsync()).ReturnsAsync((string)null);

            // Act & Assert
            var exception = Assert.ThrowsAsync<UnauthorizedAccessException>(async () =>
                await _visitTypeService.GetAllVisitTypesAsync());

            Assert.That(exception.Message, Is.EqualTo("Access token missing"));

            // Verify that error was logged
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.IsAny<Exception>(),
                    It.Is<Func<It.IsAnyType, Exception?, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }

    }
}
