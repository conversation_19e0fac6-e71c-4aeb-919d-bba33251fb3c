using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using Moq;
using Moq.Protected;
using NUnit.Framework;
using TeyaUIModels.Model;
using TeyaWebApp.Services;
using TeyaUIViewModels.ViewModel;
using System.Net.Http.Json;
using System.Text;
using System.Text.Json;

namespace TeyaWebClientUIViewModelTestCases
{
    [TestFixture]
    public class CountryServiceTests
    {
        private Mock<HttpMessageHandler> _mockHttpMessageHandler;
        private HttpClient _httpClient;
        private Mock<IStringLocalizer<countryService>> _mockLocalizer;
        private Mock<ILogger<countryService>> _mockLogger;
        private Mock<ITokenService> _mockTokenService;
        private countryService _countryService;
        private readonly string _baseUrl = "http://test-api.com";
        private readonly string _accessToken = "test-token";

        [SetUp]
        public void Setup()
        {
            // Setup environment variable
            Environment.SetEnvironmentVariable("MemberServiceURL", _baseUrl);

            // Setup mock HTTP handler
            _mockHttpMessageHandler = new Mock<HttpMessageHandler>();

            // Setup HttpClient with mock handler
            _httpClient = new HttpClient(_mockHttpMessageHandler.Object)
            {
                BaseAddress = new Uri(_baseUrl)
            };

            // Setup mock localizer
            _mockLocalizer = new Mock<IStringLocalizer<countryService>>();
            _mockLocalizer.Setup(l => l["ErrorFetchingAllCountries"])
                .Returns(new LocalizedString("ErrorFetchingAllCountries", "Error fetching all countries"));
            _mockLocalizer.Setup(l => l["GetCountryByIdFailed"])
                .Returns(new LocalizedString("GetCountryByIdFailed", "Failed to get country by ID"));
            _mockLocalizer.Setup(l => l["ErrorFetchingCountryById"])
                .Returns(new LocalizedString("ErrorFetchingCountryById", "Error fetching country by ID"));
            _mockLocalizer.Setup(l => l["GetCountriesByNameFailed"])
                .Returns(new LocalizedString("GetCountriesByNameFailed", "Failed to get countries by name"));
            _mockLocalizer.Setup(l => l["ErrorFetchingCountriesByName"])
                .Returns(new LocalizedString("ErrorFetchingCountriesByName", "Error fetching countries by name"));
            _mockLocalizer.Setup(l => l["CountryAddedSuccessfully"])
                .Returns(new LocalizedString("CountryAddedSuccessfully", "Country added successfully"));
            _mockLocalizer.Setup(l => l["AddCountryFailed"])
                .Returns(new LocalizedString("AddCountryFailed", "Failed to add country"));
            _mockLocalizer.Setup(l => l["ErrorAddingCountry"])
                .Returns(new LocalizedString("ErrorAddingCountry", "Error adding country"));
            _mockLocalizer.Setup(l => l["CountryUpdatedSuccessfully"])
                .Returns(new LocalizedString("CountryUpdatedSuccessfully", "Country updated successfully"));
            _mockLocalizer.Setup(l => l["CountryUpdateFailed"])
                .Returns(new LocalizedString("CountryUpdateFailed", "Failed to update country"));
            _mockLocalizer.Setup(l => l["ErrorUpdatingCountry"])
                .Returns(new LocalizedString("ErrorUpdatingCountry", "Error updating country"));
            _mockLocalizer.Setup(l => l["CountryDeletedSuccessfully"])
                .Returns(new LocalizedString("CountryDeletedSuccessfully", "Country deleted successfully"));
            _mockLocalizer.Setup(l => l["CountryDeletionFailed"])
                .Returns(new LocalizedString("CountryDeletionFailed", "Failed to delete country"));
            _mockLocalizer.Setup(l => l["ErrorDeletingCountry"])
                .Returns(new LocalizedString("ErrorDeletingCountry", "Error deleting country"));

            // Setup mock logger
            _mockLogger = new Mock<ILogger<countryService>>();

            // Setup mock token service
            _mockTokenService = new Mock<ITokenService>();
            _mockTokenService.Setup(t => t.AccessToken).Returns(_accessToken);

            // Create CountryService with mocked dependencies
            _countryService = new countryService(_httpClient, _mockLocalizer.Object, _mockLogger.Object, _mockTokenService.Object);
        }

        [TearDown]
        public void TearDown()
        {
            _httpClient.Dispose();
            Environment.SetEnvironmentVariable("MemberServiceURL", null);
        }

        [Test]
        public async Task GetAllCountriesAsync_WhenSuccessful_ReturnsCountries()
        {
            // Arrange
            var expectedCountries = new List<Country>
            {
                new Country
                {
                    CountryId = Guid.NewGuid(),
                    CountryName = "United States"
                },
                new Country
                {
                    CountryId = Guid.NewGuid(),
                    CountryName = "Canada"
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedCountries)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _countryService.GetAllCountriesAsync();

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<IEnumerable<Country>>());
            // Further assertions would compare the result with expectedCountries
        }

        [Test]
        public void GetAllCountriesAsync_WhenHttpRequestFails_ThrowsException()
        {
            // Arrange
            var expectedException = new HttpRequestException("Test exception");

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ThrowsAsync(expectedException);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _countryService.GetAllCountriesAsync());

            Assert.That(exception.Message, Is.EqualTo(expectedException.Message));
        }

        [Test]
        public async Task GetCountryByIdAsync_WhenSuccessful_ReturnsCountry()
        {
            // Arrange
            var countryId = Guid.NewGuid();
            var expectedCountry = new Country
            {
                CountryId = countryId,
                CountryName = "United States"
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedCountry)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _countryService.GetCountryByIdAsync(countryId);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<Country>());
            // Further assertions would compare the result with expectedCountry
        }

        [Test]
        public void GetCountryByIdAsync_WhenHttpRequestFails_ThrowsException()
        {
            // Arrange
            var countryId = Guid.NewGuid();
            var expectedException = new HttpRequestException("Test exception");

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ThrowsAsync(expectedException);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _countryService.GetCountryByIdAsync(countryId));

            Assert.That(exception.Message, Is.EqualTo(expectedException.Message));
        }

        [Test]
        public async Task GetCountriesByNameAsync_WhenSuccessful_ReturnsCountries()
        {
            // Arrange
            var countryName = "United";
            var expectedCountries = new List<Country>
            {
                new Country
                {
                    CountryId = Guid.NewGuid(),
                    CountryName = "United States"
                },
                new Country
                {
                    CountryId = Guid.NewGuid(),
                    CountryName = "United Kingdom"
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedCountries)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _countryService.GetCountriesByNameAsync(countryName);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<IEnumerable<Country>>());
            // Further assertions would compare the result with expectedCountries
        }

        [Test]
        public void GetCountriesByNameAsync_WhenHttpRequestFails_ThrowsException()
        {
            // Arrange
            var countryName = "United";
            var expectedException = new HttpRequestException("Test exception");

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ThrowsAsync(expectedException);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _countryService.GetCountriesByNameAsync(countryName));

            Assert.That(exception.Message, Is.EqualTo(expectedException.Message));
        }

        [Test]
        public async Task AddCountryAsync_WhenSuccessful_CompletesWithoutException()
        {
            // Arrange
            var country = new Country
            {
                CountryId = Guid.NewGuid(),
                CountryName = "New Country"
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.Created
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            // No exception should be thrown
            await _countryService.AddCountryAsync(country);
        }

        [Test]
        public void AddCountryAsync_WhenHttpRequestFails_ThrowsException()
        {
            // Arrange
            var country = new Country
            {
                CountryId = Guid.NewGuid(),
                CountryName = "New Country"
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _countryService.AddCountryAsync(country));

            Assert.That(exception.Message, Is.EqualTo("Failed to add country"));
        }

        [Test]
        public async Task UpdateCountryAsync_WhenSuccessful_CompletesWithoutException()
        {
            // Arrange
            var country = new Country
            {
                CountryId = Guid.NewGuid(),
                CountryName = "Updated Country"
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            // No exception should be thrown
            await _countryService.UpdateCountryAsync(country);
        }

        [Test]
        public void UpdateCountryAsync_WhenHttpRequestFails_ThrowsException()
        {
            // Arrange
            var country = new Country
            {
                CountryId = Guid.NewGuid(),
                CountryName = "Updated Country"
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _countryService.UpdateCountryAsync(country));

            Assert.That(exception.Message, Is.EqualTo("Failed to update country"));
        }

        [Test]
        public async Task DeleteCountryByIdAsync_WhenSuccessful_CompletesWithoutException()
        {
            // Arrange
            var countryId = Guid.NewGuid();
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            // No exception should be thrown
            await _countryService.DeleteCountryByIdAsync(countryId);
        }

        [Test]
        public void DeleteCountryByIdAsync_WhenHttpRequestFails_ThrowsException()
        {
            // Arrange
            var countryId = Guid.NewGuid();
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _countryService.DeleteCountryByIdAsync(countryId));

            Assert.That(exception.Message, Is.EqualTo("Failed to delete country"));
        }
    }
}



