﻿@inherits LayoutComponentBase
@using Microsoft.AspNetCore.Components.Web
@inject IStringLocalizer<TeyaAIScribeResource> Localizer

<MudThemeProvider />
<MudPopoverProvider />
<MudDialogProvider />
<MudSnackbarProvider />
<div class="page">
    <div class="sidebar">
        <NavMenu />
    </div>
    <main>
        <article class="content px-4">
            <ErrorBoundary @ref="errorBoundary">
                <ChildContent>
                    @Body
                </ChildContent>
                <ErrorContent Context="exception">
                    <div class="error-container">
                        <h1>@Localizer["Something went wrong"]</h1>
                        <p>
                            @Localizer["Sorry, an error occurred while rendering this page."]</p>
                        <div class="error-details">
                            <h3>
                                @Localizer["Error Details:"]</h3>
                            <p>@exception.Message</p>
                            <button class="btn btn-primary" @onclick="RecoverFromError">@Localizer["Try Again"]</button>
                        </div>
                    </div>
                </ErrorContent>
            </ErrorBoundary>
        </article>
    </main>
</div>

@code {
    private ErrorBoundary? errorBoundary;

    protected override void OnParametersSet()
    {
        errorBoundary?.Recover();
    }

    private void RecoverFromError()
    {
        errorBoundary?.Recover();
    }
}