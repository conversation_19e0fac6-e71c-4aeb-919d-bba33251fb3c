using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using Moq;
using Moq.Protected;
using NUnit.Framework;
using TeyaUIModels.Model;
using TeyaWebApp.Services;
using System.Net.Http.Json;
using System.Text.Json;
using TeyaUIViewModels.ViewModel;

namespace TeyaWebClientUIViewModelTestCases
{
    [TestFixture]
    public class BillingServiceTests
    {
        private Mock<HttpMessageHandler> _mockHttpMessageHandler;
        private HttpClient _httpClient;
        private Mock<ILogger<BillingService>> _mockLogger;
        private Mock<IStringLocalizer<BillingService>> _mockLocalizer;
        private Mock<ITokenService> _mockTokenService;
        private BillingService _billingService;
        private readonly string _baseUrl = "http://test-api.com";
        private readonly string _accessToken = "test-access-token";

        [SetUp]
        public void Setup()
        {
            // Setup environment variable
            Environment.SetEnvironmentVariable("EncounterNotesURL", _baseUrl);

            // Setup mock HTTP handler
            _mockHttpMessageHandler = new Mock<HttpMessageHandler>();

            // Setup HttpClient with mock handler
            _httpClient = new HttpClient(_mockHttpMessageHandler.Object)
            {
                BaseAddress = new Uri(_baseUrl)
            };

            // Setup mock logger
            _mockLogger = new Mock<ILogger<BillingService>>();

            // Setup mock localizer
            _mockLocalizer = new Mock<IStringLocalizer<BillingService>>();
            _mockLocalizer.Setup(l => l["ErrorFetchingAllBillings"])
                .Returns(new LocalizedString("ErrorFetchingAllBillings", "Error fetching all billings"));
            _mockLocalizer.Setup(l => l["ErrorFetchingBillingById"])
                .Returns(new LocalizedString("ErrorFetchingBillingById", "Error fetching billing by ID"));
            _mockLocalizer.Setup(l => l["GetBillingByPatientIdFailed"])
                .Returns(new LocalizedString("GetBillingByPatientIdFailed", "Failed to get billing by patient ID"));
            _mockLocalizer.Setup(l => l["ErrorFetchingBillingByPatientId"])
                .Returns(new LocalizedString("ErrorFetchingBillingByPatientId", "Error fetching billing by patient ID"));
            _mockLocalizer.Setup(l => l["BillingAddedSuccessfully"])
                .Returns(new LocalizedString("BillingAddedSuccessfully", "Billing added successfully"));
            _mockLocalizer.Setup(l => l["BillingAddFailed"])
                .Returns(new LocalizedString("BillingAddFailed", "Failed to add billing"));
            _mockLocalizer.Setup(l => l["ErrorAddingBilling"])
                .Returns(new LocalizedString("ErrorAddingBilling", "Error adding billing"));
            _mockLocalizer.Setup(l => l["BillingUpdatedSuccessfully"])
                .Returns(new LocalizedString("BillingUpdatedSuccessfully", "Billing updated successfully"));
            _mockLocalizer.Setup(l => l["BillingUpdateFailed"])
                .Returns(new LocalizedString("BillingUpdateFailed", "Failed to update billing"));
            _mockLocalizer.Setup(l => l["ErrorUpdatingBilling"])
                .Returns(new LocalizedString("ErrorUpdatingBilling", "Error updating billing"));
            _mockLocalizer.Setup(l => l["BillingDeletedSuccessfully"])
                .Returns(new LocalizedString("BillingDeletedSuccessfully", "Billing deleted successfully"));
            _mockLocalizer.Setup(l => l["BillingDeletionFailed"])
                .Returns(new LocalizedString("BillingDeletionFailed", "Failed to delete billing"));
            _mockLocalizer.Setup(l => l["ErrorDeletingBilling"])
                .Returns(new LocalizedString("ErrorDeletingBilling", "Error deleting billing"));

            // Setup mock token service
            _mockTokenService = new Mock<ITokenService>();
            _mockTokenService.Setup(t => t.AccessToken).Returns(_accessToken);

            // Create BillingService with mocked dependencies
            _billingService = new BillingService(_httpClient, _mockLocalizer.Object, _mockLogger.Object, _mockTokenService.Object);
        }

        [TearDown]
        public void TearDown()
        {
            _httpClient.Dispose();
            Environment.SetEnvironmentVariable("EncounterNotesURL", null);
        }

        [Test]
        public async Task GetAllBillingsAsync_WhenSuccessful_ReturnsBillings()
        {
            // Arrange
            var expectedBillings = new List<Billing>
            {
                new Billing
                {
                    BillingId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    VisitTypeId = Guid.NewGuid(),
                    AssessmentId = Guid.NewGuid(),
                    ProcedureCodeId = Guid.NewGuid(),
                    EMCodeId = Guid.NewGuid(),
                    BillingNotes = "Test billing 1",
                    Amount = 100.50m,
                    CreatedDate = DateTime.UtcNow
                },
                new Billing
                {
                    BillingId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    VisitTypeId = Guid.NewGuid(),
                    AssessmentId = Guid.NewGuid(),
                    ProcedureCodeId = Guid.NewGuid(),
                    EMCodeId = Guid.NewGuid(),
                    BillingNotes = "Test billing 2",
                    Amount = 200.75m,
                    CreatedDate = DateTime.UtcNow
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(JsonSerializer.Serialize(expectedBillings))
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _billingService.GetAllBillingsAsync();

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count(), Is.EqualTo(2));
        }

        [Test]
        public void GetAllBillingsAsync_WhenHttpRequestFails_ThrowsException()
        {
            // Arrange
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _billingService.GetAllBillingsAsync());

            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.IsAny<Exception>(),
                    It.Is<Func<It.IsAnyType, Exception, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }

        [Test]
        public async Task GetBillingByIdAsync_WhenSuccessful_ReturnsBilling()
        {
            // Arrange
            var billingId = Guid.NewGuid();
            var expectedBilling = new Billing
            {
                BillingId = billingId,
                PatientId = Guid.NewGuid(),
                VisitTypeId = Guid.NewGuid(),
                AssessmentId = Guid.NewGuid(),
                ProcedureCodeId = Guid.NewGuid(),
                EMCodeId = Guid.NewGuid(),
                BillingNotes = "Test billing",
                Amount = 150.25m,
                CreatedDate = DateTime.UtcNow
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(JsonSerializer.Serialize(expectedBilling))
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _billingService.GetBillingByIdAsync(billingId);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.BillingId, Is.EqualTo(expectedBilling.BillingId));
            Assert.That(result.PatientId, Is.EqualTo(expectedBilling.PatientId));
            Assert.That(result.BillingNotes, Is.EqualTo(expectedBilling.BillingNotes));
            Assert.That(result.Amount, Is.EqualTo(expectedBilling.Amount));
        }

        [Test]
        public void GetBillingByIdAsync_WhenNotFound_ThrowsException()
        {
            // Arrange
            var billingId = Guid.NewGuid();

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.NotFound,
                Content = new StringContent("Not found")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _billingService.GetBillingByIdAsync(billingId));

            // Verify that both error logs are called (HTTP failure + catch block)
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.IsAny<Exception>(),
                    It.Is<Func<It.IsAnyType, Exception?, string>>((v, t) => true)),
                Times.Exactly(2));
        }

        [Test]
        public void GetBillingByIdAsync_WhenExceptionThrown_PropagatesException()
        {
            // Arrange
            var billingId = Guid.NewGuid();
            var expectedException = new HttpRequestException("Test exception");

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ThrowsAsync(expectedException);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _billingService.GetBillingByIdAsync(billingId));

            Assert.That(exception.Message, Is.EqualTo(expectedException.Message));
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.Is<Exception>(ex => ex.Message == expectedException.Message),
                    It.Is<Func<It.IsAnyType, Exception?, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }

        [Test]
        public async Task GetBillingsByPatientIdAsync_WhenSuccessful_ReturnsBillings()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var expectedBillings = new List<Billing>
            {
                new Billing
                {
                    BillingId = Guid.NewGuid(),
                    PatientId = patientId,
                    VisitTypeId = Guid.NewGuid(),
                    AssessmentId = Guid.NewGuid(),
                    ProcedureCodeId = Guid.NewGuid(),
                    EMCodeId = Guid.NewGuid(),
                    BillingNotes = "Patient billing 1",
                    Amount = 100.00m,
                    CreatedDate = DateTime.UtcNow
                },
                new Billing
                {
                    BillingId = Guid.NewGuid(),
                    PatientId = patientId,
                    VisitTypeId = Guid.NewGuid(),
                    AssessmentId = Guid.NewGuid(),
                    ProcedureCodeId = Guid.NewGuid(),
                    EMCodeId = Guid.NewGuid(),
                    BillingNotes = "Patient billing 2",
                    Amount = 200.00m,
                    CreatedDate = DateTime.UtcNow
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(JsonSerializer.Serialize(expectedBillings))
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _billingService.GetBillingsByPatientIdAsync(patientId);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count(), Is.EqualTo(2));
            Assert.That(result.All(b => b.PatientId == patientId), Is.True);
        }

        [Test]
        public void GetBillingsByPatientIdAsync_WhenHttpRequestFails_ThrowsException()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _billingService.GetBillingsByPatientIdAsync(patientId));

            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.IsAny<Exception>(),
                    It.Is<Func<It.IsAnyType, Exception?, string>>((v, t) => true)),
                Times.Exactly(2));
        }

        [Test]
        public async Task AddBillingAsync_WhenSuccessful_CompletesWithoutException()
        {
            // Arrange
            var billing = new Billing
            {
                BillingId = Guid.NewGuid(),
                PatientId = Guid.NewGuid(),
                VisitTypeId = Guid.NewGuid(),
                AssessmentId = Guid.NewGuid(),
                ProcedureCodeId = Guid.NewGuid(),
                EMCodeId = Guid.NewGuid(),
                BillingNotes = "New billing",
                Amount = 300.00m,
                CreatedDate = DateTime.UtcNow
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.Created
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            // No exception should be thrown
            await _billingService.AddBillingAsync(billing);

            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Information,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.IsAny<Exception>(),
                    It.Is<Func<It.IsAnyType, Exception?, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }

        [Test]
        public void AddBillingAsync_WhenHttpRequestFails_ThrowsException()
        {
            // Arrange
            var billing = new Billing
            {
                BillingId = Guid.NewGuid(),
                PatientId = Guid.NewGuid(),
                VisitTypeId = Guid.NewGuid(),
                AssessmentId = Guid.NewGuid(),
                ProcedureCodeId = Guid.NewGuid(),
                EMCodeId = Guid.NewGuid(),
                BillingNotes = "New billing",
                Amount = 300.00m,
                CreatedDate = DateTime.UtcNow
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _billingService.AddBillingAsync(billing));

            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.IsAny<Exception>(),
                    It.Is<Func<It.IsAnyType, Exception?, string>>((v, t) => true)),
                Times.Exactly(2));
        }

        [Test]
        public async Task UpdateBillingAsync_WhenSuccessful_CompletesWithoutException()
        {
            // Arrange
            var billing = new Billing
            {
                BillingId = Guid.NewGuid(),
                PatientId = Guid.NewGuid(),
                VisitTypeId = Guid.NewGuid(),
                AssessmentId = Guid.NewGuid(),
                ProcedureCodeId = Guid.NewGuid(),
                EMCodeId = Guid.NewGuid(),
                BillingNotes = "Updated billing",
                Amount = 250.00m,
                CreatedDate = DateTime.UtcNow
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            // No exception should be thrown
            await _billingService.UpdateBillingAsync(billing);

            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Information,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.IsAny<Exception>(),
                    It.Is<Func<It.IsAnyType, Exception?, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }

        [Test]
        public void UpdateBillingAsync_WhenHttpRequestFails_ThrowsException()
        {
            // Arrange
            var billing = new Billing
            {
                BillingId = Guid.NewGuid(),
                PatientId = Guid.NewGuid(),
                VisitTypeId = Guid.NewGuid(),
                AssessmentId = Guid.NewGuid(),
                ProcedureCodeId = Guid.NewGuid(),
                EMCodeId = Guid.NewGuid(),
                BillingNotes = "Updated billing",
                Amount = 250.00m,
                CreatedDate = DateTime.UtcNow
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _billingService.UpdateBillingAsync(billing));

            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.IsAny<Exception>(),
                    It.Is<Func<It.IsAnyType, Exception?, string>>((v, t) => true)),
                Times.Exactly(2));
        }

        [Test]
        public async Task DeleteBillingByIdAsync_WhenSuccessful_CompletesWithoutException()
        {
            // Arrange
            var billingId = Guid.NewGuid();

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.NoContent
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            // No exception should be thrown
            await _billingService.DeleteBillingByIdAsync(billingId);

            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Information,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.IsAny<Exception>(),
                    It.Is<Func<It.IsAnyType, Exception?, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }

        [Test]
        public void DeleteBillingByIdAsync_WhenHttpRequestFails_ThrowsException()
        {
            // Arrange
            var billingId = Guid.NewGuid();

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _billingService.DeleteBillingByIdAsync(billingId));

            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.IsAny<Exception>(),
                    It.Is<Func<It.IsAnyType, Exception?, string>>((v, t) => true)),
                Times.Exactly(2));
        }
    }
}


