using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using Moq;
using Moq.Protected;
using NUnit.Framework;
using TeyaUIModels.Model;
using TeyaUIViewModels.ViewModel;
using TeyaUIViewModels.TeyaUIViewModelResources;

namespace TeyaWebClientUIViewModelTestCases
{
    [TestFixture]
    public class MedicalHistoryServiceTests
    {
        private Mock<HttpMessageHandler> _mockHttpMessageHandler;
        private Mock<IConfiguration> _mockConfiguration;
        private Mock<IStringLocalizer<TeyaUIViewModelsStrings>> _mockLocalizer;
        private Mock<ITokenService> _mockTokenService;
        private HttpClient _httpClient;
        private MedicalHistoryService _medicalHistoryService;

        private const string TestEncounterNotesUrl = "https://test-encounternotes.com";
        private const string TestAccessToken = "test-access-token";

        [SetUp]
        public void SetUp()
        {
            _mockHttpMessageHandler = new Mock<HttpMessageHandler>();
            _mockConfiguration = new Mock<IConfiguration>();
            _mockLocalizer = new Mock<IStringLocalizer<TeyaUIViewModelsStrings>>();
            _mockTokenService = new Mock<ITokenService>();

            _httpClient = new HttpClient(_mockHttpMessageHandler.Object);

            // Set up environment variable
            Environment.SetEnvironmentVariable("EncounterNotesURL", TestEncounterNotesUrl);

            // Set up mock responses
            _mockTokenService.Setup(t => t.AccessToken).Returns(TestAccessToken);

            _medicalHistoryService = new MedicalHistoryService(
                _httpClient,
                _mockConfiguration.Object,
                _mockLocalizer.Object,
                _mockTokenService.Object);
        }

        [TearDown]
        public void TearDown()
        {
            _httpClient?.Dispose();
            Environment.SetEnvironmentVariable("EncounterNotesURL", null);
        }

        private MedicalHistoryDTO CreateTestMedicalHistory()
        {
            return new MedicalHistoryDTO
            {
                MedicalHistoryID = Guid.NewGuid(),
                PatientId = Guid.NewGuid(),
                OrganizationId = Guid.NewGuid(),
                PCPId = Guid.NewGuid(),
                CreatedDate = DateTime.UtcNow,
                UpdatedDate = DateTime.UtcNow,
                History = "Patient has a history of hypertension and diabetes",
                Subscription = false,
                IsActive = true
            };
        }

        [Test]
        public async Task GetAllByIdAsync_WhenSuccessful_ReturnsMedicalHistoryList()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = false;
            var expectedMedicalHistories = new List<MedicalHistoryDTO>
            {
                CreateTestMedicalHistory(),
                CreateTestMedicalHistory()
            };

            var responseContent = JsonSerializer.Serialize(expectedMedicalHistories);
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(responseContent, Encoding.UTF8, "application/json")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _medicalHistoryService.GetAllByIdAsync(patientId, orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(2));
            Assert.That(result[0].History, Is.EqualTo("Patient has a history of hypertension and diabetes"));
        }

        [Test]
        public async Task GetAllByIdAsync_WhenHttpRequestFails_ThrowsHttpRequestException()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = false;
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.NotFound,
                Content = new StringContent("Not Found")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _medicalHistoryService.GetAllByIdAsync(patientId, orgId, subscription));
        }

        [Test]
        public async Task GetAllByIdAndIsActiveAsync_WhenSuccessful_ReturnsActiveMedicalHistories()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = false;
            var activeMedicalHistory = CreateTestMedicalHistory();
            activeMedicalHistory.IsActive = true;

            var expectedMedicalHistories = new List<MedicalHistoryDTO> { activeMedicalHistory };
            var responseContent = JsonSerializer.Serialize(expectedMedicalHistories);
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(responseContent, Encoding.UTF8, "application/json")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.Is<HttpRequestMessage>(req =>
                        req.RequestUri.ToString().Contains($"/api/MedicalHistory/{patientId}/IsActive/{orgId}/{subscription}") &&
                        req.Headers.Authorization != null &&
                        req.Headers.Authorization.Scheme == "Bearer" &&
                        req.Headers.Authorization.Parameter == TestAccessToken),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _medicalHistoryService.GetAllByIdAndIsActiveAsync(patientId, orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(1));
            Assert.That(result[0].IsActive, Is.True);
        }

        [Test]
        public async Task AddMedicalHistoryAsync_WhenSuccessful_CompletesWithoutException()
        {
            // Arrange
            var medicalHistories = new List<MedicalHistoryDTO>
            {
                CreateTestMedicalHistory(),
                CreateTestMedicalHistory()
            };
            var orgId = Guid.NewGuid();
            var subscription = false;

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.Created
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            // No exception should be thrown
            await _medicalHistoryService.AddMedicalHistoryAsync(medicalHistories, orgId, subscription);
        }

        [Test]
        public async Task AddMedicalHistoryAsync_WhenHttpRequestFails_ThrowsHttpRequestException()
        {
            // Arrange
            var medicalHistories = new List<MedicalHistoryDTO> { CreateTestMedicalHistory() };
            var orgId = Guid.NewGuid();
            var subscription = false;
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad Request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _medicalHistoryService.AddMedicalHistoryAsync(medicalHistories, orgId, subscription));
        }

        [Test]
        public async Task UpdateMedicalHistoryAsync_WhenSuccessful_CompletesWithoutException()
        {
            // Arrange
            var medicalHistory = CreateTestMedicalHistory();
            var orgId = Guid.NewGuid();
            var subscription = false;

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            // No exception should be thrown
            await _medicalHistoryService.UpdateMedicalHistoryAsync(medicalHistory, orgId, subscription);
        }

        [Test]
        public async Task UpdateMedicalHistoryListAsync_WhenSuccessful_CompletesWithoutException()
        {
            // Arrange
            var medicalHistories = new List<MedicalHistoryDTO>
            {
                CreateTestMedicalHistory(),
                CreateTestMedicalHistory()
            };
            var orgId = Guid.NewGuid();
            var subscription = false;

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            // No exception should be thrown
            await _medicalHistoryService.UpdateMedicalHistoryListAsync(medicalHistories, orgId, subscription);
        }

        [Test]
        public async Task DeleteMedicalHistoryByEntityAsync_WhenSuccessful_CompletesWithoutException()
        {
            // Arrange
            var medicalHistory = CreateTestMedicalHistory();
            var orgId = Guid.NewGuid();
            var subscription = false;

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.NoContent
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response)
                .Verifiable();

            // Act & Assert
            // No exception should be thrown
            await _medicalHistoryService.DeleteMedicalHistoryByEntityAsync(medicalHistory, orgId, subscription);

            // Verify the HTTP call was made
            _mockHttpMessageHandler.Protected().Verify(
                "SendAsync",
                Times.Once(),
                ItExpr.IsAny<HttpRequestMessage>(),
                ItExpr.IsAny<CancellationToken>());
        }

        [Test]
        public async Task UpdateMedicalHistoryListAsync_WhenHttpRequestFails_ThrowsHttpRequestException()
        {
            // Arrange
            var medicalHistories = new List<MedicalHistoryDTO> { CreateTestMedicalHistory() };
            var orgId = Guid.NewGuid();
            var subscription = false;
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad Request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _medicalHistoryService.UpdateMedicalHistoryListAsync(medicalHistories, orgId, subscription));
        }

        [Test]
        public async Task GetAllByIdAndIsActiveAsync_WhenEmptyResponse_ReturnsEmptyList()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = false;

            var responseContent = JsonSerializer.Serialize(new List<MedicalHistoryDTO>());
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(responseContent, Encoding.UTF8, "application/json")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _medicalHistoryService.GetAllByIdAndIsActiveAsync(patientId, orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(0));
        }
    }
}



