﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Components;
using TeyaUIModels.Model;
using TeyaUIModels.ViewModel;
using TeyaUIViewModels.ViewModel;
using TeyaWebApp.Components.Layout;
using TeyaWebApp.Components.Pages;
using TeyaWebApp.Services;
using TeyaWebApp.ViewModel;

namespace TeyaWebApp.Authorization
{
    public class DynamicRoleAuthorizationHandler : AuthorizationHandler<DynamicRoleRequirement>
    {
        private readonly GraphApiService _AuthService;
        private readonly IPageRoleMappingService _pageRoleService;
        private readonly IPreDefinedPageRoleMappingService _preDefinedPageRoleMappingService;
        private IPlanTypeService _planTypeService;
        private readonly IMemberService _memberService;
        private IUserLicenseService _userLicenseService;
        private readonly IOrganizationService _organizationService;
        private readonly ActiveUser _activeUser;
        private readonly ILogger<DynamicRoleAuthorizationHandler> _logger;
        private readonly string adminRole = "Admin";
        private bool Subscription = false;


        public DynamicRoleAuthorizationHandler(GraphApiService AuthService, IPageRoleMappingService roleService, IPreDefinedPageRoleMappingService preDefinedPageRoleMappingService, IMemberService memberService,IUserLicenseService UserLicenseService, IPlanTypeService PlanTypeService, IOrganizationService organizationService, ActiveUser activeUser, ILogger<DynamicRoleAuthorizationHandler> logger)
        {
            _AuthService = AuthService;
            _pageRoleService = roleService;
            _preDefinedPageRoleMappingService = preDefinedPageRoleMappingService;
            _memberService = memberService;
            _organizationService = organizationService;
            _userLicenseService = UserLicenseService;
            _planTypeService = PlanTypeService; 
            _activeUser = activeUser;
            _logger = logger;
        }

        protected override async Task HandleRequirementAsync(AuthorizationHandlerContext context, DynamicRoleRequirement requirement)
        {
            await _AuthService.GetLoggedInUserDetailsAsync();
            try
            {
                var userId = _activeUser.id;
                var OrganizationId = await _organizationService.GetOrganizationIdByNameAsync(_activeUser.OrganizationName);
                var activeUserLicense = await _userLicenseService.GetUserLicenseByOrganizationIdAsync(OrganizationId);
                var planType = await _planTypeService.GetPlanTypeByIdAsync(activeUserLicense.PlanId);
                Subscription = planType.PlanName == "Enterprise";

                if (userId == null)
                {
                    context.Fail();
                    return;
                }

                var currentUser = await _memberService.GetMemberByIdAsync(Guid.Parse(userId), OrganizationId, Subscription);
                var currentUserRole = currentUser.RoleName;
                if (currentUserRole != adminRole)
                {
                    var nonAdminApprovedRoles = await _preDefinedPageRoleMappingService.GetRolesByPagePathAsync(requirement.PagePath);
                    if (nonAdminApprovedRoles.Contains(currentUser.RoleName))
                    {
                        context.Succeed(requirement);
                    }
                    else
                    {
                        context.Fail();
                    }
                }
                else
                {
                    var approvedRoles = await _pageRoleService.GetRolesByPagePathAsync(requirement.PagePath, OrganizationId,Subscription);

                    if (approvedRoles.Contains(currentUser.RoleName))
                    {
                        context.Succeed(requirement);
                    }
                    else
                    {
                        context.Fail();
                    }
                }
            }
            catch (Exception ex)
            {
                context.Fail();
                _logger.LogError(ex, "Unauthorized Role.");
            }
        }
    }
}
