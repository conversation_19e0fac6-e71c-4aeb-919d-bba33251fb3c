﻿<?xml version="1.0" encoding="UTF-8" ?>
<Application xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:local="clr-namespace:TeyaMobile"
             xmlns:converters="clr-namespace:TeyaMobile.Converters"
             x:Class="TeyaMobile.App">
    <Application.Resources>
        <ResourceDictionary>

            <!--<ResourceDictionary.MergedDictionaries>
                --><!--<ResourceDictionary Source="Resources/Styles/Colors.xaml" />
                <ResourceDictionary Source="Resources/Styles/Styles.xaml" />--><!--
            </ResourceDictionary.MergedDictionaries>-->

            <Color x:Key="PageBackgroundColor">#512bdf</Color>
            <Color x:Key="PrimaryTextColor">White</Color>

            <Style TargetType="Label">
                <Setter Property="TextColor" Value="{DynamicResource PrimaryTextColor}" />
                <Setter Property="FontFamily" Value="OpenSansRegular" />
            </Style>

            <Style TargetType="Button" x:Key="rounded-button" />
            <Style TargetType="Frame" x:Key="frame-shadow" />
            
            <Style TargetType="Button">
                <Setter Property="TextColor" Value="{DynamicResource PrimaryTextColor}" />
                <Setter Property="FontFamily" Value="OpenSansRegular" />
                <Setter Property="BackgroundColor" Value="#2b0b98" />
                <Setter Property="Padding" Value="14,10" />
            </Style>

            <!-- Converters -->
            <converters:RecordingStateToColorConverter x:Key="RecordingStateToColorConverter" />
            <converters:RecordingStateToButtonEnabledConverter x:Key="RecordingStateToButtonEnabledConverter" />
            <converters:StringToBoolConverter x:Key="StringToBoolConverter" />
            
        </ResourceDictionary>
    </Application.Resources>
</Application>