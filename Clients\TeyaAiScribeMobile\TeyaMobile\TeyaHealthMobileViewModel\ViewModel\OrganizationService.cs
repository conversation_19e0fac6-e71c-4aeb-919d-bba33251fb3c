﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using System.Text;
using System.Text.Json;
using TeyaHealthMobileModel.Model;


namespace TeyaHealthMobileViewModel.ViewModel
{
    public class OrganizationService : IOrganizationService
    {
        private readonly HttpClient _httpClient;
        private readonly string _MemberService;
        private readonly IStringLocalizer<OrganizationService> _localizer;
        private readonly ILogger<OrganizationService> _logger;
        private readonly IAuthenticationService _authenticationService;
        private readonly IConfiguration _configuration;

        public OrganizationService(HttpClient httpClient, IStringLocalizer<OrganizationService> localizer, ILogger<OrganizationService> logger, IAuthenticationService authenticationService, IConfiguration configuration)
        {
            _httpClient = httpClient ?? throw new ArgumentNullException(nameof(httpClient));
            _localizer = localizer ?? throw new ArgumentNullException(nameof(localizer));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _authenticationService = authenticationService ?? throw new ArgumentNullException(nameof(authenticationService));
            _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
            _MemberService = _configuration["MemberServiceURL"] ?? throw new InvalidOperationException("MemberServiceURL environment variable not set");
        }

        public async Task<Organization> RegisterOrganizationsAsync(Organization organization)
        {
            try
            {
                var bodyContent = JsonSerializer.Serialize(organization);
                var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");
                var apiUrl = $"{_MemberService}/api/Organizations";
                var requestMessage = new HttpRequestMessage(HttpMethod.Post, apiUrl)
                {
                    Content = content
                };
                var response = await _httpClient.SendAsync(requestMessage);

                if (response.IsSuccessStatusCode)
                {
                    var responseData = await response.Content.ReadAsStringAsync();
                    _logger.LogInformation(_localizer["API Response: {ResponseData}"], responseData);

                    try
                    {
                        return JsonSerializer.Deserialize<Organization>(responseData);
                    }
                    catch (JsonException ex)
                    {
                        _logger.LogError(ex, _localizer["JsonDeserializationError", ex.Message]);
                        throw;
                    }
                }
                else if (response.StatusCode == System.Net.HttpStatusCode.Conflict)
                {
                    _logger.LogError("Organization already exists with status code {StatusCode}", response.StatusCode);
                    throw new HttpRequestException(_localizer["OrganizationAlreadyExists"]);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError($"Registration failed. Status Code: {response.StatusCode}, Reason: {response.ReasonPhrase}, Response: {errorContent}");
                    throw new HttpRequestException(_localizer["RegistrationFailed"]);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["OrganizationRegistrationError"]);
                throw;
            }
        }

        public async Task<Organization> GetOrganizationByIdAsync(Guid OrganizationId)
        {
            try
            {
                var apiUrl = $"{_MemberService}/api/Organizations/{OrganizationId}";
                var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
                var response = await _httpClient.SendAsync(requestMessage);
                if (response.IsSuccessStatusCode)
                {
                    var responseData = await response.Content.ReadAsStringAsync();
                    var options = new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    };
                    return JsonSerializer.Deserialize<Organization>(responseData, options);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError($"API Request Failed. Status Code: {response.StatusCode}, Response: {errorContent}");
                    throw new HttpRequestException(_localizer["GetByIdFailed"]);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorFetchingOrganizationById"], OrganizationId);
                throw;
            }
        }

        public async Task<List<Organization>> GetAllOrganizationsAsync()
        {
            try
            {
                var apiUrl = $"{_MemberService}/api/Organizations";
                var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
                var response = await _httpClient.SendAsync(requestMessage);
                response.EnsureSuccessStatusCode();

                var responseData = await response.Content.ReadAsStringAsync();
                var options = new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                };
                return JsonSerializer.Deserialize<List<Organization>>(responseData, options);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorFetchingAllOrganizations"]);
                throw;
            }
        }

        public async Task DeleteOrganizationByIdAsync(Guid OrganizationId)
        {
            try
            {
                var apiUrl = $"{_MemberService}/api/Organizations/{OrganizationId}";
                var requestMessage = new HttpRequestMessage(HttpMethod.Delete, apiUrl);
                var response = await _httpClient.SendAsync(requestMessage);
                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation(_localizer["OrganizationDeletedSuccessfully"], OrganizationId);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError($"DeleteFaileStatusCode: {response.StatusCode}, Response: {errorContent}");
                    throw new HttpRequestException(_localizer["OrganizationDeletionFailed"]);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorDeletingOrganization"], OrganizationId);
                throw;
            }
        }

        public async Task UpdateOrganizationByIdAsync(Guid OrganizationId, Organization Organization)
        {
            if (Organization == null || Organization.OrganizationId != OrganizationId)
            {
                _logger.LogError(_localizer["InvalidOrganization"]);
                throw new ArgumentException(_localizer["InvalidOrganization"]);
            }

            try
            {
                var apiUrl = $"{_MemberService}/api/Organizations/{OrganizationId}";
                var bodyContent = JsonSerializer.Serialize(Organization);
                var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");
                var requestMessage = new HttpRequestMessage(HttpMethod.Put, apiUrl);
                requestMessage.Content = content;
                var response = await _httpClient.SendAsync(requestMessage);

                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation(_localizer["UpdateSuccessful"], OrganizationId);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError($"Update failed. Status Code: {response.StatusCode}, Response: {errorContent}");
                    throw new HttpRequestException(_localizer["UpdateFailed"]);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorUpdatingOrganization"], OrganizationId);
                throw;
            }
        }

        public async Task<List<Organization>> GetOrganizationsByNameAsync(string name)
        {
            if (string.IsNullOrWhiteSpace(name))
            {
                _logger.LogWarning("Organization name is null, empty, or whitespace. Returning empty list.");
                return new List<Organization>();
            }

            var organizations = new List<Organization>();

            try
            {
                var accessToken = await _authenticationService.GetServiceSpecificTokenAsync();
                if (string.IsNullOrEmpty(accessToken))
                {
                    _logger.LogError("Access token not available for organization search");
                    throw new UnauthorizedAccessException("Authentication required");
                }

                var escapedName = Uri.EscapeDataString(name.Trim());
                var requestUrl = $"{_MemberService}/api/Organizations/search/?name={escapedName}";

                var requestMessage = new HttpRequestMessage(HttpMethod.Get, requestUrl);
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);

                if (response.StatusCode == System.Net.HttpStatusCode.NotFound)
                {
                    _logger.LogInformation("No organizations found for name: {Name}", name);
                    return organizations;
                }

                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    if (!string.IsNullOrWhiteSpace(responseContent))
                    {
                        organizations = JsonSerializer.Deserialize<List<Organization>>(responseContent, new JsonSerializerOptions
                        {
                            PropertyNameCaseInsensitive = true
                        }) ?? new List<Organization>();
                    }
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError("Failed to fetch organizations. Status: {StatusCode}, Content: {ErrorContent}",
                        response.StatusCode, errorContent);
                }
            }
            catch (HttpRequestException httpEx)
            {
                _logger.LogError(httpEx, "HTTP error while fetching organizations for name: {Name}", name);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Unexpected error while fetching organizations for name: {Name}", name);
            }

            return organizations;
        }

        public async Task<Guid> GetOrganizationIdByNameAsync(string orgName)
        {
            if (string.IsNullOrWhiteSpace(orgName))
            {
                _logger.LogWarning("Organization name is null or empty");
                throw new ArgumentException("Organization name cannot be null or empty", nameof(orgName));
            }

            var organizations = await GetOrganizationsByNameAsync(orgName);
            var organization = organizations?.FirstOrDefault(org =>
                string.Equals(org.OrganizationName, orgName.Trim(), StringComparison.OrdinalIgnoreCase));

            if (organization == null)
            {
                _logger.LogWarning("Organization not found for name: {OrgName}", orgName);
                throw new KeyNotFoundException(_localizer["OrganizationNotFound", orgName]);
            }

            return organization.OrganizationId;
        }
    }
}
