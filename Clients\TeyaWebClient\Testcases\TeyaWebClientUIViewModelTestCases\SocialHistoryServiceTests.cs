using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Moq;
using Moq.Protected;
using NUnit.Framework;
using TeyaUIModels.Model;
using TeyaUIViewModels.ViewModel;
using System.Net.Http.Json;
using System.Text.Json;

namespace TeyaWebClientUIViewModelTestCases
{
    [TestFixture]
    public class SocialHistoryServiceTests
    {
        private Mock<HttpMessageHandler> _mockHttpMessageHandler;
        private HttpClient _httpClient;
        private Mock<IConfiguration> _mockConfiguration;
        private Mock<IStringLocalizer<SocialHistoryService>> _mockLocalizer;
        private Mock<ILogger<SocialHistoryService>> _mockLogger;
        private Mock<ITokenService> _mockTokenService;
        private SocialHistoryService _socialHistoryService;
        private readonly string _baseUrl = "http://test-api.com";
        private readonly string _accessToken = "test-access-token";

        [SetUp]
        public void Setup()
        {
            // Setup environment variable
            Environment.SetEnvironmentVariable("EncounterNotesURL", _baseUrl);

            // Setup mock HTTP handler
            _mockHttpMessageHandler = new Mock<HttpMessageHandler>();

            // Setup HttpClient with mock handler
            _httpClient = new HttpClient(_mockHttpMessageHandler.Object)
            {
                BaseAddress = new Uri(_baseUrl)
            };

            // Setup mock configuration
            _mockConfiguration = new Mock<IConfiguration>();

            // Setup mock localizer
            _mockLocalizer = new Mock<IStringLocalizer<SocialHistoryService>>();
            _mockLocalizer.Setup(l => l["RecordNotFound"])
                .Returns(new LocalizedString("RecordNotFound", "Record not found"));
            _mockLocalizer.Setup(l => l["TasksRetrievalFailure"])
                .Returns(new LocalizedString("TasksRetrievalFailure", "Tasks retrieval failure"));

            // Setup mock logger
            _mockLogger = new Mock<ILogger<SocialHistoryService>>();

            // Setup mock token service
            _mockTokenService = new Mock<ITokenService>();
            _mockTokenService.Setup(t => t.AccessToken).Returns(_accessToken);

            // Create SocialHistoryService with mocked dependencies
            _socialHistoryService = new SocialHistoryService(_httpClient, _mockConfiguration.Object, _mockLocalizer.Object, _mockTokenService.Object);
        }

        [TearDown]
        public void TearDown()
        {
            _httpClient.Dispose();
            Environment.SetEnvironmentVariable("EncounterNotesURL", null);
        }

        [Test]
        public async Task GetAllByPatientIdAsync_WhenSuccessful_ReturnsPatientSocialHistories()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var expectedHistories = new List<PatientSocialHistory>
            {
                new PatientSocialHistory
                {
                    SocialHistoryId = Guid.NewGuid(),
                    PatientId = patientId,
                    OrganizationId = Guid.NewGuid(),
                    PCPId = Guid.NewGuid(),
                    CreatedBy = Guid.NewGuid(),
                    UpdatedBy = Guid.NewGuid(),
                    CreatedDate = DateTime.Now.AddDays(-30),
                    UpdatedDate = DateTime.Now.AddDays(-25),
                    Occupation = "Software Engineer",
                    LivingSituation = "Lives with spouse",
                    MaritalStatus = "Married",
                    LifestyleHabits = "Never smoker, occasional alcohol, balanced diet, 3 times per week exercise",
                    Educationlevel = "Bachelor's degree",
                    isActive = true,
                    Subscription = false
                },
                new PatientSocialHistory
                {
                    SocialHistoryId = Guid.NewGuid(),
                    PatientId = patientId,
                    OrganizationId = Guid.NewGuid(),
                    PCPId = Guid.NewGuid(),
                    CreatedBy = Guid.NewGuid(),
                    UpdatedBy = Guid.NewGuid(),
                    CreatedDate = DateTime.Now.AddDays(-60),
                    UpdatedDate = DateTime.Now.AddDays(-55),
                    Occupation = "Teacher",
                    LivingSituation = "Lives alone",
                    MaritalStatus = "Single",
                    LifestyleHabits = "Former smoker, no alcohol, vegetarian diet, daily exercise",
                    Educationlevel = "Master's degree",
                    isActive = false,
                    Subscription = false
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedHistories)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _socialHistoryService.GetAllByPatientIdAsync(patientId, orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(expectedHistories.Count));
            Assert.That(result[0].SocialHistoryId, Is.EqualTo(expectedHistories[0].SocialHistoryId));
            Assert.That(result[0].PatientId, Is.EqualTo(expectedHistories[0].PatientId));
            Assert.That(result[0].Occupation, Is.EqualTo(expectedHistories[0].Occupation));
            Assert.That(result[0].LivingSituation, Is.EqualTo(expectedHistories[0].LivingSituation));
            Assert.That(result[0].MaritalStatus, Is.EqualTo(expectedHistories[0].MaritalStatus));
            Assert.That(result[0].LifestyleHabits, Is.EqualTo(expectedHistories[0].LifestyleHabits));
            Assert.That(result[0].Educationlevel, Is.EqualTo(expectedHistories[0].Educationlevel));
            Assert.That(result[0].isActive, Is.EqualTo(expectedHistories[0].isActive));
        }

        [Test]
        public void GetAllByPatientIdAsync_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.NotFound,
                Content = new StringContent("Not found")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _socialHistoryService.GetAllByPatientIdAsync(patientId, orgId, subscription));

            Assert.That(exception.Message, Is.EqualTo("Record not found"));
        }

        [Test]
        public async Task GetAllByIdAndIsActiveAsync_WhenSuccessful_ReturnsActiveSocialHistories()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var expectedHistories = new List<PatientSocialHistory>
            {
                new PatientSocialHistory
                {
                    SocialHistoryId = Guid.NewGuid(),
                    PatientId = patientId,
                    OrganizationId = Guid.NewGuid(),
                    PCPId = Guid.NewGuid(),
                    CreatedBy = Guid.NewGuid(),
                    UpdatedBy = Guid.NewGuid(),
                    CreatedDate = DateTime.Now.AddDays(-10),
                    UpdatedDate = DateTime.Now.AddDays(-5),
                    Occupation = "Construction worker",
                    LivingSituation = "Lives with children",
                    MaritalStatus = "Divorced",
                    LifestyleHabits = "Current smoker, heavy alcohol, marijuana use, rarely exercises, fast food diet",
                    Educationlevel = "High school",
                    isActive = true,
                    Subscription = false
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedHistories)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _socialHistoryService.GetAllByIdAndIsActiveAsync(patientId, orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(expectedHistories.Count));
            Assert.That(result[0].isActive, Is.True);
            Assert.That(result[0].LifestyleHabits, Does.Contain("Current smoker"));
            Assert.That(result[0].LifestyleHabits, Does.Contain("heavy alcohol"));
        }

        [Test]
        public void GetAllByIdAndIsActiveAsync_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _socialHistoryService.GetAllByIdAndIsActiveAsync(patientId, orgId, subscription));

            Assert.That(exception.Message, Is.EqualTo("Record not found"));
        }

        [Test]
        public async Task AddHistoryListAsync_WhenSuccessful_CompletesSuccessfully()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;
            var histories = new List<PatientSocialHistory>
            {
                new PatientSocialHistory
                {
                    SocialHistoryId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    OrganizationId = Guid.NewGuid(),
                    PCPId = Guid.NewGuid(),
                    CreatedBy = Guid.NewGuid(),
                    UpdatedBy = Guid.NewGuid(),
                    CreatedDate = DateTime.Now,
                    UpdatedDate = DateTime.Now,
                    Occupation = "Doctor",
                    LivingSituation = "Lives with family",
                    MaritalStatus = "Married",
                    LifestyleHabits = "Never smoker, social drinker, daily exercise, Mediterranean diet",
                    Educationlevel = "Medical degree",
                    isActive = true,
                    Subscription = false
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.Created,
                Content = new StringContent("Success")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.DoesNotThrowAsync(async () => await _socialHistoryService.AddHistoryListAsync(histories, orgId, subscription));
        }

        [Test]
        public void AddHistoryListAsync_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;
            var histories = new List<PatientSocialHistory>
            {
                new PatientSocialHistory
                {
                    SocialHistoryId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    OrganizationId = Guid.NewGuid(),
                    PCPId = Guid.NewGuid(),
                    CreatedBy = Guid.NewGuid(),
                    UpdatedBy = Guid.NewGuid(),
                    CreatedDate = DateTime.Now,
                    UpdatedDate = DateTime.Now,
                    Occupation = "Doctor",
                    LivingSituation = "Lives with family",
                    MaritalStatus = "Married",
                    LifestyleHabits = "Never smoker, social drinker, daily exercise, Mediterranean diet",
                    Educationlevel = "Medical degree",
                    isActive = true,
                    Subscription = false
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _socialHistoryService.AddHistoryListAsync(histories, orgId, subscription));

            Assert.That(exception.Message, Does.Contain("Exception of type"));
        }

        [Test]
        public async Task UpdateHistoryListAsync_WhenSuccessful_CompletesSuccessfully()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;
            var histories = new List<PatientSocialHistory>
            {
                new PatientSocialHistory
                {
                    SocialHistoryId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    OrganizationId = Guid.NewGuid(),
                    PCPId = Guid.NewGuid(),
                    CreatedBy = Guid.NewGuid(),
                    UpdatedBy = Guid.NewGuid(),
                    CreatedDate = DateTime.Now.AddDays(-30),
                    UpdatedDate = DateTime.Now.AddDays(-25),
                    Occupation = "Retired",
                    LivingSituation = "Lives in assisted living",
                    MaritalStatus = "Widowed",
                    LifestyleHabits = "Former smoker, no alcohol, light exercise, low sodium diet",
                    Educationlevel = "High school",
                    isActive = true,
                    Subscription = false
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent("Success")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.DoesNotThrowAsync(async () => await _socialHistoryService.UpdateHistoryListAsync(histories, orgId, subscription));
        }

        [Test]
        public void UpdateHistoryListAsync_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;
            var histories = new List<PatientSocialHistory>
            {
                new PatientSocialHistory
                {
                    SocialHistoryId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    OrganizationId = Guid.NewGuid(),
                    PCPId = Guid.NewGuid(),
                    CreatedBy = Guid.NewGuid(),
                    UpdatedBy = Guid.NewGuid(),
                    CreatedDate = DateTime.Now.AddDays(-30),
                    UpdatedDate = DateTime.Now.AddDays(-25),
                    Occupation = "Retired",
                    LivingSituation = "Lives in assisted living",
                    MaritalStatus = "Widowed",
                    LifestyleHabits = "Former smoker, no alcohol, light exercise, low sodium diet",
                    Educationlevel = "High school",
                    isActive = true,
                    Subscription = false
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _socialHistoryService.UpdateHistoryListAsync(histories, orgId, subscription));

            Assert.That(exception.Message, Does.Contain("Exception of type"));
        }

        [Test]
        public async Task DeleteHistoryAsync_WhenSuccessful_CompletesSuccessfully()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;
            var history = new PatientSocialHistory
            {
                SocialHistoryId = Guid.NewGuid(),
                PatientId = Guid.NewGuid(),
                OrganizationId = Guid.NewGuid(),
                PCPId = Guid.NewGuid(),
                CreatedBy = Guid.NewGuid(),
                UpdatedBy = Guid.NewGuid(),
                CreatedDate = DateTime.Now.AddDays(-100),
                UpdatedDate = DateTime.Now.AddDays(-95),
                Occupation = "Deleted occupation",
                LivingSituation = "Lives alone",
                MaritalStatus = "Single",
                LifestyleHabits = "Never smoker, no alcohol, no exercise, regular diet",
                Educationlevel = "High school",
                isActive = false,
                Subscription = false
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent("Success")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.DoesNotThrowAsync(async () => await _socialHistoryService.UpdateHistoryListAsync(new List<PatientSocialHistory> { history }, orgId, subscription));
        }

        [Test]
        public void DeleteHistoryAsync_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;
            var history = new PatientSocialHistory
            {
                SocialHistoryId = Guid.NewGuid(),
                PatientId = Guid.NewGuid(),
                OrganizationId = Guid.NewGuid(),
                PCPId = Guid.NewGuid(),
                CreatedBy = Guid.NewGuid(),
                UpdatedBy = Guid.NewGuid(),
                CreatedDate = DateTime.Now.AddDays(-100),
                UpdatedDate = DateTime.Now.AddDays(-95),
                Occupation = "Deleted occupation",
                LivingSituation = "Lives alone",
                MaritalStatus = "Single",
                LifestyleHabits = "Never smoker, no alcohol, no exercise, regular diet",
                Educationlevel = "High school",
                isActive = false,
                Subscription = false
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _socialHistoryService.UpdateHistoryListAsync(new List<PatientSocialHistory> { history }, orgId, subscription));

            Assert.That(exception.Message, Does.Contain("Exception of type"));
        }

        [Test]
        public async Task UpdateHistoryAsync_WhenSuccessful_CompletesSuccessfully()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;
            var history = new PatientSocialHistory
            {
                SocialHistoryId = Guid.NewGuid(),
                PatientId = Guid.NewGuid(),
                OrganizationId = Guid.NewGuid(),
                PCPId = Guid.NewGuid(),
                CreatedBy = Guid.NewGuid(),
                UpdatedBy = Guid.NewGuid(),
                CreatedDate = DateTime.Now.AddDays(-5),
                UpdatedDate = DateTime.Now.AddDays(-3),
                Occupation = "Updated occupation",
                LivingSituation = "Lives with spouse",
                MaritalStatus = "Married",
                LifestyleHabits = "Quit smoking, reduced alcohol, improved diet, increased to daily exercise",
                Educationlevel = "Bachelor's degree",
                isActive = true,
                Subscription = false
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent("Success")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.DoesNotThrowAsync(async () => await _socialHistoryService.UpdateHistoryListAsync(new List<PatientSocialHistory> { history }, orgId, subscription));
        }

        [Test]
        public void UpdateHistoryAsync_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;
            var history = new PatientSocialHistory
            {
                SocialHistoryId = Guid.NewGuid(),
                PatientId = Guid.NewGuid(),
                OrganizationId = Guid.NewGuid(),
                PCPId = Guid.NewGuid(),
                CreatedBy = Guid.NewGuid(),
                UpdatedBy = Guid.NewGuid(),
                CreatedDate = DateTime.Now.AddDays(-5),
                UpdatedDate = DateTime.Now.AddDays(-3),
                Occupation = "Updated occupation",
                LivingSituation = "Lives with spouse",
                MaritalStatus = "Married",
                LifestyleHabits = "Quit smoking, reduced alcohol, improved diet, increased to daily exercise",
                Educationlevel = "Bachelor's degree",
                isActive = true,
                Subscription = false
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _socialHistoryService.UpdateHistoryListAsync(new List<PatientSocialHistory> { history }, orgId, subscription));

            Assert.That(exception.Message, Does.Contain("Exception of type"));
        }

        [Test]
        public async Task GetAllByPatientIdAsync_WhenEmptyResponse_ReturnsEmptyList()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var expectedHistories = new List<PatientSocialHistory>();

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedHistories)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _socialHistoryService.GetAllByPatientIdAsync(patientId, orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(0));
        }

        [Test]
        public void GetAllByPatientIdAsync_WhenExceptionThrown_PropagatesException()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var expectedException = new HttpRequestException("Test exception");

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ThrowsAsync(expectedException);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _socialHistoryService.GetAllByPatientIdAsync(patientId, orgId, subscription));

            Assert.That(exception, Is.Not.Null);
            Assert.That(exception.Message, Is.EqualTo("Test exception"));
        }

        [Test]
        public async Task GetAllByIdAndIsActiveAsync_WhenEmptyResponse_ReturnsEmptyList()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var expectedHistories = new List<PatientSocialHistory>();

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedHistories)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _socialHistoryService.GetAllByIdAndIsActiveAsync(patientId, orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(0));
        }
    }
}



