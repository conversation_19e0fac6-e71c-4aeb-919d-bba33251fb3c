using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Moq;
using Moq.Protected;
using NUnit.Framework;
using TeyaUIModels.Model;
using TeyaUIViewModels.ViewModels;
using TeyaUIViewModels.ViewModel;
using System.Net.Http.Json;
using System.Text.Json;
using System.Linq;

namespace TeyaWebClientUIViewModelTestCases
{
    [TestFixture]
    public class VisitStatusServiceTests
    {
        private Mock<HttpMessageHandler> _mockHttpMessageHandler;
        private HttpClient _httpClient;
        private Mock<IStringLocalizer<VisitStatusService>> _mockLocalizer;
        private Mock<ILogger<VisitStatusService>> _mockLogger;
        private Mock<ITokenService> _mockTokenService;
        private VisitStatusService _visitStatusService;
        private readonly string _baseUrl = "http://test-api.com";
        private readonly string _accessToken = "test-access-token";

        [SetUp]
        public void Setup()
        {
            // Setup environment variable
            Environment.SetEnvironmentVariable("MemberServiceURL", _baseUrl);

            // Setup mock HTTP handler
            _mockHttpMessageHandler = new Mock<HttpMessageHandler>();

            // Setup HttpClient with mock handler
            _httpClient = new HttpClient(_mockHttpMessageHandler.Object)
            {
                BaseAddress = new Uri(_baseUrl)
            };

            // Setup mock localizer
            _mockLocalizer = new Mock<IStringLocalizer<VisitStatusService>>();
            _mockLocalizer.Setup(l => l["AccessTokenNotFound"])
                .Returns(new LocalizedString("AccessTokenNotFound", "Access token not found"));
            _mockLocalizer.Setup(l => l["AccessTokenMissing"])
                .Returns(new LocalizedString("AccessTokenMissing", "Access token missing"));
            _mockLocalizer.Setup(l => l["ErrorFetchingVisitStatus"])
                .Returns(new LocalizedString("ErrorFetchingVisitStatus", "Error fetching visit status"));
            _mockLocalizer.Setup(l => l["An error occurred while fetching visit Status names."])
                .Returns(new LocalizedString("An error occurred while fetching visit Status names.", "An error occurred while fetching visit status names."));

            // Setup mock logger
            _mockLogger = new Mock<ILogger<VisitStatusService>>();

            // Setup mock token service
            _mockTokenService = new Mock<ITokenService>();
            _mockTokenService.Setup(t => t.AccessToken).Returns(_accessToken);
            _mockTokenService.Setup(t => t.GetValidatedAccessTokenAsync()).ReturnsAsync(_accessToken);
            _mockTokenService.Setup(t => t.GetValidatedAccessTokenAsync()).ReturnsAsync(_accessToken);

            // Create VisitStatusService with mocked dependencies
            _visitStatusService = new VisitStatusService(_httpClient, _mockLocalizer.Object, _mockLogger.Object, _mockTokenService.Object);
        }

        [TearDown]
        public void TearDown()
        {
            _httpClient.Dispose();
            Environment.SetEnvironmentVariable("MemberServiceURL", null);
        }

        [Test]
        public async Task GetAllVisitStatusAsync_WhenSuccessful_ReturnsVisitStatuses()
        {
            // Arrange
            var expectedStatuses = new List<VisitStatus>
            {
                new VisitStatus
                {
                    ID = Guid.NewGuid(),
                    Visitstatus = "Scheduled",
                    OrganizationId = Guid.NewGuid()
                },
                new VisitStatus
                {
                    ID = Guid.NewGuid(),
                    Visitstatus = "In Progress",
                    OrganizationId = Guid.NewGuid()
                },
                new VisitStatus
                {
                    ID = Guid.NewGuid(),
                    Visitstatus = "Completed",
                    OrganizationId = Guid.NewGuid()
                },
                new VisitStatus
                {
                    ID = Guid.NewGuid(),
                    Visitstatus = "Cancelled",
                    OrganizationId = Guid.NewGuid()
                },
                new VisitStatus
                {
                    ID = Guid.NewGuid(),
                    Visitstatus = "No Show",
                    OrganizationId = Guid.NewGuid()
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(JsonSerializer.Serialize(expectedStatuses, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                }))
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.Is<HttpRequestMessage>(req =>
                        req.Method == HttpMethod.Get &&
                        req.RequestUri.ToString() == $"{_baseUrl}/api/VisitStatus" &&
                        req.Headers.Authorization.Scheme == "Bearer" &&
                        req.Headers.Authorization.Parameter == _accessToken),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _visitStatusService.GetAllVisitStatusAsync();

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(expectedStatuses.Count));
            Assert.That(result[0].ID, Is.EqualTo(expectedStatuses[0].ID));
            Assert.That(result[0].Visitstatus, Is.EqualTo(expectedStatuses[0].Visitstatus));
            Assert.That(result[0].OrganizationId, Is.EqualTo(expectedStatuses[0].OrganizationId));

            // Verify all status names
            Assert.That(result[1].Visitstatus, Is.EqualTo("In Progress"));
            Assert.That(result[2].Visitstatus, Is.EqualTo("Completed"));
            Assert.That(result[3].Visitstatus, Is.EqualTo("Cancelled"));
            Assert.That(result[4].Visitstatus, Is.EqualTo("No Show"));
        }

        [Test]
        public void GetAllVisitStatusAsync_WhenAccessTokenIsNull_ThrowsUnauthorizedAccessException()
        {
            // Arrange
            _mockTokenService.Setup(t => t.GetValidatedAccessTokenAsync()).ReturnsAsync((string)null);

            // Act & Assert
            var exception = Assert.ThrowsAsync<UnauthorizedAccessException>(async () =>
                await _visitStatusService.GetAllVisitStatusAsync());

            Assert.That(exception.Message, Is.EqualTo("Access token missing"));

            // Verify that error was logged
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.IsAny<Exception>(),
                    It.Is<Func<It.IsAnyType, Exception?, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }

        [Test]
        public void GetAllVisitStatusAsync_WhenAccessTokenIsEmpty_ThrowsUnauthorizedAccessException()
        {
            // Arrange
            _mockTokenService.Setup(t => t.GetValidatedAccessTokenAsync()).ReturnsAsync(string.Empty);

            // Act & Assert
            var exception = Assert.ThrowsAsync<UnauthorizedAccessException>(async () =>
                await _visitStatusService.GetAllVisitStatusAsync());

            Assert.That(exception.Message, Is.EqualTo("Access token missing"));

            // Verify that error was logged
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.IsAny<Exception>(),
                    It.Is<Func<It.IsAnyType, Exception?, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }

        [Test]
        public void GetAllVisitStatusAsync_WhenNotSuccessful_ThrowsException()
        {
            // Arrange
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.InternalServerError,
                Content = new StringContent("Internal server error")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.Is<HttpRequestMessage>(req =>
                        req.Method == HttpMethod.Get &&
                        req.RequestUri.ToString() == $"{_baseUrl}/api/VisitStatus"),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _visitStatusService.GetAllVisitStatusAsync());

            Assert.That(exception, Is.Not.Null);

            // Verify that error was logged
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.IsAny<Exception>(),
                    It.Is<Func<It.IsAnyType, Exception?, string>>((v, t) => true)),
                Times.Once);
        }

        [Test]
        public async Task GetAllVisitStatusAsync_WhenEmptyResponse_ReturnsEmptyCollection()
        {
            // Arrange
            var expectedStatuses = new List<VisitStatus>();

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(JsonSerializer.Serialize(expectedStatuses, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                }))
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.Is<HttpRequestMessage>(req =>
                        req.Method == HttpMethod.Get &&
                        req.RequestUri.ToString() == $"{_baseUrl}/api/VisitStatus" &&
                        req.Headers.Authorization.Scheme == "Bearer" &&
                        req.Headers.Authorization.Parameter == _accessToken),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _visitStatusService.GetAllVisitStatusAsync();

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(0));
        }

        [Test]
        public void GetAllVisitStatusAsync_WhenExceptionThrown_PropagatesException()
        {
            // Arrange
            var expectedException = new HttpRequestException("Test exception");

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.Is<HttpRequestMessage>(req =>
                        req.Method == HttpMethod.Get &&
                        req.RequestUri.ToString() == $"{_baseUrl}/api/VisitStatus"),
                    ItExpr.IsAny<CancellationToken>())
                .ThrowsAsync(expectedException);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _visitStatusService.GetAllVisitStatusAsync());

            Assert.That(exception, Is.Not.Null);

            // Verify that error was logged
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.Is<Exception>(ex => ex == expectedException),
                    It.Is<Func<It.IsAnyType, Exception?, string>>((v, t) => true)),
                Times.Once);
        }

        [Test]
        public async Task GetVisitStatus_StatusAsync_WhenSuccessful_ReturnsStatusNames()
        {
            // Arrange
            var expectedStatuses = new List<VisitStatus>
            {
                new VisitStatus
                {
                    ID = Guid.NewGuid(),
                    Visitstatus = "Pending",
                    OrganizationId = Guid.NewGuid()
                },
                new VisitStatus
                {
                    ID = Guid.NewGuid(),
                    Visitstatus = "Confirmed",
                    OrganizationId = Guid.NewGuid()
                },
                new VisitStatus
                {
                    ID = Guid.NewGuid(),
                    Visitstatus = "Completed",
                    OrganizationId = Guid.NewGuid()
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(JsonSerializer.Serialize(expectedStatuses, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                }))
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.Is<HttpRequestMessage>(req =>
                        req.Method == HttpMethod.Get &&
                        req.RequestUri.ToString() == $"{_baseUrl}/api/VisitStatus" &&
                        req.Headers.Authorization.Scheme == "Bearer" &&
                        req.Headers.Authorization.Parameter == _accessToken),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _visitStatusService.GetVisitStatus_StatusAsync();

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(3));
            Assert.That(result[0], Is.EqualTo("Pending"));
            Assert.That(result[1], Is.EqualTo("Confirmed"));
            Assert.That(result[2], Is.EqualTo("Completed"));
        }

        [Test]
        public void GetVisitStatus_StatusAsync_WhenGetAllVisitStatusAsyncThrows_PropagatesException()
        {
            // Arrange
            var expectedException = new Exception("Test exception");

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ThrowsAsync(expectedException);

            // Act & Assert
            var exception = Assert.ThrowsAsync<Exception>(async () =>
                await _visitStatusService.GetVisitStatus_StatusAsync());

            Assert.That(exception, Is.Not.Null);

            // Verify that error was logged
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.Is<Exception>(ex => ex == expectedException),
                    It.Is<Func<It.IsAnyType, Exception?, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }
    }
}
