﻿@page "/reviewrequests"
@using TeyaUIModels.Model
@using TeyaUIViewModels.ViewModel
@using Microsoft.Extensions.Localization
@using Microsoft.Extensions.Logging
@using Microsoft.AspNetCore.Authorization
@attribute [Authorize]
@inject IStringLocalizer<TeyaWebApp.TeyaAIScribeResource.TeyaAIScribeResource> Localizer
@using TeyaWebApp.TeyaAIScribeResource
@using TeyaWebApp.Components.Layout
@layout Admin

<PageTitle>@Localizer["ReviewRequests"]</PageTitle>

<MudContainer MaxWidth="MaxWidth.False" Class="pa-6">
    <!-- Main Header -->
    <MudStack Row AlignItems="AlignItems.Center" Justify="Justify.SpaceBetween" Class="mb-6">
        <MudStack Row AlignItems="AlignItems.Center" Spacing="3">
            <MudIcon Icon="@Icons.Material.Filled.RateReview" Color="Color.Primary" Size="Size.Large" />
            <MudStack Spacing="1">
                <MudText Typo="Typo.h4" Style="font-weight: 700; color: #1976d2;">
                    @Localizer["ReviewRequests"]
                </MudText>
                <MudText Typo="Typo.body2" Style="color: #666;">
                    @Localizer["ManageIncomingCosigningRequests"]
                </MudText>
            </MudStack>
        </MudStack>

        <!-- Status Summary -->
        <MudStack Row Spacing="2">
            <MudChip T="string" Color="Color.Warning" Size="Size.Medium" Icon="@Icons.Material.Filled.Schedule">
                @_reviewRequests.Count(r => r.Status == CosigningRequestStatus.Pending) @Localizer["Pending"]
            </MudChip>
            <MudChip T="string" Color="Color.Success" Size="Size.Medium" Icon="@Icons.Material.Filled.CheckCircle">
                @_reviewRequests.Count(r => r.Status == CosigningRequestStatus.Approved) @Localizer["Approved"]
            </MudChip>
            <MudChip T="string" Color="Color.Error" Size="Size.Medium" Icon="@Icons.Material.Filled.Comment">
                @_reviewRequests.Count(r => r.Status == CosigningRequestStatus.ChangesRequested) @Localizer["ChangesRequested"]
            </MudChip>
        </MudStack>
    </MudStack>

    <!-- Main Content -->
    @if (!_showReviewForm)
    {
        <!-- Loading State -->
        @if (_isLoading)
        {
            <MudPaper Class="pa-8" Elevation="2" Style="border-radius: 12px;">
                <MudStack AlignItems="AlignItems.Center" Spacing="3">
                    <MudProgressCircular Color="Color.Primary" Size="Size.Large" Indeterminate="true" />
                    <MudText Typo="Typo.h6">@Localizer["LoadingRequests"]</MudText>
                    <MudText Typo="Typo.body2" Style="color: #666;">@Localizer["PleaseWait"]</MudText>
                </MudStack>
            </MudPaper>
        }
        else if (!_reviewRequests.Any())
        {
            <!-- Empty State -->
            <MudPaper Class="pa-8" Elevation="2" Style="border-radius: 12px; text-align: center;">
                <MudStack AlignItems="AlignItems.Center" Spacing="4">
                    <MudIcon Icon="@Icons.Material.Filled.RateReview" Size="Size.Large" Style="color: #9e9e9e; font-size: 4rem;" />
                    <MudStack Spacing="2" AlignItems="AlignItems.Center">
                        <MudText Typo="Typo.h5" Style="color: #666; font-weight: 600;">
                            @Localizer["NoReviewRequestsFound"]
                        </MudText>
                        <MudText Typo="Typo.body1" Style="color: #999; max-width: 400px;">
                            @Localizer["NoIncomingRequestsMessage"]
                        </MudText>
                    </MudStack>
                    <MudButton Variant="Variant.Outlined"
                               Color="Color.Primary"
                               OnClick="LoadReviewRequests"
                               StartIcon="@Icons.Material.Filled.Refresh">
                        @Localizer["Refresh"]
                    </MudButton>
                </MudStack>
            </MudPaper>
        }
        else
        {
            <!-- GitHub-style Request Cards -->
            <MudStack Spacing="3">
                @foreach (var request in _reviewRequests.OrderByDescending(r => r.RequestedDate))
                {
                    <MudPaper Class="request-card"
                              Elevation="2"
                              Style="@GetRequestCardStyle(request)"
                              @onclick="@(() => OpenReviewForm(request))">
                        <MudStack Row AlignItems="AlignItems.Center" Justify="Justify.SpaceBetween" Class="pa-4">
                            <!-- Left Section: Request Info -->
                            <MudStack Row AlignItems="AlignItems.Center" Spacing="3">
                                <!-- Status Indicator -->
                                <div class="status-indicator @GetStatusClass(request.Status)"></div>

                                <!-- Request Details -->
                                <MudStack Spacing="1">
                                    <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
                                        <MudIcon Icon="@Icons.Material.Filled.Person"
                                                 Color="Color.Primary"
                                                 Size="Size.Small" />
                                        <MudText Typo="Typo.subtitle1" Style="font-weight: 600;">
                                            @request.PatientName
                                        </MudText>
                                        <MudText Typo="Typo.body2" Style="color: #666;">
                                            (@request.PatientAge, @request.PatientGender)
                                        </MudText>
                                    </MudStack>

                                    <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
                                        <MudIcon Icon="@Icons.Material.Filled.MedicalServices"
                                                 Color="Color.Secondary"
                                                 Size="Size.Small" />
                                        <MudText Typo="Typo.body2">
                                            @Localizer["RequestedBy"]: @request.RequesterName
                                        </MudText>
                                        <MudText Typo="Typo.caption" Style="color: #999;">
                                            @request.RequestedDate.ToString("MMM dd, yyyy 'at' h:mm tt")
                                        </MudText>
                                    </MudStack>
                                </MudStack>
                            </MudStack>

                            <!-- Right Section: Status & Actions -->
                            <MudStack Row AlignItems="AlignItems.Center" Spacing="3">
                                <!-- Status Badge -->
                                <MudChip T="string"
                                         Color="@GetStatusColor(request.Status)"
                                         Size="Size.Medium"
                                         Icon="@GetStatusIcon(request.Status)"
                                         Variant="Variant.Filled">
                                    @GetStatusText(request.Status)
                                </MudChip>

                                <!-- Action Button -->
                                @if (request.Status == CosigningRequestStatus.Pending)
                                {
                                    <MudButton Variant="Variant.Filled"
                                               Color="Color.Success"
                                               Size="Size.Medium"
                                               StartIcon="@Icons.Material.Filled.RateReview"
                                               OnClick="@(async (e) => { e.StopPropagation(); await OpenReviewForm(request); })"
                                               Class="review-button">
                                        @Localizer["Review"]
                                    </MudButton>
                                }
                                else if (request.Status == CosigningRequestStatus.ChangesRequested)
                                {
                                    <MudButton Variant="Variant.Outlined"
                                               Color="Color.Warning"
                                               Size="Size.Medium"
                                               StartIcon="@Icons.Material.Filled.Comment"
                                               OnClick="@((e) => { e.StopPropagation(); ViewComments(request); })"
                                               Class="comment-button">
                                        @Localizer["ViewComments"]
                                    </MudButton>
                                }
                                else if (request.Status == CosigningRequestStatus.Approved)
                                {
                                    <MudButton Variant="Variant.Text"
                                               Color="Color.Success"
                                               Size="Size.Medium"
                                               StartIcon="@Icons.Material.Filled.CheckCircle"
                                               OnClick="@((e) => { e.StopPropagation(); ViewComments(request); })"
                                               Class="approved-button">
                                        @Localizer["ViewDetails"]
                                    </MudButton>
                                }

                                <!-- Expand Arrow -->
                                <MudIconButton Icon="@Icons.Material.Filled.ChevronRight"
                                               Size="Size.Small"
                                               Color="Color.Default"
                                               Style="opacity: 0.6;" />
                            </MudStack>
                        </MudStack>

                        <!-- Comments Preview (if any) -->
                        @if (!string.IsNullOrEmpty(request.CommentsJson) && request.CommentsJson != "[]")
                        {
                            <MudDivider />
                            <div class="pa-3" style="background: #f8f9fa;">
                                <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
                                    <MudIcon Icon="@Icons.Material.Filled.Comment"
                                             Size="Size.Small"
                                             Color="Color.Info" />
                                    <MudText Typo="Typo.caption" Style="color: #666;">
                                        @GetCommentsPreview(request.CommentsJson)
                                    </MudText>
                                </MudStack>
                            </div>
                        }
                    </MudPaper>
                }
            </MudStack>
        }
    }
    <!-- GitHub-style Review Form -->
    @if (_showReviewForm && _selectedRequest != null)
    {
        <MudPaper Class="mt-6" Elevation="4" Style="border-radius: 12px; overflow: hidden;">
            <!-- Review Header -->
            <div class="review-header">
                <MudStack Row AlignItems="AlignItems.Center" Justify="Justify.SpaceBetween" Class="pa-6">
                    <MudStack Row AlignItems="AlignItems.Center" Spacing="3">
                        <MudAvatar Color="Color.Primary" Size="Size.Large">
                            <MudIcon Icon="@Icons.Material.Filled.RateReview" />
                        </MudAvatar>
                        <MudStack Spacing="1">
                            <MudText Typo="Typo.h5" Style="font-weight: 700; color: white;">
                                @Localizer["ReviewingNotes"]
                            </MudText>
                            <MudText Typo="Typo.body2" Style="color: rgba(255,255,255,0.8);">
                                @_selectedRequest.PatientName • @Localizer["RequestedBy"] @_selectedRequest.RequesterName
                            </MudText>
                        </MudStack>
                    </MudStack>

                    <MudStack Row Spacing="2">
                        <MudButton Variant="Variant.Text"
                                   Color="Color.Inherit"
                                   OnClick="CloseReviewForm"
                                   StartIcon="@Icons.Material.Filled.ArrowBack"
                                   Style="color: white;">
                            @Localizer["BackToList"]
                        </MudButton>
                    </MudStack>
                </MudStack>
            </div>

            <!-- Request Summary Card -->
            <div class="pa-6" style="background: #f8f9fa;">
                <MudGrid>
                    <MudItem xs="12" md="8">
                        <MudStack Row AlignItems="AlignItems.Center" Spacing="4">
                            <MudStack Spacing="1">
                                <MudText Typo="Typo.caption" Style="color: #666; text-transform: uppercase; font-weight: 600;">
                                    @Localizer["Patient"]
                                </MudText>
                                <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
                                    <MudIcon Icon="@Icons.Material.Filled.Person" Color="Color.Primary" Size="Size.Small" />
                                    <MudText Typo="Typo.subtitle1" Style="font-weight: 600;">
                                        @_selectedRequest.PatientName
                                    </MudText>
                                    <MudText Typo="Typo.body2" Style="color: #666;">
                                        (@_selectedRequest.PatientAge, @_selectedRequest.PatientGender)
                                    </MudText>
                                </MudStack>
                            </MudStack>

                            <MudDivider Vertical="true" FlexItem="true" />

                            <MudStack Spacing="1">
                                <MudText Typo="Typo.caption" Style="color: #666; text-transform: uppercase; font-weight: 600;">
                                    @Localizer["RequestedOn"]
                                </MudText>
                                <MudText Typo="Typo.body2">
                                    @_selectedRequest.RequestedDate.ToString("MMM dd, yyyy 'at' h:mm tt")
                                </MudText>
                            </MudStack>
                        </MudStack>
                    </MudItem>

                    <MudItem xs="12" md="4">
                        <MudStack AlignItems="AlignItems.End" Spacing="2">
                            <MudChip T="string"
                                     Color="@GetStatusColor(_selectedRequest.Status)"
                                     Size="Size.Large"
                                     Icon="@GetStatusIcon(_selectedRequest.Status)"
                                     Variant="Variant.Filled">
                                @GetStatusText(_selectedRequest.Status)
                            </MudChip>

                            @if (_selectedRequest.Status == CosigningRequestStatus.Pending)
                            {
                                <MudStack Row Spacing="2">
                                    <MudButton Variant="Variant.Outlined"
                                               Color="Color.Warning"
                                               OnClick="ShowRequestChangesDialog"
                                               StartIcon="@Icons.Material.Filled.Comment"
                                               Disabled="@_isProcessing">
                                        @Localizer["RequestChanges"]
                                    </MudButton>
                                    <MudButton Variant="Variant.Filled"
                                               Color="Color.Success"
                                               OnClick="ApproveRequest"
                                               StartIcon="@Icons.Material.Filled.CheckCircle"
                                               Disabled="@_isProcessing">
                                        @if (_isProcessing)
                                        {
                                            <MudProgressCircular Size="Size.Small" Indeterminate="true" />
                                            @Localizer["Processing"]
                                        }
                                        else
                                        {
                                            @Localizer["Approve"]
                                        }
                                    </MudButton>
                                </MudStack>
                            }
                        </MudStack>
                    </MudItem>
                </MudGrid>
            </div>

            <!-- Notes Content (GitHub-style review) -->
            @if (_isLoadingRecord)
            {
                <div class="pa-8" style="background: white;">
                    <MudStack AlignItems="AlignItems.Center" Spacing="3">
                        <MudProgressCircular Color="Color.Primary" Size="Size.Large" Indeterminate="true" />
                        <MudText Typo="Typo.h6">@Localizer["LoadingNotes"]</MudText>
                        <MudText Typo="Typo.body2" Style="color: #666;">@Localizer["PleaseWait"]</MudText>
                    </MudStack>
                </div>
            }
            else if (_selectedRecord != null)
            {
                <!-- Notes Review Interface -->
                <div class="notes-review-container">
                    @{
                        var NotesData = ExtractNotesData(_selectedRecord.Notes);
                    }

                    @foreach (var section in NotesData)
                    {
                        @foreach (var kvp in section)
                        {
                            <div class="review-section-card" data-section="@kvp.Key">
                                <!-- Section Header -->
                                <div class="section-header">
                                    <MudStack Row AlignItems="AlignItems.Center" Justify="Justify.SpaceBetween" Class="pa-4">
                                        <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
                                            <MudIcon Icon="@Icons.Material.Filled.Description"
                                                     Color="Color.Primary"
                                                     Size="Size.Medium" />
                                            <MudText Typo="Typo.h6" Style="font-weight: 600; color: #1976d2;">
                                                @kvp.Key
                                            </MudText>
                                            @if (GetSectionCommentCount(kvp.Key) > 0)
                                            {
                                                <MudChip T="string"
                                                         Color="Color.Info"
                                                         Size="Size.Small"
                                                         Icon="@Icons.Material.Filled.Comment">
                                                    @GetSectionCommentCount(kvp.Key)
                                                </MudChip>
                                            }
                                        </MudStack>

                                        <MudButton Variant="Variant.Text"
                                                   Color="Color.Primary"
                                                   Size="Size.Small"
                                                   StartIcon="@Icons.Material.Filled.Comment"
                                                   OnClick="@(() => AddSectionComment(kvp.Key))">
                                            @Localizer["AddComment"]
                                        </MudButton>
                                    </MudStack>
                                </div>

                                <!-- Section Content -->
                                <div class="section-content">
                                    @foreach (var data in kvp.Value.Where(d => d.Key != "Transcription"))
                                    {
                                        <div class="subsection-item" data-subsection="@data.Key">
                                            <!-- Subsection Header -->
                                            <div class="subsection-header">
                                                <MudStack Row AlignItems="AlignItems.Center" Justify="Justify.SpaceBetween" Class="pa-3">
                                                    <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
                                                        <MudText Typo="Typo.subtitle1" Style="font-weight: 600; color: #333;">
                                                            @data.Key
                                                        </MudText>
                                                        @if (GetSubsectionCommentCount(kvp.Key, data.Key) > 0)
                                                        {
                                                            <MudChip T="string"
                                                                     Color="Color.Warning"
                                                                     Size="Size.Small"
                                                                     Icon="@Icons.Material.Filled.Comment">
                                                                @GetSubsectionCommentCount(kvp.Key, data.Key)
                                                            </MudChip>
                                                        }
                                                    </MudStack>

                                                    <MudIconButton Icon="@Icons.Material.Filled.Comment"
                                                                   Color="Color.Secondary"
                                                                   Size="Size.Small"
                                                                   OnClick="@(() => AddSubsectionComment(kvp.Key, data.Key))"
                                                                   Title="@Localizer["AddComment"]" />
                                                </MudStack>
                                            </div>

                                            <!-- Content Display -->
                                            <div class="content-display">
                                                @if (!string.IsNullOrWhiteSpace(data.Value))
                                                {
                                                    <div class="content-text">
                                                        @((MarkupString)CleanHtml(data.Value))
                                                    </div>
                                                }
                                                else
                                                {
                                                    var editorContent = GetEditorContent(_selectedRecord, kvp.Key, data.Key);
                                                    if (!string.IsNullOrWhiteSpace(editorContent))
                                                    {
                                                        <div class="content-text">
                                                            @((MarkupString)CleanHtml(editorContent))
                                                        </div>
                                                    }
                                                    else
                                                    {
                                                        <div class="empty-content">
                                                            <MudText Typo="Typo.body2" Style="color: #999; font-style: italic;">
                                                                @Localizer["NoContent"]
                                                            </MudText>
                                                        </div>
                                                    }
                                                }
                                            </div>

                                            <!-- Comments Thread -->
                                            @if (_sectionComments.ContainsKey($"{kvp.Key}|{data.Key}"))
                                            {
                                                <div class="comments-thread">
                                                    @foreach (var comment in _sectionComments[$"{kvp.Key}|{data.Key}"].OrderBy(c => c.CommentDate))
                                                    {
                                                        <div class="comment-item">
                                                            <MudStack Row AlignItems="AlignItems.Start" Spacing="3" Class="pa-3">
                                                                <MudAvatar Color="Color.Secondary" Size="Size.Small">
                                                                    @comment.CommenterName.Substring(0, 1).ToUpper()
                                                                </MudAvatar>
                                                                <MudStack Spacing="1" Style="flex: 1;">
                                                                    <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
                                                                        <MudText Typo="Typo.subtitle2" Style="font-weight: 600;">
                                                                            @comment.CommenterName
                                                                        </MudText>
                                                                        <MudText Typo="Typo.caption" Style="color: #666;">
                                                                            @comment.CommentDate.ToString("MMM dd, yyyy 'at' h:mm tt")
                                                                        </MudText>
                                                                    </MudStack>
                                                                    <MudText Typo="Typo.body2" Style="line-height: 1.5;">
                                                                        @comment.Comment
                                                                    </MudText>
                                                                </MudStack>
                                                            </MudStack>
                                                        </div>
                                                    }
                                                </div>
                                            }
                                        </div>
                                    }
                                </div>
                            </div>
                        }
                    }
                </div>
            }
        </MudPaper>
    }
</MudContainer>

<!-- Comment Dialog -->
<MudDialog @bind-IsVisible="_showCommentDialog" Options="_commentDialogOptions">
    <TitleContent>
        <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
            <MudIcon Icon="@Icons.Material.Filled.Comment" Color="Color.Primary" />
            <MudText Typo="Typo.h6">@Localizer["AddComment"]</MudText>
        </MudStack>
    </TitleContent>
    <DialogContent>
        <MudStack Spacing="3">
            <MudText Typo="Typo.body2" Style="color: #666;">
                @Localizer["CommentingOn"]: <strong>@_commentContext</strong>
            </MudText>
            <MudTextField @bind-Value="_newComment"
                          Label="@Localizer["Comment"]"
                          Placeholder="@Localizer["EnterYourComment"]"
                          Lines="4"
                          Variant="Variant.Outlined"
                          HelperText="@Localizer["CommentHelperText"]" />
        </MudStack>
    </DialogContent>
    <DialogActions>
        <MudButton OnClick="CloseCommentDialog">@Localizer["Cancel"]</MudButton>
        <MudButton Color="Color.Primary"
                   Variant="Variant.Filled"
                   OnClick="SubmitComment"
                   Disabled="@(string.IsNullOrWhiteSpace(_newComment) || _isProcessing)">
            @Localizer["AddComment"]
        </MudButton>
    </DialogActions>
</MudDialog>

<!-- Request Changes Dialog -->
<MudDialog @bind-IsVisible="_showRequestChangesDialog" Options="_commentDialogOptions">
    <TitleContent>
        <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
            <MudIcon Icon="@Icons.Material.Filled.Comment" Color="Color.Warning" />
            <MudText Typo="Typo.h6">@Localizer["RequestChanges"]</MudText>
        </MudStack>
    </TitleContent>
    <DialogContent>
        <MudStack Spacing="3">
            <MudAlert Severity="Severity.Warning" Class="mb-3">
                @Localizer["RequestChangesWarning"]
            </MudAlert>
            <MudText Typo="Typo.body2" Style="color: #666;">
                @Localizer["ExplainChangesNeeded"]
            </MudText>
            <MudTextField @bind-Value="_requestChangesComment"
                          Label="@Localizer["ChangesRequested"]"
                          Placeholder="@Localizer["DescribeChangesNeeded"]"
                          Lines="5"
                          Variant="Variant.Outlined"
                          HelperText="@Localizer["RequestChangesHelperText"]" />
        </MudStack>
    </DialogContent>
    <DialogActions>
        <MudButton OnClick="CloseRequestChangesDialog">@Localizer["Cancel"]</MudButton>
        <MudButton Color="Color.Warning"
                   Variant="Variant.Filled"
                   OnClick="SubmitRequestChanges"
                   Disabled="@(string.IsNullOrWhiteSpace(_requestChangesComment) || _isProcessing)">
            @if (_isProcessing)
            {
                <MudProgressCircular Size="Size.Small" Indeterminate="true" />
                @Localizer["Processing"]
            }
            else
            {
                @Localizer["RequestChanges"]
            }
        </MudButton>
    </DialogActions>
</MudDialog>

<style>
    .requests-table {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    .text-muted {
        color: #6c757d;
    }

    .mud-table-row:hover {
        background-color: rgba(25, 118, 210, 0.04) !important;
        cursor: pointer;
    }

    /* GitHub-style review form styles */
    .review-section {
        margin-bottom: 1rem;
    }

    .review-card {
        border: 1px solid #e1e4e8;
        border-radius: 6px;
        transition: box-shadow 0.2s ease;
    }

    .review-card:hover {
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .review-subsection {
        border-left: 3px solid #f6f8fa;
        padding-left: 1rem;
        margin: 0.5rem 0;
    }

    .review-content {
        background-color: #f6f8fa;
        border: 1px solid #e1e4e8;
        border-radius: 6px;
        padding: 1rem;
        margin: 0.5rem 0;
        position: relative;
    }

    .content-display {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        line-height: 1.6;
        color: #24292e;
    }

    .comments-section {
        border-top: 1px solid #e1e4e8;
        padding-top: 0.5rem;
    }

    .section-heading {
        color: #1976d2 !important;
        font-weight: 600 !important;
        border-bottom: 2px solid #e1e4e8;
        padding-bottom: 0.5rem;
    }

    /* GitHub-style Request Cards */
    .request-card {
        border-radius: 12px;
        border: 1px solid #e1e4e8;
        transition: all 0.2s ease;
        cursor: pointer;
        background: white;
    }

    .request-card:hover {
        border-color: #1976d2;
        box-shadow: 0 4px 12px rgba(25, 118, 210, 0.15);
        transform: translateY(-1px);
    }

    .status-indicator {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        flex-shrink: 0;
    }

    .status-indicator.pending {
        background-color: #ff9800;
        box-shadow: 0 0 0 3px rgba(255, 152, 0, 0.2);
    }

    .status-indicator.approved {
        background-color: #4caf50;
        box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.2);
    }

    .status-indicator.changes-requested {
        background-color: #f44336;
        box-shadow: 0 0 0 3px rgba(244, 67, 54, 0.2);
    }

    /* Review Form Styles */
    .review-header {
        background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
        color: white;
    }

    .notes-review-container {
        background: white;
    }

    .review-section-card {
        border: 1px solid #e1e4e8;
        border-radius: 8px;
        margin-bottom: 16px;
        overflow: hidden;
        background: white;
    }

    .section-header {
        background: #f6f8fa;
        border-bottom: 1px solid #e1e4e8;
    }

    .section-content {
        background: white;
    }

    .subsection-item {
        border-bottom: 1px solid #f0f0f0;
    }

    .subsection-item:last-child {
        border-bottom: none;
    }

    .subsection-header {
        background: #fafbfc;
        border-bottom: 1px solid #e1e4e8;
    }

    .content-display {
        padding: 16px;
        background: white;
        border-bottom: 1px solid #f0f0f0;
    }

    .content-text {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        line-height: 1.6;
        color: #24292e;
        background: #f8f9fa;
        padding: 12px;
        border-radius: 6px;
        border: 1px solid #e1e4e8;
    }

    .empty-content {
        padding: 24px;
        text-align: center;
        background: #f8f9fa;
        border-radius: 6px;
        border: 1px dashed #d0d7de;
    }

    .comments-thread {
        background: #f6f8fa;
        border-top: 1px solid #e1e4e8;
    }

    .comment-item {
        background: white;
        border: 1px solid #e1e4e8;
        border-radius: 6px;
        margin: 8px 12px;
    }

    .comment-item:first-child {
        margin-top: 12px;
    }

    .comment-item:last-child {
        margin-bottom: 12px;
    }

    /* Button Styles */
    .review-button {
        background: linear-gradient(135deg, #4caf50 0%, #45a049 100%);
        border: none;
        box-shadow: 0 2px 4px rgba(76, 175, 80, 0.3);
    }

    .review-button:hover {
        background: linear-gradient(135deg, #45a049 0%, #3d8b40 100%);
        box-shadow: 0 4px 8px rgba(76, 175, 80, 0.4);
    }

    .comment-button {
        border-color: #ff9800;
        color: #ff9800;
    }

    .comment-button:hover {
        background: #ff9800;
        color: white;
    }

    .approved-button {
        color: #4caf50;
    }

    .approved-button:hover {
        background: rgba(76, 175, 80, 0.1);
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .request-card .pa-4 {
            padding: 16px !important;
        }

        .content-text {
            padding: 8px;
        }

        .comment-item {
            margin: 4px 8px;
        }

        .review-header .pa-6 {
            padding: 16px !important;
        }
    }
</style>