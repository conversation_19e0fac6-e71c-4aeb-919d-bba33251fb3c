using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using Moq;
using Moq.Protected;
using NUnit.Framework;
using TeyaUIModels.Model;
using TeyaUIViewModels.ViewModel;
using TeyaUIViewModels.TeyaUIViewModelResources;
using System.Net.Http.Json;
using System.Text.Json;

namespace TeyaWebClientUIViewModelTestCases
{
    [TestFixture]
    public class TherapeuticInterventionsServiceTests
    {
        private Mock<HttpMessageHandler> _mockHttpMessageHandler;
        private HttpClient _httpClient;
        private Mock<IConfiguration> _mockConfiguration;
        private Mock<IStringLocalizer<TeyaUIViewModelsStrings>> _mockLocalizer;
        private Mock<ITokenService> _mockTokenService;
        private TherapeuticInterventionsService _therapeuticInterventionsService;
        private readonly string _baseUrl = "http://test-api.com";
        private readonly string _accessToken = "test-access-token";

        [SetUp]
        public void Setup()
        {
            // Setup environment variable
            Environment.SetEnvironmentVariable("EncounterNotesURL", _baseUrl);

            // Setup mock HTTP handler
            _mockHttpMessageHandler = new Mock<HttpMessageHandler>();

            // Setup HttpClient with mock handler
            _httpClient = new HttpClient(_mockHttpMessageHandler.Object)
            {
                BaseAddress = new Uri(_baseUrl)
            };

            // Setup mock configuration
            _mockConfiguration = new Mock<IConfiguration>();

            // Setup mock localizer
            _mockLocalizer = new Mock<IStringLocalizer<TeyaUIViewModelsStrings>>();
            _mockLocalizer.Setup(l => l["RecordNotFound"])
                .Returns(new LocalizedString("RecordNotFound", "Tasks retrieval failure"));
            _mockLocalizer.Setup(l => l["DatabaseError"])
                .Returns(new LocalizedString("DatabaseError", "Database error"));
            _mockLocalizer.Setup(l => l["DeleteLogError"])
                .Returns(new LocalizedString("DeleteLogError", "Database error"));

            // Setup mock token service
            _mockTokenService = new Mock<ITokenService>();
            _mockTokenService.Setup(t => t.AccessToken).Returns(_accessToken);

            // Create TherapeuticInterventionsService with mocked dependencies
            _therapeuticInterventionsService = new TherapeuticInterventionsService(_httpClient, _mockConfiguration.Object, _mockLocalizer.Object, _mockTokenService.Object);
        }

        [TearDown]
        public void TearDown()
        {
            _httpClient.Dispose();
            Environment.SetEnvironmentVariable("EncounterNotesURL", null);
        }

        [Test]
        public async Task GetAllByIdAsync_WhenSuccessful_ReturnsTherapeuticInterventions()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var expectedInterventions = new List<TherapeuticInterventionsData>
            {
                new TherapeuticInterventionsData
                {
                    TherapeuticInterventionsID = Guid.NewGuid(),
                    PatientId = patientId,
                    OrganizationId = orgId,
                    TherapyType = "Physical Therapy",
                    Notes = "Physical therapy for lower back pain - Patient responding well to treatment",
                    IsActive = true,
                    CreatedDate = DateTime.Now.AddDays(-30),
                    UpdatedDate = DateTime.Now.AddDays(-1),
                    PCPId = Guid.NewGuid(),
                    Subscription = subscription
                },
                new TherapeuticInterventionsData
                {
                    TherapeuticInterventionsID = Guid.NewGuid(),
                    PatientId = patientId,
                    OrganizationId = orgId,
                    TherapyType = "Occupational Therapy",
                    Notes = "Occupational therapy for hand function - Improving grip strength",
                    IsActive = true,
                    CreatedDate = DateTime.Now.AddDays(-20),
                    UpdatedDate = DateTime.Now.AddDays(-1),
                    PCPId = Guid.NewGuid(),
                    Subscription = subscription
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedInterventions)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _therapeuticInterventionsService.GetAllByIdAsync(patientId, orgId, subscription);

            // Assert
            Assert.Multiple(() =>
            {
                Assert.That(result, Is.Not.Null);
                Assert.That(result, Has.Count.EqualTo(expectedInterventions.Count));
                Assert.That(result[0].TherapeuticInterventionsID, Is.EqualTo(expectedInterventions[0].TherapeuticInterventionsID));
                Assert.That(result[0].PatientId, Is.EqualTo(expectedInterventions[0].PatientId));
                Assert.That(result[0].OrganizationId, Is.EqualTo(expectedInterventions[0].OrganizationId));
                Assert.That(result[0].TherapyType, Is.EqualTo(expectedInterventions[0].TherapyType));
                Assert.That(result[0].Notes, Is.EqualTo(expectedInterventions[0].Notes));
                Assert.That(result[0].IsActive, Is.EqualTo(expectedInterventions[0].IsActive));
                Assert.That(result[1].TherapyType, Is.EqualTo("Occupational Therapy"));
            });
        }

        [Test]
        public void GetAllByIdAsync_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.NotFound,
                Content = new StringContent("Not found")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _therapeuticInterventionsService.GetAllByIdAsync(patientId, orgId, subscription));

            Assert.That(exception.Message, Is.EqualTo("Tasks retrieval failure"));
        }

        [Test]
        public async Task GetAllByIdAndIsActiveAsync_WhenSuccessful_ReturnsActiveInterventions()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var expectedInterventions = new List<TherapeuticInterventionsData>
            {
                new TherapeuticInterventionsData
                {
                    TherapeuticInterventionsID = Guid.NewGuid(),
                    PatientId = patientId,
                    OrganizationId = orgId,
                    TherapyType = "Active Speech Therapy",
                    Notes = "Speech therapy for articulation - Making good progress",
                    IsActive = true,
                    CreatedDate = DateTime.Now.AddDays(-10),
                    UpdatedDate = DateTime.Now.AddDays(-1),
                    PCPId = Guid.NewGuid(),
                    Subscription = subscription
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedInterventions)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _therapeuticInterventionsService.GetAllByIdAndIsActiveAsync(patientId, orgId, subscription);

            // Assert
            Assert.Multiple(() =>
            {
                Assert.That(result, Is.Not.Null);
                Assert.That(result, Has.Count.EqualTo(expectedInterventions.Count));
                Assert.That(result[0].IsActive, Is.True);
                Assert.That(result[0].TherapyType, Is.EqualTo("Active Speech Therapy"));
                Assert.That(result[0].PatientId, Is.EqualTo(patientId));
            });
        }

        [Test]
        public void GetAllByIdAndIsActiveAsync_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _therapeuticInterventionsService.GetAllByIdAndIsActiveAsync(patientId, orgId, subscription));

            Assert.That(exception.Message, Is.EqualTo("Tasks retrieval failure"));
        }

        [Test]
        public async Task AddTherapeuticInterventionsAsync_WhenSuccessful_CompletesSuccessfully()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;
            var interventions = new List<TherapeuticInterventionsData>
            {
                new TherapeuticInterventionsData
                {
                    TherapeuticInterventionsID = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    OrganizationId = orgId,
                    TherapyType = "New Intervention",
                    Notes = "New therapeutic intervention notes",
                    IsActive = true,
                    CreatedDate = DateTime.Now,
                    UpdatedDate = DateTime.Now,
                    PCPId = Guid.NewGuid(),
                    Subscription = subscription
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.Created,
                Content = new StringContent("Success")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.DoesNotThrowAsync(async () => await _therapeuticInterventionsService.AddTherapeuticInterventionsAsync(interventions, orgId, subscription));
        }

        [Test]
        public void AddTherapeuticInterventionsAsync_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;
            var interventions = new List<TherapeuticInterventionsData>
            {
                new TherapeuticInterventionsData
                {
                    TherapeuticInterventionsID = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    OrganizationId = orgId,
                    TherapyType = "Failed Intervention",
                    Notes = "Failed therapeutic intervention notes",
                    IsActive = true,
                    CreatedDate = DateTime.Now,
                    UpdatedDate = DateTime.Now,
                    PCPId = Guid.NewGuid(),
                    Subscription = subscription
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _therapeuticInterventionsService.AddTherapeuticInterventionsAsync(interventions, orgId, subscription));

            Assert.That(exception.Message, Is.EqualTo("Database error"));
        }

        [Test]
        public async Task UpdateTherapeuticInterventionsAsync_WhenSuccessful_CompletesSuccessfully()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;
            var intervention = new TherapeuticInterventionsData
            {
                TherapeuticInterventionsID = Guid.NewGuid(),
                PatientId = Guid.NewGuid(),
                OrganizationId = orgId,
                TherapyType = "Updated Intervention",
                Notes = "Updated intervention notes",
                IsActive = true,
                CreatedDate = DateTime.Now.AddDays(-5),
                UpdatedDate = DateTime.Now,
                PCPId = Guid.NewGuid(),
                Subscription = subscription
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent("Success")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.DoesNotThrowAsync(async () => await _therapeuticInterventionsService.UpdateTherapeuticInterventionsAsync(intervention, orgId, subscription));
        }

        [Test]
        public void UpdateTherapeuticInterventionsAsync_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;
            var intervention = new TherapeuticInterventionsData
            {
                TherapeuticInterventionsID = Guid.NewGuid(),
                PatientId = Guid.NewGuid(),
                OrganizationId = orgId,
                TherapyType = "Failed Update Intervention",
                Notes = "Failed update intervention notes",
                IsActive = true,
                CreatedDate = DateTime.Now.AddDays(-5),
                UpdatedDate = DateTime.Now,
                PCPId = Guid.NewGuid(),
                Subscription = subscription
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _therapeuticInterventionsService.UpdateTherapeuticInterventionsAsync(intervention, orgId, subscription));

            Assert.That(exception, Is.Not.Null);
        }

        [Test]
        public async Task DeleteTherapeuticInterventionsAsync_WhenSuccessful_CompletesSuccessfully()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;
            var intervention = new TherapeuticInterventionsData
            {
                TherapeuticInterventionsID = Guid.NewGuid(),
                PatientId = Guid.NewGuid(),
                OrganizationId = orgId,
                TherapyType = "Intervention to Delete",
                Notes = "Intervention completed successfully",
                IsActive = false,
                CreatedDate = DateTime.Now.AddDays(-30),
                UpdatedDate = DateTime.Now.AddDays(-1),
                PCPId = Guid.NewGuid(),
                Subscription = subscription
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent("Success")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.DoesNotThrowAsync(async () => await _therapeuticInterventionsService.DeleteTherapeuticInterventionsByEntityAsync(intervention, orgId, subscription));
        }

        [Test]
        public void DeleteTherapeuticInterventionsAsync_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;
            var intervention = new TherapeuticInterventionsData
            {
                TherapeuticInterventionsID = Guid.NewGuid(),
                PatientId = Guid.NewGuid(),
                OrganizationId = orgId,
                TherapyType = "Failed Delete Intervention",
                Notes = "Failed delete intervention",
                IsActive = false,
                CreatedDate = DateTime.Now.AddDays(-30),
                UpdatedDate = DateTime.Now.AddDays(-1),
                PCPId = Guid.NewGuid(),
                Subscription = subscription
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _therapeuticInterventionsService.DeleteTherapeuticInterventionsByEntityAsync(intervention, orgId, subscription));

            Assert.That(exception.Message, Is.EqualTo("Database error"));
        }

        [Test]
        public async Task UpdateTherapeuticInterventionsListAsync_WhenSuccessful_CompletesSuccessfully()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;
            var interventions = new List<TherapeuticInterventionsData>
            {
                new TherapeuticInterventionsData
                {
                    TherapeuticInterventionsID = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    OrganizationId = orgId,
                    TherapyType = "Batch Update Intervention 1",
                    Notes = "Batch updated notes 1",
                    IsActive = true,
                    CreatedDate = DateTime.Now.AddDays(-10),
                    UpdatedDate = DateTime.Now,
                    PCPId = Guid.NewGuid(),
                    Subscription = subscription
                },
                new TherapeuticInterventionsData
                {
                    TherapeuticInterventionsID = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    OrganizationId = orgId,
                    TherapyType = "Batch Update Intervention 2",
                    Notes = "Batch updated notes 2",
                    IsActive = true,
                    CreatedDate = DateTime.Now.AddDays(-5),
                    UpdatedDate = DateTime.Now,
                    PCPId = Guid.NewGuid(),
                    Subscription = subscription
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent("Success")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.DoesNotThrowAsync(async () => await _therapeuticInterventionsService.UpdateTherapeuticInterventionsListAsync(interventions, orgId, subscription));
        }

        [Test]
        public void UpdateTherapeuticInterventionsListAsync_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;
            var interventions = new List<TherapeuticInterventionsData>
            {
                new TherapeuticInterventionsData
                {
                    TherapeuticInterventionsID = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    OrganizationId = orgId,
                    TherapyType = "Failed Batch Update Intervention",
                    Notes = "Failed batch update notes",
                    IsActive = true,
                    CreatedDate = DateTime.Now.AddDays(-10),
                    UpdatedDate = DateTime.Now,
                    PCPId = Guid.NewGuid(),
                    Subscription = subscription
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _therapeuticInterventionsService.UpdateTherapeuticInterventionsListAsync(interventions, orgId, subscription));

            Assert.That(exception, Is.Not.Null);
        }

        [Test]
        public async Task GetAllByIdAsync_WhenEmptyResponse_ReturnsEmptyList()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var expectedInterventions = new List<TherapeuticInterventionsData>();

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedInterventions)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _therapeuticInterventionsService.GetAllByIdAsync(patientId, orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(0));
        }

        [Test]
        public void GetAllByIdAsync_WhenExceptionThrown_PropagatesException()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var expectedException = new HttpRequestException("Test exception");

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ThrowsAsync(expectedException);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _therapeuticInterventionsService.GetAllByIdAsync(patientId, orgId, subscription));

            Assert.That(exception.Message, Is.EqualTo("Test exception"));
            Assert.That(exception, Is.EqualTo(expectedException));
        }
    }
}



