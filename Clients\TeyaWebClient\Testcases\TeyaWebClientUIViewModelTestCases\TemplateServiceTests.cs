using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using Moq;
using Moq.Protected;
using NUnit.Framework;
using TeyaUIModels.Model;
using TeyaUIViewModels.ViewModel;
using TeyaUIViewModels.TeyaUIViewModelResources;
using System.Net.Http.Json;
using System.Text.Json;

namespace TeyaWebClientUIViewModelTestCases
{
    [TestFixture]
    public class TemplateServiceTests
    {
        private Mock<HttpMessageHandler> _mockHttpMessageHandler;
        private HttpClient _httpClient;
        private Mock<IConfiguration> _mockConfiguration;
        private Mock<IStringLocalizer<TeyaUIViewModelsStrings>> _mockLocalizer;
        private Mock<ITokenService> _mockTokenService;
        private TemplateService _templateService;
        private readonly string _baseUrl = "http://test-api.com";
        private readonly string _accessToken = "test-access-token";

        [SetUp]
        public void Setup()
        {
            // Setup environment variable
            Environment.SetEnvironmentVariable("EncounterNotesURL", _baseUrl);

            // Setup mock HTTP handler
            _mockHttpMessageHandler = new Mock<HttpMessageHandler>();

            // Setup HttpClient with mock handler
            _httpClient = new HttpClient(_mockHttpMessageHandler.Object)
            {
                BaseAddress = new Uri(_baseUrl)
            };

            // Setup mock configuration
            _mockConfiguration = new Mock<IConfiguration>();

            // Setup mock localizer
            _mockLocalizer = new Mock<IStringLocalizer<TeyaUIViewModelsStrings>>();
            _mockLocalizer.Setup(l => l["AppointmentRetrievalFailure"])
                .Returns(new LocalizedString("AppointmentRetrievalFailure", "Appointment retrieval failure"));
            _mockLocalizer.Setup(l => l["TemplateRetrievalFailure"])
                .Returns(new LocalizedString("TemplateRetrievalFailure", "Template retrieval failure"));
            _mockLocalizer.Setup(l => l["Error"])
                .Returns(new LocalizedString("Error", "Error"));

            // Setup mock token service
            _mockTokenService = new Mock<ITokenService>();
            _mockTokenService.Setup(t => t.AccessToken).Returns(_accessToken);

            // Create TemplateService with mocked dependencies
            _templateService = new TemplateService(_httpClient, _mockConfiguration.Object, _mockLocalizer.Object, _mockTokenService.Object);
        }

        [TearDown]
        public void TearDown()
        {
            _httpClient.Dispose();
            Environment.SetEnvironmentVariable("EncounterNotesURL", null);
        }

        [Test]
        public async Task GetTemplatesAsync_WhenSuccessful_ReturnsTemplates()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;
            var expectedTemplates = new List<TemplateData>
            {
                new TemplateData
                {
                    Id = Guid.NewGuid(),
                    TemplateName = "General Consultation Template",
                    Template = "Patient presents with...",
                    VisitType = "Consultation",
                    IsDefault = true,
                    CreatedDate = DateTime.Now.AddDays(-30),
                    OrganizationId = orgId
                },
                new TemplateData
                {
                    Id = Guid.NewGuid(),
                    TemplateName = "Follow-up Template",
                    Template = "Follow-up visit for...",
                    VisitType = "Follow-up",
                    IsDefault = true,
                    CreatedDate = DateTime.Now.AddDays(-15),
                    OrganizationId = orgId
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedTemplates)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _templateService.GetTemplatesAsync(orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(expectedTemplates.Count));
            Assert.That(result[0].Id, Is.EqualTo(expectedTemplates[0].Id));
            Assert.That(result[0].TemplateName, Is.EqualTo(expectedTemplates[0].TemplateName));
            Assert.That(result[0].Template, Is.EqualTo(expectedTemplates[0].Template));
            Assert.That(result[0].VisitType, Is.EqualTo(expectedTemplates[0].VisitType));
            Assert.That(result[0].IsDefault, Is.EqualTo(expectedTemplates[0].IsDefault));
            Assert.That(result[0].OrganizationId, Is.EqualTo(expectedTemplates[0].OrganizationId));
        }

        [Test]
        public void GetTemplatesAsync_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.NotFound,
                Content = new StringContent("Not found")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _templateService.GetTemplatesAsync(orgId, subscription));

            Assert.That(exception.Message, Is.EqualTo("Appointment retrieval failure"));
        }

        [Test]
        public async Task GetTemplatesByIdAsync_WhenSuccessful_ReturnsTemplates()
        {
            // Arrange
            var templateId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var expectedTemplates = new List<TemplateData>
            {
                new TemplateData
                {
                    Id = templateId,
                    TemplateName = "Specific Template",
                    Template = "Specific template content...",
                    VisitType = "Specific",
                    IsDefault = true,
                    CreatedDate = DateTime.Now.AddDays(-10),
                    OrganizationId = orgId
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(JsonSerializer.Serialize(expectedTemplates, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                }))
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _templateService.GetTemplatesByIdAsync(templateId, orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(1));
            Assert.That(result[0].Id, Is.EqualTo(templateId));
            Assert.That(result[0].TemplateName, Is.EqualTo("Specific Template"));
        }

        [Test]
        public async Task GetTemplatesByIdAsync_WhenNotSuccessful_ReturnsNull()
        {
            // Arrange
            var templateId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.NotFound,
                Content = new StringContent("Not found")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _templateService.GetTemplatesByIdAsync(templateId, orgId, subscription);

            // Assert
            Assert.That(result, Is.Null);
        }

        [Test]
        public async Task GetTemplatesByPCPIdAsync_WhenSuccessful_ReturnsTemplates()
        {
            // Arrange
            var pcpId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var expectedTemplates = new List<TemplateData>
            {
                new TemplateData
                {
                    Id = Guid.NewGuid(),
                    TemplateName = "PCP Template 1",
                    Template = "PCP specific template content 1...",
                    VisitType = "PCP",
                    IsDefault = true,
                    CreatedDate = DateTime.Now.AddDays(-20),
                    OrganizationId = orgId,
                    PCPId = pcpId
                },
                new TemplateData
                {
                    Id = Guid.NewGuid(),
                    TemplateName = "PCP Template 2",
                    Template = "PCP specific template content 2...",
                    VisitType = "PCP",
                    IsDefault = true,
                    CreatedDate = DateTime.Now.AddDays(-10),
                    OrganizationId = orgId,
                    PCPId = pcpId
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(JsonSerializer.Serialize(expectedTemplates, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                }))
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _templateService.GetTemplatesByPCPIdAsync(pcpId, orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(2));
            Assert.That(result[0].PCPId, Is.EqualTo(pcpId));
            Assert.That(result[1].PCPId, Is.EqualTo(pcpId));
            Assert.That(result[0].TemplateName, Is.EqualTo("PCP Template 1"));
            Assert.That(result[1].TemplateName, Is.EqualTo("PCP Template 2"));
        }

        [Test]
        public async Task GetTemplatesByPCPIdAsync_WhenNotSuccessful_ReturnsNull()
        {
            // Arrange
            var pcpId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _templateService.GetTemplatesByPCPIdAsync(pcpId, orgId, subscription);

            // Assert
            Assert.That(result, Is.Null);
        }

        [Test]
        public async Task AddTemplateAsync_WhenSuccessful_CompletesSuccessfully()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;
            var template = new TemplateData
            {
                Id = Guid.NewGuid(),
                TemplateName = "New Template",
                Template = "New template content for testing...",
                VisitType = "New",
                IsDefault = true,
                CreatedDate = DateTime.Now,
                OrganizationId = orgId
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.Created,
                Content = new StringContent("Success")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.DoesNotThrowAsync(async () => await _templateService.CreateTemplatesAsync(template, orgId, subscription));
        }

        [Test]
        public void AddTemplateAsync_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;
            var template = new TemplateData
            {
                Id = Guid.NewGuid(),
                TemplateName = "Failed Template",
                Template = "Failed template content...",
                VisitType = "Failed",
                IsDefault = true,
                CreatedDate = DateTime.Now,
                OrganizationId = orgId
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _templateService.CreateTemplatesAsync(template, orgId, subscription));

            Assert.That(exception.Message, Is.EqualTo("Template retrieval failure"));
        }

        [Test]
        public async Task UpdateTemplateAsync_WhenSuccessful_CompletesSuccessfully()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;
            var template = new TemplateData
            {
                Id = Guid.NewGuid(),
                TemplateName = "Updated Template",
                Template = "Updated template content...",
                VisitType = "Updated",
                IsDefault = true,
                CreatedDate = DateTime.Now.AddDays(-5),
                OrganizationId = orgId
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent("Success")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.DoesNotThrowAsync(async () => await _templateService.UpdateTemplatesAsync(new List<TemplateData> { template }, orgId, subscription));
        }

        [Test]
        public void UpdateTemplateAsync_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;
            var template = new TemplateData
            {
                Id = Guid.NewGuid(),
                TemplateName = "Failed Update Template",
                Template = "Failed update template content...",
                VisitType = "Failed Update",
                IsDefault = true,
                CreatedDate = DateTime.Now.AddDays(-5),
                OrganizationId = orgId
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _templateService.UpdateTemplatesAsync(new List<TemplateData> { template }, orgId, subscription));

            Assert.That(exception.Message, Does.Contain("Response status code does not indicate success"));
        }

        [Test]
        public async Task DeleteTemplateAsync_WhenSuccessful_CompletesSuccessfully()
        {
            // Arrange
            var templateId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent("Success")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.DoesNotThrowAsync(async () => await _templateService.DeleteTemplatesAsync(templateId, orgId, subscription));
        }

        [Test]
        public void DeleteTemplateAsync_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var templateId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<Exception>(async () =>
                await _templateService.DeleteTemplatesAsync(templateId, orgId, subscription));

            Assert.That(exception.Message, Is.EqualTo("Error"));
        }

        [Test]
        public async Task GetTemplatesAsync_WhenEmptyResponse_ReturnsEmptyList()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;
            var expectedTemplates = new List<TemplateData>();

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedTemplates)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _templateService.GetTemplatesAsync(orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(0));
        }

        [Test]
        public void GetTemplatesAsync_WhenExceptionThrown_PropagatesException()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;
            var expectedException = new HttpRequestException("Test exception");

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ThrowsAsync(expectedException);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _templateService.GetTemplatesAsync(orgId, subscription));

            Assert.That(exception.Message, Is.EqualTo("Test exception"));
        }
    }
}



