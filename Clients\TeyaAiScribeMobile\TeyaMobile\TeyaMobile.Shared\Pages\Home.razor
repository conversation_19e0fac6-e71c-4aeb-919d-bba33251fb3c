﻿@page "/"
@using TeyaMobile.Shared.Services
@using TeyaMobile.Shared.Components.Authentication
@using TeyaMobile.Shared.Components
@inject NavigationManager Navigation
@inject IFormFactor FormFactor
@inject ActiveUser user
@inject IAuthenticationService AuthService
@using Mud<PERSON><PERSON>zor
@inject IFormFactor formFactor

<PageTitle>TeyaHealth</PageTitle>
<AuthenticationHandler />

<div class="home-container">
    <!-- Hero Section -->
    <div class="hero-section">
        <!-- Logo and Loading Animation -->
        <div class="logo-container">
            <img src="_content/TeyaMobile.Shared/images/TeyaHealth.png" alt="TeyaHealth Logo" style="height:40px; width:40px; backface-visibility:hidden;" class="main-logo" />
        </div>

        <!-- Welcome Content -->
        <div class="welcome-content">
            <MudText Typo="Typo.h4" Class="welcome-title" Align="Align.Center">
                Welcome to TeyaHealth
            </MudText>
            <MudText Typo="Typo.h6" Class="welcome-subtitle" Align="Align.Center">
                Your Complete Healthcare Management Solution Partner
            </MudText>
        </div>
    </div>
</div>

@if (IsAuthenticated)
{
    @if (IsTablet)
    {
        <MudContainer MaxWidth="MaxWidth.Large" Style="margin: 0 auto 0 40px; padding: 0; display: flex; justify-content: flex-start;">
            <div class="cards-grid-section" style="width: 100%;">
                <MudGrid Spacing="4" Class="cards-grid" Style="max-width: 1200px; margin: 0 auto;">
                    <MudItem md="6" lg="3" Class="card-item">
                        <MudPaper Class="action-card" @onclick="@(() => NavigateToPage("/appointments"))" Elevation="6" Style="min-height: 180px;">
                            <div class="card-content" style="gap: 24px;">
                                <div class="card-icon">
                                    <img src="_content/TeyaMobile.Shared/images/cal.png" alt="Calendar" class="card-image" style="width: 60px; height: 60px;" />
                                </div>
                                <MudText Typo="Typo.h6" Class="card-label" Style="font-size: 1.3em;">
                                    View Appointments
                                </MudText>
                            </div>
                        </MudPaper>
                    </MudItem>
                    <MudItem md="6" lg="3" Class="card-item">
                        <MudPaper Class="action-card" @onclick="@(() => NavigateToPage($"/soapnotes?pcpId={user.id}&sub={false}"))" Elevation="6" Style="min-height: 180px;">
                            <div class="card-content" style="gap: 24px;">
                                <div class="card-icon">
                                    <img src="_content/TeyaMobile.Shared/images/visit.png" alt="Visits" class="card-image" style="width: 60px; height: 60px;" />
                                </div>
                                <MudText Typo="Typo.h6" Class="card-label" Style="font-size: 1.3em;">
                                    Patients Record
                                </MudText>
                            </div>
                        </MudPaper>
                    </MudItem>
                    <MudItem md="6" lg="3" Class="card-item">
                        <MudPaper Class="action-card" @onclick="@(() => NavigateToPage("/messages"))" Elevation="6" Style="min-height: 180px;">
                            <div class="card-content" style="gap: 24px;">
                                <div class="card-icon">
                                    <img src="_content/TeyaMobile.Shared/images/msg.png" alt="Messages" class="card-image" style="width: 60px; height: 60px;" />
                                </div>
                                <MudText Typo="Typo.h6" Class="card-label" Style="font-size: 1.3em;">
                                    Messages
                                </MudText>
                            </div>
                        </MudPaper>
                    </MudItem>
                </MudGrid>
            </div>
        </MudContainer>
    }
    else
    {
        <div class="cards-grid-section">
            <MudGrid Spacing="3" Class="cards-grid">
                <!-- Row 1 -->
                <MudItem xs="4" Class="card-item">
                    <MudPaper Class="action-card" @onclick="@(() => NavigateToPage("/appointments"))" Elevation="4">
                        <div class="card-content">
                            <div class="card-icon">
                                <img src="_content/TeyaMobile.Shared/images/cal.png" alt="Calendar" class="card-image" />
                            </div>
                            <MudText Typo="Typo.body2" Class="card-label">
                                View Appointments
                            </MudText>
                        </div>
                    </MudPaper>
                </MudItem>

                <MudItem xs="4" Class="card-item">
                    <MudPaper Class="action-card" @onclick="@(() => NavigateToPage($"/soapnotes?pcpId={user.id}&sub={false}"))" Elevation="4">
                        <div class="card-content">
                            <div class="card-icon">
                                <img src="_content/TeyaMobile.Shared/images/visit.png" alt="Visits" class="card-image" />
                            </div>
                            <MudText Typo="Typo.body2" Class="card-label">
                                Patients Record
                            </MudText>
                        </div>
                    </MudPaper>
                </MudItem>

                <MudItem xs="4" Class="card-item">
                    <MudPaper Class="action-card" @onclick="@(() => NavigateToPage("/messages"))" Elevation="4">
                        <div class="card-content">
                            <div class="card-icon">
                                <img src="_content/TeyaMobile.Shared/images/msg.png" alt="Messages" class="card-image" />
                            </div>
                            <MudText Typo="Typo.body2" Class="card-label">
                                Messages
                            </MudText>
                        </div>
                    </MudPaper>
                </MudItem>
            </MudGrid>
        </div>
    }
}
@* <!-- Additional Quick Actions (if authenticated) -->
    @if (IsAuthenticated)
    {
        <div class="additional-actions">
            <MudGrid Spacing="3">
                <MudItem xs="6">
                    <MudPaper Class="action-card secondary-card" @onclick="@(() => NavigateToPage($"/soapnotes?pcpId={CurrentUser.PCPId}&orgId={CurrentUser.OrganizationId}&sub={CurrentUser.Subscription}"))" Elevation="3">
                        <div class="card-content">
                            <div class="card-icon">
                                <MudIcon Icon="@Icons.Material.Filled.Description" Size="Size.Large" Color="Color.Primary" />
                            </div>
                            <MudText Typo="Typo.body2" Class="card-label">
                                SOAP Notes
                            </MudText>
                        </div>
                    </MudPaper>
                </MudItem>

                <MudItem xs="6">
                    <MudPaper Class="action-card secondary-card" @onclick="@(() => NavigateToPage("/users"))" Elevation="3">
                        <div class="card-content">
                            <div class="card-icon">
                                <MudIcon Icon="@Icons.Material.Filled.People" Size="Size.Large" Color="Color.Primary" />
                            </div>
                            <MudText Typo="Typo.body2" Class="card-label">
                                Manage Users
                            </MudText>
                        </div>
                    </MudPaper>
                </MudItem>
            </MudGrid>
        </div>
    } *@

<style>
    .home-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 16px;
    }

    .hero-section {
        text-align: center;
        padding: 40px 0 60px 0;
        background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(147, 51, 234, 0.1) 100%);
        border-radius: 20px;
        margin-bottom: 40px;
    }

    .logo-container {
        margin-bottom: 32px;
        position: relative;
    }

    .main-logo {
        width: 30px;
        height: 30px;
        border-radius: 10px;
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
        animation: float 3s ease-in-out infinite;
        background: transparent;
    }

    .loading-animation {
        width: 40px;
        height: 40px;
        position: absolute;
        bottom: -10px;
        right: -10px;
    }

    .welcome-content {
        max-width: 600px;
        margin: 0 auto;
    }

    .welcome-title {
        font-weight: 700 !important;
        color: var(--mud-palette-primary) !important;
        margin-bottom: 16px !important;
    }

    .welcome-subtitle {
        color: var(--mud-palette-text-secondary) !important;
        margin-bottom: 24px !important;
        font-weight: 400 !important;
    }

    /* Cards Grid Section */
    .cards-grid-section {
        margin-bottom: 30px;
    }

    .cards-grid {
        max-width: 800px;
        margin: 0 auto;
    }

    .card-item {
        display: flex;
        justify-content: center;
    }

    .action-card {
        width: 100%;
        max-width: 140px;
        background: white !important;
        border-radius: 12px !important;
        cursor: pointer;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        border: 1px solid rgba(0, 0, 0, 0.08);
        overflow: hidden;
    }

        .action-card:hover {
            transform: translateY(-4px) scale(1.02);
            box-shadow: 0 12px 40px rgba(0, 48, 135, 0.15) !important;
            border-color: #003087;
        }

        .action-card:active {
            transform: translateY(-2px) scale(1.01);
        }

    .card-content {
        padding: 20px 16px;
        text-align: center;
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 12px;
        min-height: 120px;
        justify-content: center;
    }

    .card-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 8px;
    }

    .card-image {
        width: 64px;
        height: 64px;
        object-fit: contain;
    }

    .card-label {
        color: #003087 !important;
        font-weight: 600 !important;
        font-size: 14px !important;
        line-height: 1.3 !important;
        text-align: center;
        margin: 0 !important;
    }

    /* Secondary Cards */
    .secondary-card {
        max-width: none;
        background: rgba(59, 130, 246, 0.05) !important;
    }

    .additional-actions {
        margin-bottom: 30px;
        max-width: 600px;
        margin-left: auto;
        margin-right: auto;
    }

    /* Platform Info */
    .platform-info-section {
        text-align: center;
        padding: 20px 0;
        border-top: 1px solid rgba(0, 0, 0, 0.08);
        margin-top: 20px;
    }

    .platform-info {
        color: var(--mud-palette-text-secondary) !important;
        font-size: 0.9rem !important;
        opacity: 0.8;
    }

    /* Animations */
    @@keyframes float {
        0%, 100% {
            transform: translateY(0px);
        }

        50% {
            transform: translateY(-10px);
        }
    }

    /* Mobile Responsive */
    @@media (max-width: 600px) {
        .hero-section {
            padding: 30px 20px 30px 20px;
            margin-bottom: 20px;
        }

        .main-logo {
            width: 100px;
            height: 100px;
        }

        .welcome-title {
            font-size: 1.8rem !important;
        }

        .welcome-subtitle {
            font-size: 1rem !important;
        }

        .action-card {
            max-width: 120px;
        }

        .card-content {
            padding: 16px 12px;
            min-height: 110px;
        }

        .card-image {
            width: 48px;
            height: 48px;
        }

        .card-label {
            font-size: 12px !important;
        }

        .cards-grid {
            max-width: 100%;
        }
    }

    /* Tablet and larger screens */
    @@media (min-width: 768px) {
        .action-card {
            max-width: 160px;
        }

        .card-content {
            padding: 24px 20px;
            min-height: 140px;
        }

        .card-image {
            width: 72px;
            height: 72px;
        }
    }

    /* Desktop */
    @@media (min-width: 960px) {
        .hero-section {
            padding: 50px 0 50px 0;
        }

        .main-logo {
            width: 140px;
            height: 140px;
        }
    }

    /* Tablet/iPad Responsive Enhancements */
    @@media (min-width: 768px) and (max-width: 1200px) {
        .home-container {
            max-width: 1000px;
            padding: 0 32px;
        }
        .hero-section {
            padding: 60px 0 80px 0;
            border-radius: 32px;
            margin-bottom: 48px;
        }
        .main-logo {
            width: 120px;
            height: 120px;
        }
        .welcome-title {
            font-size: 2.2rem !important;
        }
        .welcome-subtitle {
            font-size: 1.2rem !important;
        }
        .cards-grid {
            max-width: 900px;
            gap: 32px;
        }
        .action-card {
            max-width: 200px;
            min-width: 180px;
            padding: 0;
        }
        .card-content {
            padding: 32px 20px;
            min-height: 180px;
            gap: 20px;
        }
        .card-image {
            width: 96px;
            height: 96px;
        }
        .card-label {
            font-size: 18px !important;
        }
    }

    @@media (min-width: 1200px) {
        .home-container {
            max-width: 1200px;
            padding: 0 48px;
        }
        .hero-section {
            padding: 80px 0 100px 0;
            border-radius: 40px;
            margin-bottom: 60px;
        }
        .main-logo {
            width: 160px;
            height: 160px;
        }
        .welcome-title {
            font-size: 2.6rem !important;
        }
        .welcome-subtitle {
            font-size: 1.4rem !important;
        }
        .cards-grid {
            max-width: 1100px;
            gap: 40px;
        }
        .action-card {
            max-width: 240px;
            min-width: 200px;
        }
        .card-content {
            padding: 40px 28px;
            min-height: 220px;
            gap: 28px;
        }
        .card-image {
            width: 120px;
            height: 120px;
        }
        .card-label {
            font-size: 20px !important;
        }
    }
</style>

@code {
    private string factor => FormFactor.GetFormFactor();
    private string platform => FormFactor.GetPlatform();
    private bool IsAuthenticated = true; // You can inject your auth service here
    private UserContext? CurrentUser;
    private bool _isLoading = true;
    private string _errorMessage = "";
    private bool IsTablet => FormFactor.GetFormFactor() == "Tablet";

    private void NavigateToPage(string url)
    {
        Navigation.NavigateTo(url);
    }

    protected override async Task OnInitializedAsync()
    {
        try
        {
            _isLoading = true;
            StateHasChanged();

            // Get user context from service with timeout
            using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(10));

            _errorMessage = "";
        }
        catch (OperationCanceledException)
        {
            _errorMessage = "Initialization timed out. Please refresh the page.";
            CurrentUser = new UserContext();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error initializing home page: {ex.Message}");
            _errorMessage = "Failed to load user information.";
        }
        finally
        {
            _isLoading = false;
            StateHasChanged();
        }
    }

    private string GetSoapNotesUrl()
    {
        bool Sub = false;
        return $"/soapnotes?pcpId={user.id}&sub={Sub}";
    }
}
