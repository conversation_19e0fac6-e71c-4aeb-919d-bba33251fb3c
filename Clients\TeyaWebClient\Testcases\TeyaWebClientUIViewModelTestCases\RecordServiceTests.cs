using System;
using System.Net;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Moq;
using Moq.Protected;
using NUnit.Framework;
using TeyaUIModels.Model;
using TeyaUIViewModels.ViewModel;
using TeyaUIViewModels.TeyaUIViewModelResources;
using System.Text.Json;

namespace TeyaWebClientUIViewModelTestCases
{
    [TestFixture]
    public class RecordServiceTests
    {
        private Mock<HttpMessageHandler> _mockHttpMessageHandler;
        private HttpClient _httpClient;
        private Mock<IStringLocalizer<TeyaUIViewModelsStrings>> _mockLocalizer;
        private Mock<ILogger<RecordService>> _mockLogger;
        private Mock<ITokenService> _mockTokenService;
        private RecordService _recordService;
        private readonly string _baseUrl = "http://test-api.com";
        private readonly string _accessToken = "test-access-token";

        [SetUp]
        public void Setup()
        {
            // Setup environment variable
            Environment.SetEnvironmentVariable("EncounterNotesURL", _baseUrl);

            // Setup mock HTTP handler
            _mockHttpMessageHandler = new Mock<HttpMessageHandler>();

            // Setup HttpClient with mock handler
            _httpClient = new HttpClient(_mockHttpMessageHandler.Object)
            {
                BaseAddress = new Uri(_baseUrl)
            };

            // Setup mock localizer
            _mockLocalizer = new Mock<IStringLocalizer<TeyaUIViewModelsStrings>>();
            _mockLocalizer.Setup(l => l["ErrorFetchingMemberById"])
                .Returns(new LocalizedString("ErrorFetchingMemberById", "Error fetching member by ID: {0}"));

            // Setup mock logger
            _mockLogger = new Mock<ILogger<RecordService>>();

            // Setup mock token service
            _mockTokenService = new Mock<ITokenService>();
            _mockTokenService.Setup(t => t.AccessToken).Returns(_accessToken);

            // Create RecordService with mocked dependencies
            _recordService = new RecordService(_httpClient, _mockLocalizer.Object, _mockLogger.Object, _mockTokenService.Object);
        }

        [TearDown]
        public void TearDown()
        {
            _httpClient.Dispose();
            Environment.SetEnvironmentVariable("EncounterNotesURL", null);
        }

        [Test]
        public async Task GetRecordByIdAsync_WhenSuccessful_ReturnsRecord()
        {
            // Arrange
            var recordId = Guid.NewGuid();
            var expectedRecord = new Record
            {
                Id = recordId,
                PatientId = Guid.NewGuid(),
                PatientName = "John Doe",
                PCPId = Guid.NewGuid(),
                DateTime = DateTime.Now.AddDays(-5),
                Notes = "Patient record notes",
                isEditable = true,
                Transcription = "Record transcription",
                OrganizationId = Guid.NewGuid()
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(JsonSerializer.Serialize(expectedRecord))
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _recordService.GetRecordByIdAsync(recordId);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Id, Is.EqualTo(expectedRecord.Id));
            Assert.That(result.PatientId, Is.EqualTo(expectedRecord.PatientId));
            Assert.That(result.PatientName, Is.EqualTo(expectedRecord.PatientName));
            Assert.That(result.PCPId, Is.EqualTo(expectedRecord.PCPId));
            Assert.That(result.Notes, Is.EqualTo(expectedRecord.Notes));
            Assert.That(result.isEditable, Is.EqualTo(expectedRecord.isEditable));
            Assert.That(result.Transcription, Is.EqualTo(expectedRecord.Transcription));
            Assert.That(result.OrganizationId, Is.EqualTo(expectedRecord.OrganizationId));
        }

        [Test]
        public void GetRecordByIdAsync_WhenExceptionThrown_LogsErrorAndRethrows()
        {
            // Arrange
            var recordId = Guid.NewGuid();
            var expectedException = new HttpRequestException("Test exception");

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ThrowsAsync(expectedException);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _recordService.GetRecordByIdAsync(recordId));

            Assert.That(exception, Is.EqualTo(expectedException));
            
            // Verify that the error was logged
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.Is<Exception>(ex => ex == expectedException),
                    It.Is<Func<It.IsAnyType, Exception, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }

        [Test]
        public async Task GetRecordByIdAsync_WhenResponseIsNotSuccessful_ReturnsDeserializedRecord()
        {
            // Arrange
            var recordId = Guid.NewGuid();
            var expectedRecord = new Record
            {
                Id = recordId,
                PatientId = Guid.NewGuid(),
                PatientName = "Jane Smith",
                PCPId = Guid.NewGuid(),
                DateTime = DateTime.Now.AddDays(-3),
                Notes = "Patient record notes for Jane",
                isEditable = false,
                Transcription = "Jane's record transcription",
                OrganizationId = Guid.NewGuid()
            };

            // Note: The original service doesn't check for success status, it just deserializes the response
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.NotFound,
                Content = new StringContent(JsonSerializer.Serialize(expectedRecord))
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _recordService.GetRecordByIdAsync(recordId);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Id, Is.EqualTo(expectedRecord.Id));
            Assert.That(result.PatientName, Is.EqualTo(expectedRecord.PatientName));
        }

        [Test]
        public async Task GetRecordByIdAsync_WhenEmptyResponse_ReturnsNull()
        {
            // Arrange
            var recordId = Guid.NewGuid();

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent("null")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _recordService.GetRecordByIdAsync(recordId);

            // Assert
            Assert.That(result, Is.Null);
        }

        [Test]
        public void GetRecordByIdAsync_WhenJsonDeserializationFails_ThrowsJsonException()
        {
            // Arrange
            var recordId = Guid.NewGuid();

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent("invalid json")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<JsonException>(async () =>
                await _recordService.GetRecordByIdAsync(recordId));

            Assert.That(exception, Is.Not.Null);
            
            // Verify that the error was logged
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.Is<Exception>(ex => ex is JsonException),
                    It.Is<Func<It.IsAnyType, Exception, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }

        [Test]
        public async Task GetRecordByIdAsync_WhenRecordHasAllProperties_ReturnsCompleteRecord()
        {
            // Arrange
            var recordId = Guid.NewGuid();
            var expectedRecord = new Record
            {
                Id = recordId,
                PatientId = Guid.NewGuid(),
                PatientName = "Complete Patient",
                PCPId = Guid.NewGuid(),
                DateTime = DateTime.Now.AddDays(-1),
                Notes = "Complete patient record notes with all properties",
                isEditable = true,
                Transcription = "Complete transcription text",
                OrganizationId = Guid.NewGuid(),
                // Add any other properties that might exist in the Record model
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(JsonSerializer.Serialize(expectedRecord, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                }))
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _recordService.GetRecordByIdAsync(recordId);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Id, Is.EqualTo(expectedRecord.Id));
            Assert.That(result.PatientId, Is.EqualTo(expectedRecord.PatientId));
            Assert.That(result.PatientName, Is.EqualTo(expectedRecord.PatientName));
            Assert.That(result.PCPId, Is.EqualTo(expectedRecord.PCPId));
            Assert.That(result.DateTime, Is.EqualTo(expectedRecord.DateTime));
            Assert.That(result.Notes, Is.EqualTo(expectedRecord.Notes));
            Assert.That(result.isEditable, Is.EqualTo(expectedRecord.isEditable));
            Assert.That(result.Transcription, Is.EqualTo(expectedRecord.Transcription));
            Assert.That(result.OrganizationId, Is.EqualTo(expectedRecord.OrganizationId));
        }

        [Test]
        public async Task GetRecordByIdAsync_WhenCaseInsensitiveDeserialization_ReturnsRecord()
        {
            // Arrange
            var recordId = Guid.NewGuid();
            var jsonResponse = @"{
                ""id"": """ + recordId + @""",
                ""patientid"": """ + Guid.NewGuid() + @""",
                ""patientname"": ""Case Test Patient"",
                ""pcpid"": """ + Guid.NewGuid() + @""",
                ""datetime"": """ + DateTime.Now.AddDays(-2).ToString("O") + @""",
                ""notes"": ""Case insensitive test notes"",
                ""iseditable"": true,
                ""transcription"": ""Case insensitive transcription"",
                ""organizationid"": """ + Guid.NewGuid() + @"""
            }";

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(jsonResponse)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _recordService.GetRecordByIdAsync(recordId);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Id, Is.EqualTo(recordId));
            Assert.That(result.PatientName, Is.EqualTo("Case Test Patient"));
            Assert.That(result.Notes, Is.EqualTo("Case insensitive test notes"));
            Assert.That(result.isEditable, Is.True);
            Assert.That(result.Transcription, Is.EqualTo("Case insensitive transcription"));
        }
    }
}



