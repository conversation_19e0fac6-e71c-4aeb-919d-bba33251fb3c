using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Moq;
using Moq.Protected;
using NUnit.Framework;
using TeyaUIModels.Model;
using TeyaUIViewModels.ViewModel;
using TeyaUIViewModels.TeyaUIViewModelResources;

namespace TeyaWebClientUIViewModelTestCases
{
    [TestFixture]
    public class ImmunizationServiceTests
    {
        private Mock<HttpMessageHandler> _mockHttpMessageHandler;
        private Mock<IConfiguration> _mockConfiguration;
        private Mock<IStringLocalizer<TeyaUIViewModelsStrings>> _mockLocalizer;
        private Mock<ITokenService> _mockTokenService;
        private Mock<ILogger<ImmunizationService>> _mockLogger;
        private HttpClient _httpClient;
        private ImmunizationService _immunizationService;

        private const string TestEncounterNotesUrl = "https://test-encounternotes.com";
        private const string TestAccessToken = "test-access-token";

        [SetUp]
        public void SetUp()
        {
            _mockHttpMessageHandler = new Mock<HttpMessageHandler>();
            _mockConfiguration = new Mock<IConfiguration>();
            _mockLocalizer = new Mock<IStringLocalizer<TeyaUIViewModelsStrings>>();
            _mockTokenService = new Mock<ITokenService>();
            _mockLogger = new Mock<ILogger<ImmunizationService>>();

            _httpClient = new HttpClient(_mockHttpMessageHandler.Object);

            // Set up environment variable
            Environment.SetEnvironmentVariable("EncounterNotesURL", TestEncounterNotesUrl);

            // Set up mock responses
            _mockTokenService.Setup(t => t.AccessToken).Returns(TestAccessToken);
            _mockTokenService.Setup(t => t.GetValidatedAccessTokenAsync()).ReturnsAsync(TestAccessToken);

            _immunizationService = new ImmunizationService(
                _httpClient,
                _mockConfiguration.Object,
                _mockLocalizer.Object,
                _mockTokenService.Object);
        }

        [TearDown]
        public void TearDown()
        {
            _httpClient?.Dispose();
            Environment.SetEnvironmentVariable("EncounterNotesURL", null);
        }

        private ImmunizationData CreateTestImmunizationData()
        {
            return new ImmunizationData
            {
                ImmunizationId = Guid.NewGuid(),
                PatientId = Guid.NewGuid(),
                OrganizationId = Guid.NewGuid(),
                CreatedDate = DateTime.UtcNow,
                UpdatedDate = DateTime.UtcNow,
                CreatedBy = Guid.NewGuid(),
                UpdatedBy = Guid.NewGuid(),
                Immunizations = "COVID-19 Vaccine",
                CPTCode = "90471",
                CVXCode = "208",
                Comments = "First dose administered",
                CPTDescription = "Immunization administration",
                GivenDate = DateTime.UtcNow.AddDays(-30),
                PCPId = Guid.NewGuid(),
                IsActive = true,
                Subscription = false
            };
        }

        [Test]
        public async Task GetImmunizationsByIdAsync_WhenSuccessful_ReturnsImmunizationList()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = false;
            var expectedImmunizations = new List<ImmunizationData>
            {
                CreateTestImmunizationData(),
                CreateTestImmunizationData()
            };

            var responseContent = JsonSerializer.Serialize(expectedImmunizations);
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(responseContent, Encoding.UTF8, "application/json")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _immunizationService.GetImmunizationsByIdAsync(patientId, orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(2));
            Assert.That(result[0].Immunizations, Is.EqualTo("COVID-19 Vaccine"));
        }

        [Test]
        public async Task GetImmunizationsByIdAsync_WhenHttpRequestFails_ThrowsHttpRequestException()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = false;

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad Request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _immunizationService.GetImmunizationsByIdAsync(patientId, orgId, subscription));
        }

        [Test]
        public async Task AddImmunizationAsync_WhenSuccessful_CompletesWithoutException()
        {
            // Arrange
            var immunizations = new List<ImmunizationData>
            {
                CreateTestImmunizationData(),
                CreateTestImmunizationData()
            };
            var orgId = Guid.NewGuid();
            var subscription = false;

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.Created
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            // No exception should be thrown
            await _immunizationService.AddImmunizationAsync(immunizations, orgId, subscription);
        }

        [Test]
        public async Task AddImmunizationAsync_WhenHttpRequestFails_ThrowsHttpRequestException()
        {
            // Arrange
            var immunizations = new List<ImmunizationData> { CreateTestImmunizationData() };
            var orgId = Guid.NewGuid();
            var subscription = false;

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.InternalServerError,
                Content = new StringContent("Internal Server Error")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _immunizationService.AddImmunizationAsync(immunizations, orgId, subscription));
        }

        [Test]
        public async Task DeleteImmunizationAsync_WhenSuccessful_CompletesWithoutException()
        {
            // Arrange
            var immunizationData = CreateTestImmunizationData();
            var orgId = Guid.NewGuid();
            var subscription = false;

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.NoContent
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response)
                .Verifiable();

            // Act & Assert
            // No exception should be thrown
            await _immunizationService.DeleteImmunizationAsync(immunizationData, orgId, subscription);

            // Verify the HTTP call was made
            _mockHttpMessageHandler.Protected().Verify(
                "SendAsync",
                Times.Once(),
                ItExpr.IsAny<HttpRequestMessage>(),
                ItExpr.IsAny<CancellationToken>());
        }

        [Test]
        public async Task UpdateImmunizationAsync_WhenSuccessful_CompletesWithoutException()
        {
            // Arrange
            var immunizationData = CreateTestImmunizationData();
            var orgId = Guid.NewGuid();
            var subscription = false;

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            // No exception should be thrown
            await _immunizationService.UpdateImmunizationAsync(immunizationData, orgId, subscription);
        }

        [Test]
        public async Task GetImmunizationByIdAsyncAndIsActive_WhenSuccessful_ReturnsActiveImmunizations()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = false;
            var expectedImmunizations = new List<ImmunizationData>
            {
                CreateTestImmunizationData()
            };
            expectedImmunizations[0].IsActive = true;

            var responseContent = JsonSerializer.Serialize(expectedImmunizations);
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(responseContent, Encoding.UTF8, "application/json")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _immunizationService.GetImmunizationByIdAsyncAndIsActive(patientId, orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(1));
            Assert.That(result[0].IsActive, Is.True);
        }

        [Test]
        public async Task UpdateImmunizationListAsync_WhenSuccessful_CompletesWithoutException()
        {
            // Arrange
            var immunizationList = new List<ImmunizationData>
            {
                CreateTestImmunizationData(),
                CreateTestImmunizationData()
            };
            var orgId = Guid.NewGuid();
            var subscription = false;

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            // No exception should be thrown
            await _immunizationService.UpdateImmunizationListAsync(immunizationList, orgId, subscription);
        }

        [Test]
        public async Task UpdateImmunizationListAsync_WhenHttpRequestFails_ThrowsHttpRequestException()
        {
            // Arrange
            var immunizationList = new List<ImmunizationData> { CreateTestImmunizationData() };
            var orgId = Guid.NewGuid();
            var subscription = false;

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad Request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _immunizationService.UpdateImmunizationListAsync(immunizationList, orgId, subscription));
        }

        [Test]
        public async Task DeleteImmunizationAsync_WhenHttpRequestFails_ThrowsHttpRequestException()
        {
            // Arrange
            var immunizationData = CreateTestImmunizationData();
            var orgId = Guid.NewGuid();
            var subscription = false;

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.NotFound,
                Content = new StringContent("Not Found")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _immunizationService.DeleteImmunizationAsync(immunizationData, orgId, subscription));
        }

        [Test]
        public async Task UpdateImmunizationAsync_WhenHttpRequestFails_ThrowsHttpRequestException()
        {
            // Arrange
            var immunizationData = CreateTestImmunizationData();
            var orgId = Guid.NewGuid();
            var subscription = false;

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad Request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _immunizationService.UpdateImmunizationAsync(immunizationData, orgId, subscription));
        }

        [Test]
        public async Task GetImmunizationByIdAsyncAndIsActive_WhenHttpRequestFails_ThrowsHttpRequestException()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = false;

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.InternalServerError,
                Content = new StringContent("Internal Server Error")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _immunizationService.GetImmunizationByIdAsyncAndIsActive(patientId, orgId, subscription));
        }

        [Test]
        public async Task GetImmunizationsByIdAsync_WhenEmptyResponse_ReturnsEmptyList()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = false;

            var responseContent = JsonSerializer.Serialize(new List<ImmunizationData>());
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(responseContent, Encoding.UTF8, "application/json")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _immunizationService.GetImmunizationsByIdAsync(patientId, orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(0));
        }

        [Test]
        public async Task GetImmunizationByIdAsyncAndIsActive_WhenEmptyResponse_ReturnsEmptyList()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = false;

            var responseContent = JsonSerializer.Serialize(new List<ImmunizationData>());
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(responseContent, Encoding.UTF8, "application/json")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _immunizationService.GetImmunizationByIdAsyncAndIsActive(patientId, orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(0));
        }
    }
}



