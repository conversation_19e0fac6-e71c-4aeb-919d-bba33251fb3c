using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Moq;
using Moq.Protected;
using NUnit.Framework;
using TeyaUIModels.Model;
using TeyaUIViewModels.ViewModel;
using TeyaUIViewModels.TeyaUIViewModelResources;
using System.Net.Http.Json;
using System.Text.Json;

namespace TeyaWebClientUIViewModelTestCases
{
    [TestFixture]
    public class PracticeServiceTests
    {
        private Mock<HttpMessageHandler> _mockHttpMessageHandler;
        private HttpClient _httpClient;
        private Mock<IConfiguration> _mockConfiguration;
        private Mock<IStringLocalizer<TeyaUIViewModelsStrings>> _mockLocalizer;
        private Mock<ITokenService> _mockTokenService;
        private Mock<ILogger<PracticeService>> _mockLogger;
        private PracticeService _practiceService;
        private readonly string _baseUrl = "http://test-api.com";
        private readonly string _accessToken = "test-access-token";

        [SetUp]
        public void Setup()
        {
            // Setup environment variable
            Environment.SetEnvironmentVariable("PracticeApiURL", _baseUrl);

            // Setup mock HTTP handler
            _mockHttpMessageHandler = new Mock<HttpMessageHandler>();

            // Setup HttpClient with mock handler
            _httpClient = new HttpClient(_mockHttpMessageHandler.Object)
            {
                BaseAddress = new Uri(_baseUrl)
            };

            // Setup mock configuration
            _mockConfiguration = new Mock<IConfiguration>();

            // Setup mock localizer
            _mockLocalizer = new Mock<IStringLocalizer<TeyaUIViewModelsStrings>>();
            _mockLocalizer.Setup(l => l["TasksRetrievalFailure"])
                .Returns(new LocalizedString("TasksRetrievalFailure", "Tasks retrieval failure"));
            _mockLocalizer.Setup(l => l["Error"])
                .Returns(new LocalizedString("Error", "Error"));

            // Setup mock logger
            _mockLogger = new Mock<ILogger<PracticeService>>();

            // Setup mock token service
            _mockTokenService = new Mock<ITokenService>();
            _mockTokenService.Setup(t => t.AccessToken).Returns(_accessToken);
            _mockTokenService.Setup(t => t.GetValidatedAccessTokenAsync()).ReturnsAsync(_accessToken);

            // Create PracticeService with mocked dependencies
            _practiceService = new PracticeService(_httpClient, _mockConfiguration.Object, _mockLocalizer.Object, _mockTokenService.Object, _mockLogger.Object);
        }

        [TearDown]
        public void TearDown()
        {
            _httpClient.Dispose();
            Environment.SetEnvironmentVariable("PracticeApiURL", null);
        }

        [Test]
        public async Task GetTasksAsync_WhenSuccessful_ReturnsTasks()
        {
            // Arrange
            var expectedTasks = new List<Tasks>
            {
                new Tasks
                {
                    Id = Guid.NewGuid(),
                    Subject = "Task 1",
                    Notes = "Description for task 1",
                    DueDate = DateTime.Now.AddDays(7),
                    Priority = "High",
                    Status = "Pending"
                },
                new Tasks
                {
                    Id = Guid.NewGuid(),
                    Subject = "Task 2",
                    Notes = "Description for task 2",
                    DueDate = DateTime.Now.AddDays(14),
                    Priority = "Medium",
                    Status = "In Progress"
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedTasks)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _practiceService.GetTasksAsync();

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(expectedTasks.Count));
            Assert.That(result[0].Id, Is.EqualTo(expectedTasks[0].Id));
            Assert.That(result[0].Subject, Is.EqualTo(expectedTasks[0].Subject));
            Assert.That(result[0].Notes, Is.EqualTo(expectedTasks[0].Notes));
            Assert.That(result[0].Priority, Is.EqualTo(expectedTasks[0].Priority));
            Assert.That(result[0].Status, Is.EqualTo(expectedTasks[0].Status));
        }

        [Test]
        public void GetTasksAsync_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.NotFound,
                Content = new StringContent("Not found")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _practiceService.GetTasksAsync());

            Assert.That(exception.Message, Does.Contain("Tasks retrieval failure"));
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.IsAny<Exception>(),
                    It.Is<Func<It.IsAnyType, Exception, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }

        [Test]
        public void GetTasksAsync_WhenExceptionThrown_PropagatesException()
        {
            // Arrange
            var expectedException = new HttpRequestException("Test exception");

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ThrowsAsync(expectedException);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _practiceService.GetTasksAsync());

            Assert.That(exception.Message, Is.EqualTo(expectedException.Message));
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.IsAny<Exception>(),
                    It.Is<Func<It.IsAnyType, Exception, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }

        [Test]
        public async Task CreateTasksAsync_WhenSuccessful_CompletesSuccessfully()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;
            var tasks = new List<Tasks>
            {
                new Tasks
                {
                    Id = Guid.NewGuid(),
                    Subject = "New Task",
                    Notes = "Description for new task",
                    DueDate = DateTime.Now.AddDays(7),
                    Priority = "High",
                    Status = "Pending"
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.Created,
                Content = new StringContent("Success")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.DoesNotThrowAsync(async () => await _practiceService.CreateTasksAsync(tasks, orgId, subscription));
        }

        [Test]
        public void CreateTasksAsync_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;
            var tasks = new List<Tasks>
            {
                new Tasks
                {
                    Id = Guid.NewGuid(),
                    Subject = "New Task",
                    Notes = "Description for new task",
                    DueDate = DateTime.Now.AddDays(7),
                    Priority = "High",
                    Status = "Pending"
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _practiceService.CreateTasksAsync(tasks, orgId, subscription));

            Assert.That(exception.Message, Is.EqualTo("Tasks retrieval failure"));
        }

        [Test]
        public async Task UpdateTasksAsync_WhenSuccessful_CompletesSuccessfully()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;
            var task = new Tasks
            {
                Id = Guid.NewGuid(),
                Subject = "Updated Task",
                Notes = "Updated description",
                DueDate = DateTime.Now.AddDays(10),
                Priority = "Medium",
                Status = "In Progress"
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent("Success")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.DoesNotThrowAsync(async () => await _practiceService.UpdateTasksAsync(task, orgId, subscription));
        }

        [Test]
        public void UpdateTasksAsync_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;
            var task = new Tasks
            {
                Id = Guid.NewGuid(),
                Subject = "Updated Task",
                Notes = "Updated description",
                DueDate = DateTime.Now.AddDays(10),
                Priority = "Medium",
                Status = "In Progress"
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _practiceService.UpdateTasksAsync(task, orgId, subscription));

            Assert.That(exception, Is.Not.Null);
        }

        [Test]
        public async Task DeleteTasksAsync_WhenSuccessful_CompletesSuccessfully()
        {
            // Arrange
            var taskId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.NoContent,
                Content = new StringContent("Success")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.DoesNotThrowAsync(async () => await _practiceService.DeleteTasksAsync(taskId, orgId, subscription));
        }

        [Test]
        public void DeleteTasksAsync_WhenExceptionThrown_ThrowsException()
        {
            // Arrange
            var taskId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var expectedException = new HttpRequestException("Test exception");

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ThrowsAsync(expectedException);

            // Act & Assert
            var exception = Assert.ThrowsAsync<Exception>(async () =>
                await _practiceService.DeleteTasksAsync(taskId, orgId, subscription));

            Assert.That(exception.Message, Is.EqualTo("Error"));
            Assert.That(exception.InnerException, Is.EqualTo(expectedException));
        }

        [Test]
        public async Task UpdateTasksByPatientIdAsync_WhenSuccessful_CompletesSuccessfully()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;
            var task = new Tasks
            {
                Id = Guid.NewGuid(),
                Subject = "Patient Task",
                Notes = "Task for specific patient",
                DueDate = DateTime.Now.AddDays(5),
                Priority = "High",
                Status = "Pending"
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent("Success")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.DoesNotThrowAsync(async () => await _practiceService.UpdateTasksByPatientIdAsync(task, orgId, subscription));
        }

        [Test]
        public void UpdateTasksByPatientIdAsync_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;
            var task = new Tasks
            {
                Id = Guid.NewGuid(),
                Subject = "Patient Task",
                Notes = "Task for specific patient",
                DueDate = DateTime.Now.AddDays(5),
                Priority = "High",
                Status = "Pending"
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _practiceService.UpdateTasksByPatientIdAsync(task, orgId, subscription));

            Assert.That(exception, Is.Not.Null);
        }

        [Test]
        public async Task GetMemberAsync_WhenSuccessful_ReturnsProviderPatients()
        {
            // Arrange
            var expectedMembers = new List<ProviderPatient>
            {
                new ProviderPatient
                {
                    Id = Guid.NewGuid(),
                    PCPId = Guid.NewGuid(),
                    SSN = "***********",
                    Username = "john.doe"
                },
                new ProviderPatient
                {
                    Id = Guid.NewGuid(),
                    PCPId = Guid.NewGuid(),
                    SSN = "***********",
                    Username = "jane.smith"
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedMembers)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _practiceService.GetMemberAsync();

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(expectedMembers.Count));
            Assert.That(result[0].Id, Is.EqualTo(expectedMembers[0].Id));
            Assert.That(result[0].PCPId, Is.EqualTo(expectedMembers[0].PCPId));
            Assert.That(result[0].SSN, Is.EqualTo(expectedMembers[0].SSN));
            Assert.That(result[0].Username, Is.EqualTo(expectedMembers[0].Username));
        }

        [Test]
        public void GetMemberAsync_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.NotFound,
                Content = new StringContent("Not found")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _practiceService.GetMemberAsync());

            Assert.That(exception.Message, Is.EqualTo("Tasks retrieval failure"));
        }
    }
}



