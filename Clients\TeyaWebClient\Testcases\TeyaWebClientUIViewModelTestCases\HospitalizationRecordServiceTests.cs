using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using Moq;
using Moq.Protected;
using NUnit.Framework;
using TeyaUIModels.Model;
using TeyaUIViewModels.ViewModel;
using TeyaUIViewModels.TeyaUIViewModelResources;
using System.Net.Http.Json;
using System.Text;

namespace TeyaWebClientUIViewModelTestCases
{
    [TestFixture]
    public class HospitalizationRecordServiceTests
    {
        private Mock<HttpMessageHandler> _mockHttpMessageHandler;
        private HttpClient _httpClient;
        private Mock<IConfiguration> _mockConfiguration;
        private Mock<IStringLocalizer<HospitalizationRecordService>> _mockLocalizer;
        private Mock<ITokenService> _mockTokenService;
        private HospitalizationRecordService _hospitalizationRecordService;
        private readonly string _baseUrl = "http://test-api.com";
        private readonly string _accessToken = "test-token";

        [SetUp]
        public void Setup()
        {
            // Setup environment variable
            Environment.SetEnvironmentVariable("EncounterNotesURL", _baseUrl);

            // Setup mock HTTP handler
            _mockHttpMessageHandler = new Mock<HttpMessageHandler>();

            // Setup HttpClient with mock handler
            _httpClient = new HttpClient(_mockHttpMessageHandler.Object)
            {
                BaseAddress = new Uri(_baseUrl)
            };

            // Setup mock configuration
            _mockConfiguration = new Mock<IConfiguration>();

            // Setup mock localizer
            _mockLocalizer = new Mock<IStringLocalizer<HospitalizationRecordService>>();
            _mockLocalizer.Setup(l => l["AddressRetrievalFailure"])
                .Returns(new LocalizedString("AddressRetrievalFailure", "Failed to retrieve address"));
            _mockLocalizer.Setup(l => l["DatabaseError"])
                .Returns(new LocalizedString("DatabaseError", "Database error"));

            // Setup mock token service
            _mockTokenService = new Mock<ITokenService>();
            _mockTokenService.Setup(t => t.AccessToken).Returns(_accessToken);

            // Create HospitalizationRecordService with mocked dependencies
            _hospitalizationRecordService = new HospitalizationRecordService(_httpClient, _mockConfiguration.Object, _mockLocalizer.Object, _mockTokenService.Object);
        }

        [TearDown]
        public void TearDown()
        {
            _httpClient.Dispose();
            Environment.SetEnvironmentVariable("EncounterNotesURL", null);
        }

        [Test]
        public async Task GetHospitalizationRecordAsync_WhenSuccessful_ReturnsHospitalizationRecords()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var expectedRecords = new List<HospitalizationRecord>
            {
                new HospitalizationRecord
                {
                    RecordID = Guid.NewGuid(),
                    PatientId = patientId,
                    Reason = "Pneumonia",
                    Date = DateTime.Now.AddDays(-30),
                    IsActive = true
                },
                new HospitalizationRecord
                {
                    RecordID = Guid.NewGuid(),
                    PatientId = patientId,
                    Reason = "Appendicitis",
                    Date = DateTime.Now.AddDays(-90),
                    IsActive = false
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedRecords)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _hospitalizationRecordService.GetHospitalizationRecordAsync(patientId, orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(2));
            Assert.That(result[0].Reason, Is.EqualTo("Pneumonia"));
            Assert.That(result[1].Reason, Is.EqualTo("Appendicitis"));
        }

        [Test]
        public void GetHospitalizationRecordAsync_WhenHttpRequestFails_ThrowsException()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _hospitalizationRecordService.GetHospitalizationRecordAsync(patientId, orgId, subscription));

            Assert.That(exception.Message, Does.Contain("HttpRequestException"));
        }

        [Test]
        public async Task GetHospitalizationRecordByIdAsyncAndIsActive_WhenSuccessful_ReturnsActiveHospitalizationRecords()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var expectedRecords = new List<HospitalizationRecord>
            {
                new HospitalizationRecord
                {
                    RecordID = Guid.NewGuid(),
                    PatientId = patientId,
                    Reason = "Pneumonia",
                    Date = DateTime.Now.AddDays(-30),
                    IsActive = true
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedRecords)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _hospitalizationRecordService.GetHospitalizationRecordByIdAsyncAndIsActive(patientId, orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(1));
            Assert.That(result[0].Reason, Is.EqualTo("Pneumonia"));
            Assert.That(result[0].IsActive, Is.True);
        }

        [Test]
        public void GetHospitalizationRecordByIdAsyncAndIsActive_WhenHttpRequestFails_ThrowsException()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _hospitalizationRecordService.GetHospitalizationRecordByIdAsyncAndIsActive(patientId, orgId, subscription));

            Assert.That(exception.Message, Is.EqualTo("Failed to retrieve address"));
        }

        [Test]
        public async Task CreateHospitalizationRecordAsync_WhenSuccessful_CompletesWithoutException()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var hospitalizationRecords = new List<HospitalizationRecord>
            {
                new HospitalizationRecord
                {
                    RecordID = Guid.NewGuid(),
                    PatientId = patientId,
                    Reason = "Pneumonia",
                    Date = DateTime.Now.AddDays(-30),
                    IsActive = true
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            // No exception should be thrown
            await _hospitalizationRecordService.CreateHospitalizationRecordAsync(hospitalizationRecords, orgId, subscription);
        }

        [Test]
        public void CreateHospitalizationRecordAsync_WhenHttpRequestFails_ThrowsException()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var hospitalizationRecords = new List<HospitalizationRecord>
            {
                new HospitalizationRecord
                {
                    RecordID = Guid.NewGuid(),
                    PatientId = patientId,
                    Reason = "Pneumonia",
                    Date = DateTime.Now.AddDays(-30),
                    IsActive = true
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _hospitalizationRecordService.CreateHospitalizationRecordAsync(hospitalizationRecords, orgId, subscription));
        }

        [Test]
        public async Task UpdateHospitalizationRecordAsync_WhenSuccessful_CompletesWithoutException()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var recordId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var hospitalizationRecord = new HospitalizationRecord
            {
                RecordID = recordId,
                PatientId = patientId,
                Date = DateTime.Now.AddDays(-30),
                Reason = "Severe Pneumonia with complications",
                IsActive = true
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            // No exception should be thrown
            await _hospitalizationRecordService.UpdateHospitalizationRecordAsync(hospitalizationRecord, orgId, subscription);
        }

        [Test]
        public void UpdateHospitalizationRecordAsync_WhenHttpRequestFails_ThrowsException()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var recordId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var hospitalizationRecord = new HospitalizationRecord
            {
                RecordID = recordId,
                PatientId = patientId,
                Date = DateTime.Now.AddDays(-30),
                Reason = "Severe Pneumonia with complications",
                IsActive = true
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _hospitalizationRecordService.UpdateHospitalizationRecordAsync(hospitalizationRecord, orgId, subscription));
        }

        [Test]
        public async Task UpdateHospitalizationRecordList_WhenSuccessful_CompletesWithoutException()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var hospitalizationRecords = new List<HospitalizationRecord>
            {
                new HospitalizationRecord
                {
                    RecordID = Guid.NewGuid(),
                    PatientId = patientId,
                    Date = DateTime.Now.AddDays(-30),
                    Reason = "Pneumonia",
                    IsActive = true
                },
                new HospitalizationRecord
                {
                    RecordID = Guid.NewGuid(),
                    PatientId = patientId,
                    Date = DateTime.Now.AddDays(-90),
                    Reason = "Appendicitis",
                    IsActive = true
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            // No exception should be thrown
            await _hospitalizationRecordService.UpdateHospitalizationRecordList(hospitalizationRecords, orgId, subscription);
        }

        [Test]
        public void UpdateHospitalizationRecordList_WhenHttpRequestFails_ThrowsException()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var hospitalizationRecords = new List<HospitalizationRecord>
            {
                new HospitalizationRecord
                {
                    RecordID = Guid.NewGuid(),
                    PatientId = patientId,
                    Date = DateTime.Now.AddDays(-30),
                    Reason = "Pneumonia",
                    IsActive = true
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _hospitalizationRecordService.UpdateHospitalizationRecordList(hospitalizationRecords, orgId, subscription));
        }

        [Test]
        public async Task DeleteHospitalizationRecordAsync_WhenSuccessful_CompletesWithoutException()
        {
            // Arrange
            var recordId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            // No exception should be thrown
            await _hospitalizationRecordService.DeleteHospitalizationRecordAsync(recordId, orgId, subscription);
        }

        [Test]
        public void DeleteHospitalizationRecordAsync_WhenHttpRequestFails_ThrowsException()
        {
            // Arrange
            var recordId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.ThrowsAsync<Exception>(async () =>
                await _hospitalizationRecordService.DeleteHospitalizationRecordAsync(recordId, orgId, subscription));
        }
    }
}



