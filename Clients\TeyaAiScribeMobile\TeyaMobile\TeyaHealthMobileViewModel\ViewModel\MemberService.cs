﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using System.Net;
using System.Text;
using System.Text.Json;
using TeyaHealthMobileModel.Model;

namespace TeyaHealthMobileViewModel.ViewModel
{
    public class MemberService : IMemberService
    {
        private readonly IAuthenticationService _authenticationService;
        private readonly HttpClient _httpClient;
        private readonly string _MemberService;
        private readonly IStringLocalizer<MemberService> _localizer;
        private readonly ILogger<MemberService> _logger;
        private readonly IConfiguration _configuration;
        private bool HasAccess { get; set; }
        public MemberService(HttpClient httpClient, IStringLocalizer<MemberService> localizer, ILogger<MemberService> logger, IAuthenticationService authenticationService, IConfiguration configuration)
        {
            _authenticationService = authenticationService;
            _httpClient = httpClient;
            _localizer = localizer;
            _logger = logger;
            _configuration = configuration;
            _MemberService = _configuration["MemberServiceURL"];
        }

        public async Task<List<Member>> SearchMembersAsync(string query, Guid OrgID, bool Subscription)
        {
            try
            {
                var accessToken = await _authenticationService.GetServiceSpecificTokenAsync();
                if (string.IsNullOrEmpty(accessToken))
                {
                    _logger.LogError(_localizer["AccessTokenNotFound"]);
                    throw new UnauthorizedAccessException(_localizer["AccessTokenMissing"]);
                }
                var apiUrl = $"{_MemberService}/api/Registration/search/{OrgID}/{Subscription}?searchTerm={Uri.EscapeDataString(query)}";
                var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
                var response = await _httpClient.SendAsync(requestMessage);

                if (response.IsSuccessStatusCode)
                {
                    var responseData = await response.Content.ReadAsStringAsync();
                    var options = new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    };

                    return JsonSerializer.Deserialize<List<Member>>(responseData, options);
                }
                else
                {

                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError($"API Request Failed. Status Code: {response.StatusCode}, Reason: {response.ReasonPhrase}, Response: {errorContent}");

                    throw new HttpRequestException($"Search failed with status code {response.StatusCode}: {response.ReasonPhrase}. Response: {errorContent}");
                }
            }
            catch (Exception ex)
            {

                _logger.LogError(ex, _localizer["ErrorSearchingMembers"], query);
                _logger.LogError($"Exception occurred: {ex.Message}, StackTrace: {ex.StackTrace}");

                throw;
            }
        }
        public async Task<bool> SearchMembersEmailAsync(string query, Guid orgID, bool subscription)
        {
            if (string.IsNullOrWhiteSpace(query))
            {
                _logger.LogWarning("Search query is null, empty, or whitespace. Returning false.");
                return false;
            }

            if (orgID == Guid.Empty)
            {
                _logger.LogWarning("Organization ID is empty. Returning false.");
                return false;
            }

            try
            {
                var accessToken = await _authenticationService.GetServiceSpecificTokenAsync();
                if (string.IsNullOrEmpty(accessToken))
                {
                    _logger.LogError("Access token not available for member email search");
                    throw new UnauthorizedAccessException("Authentication required");
                }

                var escapedQuery = Uri.EscapeDataString(query.Trim());
                var apiUrl = $"{_MemberService}/api/Registration/searchByEmail/{orgID}/{subscription}?searchTerm={escapedQuery}";

                var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);

                if (response.IsSuccessStatusCode)
                {
                    var responseData = await response.Content.ReadAsStringAsync();
                    if (!string.IsNullOrWhiteSpace(responseData))
                    {
                        var options = new JsonSerializerOptions { PropertyNameCaseInsensitive = true };
                        var members = JsonSerializer.Deserialize<List<Member>>(responseData, options);
                        return members?.Any() == true;
                    }
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogWarning("Member email search failed. Status: {StatusCode}, Content: {ErrorContent}",
                        response.StatusCode, errorContent);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching members by email: {Query}, OrgID: {OrgID}", query, orgID);
            }

            return false;
        }

        public async Task<List<ProviderPatient>> GetProviderPatientByOrganizationIdAsync(Guid OrganizationId, bool Subscription)
        {
            try
            {
                var accessToken = await _authenticationService.GetServiceSpecificTokenAsync();

                if (string.IsNullOrEmpty(accessToken))
                {
                    _logger.LogError(_localizer["AccessTokenNotFound"]);
                    throw new UnauthorizedAccessException(_localizer["AccessTokenMissing"]);
                }
                var apiUrl = $"{_MemberService}/api/Registration/organization/provider/{OrganizationId}/{Subscription}";
                var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);
                if (response.IsSuccessStatusCode)
                {
                    var responseData = await response.Content.ReadAsStringAsync();
                    var options = new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    };


                    return JsonSerializer.Deserialize<List<ProviderPatient>>(responseData, options);
                }
                else if (response.StatusCode == HttpStatusCode.NotFound)
                {
                    _logger.LogWarning($"No providers found for Organization ID: {OrganizationId}/{Subscription}");
                    return new List<ProviderPatient>();
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError($"API Request Failed. Status Code: {response.StatusCode}, Response: {errorContent}");
                    throw new HttpRequestException(_localizer["GetByIdFailed"]);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorFetchingProviderPatientByOrganizationId"], OrganizationId);
                throw;
            }
        }

        public async Task<List<ProviderPatient>> GetPatientsByOrganizationIdAsync(Guid OrganizationId, bool Subscription)
        {
            try
            {
                var accessToken = await _authenticationService.GetServiceSpecificTokenAsync();

                if (string.IsNullOrEmpty(accessToken))
                {
                    _logger.LogError(_localizer["AccessTokenNotFound"]);
                    throw new UnauthorizedAccessException(_localizer["AccessTokenMissing"]);
                }
                var apiUrl = $"{_MemberService}/api/Registration/organization/patient/{OrganizationId}/{Subscription}";
                var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);
                if (response.IsSuccessStatusCode)
                {
                    var responseData = await response.Content.ReadAsStringAsync();
                    var options = new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    };


                    return JsonSerializer.Deserialize<List<ProviderPatient>>(responseData, options);
                }
                else if (response.StatusCode == HttpStatusCode.NotFound)
                {
                    _logger.LogWarning($"No patient found for Organization ID: {OrganizationId}");
                    return new List<ProviderPatient>();
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError($"API Request Failed. Status Code: {response.StatusCode}, Response: {errorContent}");
                    throw new HttpRequestException(_localizer["GetByIdFailed"]);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorFetchingPatientByOrganizationId"], OrganizationId);
                throw;
            }
        }

        public async Task<Member> RegisterMembersAsync(List<Member> members)
        {
            try
            {
                var accessToken = await _authenticationService.GetServiceSpecificTokenAsync();

                if (string.IsNullOrEmpty(accessToken))
                {
                    _logger.LogError(_localizer["AccessTokenNotFound"]);
                    throw new UnauthorizedAccessException(_localizer["AccessTokenMissing"]);
                }

                var bodyContent = JsonSerializer.Serialize(members);
                var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");
                var apiUrl = $"{_MemberService}/api/Registration/registration";
                var requestMessage = new HttpRequestMessage(HttpMethod.Post, apiUrl)
                {
                    Content = content
                };
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
                var response = await _httpClient.SendAsync(requestMessage);

                if (response.IsSuccessStatusCode)
                {
                    var responseData = await response.Content.ReadAsStringAsync();


                    try
                    {
                        return JsonSerializer.Deserialize<Member>(responseData);
                    }
                    catch (JsonException ex)
                    {
                        _logger.LogError(ex, _localizer["JsonDeserializationError", ex.Message]);
                        throw;
                    }
                }
                else if (response.StatusCode == System.Net.HttpStatusCode.Conflict)
                {
                    throw new HttpRequestException(_localizer["UserAlreadyExists"]);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    throw new HttpRequestException(_localizer["RegistrationFailed"]);
                }
            }
            catch (Exception ex)
            {
                _logger.LogInformation(ex, _localizer["MemberRegistrationError"]);
                throw;
            }
        }
        public async Task<string> RegisterMembersContentAsync(List<Member> members)
        {
            string result;

            try
            {
                var accessToken = await _authenticationService.GetServiceSpecificTokenAsync();

                if (string.IsNullOrEmpty(accessToken))
                {
                    _logger.LogError(_localizer["AccessTokenNotFound"]);
                    throw new UnauthorizedAccessException(_localizer["AccessTokenMissing"]);
                }

                var bodyContent = JsonSerializer.Serialize(members);
                var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");
                var apiUrl = $"{_MemberService}/api/Registration/registration";
                var requestMessage = new HttpRequestMessage(HttpMethod.Post, apiUrl)
                {
                    Content = content
                };
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
                var response = await _httpClient.SendAsync(requestMessage);

                var responseData = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    _logger.LogError(_localizer["RegistrationFailed"] + ": " + responseData);
                }

                result = responseData;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["MemberRegistrationError"]);
                result = _localizer["MemberRegistrationError"];
            }

            return result;
        }

        public async Task<Member> GetMemberByIdAsync(Guid memberId, Guid orgId, bool Subscription)
        {
            try
            {
                var accessToken = await _authenticationService.GetServiceSpecificTokenAsync();

                if (string.IsNullOrEmpty(accessToken))
                {
                    _logger.LogError(_localizer["AccessTokenNotFound"]);
                    throw new UnauthorizedAccessException(_localizer["AccessTokenMissing"]);
                }
                var apiUrl = $"{_MemberService}/api/Registration/{memberId}/{orgId}/{Subscription}";
                var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);
                if (response.IsSuccessStatusCode)
                {
                    var responseData = await response.Content.ReadAsStringAsync();
                    var options = new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    };

                    return JsonSerializer.Deserialize<Member>(responseData, options);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError($"API Request Failed. Status Code: {response.StatusCode}, Response: {errorContent}");
                    throw new HttpRequestException(_localizer["GetByIdFailed"]);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorFetchingMemberById"], memberId);
                throw;
            }
        }

        public async Task<List<string>> GetProviderlistAsync(Guid orgId, bool Subscription)
        {
            try
            {
                var allMembers = await GetAllMembersAsync(orgId, Subscription);

                return allMembers?
                .Where(member => member.OrganizationID == orgId)
                .Select(member => member.UserName)
                .Where(userName => !string.IsNullOrEmpty(userName))
                .Distinct()
                .ToList() ?? new List<string>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorFetchingProviderTypes"]);
                throw;
            }
        }


        public async Task<List<Member>> GetAllMembersAsync(Guid OrgID, bool Subscription)
        {
            try
            {
                var accessToken = await _authenticationService.GetServiceSpecificTokenAsync();
                if (string.IsNullOrEmpty(accessToken))
                {
                    _logger.LogError(_localizer["AccessTokenNotFound"]);
                    throw new UnauthorizedAccessException(_localizer["AccessTokenMissing"]);
                }
                var apiUrl = $"{_MemberService}/api/Registration/{OrgID}/{Subscription}";
                var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);
                response.EnsureSuccessStatusCode();

                var responseData = await response.Content.ReadAsStringAsync();
                var options = new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                };

                return JsonSerializer.Deserialize<List<Member>>(responseData, options);
            }
            catch (Exception ex)
            {
                var response = ex.InnerException.Message;
                var response1 = ex.InnerException.ToString;
                _logger.LogError(ex, _localizer["ErrorFetchingAllMembers"]);
                throw;
            }
        }

        public async Task DeleteMemberByIdAsync(Guid memberId, Guid OrgID, bool Subscription)
        {
            try
            {
                var accessToken = await _authenticationService.GetServiceSpecificTokenAsync();
                if (string.IsNullOrEmpty(accessToken))
                {
                    _logger.LogError(_localizer["AccessTokenNotFound"]);
                    throw new UnauthorizedAccessException(_localizer["AccessTokenMissing"]);
                }

                var apiUrl = $"{_MemberService}/api/Registration/{memberId}";
                var requestMessage = new HttpRequestMessage(HttpMethod.Delete, apiUrl);
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);
                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation(_localizer["MemberDeletedSuccessfully"], memberId);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError($"Delete failed. Status Code: {response.StatusCode}, Response: {errorContent}");
                    throw new HttpRequestException(_localizer["MemberDeletionFailed"]);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorDeletingMember"], memberId);
                throw;
            }
        }

        public async Task UpdateMemberByIdAsync(Guid memberId, Member member)
        {
            if (member == null || member.Id != memberId)
            {
                _logger.LogError(_localizer["InvalidMember"]);
                throw new ArgumentException(_localizer["InvalidMember"]);
            }

            try
            {
                var accessToken = await _authenticationService.GetServiceSpecificTokenAsync();
                if (string.IsNullOrEmpty(accessToken))
                {
                    _logger.LogError(_localizer["AccessTokenNotFound"]);
                    throw new UnauthorizedAccessException(_localizer["AccessTokenMissing"]);
                }

                var apiUrl = $"{_MemberService}/api/Registration/{memberId}";
                var bodyContent = JsonSerializer.Serialize(member);
                var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");

                var requestMessage = new HttpRequestMessage(HttpMethod.Put, apiUrl);
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
                requestMessage.Content = content;

                var response = await _httpClient.SendAsync(requestMessage);

                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation(_localizer["UpdateSuccessful"], memberId);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError($"Update failed. Status Code: {response.StatusCode}, Response: {errorContent}");
                    throw new HttpRequestException(_localizer["UpdateFailed"]);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorUpdatingMember"], memberId);
                throw;
            }
        }

        public async Task<List<Member>> GetMembersForProductAsync(Guid productId, Guid OrgID, bool Subscription)
        {
            try
            {
                var accessToken = await _authenticationService.GetServiceSpecificTokenAsync();
                if (string.IsNullOrEmpty(accessToken))
                {
                    _logger.LogError(_localizer["AccessTokenNotFound"]);
                    throw new UnauthorizedAccessException(_localizer["AccessTokenMissing"]);
                }

                var apiUrl = $"{_MemberService}/{productId.ToString()}/members/{OrgID}/{Subscription}";

                var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);
                response.EnsureSuccessStatusCode();

                var responseData = await response.Content.ReadAsStringAsync();
                var options = new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                };

                return JsonSerializer.Deserialize<List<Member>>(responseData, options);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorFetchingMembersForProduct"], productId);
                throw;
            }
        }
        public async Task<bool> HasProductAccess(Guid memberId, Guid productId, Guid OrgID, bool Subscription)
        {
            try
            {
                var accessToken = await _authenticationService.GetServiceSpecificTokenAsync();
                if (string.IsNullOrEmpty(accessToken))
                {
                    _logger.LogError(_localizer["AccessTokenNotFound"]);
                    throw new UnauthorizedAccessException(_localizer["AccessTokenMissing"]);
                }

                var apiUrl = $"{_MemberService}/api/Registration/check-access/{OrgID}/{Subscription}?memberId={memberId}&productId={productId}";

                var requestBody = new
                {
                    MemberId = memberId,
                    ProductId = productId
                };

                var bodyContent = JsonSerializer.Serialize(requestBody);
                var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");

                var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
                requestMessage.Content = content;

                var response = await _httpClient.SendAsync(requestMessage);

                var responseData = await response.Content.ReadAsStringAsync();


                if (response.IsSuccessStatusCode)
                {
                    if (responseData.Contains("AccessGranted"))
                    {
                        _logger.LogInformation(_localizer["UpdateSuccessful"], memberId);
                        HasAccess = true;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorUpdatingMember"], memberId);
            }
            return HasAccess;
        }
    }
}
