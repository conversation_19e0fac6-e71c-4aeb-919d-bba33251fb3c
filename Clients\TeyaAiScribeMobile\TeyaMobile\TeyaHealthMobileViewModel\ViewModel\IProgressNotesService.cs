﻿using TeyaHealthMobileModel.Model;

namespace TeyaHealthMobileViewModel.ViewModel
{
    public interface IProgressNotesService
    {
        Task<List<Record>> GetRecordsByPatientIdAsync(Guid id, Guid? OrgID, bool Subscription);
        Task<List<Record>> GetRecordsByPCPIdAsync(Guid pcpId, Guid? OrgID, bool Subscription);
        Task<HttpResponseMessage> SaveRecordAsync(Record updatedRecord, Guid? OrgID, bool Subscription);
        Task<HttpResponseMessage> UploadRecordAsync(Record newRecord, Guid? OrgID, bool Subscription);
        Task<Record> GetRecordByIdAsync(Guid recordId, Guid? OrgId, bool Subscription);
        Task<List<Record>> GetAllByOrgIdAsync(Guid? OrgId, bool Subscription);
    }
}
