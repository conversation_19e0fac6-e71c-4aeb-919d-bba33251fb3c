using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using Moq;
using Moq.Protected;
using NUnit.Framework;
using TeyaUIModels.Model;
using TeyaUIViewModels.ViewModel;
using TeyaUIViewModels.TeyaUIViewModelResources;
using System.Net.Http.Json;
using System.Text.Json;

namespace TeyaWebClientUIViewModelTestCases
{
    [TestFixture]
    public class VaccineServiceTests
    {
        private Mock<HttpMessageHandler> _mockHttpMessageHandler;
        private HttpClient _httpClient;
        private Mock<IConfiguration> _mockConfiguration;
        private Mock<IStringLocalizer<TeyaUIViewModelsStrings>> _mockLocalizer;
        private Mock<ITokenService> _mockTokenService;
        private VaccineService _vaccineService;
        private readonly string _baseUrl = "http://test-api.com";
        private readonly string _accessToken = "test-access-token";

        [SetUp]
        public void Setup()
        {
            // Setup environment variable
            Environment.SetEnvironmentVariable("EncounterNotesURL", _baseUrl);

            // Setup mock HTTP handler
            _mockHttpMessageHandler = new Mock<HttpMessageHandler>();

            // Setup HttpClient with mock handler
            _httpClient = new HttpClient(_mockHttpMessageHandler.Object)
            {
                BaseAddress = new Uri(_baseUrl)
            };

            // Setup mock configuration
            _mockConfiguration = new Mock<IConfiguration>();

            // Setup mock localizer
            _mockLocalizer = new Mock<IStringLocalizer<TeyaUIViewModelsStrings>>();
            _mockLocalizer.Setup(l => l["VaccineDataRetrievalFailure"])
                .Returns(new LocalizedString("VaccineDataRetrievalFailure", "Vaccine Data Retrieval Failure"));
            _mockLocalizer.Setup(l => l["VaccineRetrievalFailure"])
                .Returns(new LocalizedString("VaccineRetrievalFailure", "Vaccine retrieval failure"));
            _mockLocalizer.Setup(l => l["VaccineAdditionFailure"])
                .Returns(new LocalizedString("VaccineAdditionFailure", "Vaccine addition failure"));
            _mockLocalizer.Setup(l => l["VaccineUpdateFailure"])
                .Returns(new LocalizedString("VaccineUpdateFailure", "Vaccine update failure"));
            _mockLocalizer.Setup(l => l["VaccineDeletionFailure"])
                .Returns(new LocalizedString("VaccineDeletionFailure", "Vaccine deletion failure"));

            // Setup mock token service
            _mockTokenService = new Mock<ITokenService>();
            _mockTokenService.Setup(t => t.AccessToken).Returns(_accessToken);
            _mockTokenService.Setup(t => t.GetValidatedAccessTokenAsync()).ReturnsAsync(_accessToken);

            // Create VaccineService with mocked dependencies
            _vaccineService = new VaccineService(_httpClient, _mockConfiguration.Object, _mockLocalizer.Object, _mockTokenService.Object);
        }

        [TearDown]
        public void TearDown()
        {
            _httpClient.Dispose();
            Environment.SetEnvironmentVariable("EncounterNotesURL", null);
        }

        [Test]
        public async Task GetAllVaccinesDataAsync_WhenSuccessful_ReturnsVaccines()
        {
            // Arrange
            var expectedVaccines = new List<Vaccines>
            {
                new Vaccines
                {
                    Id = Guid.NewGuid(),
                    VaccineName = "COVID-19 Vaccine",
                    CVXCode = "208",
                    CPTCode = "91300",
                    CPTDescription = "COVID-19 vaccine, mRNA, spike protein, LNP, preservative free, 30 mcg/0.3mL dosage"
                },
                new Vaccines
                {
                    Id = Guid.NewGuid(),
                    VaccineName = "Influenza Vaccine",
                    CVXCode = "141",
                    CPTCode = "90688",
                    CPTDescription = "Influenza virus vaccine, quadrivalent, split virus, preservative free"
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedVaccines)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _vaccineService.GetAllVaccinesDataAsync();

            // Assert
            Assert.Multiple(() =>
            {
                Assert.That(result, Is.Not.Null);
                Assert.That(result, Has.Count.EqualTo(expectedVaccines.Count));
                Assert.That(result[0].Id, Is.EqualTo(expectedVaccines[0].Id));
                Assert.That(result[0].VaccineName, Is.EqualTo(expectedVaccines[0].VaccineName));
                Assert.That(result[0].CVXCode, Is.EqualTo(expectedVaccines[0].CVXCode));
                Assert.That(result[0].CPTCode, Is.EqualTo(expectedVaccines[0].CPTCode));
                Assert.That(result[0].CPTDescription, Is.EqualTo(expectedVaccines[0].CPTDescription));
                Assert.That(result[1].VaccineName, Is.EqualTo("Influenza Vaccine"));
            });
        }

        [Test]
        public void GetAllVaccinesDataAsync_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.NotFound,
                Content = new StringContent("Not found")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _vaccineService.GetAllVaccinesDataAsync());

            Assert.That(exception.Message, Is.EqualTo("Vaccine Data Retrieval Failure"));
        }

        [Test]
        public async Task GetAllVaccinesDataBySearchTermAsync_WhenSuccessful_ReturnsFilteredVaccines()
        {
            // Arrange
            var searchTerm = "COVID";
            var expectedVaccines = new List<Vaccines>
            {
                new Vaccines
                {
                    Id = Guid.NewGuid(),
                    VaccineName = "Active COVID-19 Vaccine",
                    CVXCode = "208",
                    CPTCode = "91301",
                    CPTDescription = "COVID-19 vaccine, mRNA, spike protein, LNP, preservative free, 100 mcg/0.5mL dosage"
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedVaccines)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _vaccineService.GetAllVaccinesDataBySearchTermAsync(searchTerm);

            // Assert
            Assert.Multiple(() =>
            {
                Assert.That(result, Is.Not.Null);
                Assert.That(result, Has.Count.EqualTo(expectedVaccines.Count));
                Assert.That(result[0].VaccineName, Is.EqualTo("Active COVID-19 Vaccine"));
                Assert.That(result[0].CVXCode, Is.EqualTo("208"));
            });
        }

        [Test]
        public void GetAllVaccinesDataBySearchTermAsync_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var searchTerm = "COVID";

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _vaccineService.GetAllVaccinesDataBySearchTermAsync(searchTerm));

            Assert.That(exception.Message, Is.EqualTo("Vaccine Data Retrieval Failure"));
        }





        [Test]
        public void VaccineService_Constructor_WhenValidDependencies_CreatesInstance()
        {
            // Act & Assert
            Assert.That(_vaccineService, Is.Not.Null);
            Assert.That(_vaccineService, Is.InstanceOf<VaccineService>());
        }

        [Test]
        public void VaccineService_WhenHttpClientIsNull_DoesNotThrow()
        {
            // Act & Assert
            Assert.DoesNotThrow(() =>
                new VaccineService(null, _mockConfiguration.Object, _mockLocalizer.Object, _mockTokenService.Object));
        }



        [Test]
        public async Task GetAllVaccinesDataAsync_WhenEmptyResponse_ReturnsEmptyList()
        {
            // Arrange
            var expectedVaccines = new List<Vaccines>();

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedVaccines)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _vaccineService.GetAllVaccinesDataAsync();

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(0));
        }

        [Test]
        public async Task GetAllVaccinesDataBySearchTermAsync_WhenEmptyResponse_ReturnsEmptyList()
        {
            // Arrange
            var searchTerm = "COVID";
            var expectedVaccines = new List<Vaccines>();

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedVaccines)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _vaccineService.GetAllVaccinesDataBySearchTermAsync(searchTerm);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(0));
        }
    }
}



