﻿using Microsoft.AspNetCore.Components;

namespace TeyaWebApp.Components.GenericFontComponents
{
    // Code/Monospace Component
    public partial class GenericCode : ComponentBase
    {
        [Parameter]
        public RenderFragment ChildContent { get; set; }

        [Parameter]
        public string Size { get; set; } = FontSize.Small;

        [Parameter]
        public string Color { get; set; } = "#333";

        [Parameter]
        public string BackgroundColor { get; set; } = "#f5f5f5";

        [Parameter]
        public string BorderRadius { get; set; } = "3px";

        [Parameter]
        public string Padding { get; set; } = "0.2em 0.4em";

        [Parameter]
        public string Class { get; set; } = "";

        [Parameter(CaptureUnmatchedValues = true)]
        public Dictionary<string, object> AdditionalAttributes { get; set; }

        protected override void BuildRenderTree(Microsoft.AspNetCore.Components.Rendering.RenderTreeBuilder builder)
        {
            builder.OpenElement(0, "code");
            builder.AddAttribute(1, "class", $"generic-code {Class}");
            builder.AddAttribute(2, "style", GetStyle());

            if (AdditionalAttributes != null)
            {
                foreach (var attribute in AdditionalAttributes)
                {
                    builder.AddAttribute(3, attribute.Key, attribute.Value);
                }
            }

            builder.AddContent(4, ChildContent);
            builder.CloseElement();
        }

        private string GetStyle()
        {
            return $"font-family: {FontFamily.Monospace}; font-size: {Size}; color: {Color}; background-color: {BackgroundColor}; border-radius: {BorderRadius}; padding: {Padding};";
        }
    }
}
