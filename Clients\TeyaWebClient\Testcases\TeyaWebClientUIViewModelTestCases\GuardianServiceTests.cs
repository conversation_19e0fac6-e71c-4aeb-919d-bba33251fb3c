using NUnit.Framework;
using Moq;
using Moq.Protected;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using TeyaUIViewModels.ViewModel;
using TeyaUIModels.Model;
using TeyaUIViewModels.TeyaUIViewModelResource;
using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Threading.Tasks;
using System.Threading;
using System.Net;
using System.Text.Json;
using System.Text;

namespace TeyaWebClientUIViewModelTestCases
{
    [TestFixture]
    public class GuardianServiceTests
    {
        private Mock<HttpMessageHandler> _mockHttpMessageHandler;
        private Mock<ILogger<GuardianService>> _mockLogger;
        private Mock<IStringLocalizer<GuardianService>> _mockLocalizer;
        private HttpClient _httpClient;
        private GuardianService _guardianService;
        private const string TestMemberServiceUrl = "https://test-memberservice.com";

        [SetUp]
        public void SetUp()
        {
            _mockHttpMessageHandler = new Mock<HttpMessageHandler>();
            _mockLogger = new Mock<ILogger<GuardianService>>();
            _mockLocalizer = new Mock<IStringLocalizer<GuardianService>>();
            _httpClient = new HttpClient(_mockHttpMessageHandler.Object);

            // Set up environment variable
            Environment.SetEnvironmentVariable("MemberServiceURL", TestMemberServiceUrl);

            // Set up localizer mock responses
            _mockLocalizer.Setup(l => l["Base URL not configured"]).Returns(new LocalizedString("Base URL not configured", "Base URL not configured"));
            _mockLocalizer.Setup(l => l["Failed to fetch Guardian with ID {id}", It.IsAny<object>()])
                .Returns((string key, object[] args) => new LocalizedString(key, $"Failed to fetch Guardian with ID {args[0]}"));
            _mockLocalizer.Setup(l => l["Error fetching Guardian with ID {id}", It.IsAny<object>()])
                .Returns((string key, object[] args) => new LocalizedString(key, $"Error fetching Guardian with ID {args[0]}"));
            _mockLocalizer.Setup(l => l["Error adding Guardian"]).Returns(new LocalizedString("Error adding Guardian", "Error adding Guardian"));
            _mockLocalizer.Setup(l => l["Error updating Guardian with ID {id}", It.IsAny<object>()])
                .Returns((string key, object[] args) => new LocalizedString(key, $"Error updating Guardian with ID {args[0]}"));
            _mockLocalizer.Setup(l => l["Failed to delete Guardian with ID {id}", It.IsAny<object>()])
                .Returns((string key, object[] args) => new LocalizedString(key, $"Failed to delete Guardian with ID {args[0]}"));
            _mockLocalizer.Setup(l => l["Error deleting Guardian with ID {id}", It.IsAny<object>()])
                .Returns((string key, object[] args) => new LocalizedString(key, $"Error deleting Guardian with ID {args[0]}"));
            _mockLocalizer.Setup(l => l["Failed to fetch Guardians by name {name}", It.IsAny<object>()])
                .Returns((string key, object[] args) => new LocalizedString(key, $"Failed to fetch Guardians by name {args[0]}"));
            _mockLocalizer.Setup(l => l["Error fetching Guardians by name {name}", It.IsAny<object>()])
                .Returns((string key, object[] args) => new LocalizedString(key, $"Error fetching Guardians by name {args[0]}"));
            _mockLocalizer.Setup(l => l["Failed to fetch all Guardianes"]).Returns(new LocalizedString("Failed to fetch all Guardianes", "Failed to fetch all Guardianes"));
            _mockLocalizer.Setup(l => l["Error fetching all Guardianes"]).Returns(new LocalizedString("Error fetching all Guardianes", "Error fetching all Guardianes"));

            _guardianService = new GuardianService(_httpClient, _mockLogger.Object, _mockLocalizer.Object);
        }

        [TearDown]
        public void TearDown()
        {
            Environment.SetEnvironmentVariable("MemberServiceURL", null);
            _httpClient?.Dispose();
        }

        [Test]
        public async Task GetGuardianByIdAsync_WhenSuccessful_ReturnsGuardian()
        {
            // Arrange
            var guardianId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = false;
            var expectedGuardian = new Guardian
            {
                GuardianId = guardianId,
                GuardianName = "John Doe",
                GuardianRelationship = "Parent",
                GuardianSex = "Male",
                GuardianCity = "Anytown",
                GuardianState = "CA",
                GuardianCountry = "USA",
                GuardianPhone = "************",
                GuardianEmail = "<EMAIL>",
                GuardianAddress = "123 Main St",
                OrganizationId = orgId,
                Subscription = subscription
            };

            var responseContent = JsonSerializer.Serialize(expectedGuardian);
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(responseContent, Encoding.UTF8, "application/json")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _guardianService.GetGuardianByIdAsync(guardianId, orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.GuardianId, Is.EqualTo(expectedGuardian.GuardianId));
            Assert.That(result.GuardianName, Is.EqualTo(expectedGuardian.GuardianName));
            Assert.That(result.GuardianRelationship, Is.EqualTo(expectedGuardian.GuardianRelationship));
            Assert.That(result.GuardianEmail, Is.EqualTo(expectedGuardian.GuardianEmail));
        }

        [Test]
        public async Task GetGuardianByIdAsync_WhenNotFound_ReturnsNull()
        {
            // Arrange
            var guardianId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = false;

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.NotFound,
                Content = new StringContent("Guardian not found")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _guardianService.GetGuardianByIdAsync(guardianId, orgId, subscription);

            // Assert
            Assert.That(result, Is.Null);

            // Verify warning was logged
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Warning,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.IsAny<Exception>(),
                    It.Is<Func<It.IsAnyType, Exception?, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }

        [Test]
        public void GetGuardianByIdAsync_WhenExceptionThrown_PropagatesException()
        {
            // Arrange
            var guardianId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = false;
            var expectedException = new HttpRequestException("Network error");

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ThrowsAsync(expectedException);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _guardianService.GetGuardianByIdAsync(guardianId, orgId, subscription));

            Assert.That(exception.Message, Is.EqualTo(expectedException.Message));

            // Verify error was logged
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.Is<Exception>(ex => ex.Message == expectedException.Message),
                    It.Is<Func<It.IsAnyType, Exception?, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }

        [Test]
        public async Task AddGuardianAsync_WhenSuccessful_ReturnsTrue()
        {
            // Arrange
            var guardian = new Guardian
            {
                GuardianId = Guid.NewGuid(),
                GuardianName = "Jane Smith",
                GuardianRelationship = "Mother",
                GuardianEmail = "<EMAIL>",
                OrganizationId = Guid.NewGuid(),
                Subscription = false
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.Created
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _guardianService.AddGuardianAsync(guardian);

            // Assert
            Assert.That(result, Is.True);
        }

        [Test]
        public async Task AddGuardianAsync_WhenHttpRequestFails_ReturnsFalse()
        {
            // Arrange
            var guardian = new Guardian
            {
                GuardianId = Guid.NewGuid(),
                GuardianName = "Jane Smith",
                OrganizationId = Guid.NewGuid(),
                Subscription = false
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _guardianService.AddGuardianAsync(guardian);

            // Assert
            Assert.That(result, Is.False);
        }

        [Test]
        public void AddGuardianAsync_WhenExceptionThrown_PropagatesException()
        {
            // Arrange
            var guardian = new Guardian();
            var expectedException = new HttpRequestException("Network error");

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ThrowsAsync(expectedException);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _guardianService.AddGuardianAsync(guardian));

            Assert.That(exception.Message, Is.EqualTo(expectedException.Message));

            // Verify error was logged
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.Is<Exception>(ex => ex.Message == expectedException.Message),
                    It.Is<Func<It.IsAnyType, Exception?, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }

        [Test]
        public async Task UpdateGuardianAsync_WhenSuccessful_ReturnsTrue()
        {
            // Arrange
            var guardianId = Guid.NewGuid();
            var guardian = new Guardian
            {
                GuardianId = guardianId,
                GuardianName = "Updated Name",
                GuardianEmail = "<EMAIL>",
                OrganizationId = Guid.NewGuid(),
                Subscription = false
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _guardianService.UpdateGuardianAsync(guardianId, guardian);

            // Assert
            Assert.That(result, Is.True);
        }

        [Test]
        public async Task UpdateGuardianAsync_WhenHttpRequestFails_ReturnsFalse()
        {
            // Arrange
            var guardianId = Guid.NewGuid();
            var guardian = new Guardian();

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _guardianService.UpdateGuardianAsync(guardianId, guardian);

            // Assert
            Assert.That(result, Is.False);
        }

        [Test]
        public async Task DeleteGuardianAsync_WhenSuccessful_ReturnsTrue()
        {
            // Arrange
            var guardianId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = false;

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.NoContent
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _guardianService.DeleteGuardianAsync(guardianId, orgId, subscription);

            // Assert
            Assert.That(result, Is.True);
        }

        [Test]
        public async Task GetAllGuardianAsync_WhenSuccessful_ReturnsGuardians()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;

            var expectedGuardians = new List<Guardian>
            {
                new Guardian
                {
                    GuardianId = Guid.NewGuid(),
                    GuardianName = "John Doe",
                    GuardianRelationship = "Father"
                },
                new Guardian
                {
                    GuardianId = Guid.NewGuid(),
                    GuardianName = "Jane Smith",
                    GuardianRelationship = "Mother"
                }
            };

            var responseContent = JsonSerializer.Serialize(expectedGuardians);
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(responseContent, Encoding.UTF8, "application/json")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _guardianService.GetAllGuardianAsync(orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(2));
            Assert.That(result[0].GuardianName, Is.EqualTo("John Doe"));
            Assert.That(result[1].GuardianName, Is.EqualTo("Jane Smith"));
        }

        [Test]
        public async Task GetAllGuardianAsync_WhenNotFound_ReturnsNull()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.NotFound,
                Content = new StringContent("No guardians found")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _guardianService.GetAllGuardianAsync(orgId, subscription);

            // Assert
            Assert.That(result, Is.Null);

            // Verify warning was logged
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Warning,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.IsAny<Exception>(),
                    It.Is<Func<It.IsAnyType, Exception?, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }
    }
}



