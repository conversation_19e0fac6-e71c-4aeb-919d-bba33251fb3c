﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <Version>1.0.50702.3</Version>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
	<ScopedCssEnabled>true</ScopedCssEnabled>
  </PropertyGroup>

  <ItemGroup>
	 <PackageReference Include = "Blazored.LocalStorage" Version="4.4.0" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.OpenIdConnect" Version="9.0.0" />
    <PackageReference Include="Microsoft.Identity.Web" Version="3.8.4" />
    <PackageReference Include="Microsoft.Identity.Web.UI" Version="3.3.0" />

    <PackageReference Include="Microsoft.AspNetCore.Components.Authorization" Version="9.0.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\TeyaMobile.Shared\TeyaMobile.Shared.csproj" />
    <ProjectReference Include="..\TeyaHealthMobileModel\TeyaHealthMobileModel.csproj" />
    <ProjectReference Include="..\TeyaHealthMobileViewModel\TeyaHealthMobileViewModel.csproj" />
  </ItemGroup>

</Project>