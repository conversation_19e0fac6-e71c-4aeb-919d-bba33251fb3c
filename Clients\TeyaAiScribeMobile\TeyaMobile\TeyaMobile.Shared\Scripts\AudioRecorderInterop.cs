﻿using Microsoft.Extensions.Logging;
using Microsoft.JSInterop;
using TeyaMobile.Shared.Pages;

namespace TeyaMobile.Shared.Scripts
{
    public class AudioRecorderInterop
    {
        private readonly IJSRuntime _jsRuntime;
        private readonly DotNetObjectReference<AudioRecorderComponent> _dotNetRef;
        private readonly ILogger<AudioRecorderInterop>? _logger;

        public AudioRecorderInterop(IJSRuntime jsRuntime, TeyaMobile.Shared.Pages.AudioRecorderComponent component, ILogger<AudioRecorderInterop>? logger = null)
        {
            _jsRuntime = jsRuntime;
            _dotNetRef = DotNetObjectReference.Create(component);
            _logger = logger;
        }

        public async Task InitializeAsync()
        {
            try
            {
                await _jsRuntime.InvokeVoidAsync("BlazorAudioRecorder.Initialize", _dotNetRef);
                _logger?.LogInformation("AudioRecorder JavaScript initialized successfully");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Failed to initialize AudioRecorder JavaScript");
                throw;
            }
        }

        public async Task StartRecordAsync()
        {
            try
            {
                await _jsRuntime.InvokeVoidAsync("BlazorAudioRecorder.StartRecord");
                _logger?.LogInformation("JavaScript recording started");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Failed to start JavaScript recording");
                throw;
            }
        }

        public async Task StopRecordAsync(Guid recordingId, string accessToken = "")
        {
            try
            {
                await _jsRuntime.InvokeVoidAsync("BlazorAudioRecorder.StopRecord", recordingId.ToString(), accessToken);
                _logger?.LogInformation($"JavaScript recording stopped for ID: {recordingId}");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"Failed to stop JavaScript recording for ID: {recordingId}");
                throw;
            }
        }

        public async Task PauseRecordAsync()
        {
            try
            {
                await _jsRuntime.InvokeVoidAsync("BlazorAudioRecorder.PauseRecord");
                _logger?.LogInformation("JavaScript recording paused");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Failed to pause JavaScript recording");
                throw;
            }
        }

        public async Task ResumeRecordAsync()
        {
            try
            {
                await _jsRuntime.InvokeVoidAsync("BlazorAudioRecorder.ResumeRecord");
                _logger?.LogInformation("JavaScript recording resumed");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Failed to resume JavaScript recording");
                throw;
            }
        }

        public void Dispose()
        {
            try
            {
                _dotNetRef?.Dispose();
                _logger?.LogInformation("AudioRecorderInterop disposed successfully");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error during AudioRecorderInterop disposal");
            }
        }
    }
}
