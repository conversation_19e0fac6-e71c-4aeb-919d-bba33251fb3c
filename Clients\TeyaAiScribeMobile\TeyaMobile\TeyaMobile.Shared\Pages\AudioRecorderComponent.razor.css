﻿@keyframes blink {
    0%, 50% {
        opacity: 1;
    }

    51%, 100% {
        opacity: 0;
    }
}

@keyframes shimmer {
    0% {
        left: -100%;
    }

    100% {
        left: 100%;
    }
}

/* Enhanced button transitions */
.mud-button {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

    .mud-button:hover:not(:disabled) {
        transform: translateY(-3px) scale(1.02) !important;
        box-shadow: 0 12px 30px rgba(0,0,0,0.15) !important;
    }

    .mud-button:active:not(:disabled) {
        transform: translateY(-1px) scale(0.98) !important;
    }

/* Custom scrollbar for transcription */
.mud-paper::-webkit-scrollbar {
    width: 6px;
}

.mud-paper::-webkit-scrollbar-track {
    background: rgba(255,255,255,0.2);
    border-radius: 3px;
}

.mud-paper::-webkit-scrollbar-thumb {
    background: rgba(255,255,255,0.5);
    border-radius: 3px;
}

    .mud-paper::-webkit-scrollbar-thumb:hover {
        background: rgba(255,255,255,0.7);
    }

/* Responsive adjustments */
@media (max-width: 768px) {
    .mud-stack-row {
        flex-direction: column !important;
    }

    .mud-button {
        width: 100% !important;
        margin-bottom: 10px !important;
    }
}
