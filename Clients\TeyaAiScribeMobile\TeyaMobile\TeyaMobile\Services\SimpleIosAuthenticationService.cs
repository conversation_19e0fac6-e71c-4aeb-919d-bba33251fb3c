using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Identity.Client;
using System.IdentityModel.Tokens.Jwt;
using TeyaHealthMobileViewModel.ViewModel;

namespace TeyaMobile.Services
{
    /// <summary>
    /// iOS-specific authentication service with keychain security group support
    /// </summary>
    public class SimpleIosAuthenticationService : IAuthenticationService
    {
        private readonly IPublicClientApplication _publicClientApp;
        private readonly ILogger<SimpleIosAuthenticationService> _logger;
        private readonly IConfiguration _configuration;
        private string[] _scopes;

        public SimpleIosAuthenticationService(
            IPublicClientApplication publicClientApp,
            ILogger<SimpleIosAuthenticationService> logger,
            IConfiguration configuration)
        {
            _publicClientApp = publicClientApp;
            _logger = logger;
            _configuration = configuration;

            // Get scopes from configuration
            var azureAdSection = _configuration.GetSection("AzureAd");
            var scopesConfig = azureAdSection.GetSection("Scopes").Get<string[]>();
            _scopes = scopesConfig ?? new[] { "openid" };

            // Configure iOS-specific settings
            ConfigureIosKeychain();
        }

        private void ConfigureIosKeychain()
        {
            // Ensure MSAL is configured with the keychain security group
            // This should be set in the MSAL PublicClientApplication builder, e.g.:
            // PublicClientApplicationBuilder.Create(clientId)
            //     .WithIosKeychainSecurityGroup("com.microsoft.adalcache")
            //     ...
            //     .Build();
        }

        public bool IsAuthenticated
        {
            get
            {
                try
                {
                    var accounts = _publicClientApp.GetAccountsAsync().Result;
                    return accounts.Any();
                }
                catch
                {
                    return false;
                }
            }
        }



        public async Task<string> GetUserNameAsync()
        {
            try
            {
                var accounts = await _publicClientApp.GetAccountsAsync();
                if (accounts.Any())
                {
                    return accounts.FirstOrDefault()?.Username;
                }
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get username: {Error}", ex.Message);
                throw;
            }
        }


        public async Task<string> GetGraphApiScopeAsync()
        {
            try
            {
                var accounts = await _publicClientApp.GetAccountsAsync();
                if (accounts.Any())
                {
                    var graphScopes = new[] { "https://graph.microsoft.com/.default" };
                    var result = await _publicClientApp.AcquireTokenSilent(graphScopes, accounts.FirstOrDefault())
                        .ExecuteAsync();
                    return result.AccessToken;
                }
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get Graph API token: {Error}", ex.Message);
                throw;
            }
        }

        public async Task<string> GetServiceSpecificTokenAsync()
        {
            try
            {
                var accounts = await _publicClientApp.GetAccountsAsync();
                if (accounts.Any())
                {
                    // Get service-specific scope from configuration
                    var serviceScope = _configuration["ServiceSpecificScope"] ?? "api://e21369d6-92b3-446b-b981-0291bcb29b1b/.default";
                    var serviceScopes = new[] { serviceScope };
                    var result = await _publicClientApp.AcquireTokenSilent(serviceScopes, accounts.FirstOrDefault())
                        .ExecuteAsync();
                    return result.AccessToken;
                }
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get service-specific token: {Error}", ex.Message);
                throw;
            }
        }

        public async Task<bool> LoginAsync()
        {
            try
            {
                // Try silent authentication first
                var accounts = await _publicClientApp.GetAccountsAsync();
                if (accounts.Any())
                {
                    try
                    {
                        var result = await _publicClientApp.AcquireTokenSilent(_scopes, accounts.FirstOrDefault())
                            .ExecuteAsync();
                        return true;
                    }
                    catch (MsalUiRequiredException)
                    {
                        // Silent auth failed, need interactive auth
                    }
                }

                // Interactive authentication
                var authResult = await _publicClientApp.AcquireTokenInteractive(_scopes)
                    .WithPrompt(Prompt.SelectAccount)
                    .ExecuteAsync();

                return authResult != null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Login failed: {Error}", ex.Message);
                return false;
            }
        }

        public async Task LogoutAsync()
        {
            try
            {
                var accounts = await _publicClientApp.GetAccountsAsync();
                foreach (var account in accounts)
                {
                    await _publicClientApp.RemoveAsync(account);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Logout failed: {Error}", ex.Message);
                throw;
            }
        }

        public async Task<string> GetAccessTokenAsync()
        {
            try
            {
                var accounts = await _publicClientApp.GetAccountsAsync();
                if (!accounts.Any())
                    return string.Empty;

                // Use Graph API scope by default
                var graphScopes = new[] { "https://graph.microsoft.com/.default" };
                var result = await _publicClientApp.AcquireTokenSilent(graphScopes, accounts.FirstOrDefault())
                    .ExecuteAsync();

                _logger.LogInformation("Successfully acquired Graph API token for iOS");
                return result.AccessToken;
            }
            catch (MsalUiRequiredException)
            {
                try
                {
                    // If silent acquisition fails, try interactive
                    var graphScopes = new[] { "https://graph.microsoft.com/.default" };
                    var result = await _publicClientApp.AcquireTokenInteractive(graphScopes)
                        .WithPrompt(Prompt.SelectAccount)
                        .ExecuteAsync();

                    _logger.LogInformation("Successfully acquired Graph API token interactively for iOS");
                    return result.AccessToken;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to get Graph API access token interactively: {Error}", ex.Message);
                    return string.Empty;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get Graph API access token: {Error}", ex.Message);
                return string.Empty;
            }
        }

        public async Task<string> GetUserEmailAsync()
        {
            try
            {
                var accessToken = await GetAccessTokenAsync();
                if (string.IsNullOrEmpty(accessToken))
                    return string.Empty;

                var handler = new JwtSecurityTokenHandler();
                var jsonToken = handler.ReadJwtToken(accessToken);

                return jsonToken.Claims.FirstOrDefault(c => c.Type == "preferred_username")?.Value ??
                       jsonToken.Claims.FirstOrDefault(c => c.Type == "email")?.Value ??
                       string.Empty;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get user email: {Error}", ex.Message);
                return string.Empty;
            }
        }
    }
}
