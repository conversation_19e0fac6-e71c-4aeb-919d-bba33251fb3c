﻿@inherits LayoutComponentBase
@using MudBlazor
@using Microsoft.AspNetCore.Components.Authorization
@using TeyaWebApp.ViewModel
@inject AuthenticationStateProvider AuthenticationStateProvider
@inject NavigationManager NavigationManager
@inject ITokenService TokenService
@using TeyaAIScribeResource
@inject IStringLocalizer<TeyaAIScribeResource> Localizer

<MudDialog Class="confluence-dialog" Style="width: auto; border-radius: 8px; position: fixed; top: 60px; right: 20px; box-shadow: 0 4px 12px rgba(0,0,0,0.15);">
    <DialogContent>
        <div class="profile-header">
            <div class="profile-avatar">
                <span class="avatar-text">@GetInitials(StateContainer.ExtractedName)</span>
            </div>
            <div class="profile-info">
                <div class="profile-name">@StateContainer.ExtractedName</div>
                <div class="profile-email">@user.mail</div>
            </div>
        </div>

        <div class="profile-divider"></div>

        <div class="profile-actions">
            <MudButton Class="action-button" StartIcon="@Icons.Material.Filled.Person" OnClick="NavigateToManageProfile">
                @Localizer["Profile"]
            </MudButton>

            <MudButton Class="action-button" StartIcon="@Icons.Material.Filled.Logout" OnClick="signout">
                @Localizer["SignOut"]
            </MudButton>
        </div>
    </DialogContent>
</MudDialog>

<style>
    .mud-overlay-scrim {
        display: none !important;
    }

    .confluence-dialog {
        background-color: #ffffff;
        color: #333333;
        border: 1px solid #e1e5e9;
    }

        .confluence-dialog .mud-dialog-content {
            padding: 0;
        }

    .profile-header {
        display: flex;
        align-items: center;
        padding: 16px;
        gap: 12px;
    }

    .profile-avatar {
        width: 48px;
        height: 48px;
        border-radius: 50%;
        background-color: #6b46c1;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-shrink: 0;
    }

    .avatar-text {
        font-size: 18px;
        font-weight: 600;
        color: white;
    }

    .profile-info {
        flex: 1;
        min-width: 0;
    }

    .profile-name {
        font-size: 16px;
        font-weight: 500;
        color: #333333;
        margin-bottom: 2px;
    }

    .profile-email {
        font-size: 14px;
        color: #6b7280;
        word-break: break-all;
    }

    .profile-divider {
        height: 1px;
        background-color: #e5e7eb;
        margin: 0 16px;
    }

    .profile-actions {
        padding: 8px 0;
    }

    .action-button {
        width: 100%;
        justify-content: flex-start;
        padding: 12px 16px;
        color: #374151;
        background-color: transparent;
        border-radius: 0;
        text-transform: none;
        font-weight: normal;
        min-height: 44px;
    }

        .action-button:hover {
            background-color: #f3f4f6;
            color: #111827;
        }

        .action-button .mud-button-start-icon {
            margin-right: 12px;
            color: #6b7280;
        }

        .action-button:hover .mud-button-start-icon {
            color: #374151;
        }

        .action-button .mud-button-label {
            font-size: 14px;
        }
</style>