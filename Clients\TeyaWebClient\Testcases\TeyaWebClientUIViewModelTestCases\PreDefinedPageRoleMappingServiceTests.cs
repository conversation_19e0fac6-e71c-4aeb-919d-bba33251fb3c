using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using Moq;
using Moq.Protected;
using NUnit.Framework;
using TeyaUIModels.Model;
using TeyaWebApp.Services;
using System.Net.Http.Json;
using System.Text.Json;
using TeyaUIViewModels.ViewModel;
using System.Linq;

namespace TeyaWebClientUIViewModelTestCases
{
    [TestFixture]
    public class PreDefinedPageRoleMappingServiceTests
    {
        private Mock<HttpMessageHandler> _mockHttpMessageHandler;
        private HttpClient _httpClient;
        private Mock<ILogger<PreDefinedPageRoleMappingService>> _mockLogger;
        private Mock<IStringLocalizer<PreDefinedPageRoleMappingService>> _mockLocalizer;
        private Mock<ITokenService> _mockTokenService;
        private PreDefinedPageRoleMappingService _preDefinedPageRoleMappingService;
        private readonly string _baseUrl = "http://test-api.com";
        private readonly string _accessToken = "test-access-token";

        [SetUp]
        public void Setup()
        {
            // Setup environment variable
            Environment.SetEnvironmentVariable("MemberServiceURL", _baseUrl);

            // Setup mock HTTP handler
            _mockHttpMessageHandler = new Mock<HttpMessageHandler>();

            // Setup HttpClient with mock handler
            _httpClient = new HttpClient(_mockHttpMessageHandler.Object)
            {
                BaseAddress = new Uri(_baseUrl)
            };

            // Setup mock logger
            _mockLogger = new Mock<ILogger<PreDefinedPageRoleMappingService>>();

            // Setup mock localizer
            _mockLocalizer = new Mock<IStringLocalizer<PreDefinedPageRoleMappingService>>();
            _mockLocalizer.Setup(l => l["ErrorFetchingAllPreDefinedPageRoleMappings"])
                .Returns(new LocalizedString("ErrorFetchingAllPreDefinedPageRoleMappings", "Error fetching all predefined page role mappings"));
            _mockLocalizer.Setup(l => l["ErrorFetchingPreDefinedPageRoleMappingById"])
                .Returns(new LocalizedString("ErrorFetchingPreDefinedPageRoleMappingById", "Error fetching predefined page role mapping by ID"));
            _mockLocalizer.Setup(l => l["GetByIdFailed"])
                .Returns(new LocalizedString("GetByIdFailed", "Get by ID failed"));
            _mockLocalizer.Setup(l => l["ErrorAddingPreDefinedPageRoleMapping"])
                .Returns(new LocalizedString("ErrorAddingPreDefinedPageRoleMapping", "Error adding predefined page role mapping"));
            _mockLocalizer.Setup(l => l["AddPreDefinedPageRoleMappingFailed"])
                .Returns(new LocalizedString("AddPreDefinedPageRoleMappingFailed", "Add predefined page role mapping failed"));
            _mockLocalizer.Setup(l => l["ErrorUpdatingPreDefinedPageRoleMapping"])
                .Returns(new LocalizedString("ErrorUpdatingPreDefinedPageRoleMapping", "Error updating predefined page role mapping"));
            _mockLocalizer.Setup(l => l["PreDefinedPageRoleMappingUpdateFailed"])
                .Returns(new LocalizedString("PreDefinedPageRoleMappingUpdateFailed", "Predefined page role mapping update failed"));
            _mockLocalizer.Setup(l => l["PreDefinedPageRoleMappingUpdatedSuccessfully"])
                .Returns(new LocalizedString("PreDefinedPageRoleMappingUpdatedSuccessfully", "Predefined page role mapping updated successfully"));
            _mockLocalizer.Setup(l => l["ErrorDeletingPreDefinedPageRoleMapping"])
                .Returns(new LocalizedString("ErrorDeletingPreDefinedPageRoleMapping", "Error deleting predefined page role mapping"));
            _mockLocalizer.Setup(l => l["PreDefinedPageRoleMappingDeletionFailed"])
                .Returns(new LocalizedString("PreDefinedPageRoleMappingDeletionFailed", "Predefined page role mapping deletion failed"));
            _mockLocalizer.Setup(l => l["PreDefinedPageRoleMappingDeletedSuccessfully"])
                .Returns(new LocalizedString("PreDefinedPageRoleMappingDeletedSuccessfully", "Predefined page role mapping deleted successfully"));
            _mockLocalizer.Setup(l => l["ErrorFetchingPagesByRoleId"])
                .Returns(new LocalizedString("ErrorFetchingPagesByRoleId", "Error fetching pages by role ID"));
            _mockLocalizer.Setup(l => l["GetPagesByRoleIdFailed"])
                .Returns(new LocalizedString("GetPagesByRoleIdFailed", "Get pages by role ID failed"));
            _mockLocalizer.Setup(l => l["ErrorFetchingRoles"])
                .Returns(new LocalizedString("ErrorFetchingRoles", "Error fetching roles"));
            _mockLocalizer.Setup(l => l["PagePathRequired"])
                .Returns(new LocalizedString("PagePathRequired", "Page path required"));
            _mockLocalizer.Setup(l => l["PagePathRequiredMessage"])
                .Returns(new LocalizedString("PagePathRequiredMessage", "Page path is required"));
            _mockLocalizer.Setup(l => l["RolesNotFoundMessage"])
                .Returns(new LocalizedString("RolesNotFoundMessage", "Roles not found"));
            _mockLocalizer.Setup(l => l["API Response: {ResponseData}"])
                .Returns(new LocalizedString("API Response: {ResponseData}", "API Response: {ResponseData}"));

            // Setup mock token service
            _mockTokenService = new Mock<ITokenService>();
            _mockTokenService.Setup(t => t.AccessToken).Returns(_accessToken);
            _mockTokenService.Setup(t => t.GetValidatedAccessTokenAsync()).ReturnsAsync(_accessToken);

            // Create PreDefinedPageRoleMappingService with mocked dependencies
            _preDefinedPageRoleMappingService = new PreDefinedPageRoleMappingService(_httpClient, _mockLocalizer.Object, _mockLogger.Object, _mockTokenService.Object);
        }

        [TearDown]
        public void TearDown()
        {
            _httpClient.Dispose();
            Environment.SetEnvironmentVariable("MemberServiceURL", null);
        }

        [Test]
        public async Task GetPreDefinedPageRoleMappingsAsync_WhenSuccessful_ReturnsPreDefinedPageRoleMappings()
        {
            // Arrange
            var expectedMappings = new List<PreDefinedPageRoleMappingData>
            {
                new PreDefinedPageRoleMappingData
                {
                    Id = Guid.NewGuid(),
                    PagePath = "/admin/dashboard",
                    RoleName = "Administrator",
                    RoleId = Guid.NewGuid(),
                    CreatedBy = Guid.NewGuid(),
                    CreatedDate = DateTime.Now,
                    IsActive = true,
                    HasAccess = true,
                    IsModified = false
                },
                new PreDefinedPageRoleMappingData
                {
                    Id = Guid.NewGuid(),
                    PagePath = "/user/profile",
                    RoleName = "User",
                    RoleId = Guid.NewGuid(),
                    CreatedBy = Guid.NewGuid(),
                    CreatedDate = DateTime.Now,
                    IsActive = true,
                    HasAccess = true,
                    IsModified = false
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedMappings)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _preDefinedPageRoleMappingService.GetPreDefinedPageRoleMappingsAsync();

            // Assert
            Assert.That(result, Is.Not.Null);
            var resultList = result.ToList();
            Assert.That(resultList.Count, Is.EqualTo(expectedMappings.Count));
            Assert.That(resultList[0].Id, Is.EqualTo(expectedMappings[0].Id));
            Assert.That(resultList[0].PagePath, Is.EqualTo(expectedMappings[0].PagePath));
            Assert.That(resultList[0].RoleName, Is.EqualTo(expectedMappings[0].RoleName));
            Assert.That(resultList[0].HasAccess, Is.EqualTo(expectedMappings[0].HasAccess));
            Assert.That(resultList[0].IsActive, Is.EqualTo(expectedMappings[0].IsActive));
        }

        [Test]
        public void GetPreDefinedPageRoleMappingsAsync_WhenExceptionThrown_PropagatesException()
        {
            // Arrange
            var expectedException = new HttpRequestException("Test exception");

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ThrowsAsync(expectedException);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _preDefinedPageRoleMappingService.GetPreDefinedPageRoleMappingsAsync());

            Assert.That(exception.Message, Is.EqualTo(expectedException.Message));
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.Is<Exception>(ex => ex.Message == expectedException.Message),
                    It.Is<Func<It.IsAnyType, Exception, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }

        [Test]
        public async Task GetPreDefinedPageRoleMappingByIdAsync_WhenSuccessful_ReturnsPreDefinedPageRoleMapping()
        {
            // Arrange
            var mappingId = Guid.NewGuid();
            var expectedMapping = new PreDefinedPageRoleMappingData
            {
                Id = mappingId,
                PagePath = "/admin/dashboard",
                RoleName = "Administrator",
                RoleId = Guid.NewGuid(),
                CreatedBy = Guid.NewGuid(),
                CreatedDate = DateTime.Now,
                IsActive = true,
                HasAccess = true,
                IsModified = false
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedMapping)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _preDefinedPageRoleMappingService.GetPreDefinedPageRoleMappingByIdAsync(mappingId);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Id, Is.EqualTo(expectedMapping.Id));
            Assert.That(result.PagePath, Is.EqualTo(expectedMapping.PagePath));
            Assert.That(result.RoleName, Is.EqualTo(expectedMapping.RoleName));
            Assert.That(result.HasAccess, Is.EqualTo(expectedMapping.HasAccess));
            Assert.That(result.IsActive, Is.EqualTo(expectedMapping.IsActive));
        }

        [Test]
        public void GetPreDefinedPageRoleMappingByIdAsync_WhenNotFound_ThrowsHttpRequestException()
        {
            // Arrange
            var mappingId = Guid.NewGuid();

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.NotFound,
                Content = new StringContent("Not found")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _preDefinedPageRoleMappingService.GetPreDefinedPageRoleMappingByIdAsync(mappingId));

            Assert.That(exception.Message, Is.EqualTo("Get by ID failed"));
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.IsAny<Exception>(),
                    It.Is<Func<It.IsAnyType, Exception, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }

        [Test]
        public async Task AddPreDefinedPageRoleMappingAsync_WhenSuccessful_CompletesSuccessfully()
        {
            // Arrange
            var mapping = new PreDefinedPageRoleMappingData
            {
                Id = Guid.NewGuid(),
                PagePath = "/new/page",
                RoleName = "NewRole",
                RoleId = Guid.NewGuid(),
                CreatedBy = Guid.NewGuid(),
                CreatedDate = DateTime.Now,
                IsActive = true,
                HasAccess = true,
                IsModified = false
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.Created,
                Content = new StringContent("Success")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.DoesNotThrowAsync(async () => await _preDefinedPageRoleMappingService.AddPreDefinedPageRoleMappingAsync(mapping));

            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Information,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.IsAny<Exception>(),
                    It.Is<Func<It.IsAnyType, Exception, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }

        [Test]
        public void AddPreDefinedPageRoleMappingAsync_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var mapping = new PreDefinedPageRoleMappingData
            {
                Id = Guid.NewGuid(),
                PagePath = "/new/page",
                RoleName = "NewRole",
                RoleId = Guid.NewGuid(),
                CreatedBy = Guid.NewGuid(),
                CreatedDate = DateTime.Now,
                IsActive = true,
                HasAccess = true,
                IsModified = false
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _preDefinedPageRoleMappingService.AddPreDefinedPageRoleMappingAsync(mapping));

            Assert.That(exception.Message, Is.EqualTo("Add predefined page role mapping failed"));
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.IsAny<Exception>(),
                    It.Is<Func<It.IsAnyType, Exception, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }

        [Test]
        public async Task UpdatePreDefinedPageRoleMappingAsync_WhenSuccessful_CompletesSuccessfully()
        {
            // Arrange
            var mapping = new PreDefinedPageRoleMappingData
            {
                Id = Guid.NewGuid(),
                PagePath = "/updated/page",
                RoleName = "UpdatedRole",
                RoleId = Guid.NewGuid(),
                CreatedBy = Guid.NewGuid(),
                CreatedDate = DateTime.Now,
                IsActive = true,
                HasAccess = true,
                IsModified = false
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent("Success")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.DoesNotThrowAsync(async () => await _preDefinedPageRoleMappingService.UpdatePreDefinedPageRoleMappingAsync(mapping));

            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Information,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.IsAny<Exception>(),
                    It.Is<Func<It.IsAnyType, Exception, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }

        [Test]
        public void UpdatePreDefinedPageRoleMappingAsync_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var mapping = new PreDefinedPageRoleMappingData
            {
                Id = Guid.NewGuid(),
                PagePath = "/updated/page",
                RoleName = "UpdatedRole",
                RoleId = Guid.NewGuid(),
                CreatedBy = Guid.NewGuid(),
                CreatedDate = DateTime.Now,
                IsActive = true,
                HasAccess = true,
                IsModified = false
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _preDefinedPageRoleMappingService.UpdatePreDefinedPageRoleMappingAsync(mapping));

            Assert.That(exception.Message, Is.EqualTo("Predefined page role mapping update failed"));
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.IsAny<Exception>(),
                    It.Is<Func<It.IsAnyType, Exception, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }

        [Test]
        public async Task DeletePreDefinedPageRoleMappingByIdAsync_WhenSuccessful_CompletesSuccessfully()
        {
            // Arrange
            var mappingId = Guid.NewGuid();

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.NoContent
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.DoesNotThrowAsync(async () => await _preDefinedPageRoleMappingService.DeletePreDefinedPageRoleMappingByIdAsync(mappingId));

            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Information,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.IsAny<Exception>(),
                    It.Is<Func<It.IsAnyType, Exception, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }

        [Test]
        public void DeletePreDefinedPageRoleMappingByIdAsync_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var mappingId = Guid.NewGuid();

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.NotFound,
                Content = new StringContent("Not found")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _preDefinedPageRoleMappingService.DeletePreDefinedPageRoleMappingByIdAsync(mappingId));

            Assert.That(exception.Message, Is.EqualTo("Predefined page role mapping deletion failed"));
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.IsAny<Exception>(),
                    It.Is<Func<It.IsAnyType, Exception, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }

        [Test]
        public async Task GetPagesByRoleNameAsync_WhenSuccessful_ReturnsPageRoleMappings()
        {
            // Arrange
            var roleName = "Administrator";
            var expectedMappings = new List<PreDefinedPageRoleMappingData>
            {
                new PreDefinedPageRoleMappingData
                {
                    Id = Guid.NewGuid(),
                    PagePath = "/admin/dashboard",
                    RoleName = roleName,
                    RoleId = Guid.NewGuid(),
                    CreatedBy = Guid.NewGuid(),
                    CreatedDate = DateTime.Now,
                    IsActive = true,
                    HasAccess = true,
                    IsModified = false
                },
                new PreDefinedPageRoleMappingData
                {
                    Id = Guid.NewGuid(),
                    PagePath = "/admin/users",
                    RoleName = roleName,
                    RoleId = Guid.NewGuid(),
                    CreatedBy = Guid.NewGuid(),
                    CreatedDate = DateTime.Now,
                    IsActive = true,
                    HasAccess = true,
                    IsModified = false
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedMappings)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _preDefinedPageRoleMappingService.GetPagesByRoleNameAsync(roleName);

            // Assert
            Assert.That(result, Is.Not.Null);
            var resultList = result.ToList();
            Assert.That(resultList.Count, Is.EqualTo(expectedMappings.Count));
            Assert.That(resultList[0].RoleName, Is.EqualTo(roleName));
            Assert.That(resultList[1].RoleName, Is.EqualTo(roleName));
        }

        [Test]
        public void GetPagesByRoleNameAsync_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var roleName = "NonExistentRole";

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.NotFound,
                Content = new StringContent("Not found")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _preDefinedPageRoleMappingService.GetPagesByRoleNameAsync(roleName));

            Assert.That(exception.Message, Is.EqualTo("Get pages by role ID failed"));
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.IsAny<Exception>(),
                    It.Is<Func<It.IsAnyType, Exception, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }

        [Test]
        public async Task GetRolesByPagePathAsync_WhenSuccessful_ReturnsRoles()
        {
            // Arrange
            var pagePath = "/admin/dashboard";
            var expectedRoles = new List<string> { "Administrator", "SuperAdmin", "Manager" };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedRoles)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.Is<HttpRequestMessage>(req =>
                        req.RequestUri.ToString().Contains("/api/PreDefinedPageRoleMapping/roles-by-pagepath") &&
                        req.Headers.Authorization != null &&
                        req.Headers.Authorization.Scheme == "Bearer" &&
                        req.Headers.Authorization.Parameter == _accessToken),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _preDefinedPageRoleMappingService.GetRolesByPagePathAsync(pagePath);

            // Assert
            Assert.That(result, Is.Not.Null);
            var resultList = result.ToList();
            Assert.That(resultList.Count, Is.EqualTo(expectedRoles.Count));
            Assert.That(resultList, Is.EquivalentTo(expectedRoles));
        }

        [Test]
        public void GetRolesByPagePathAsync_WhenPagePathIsEmpty_ThrowsArgumentException()
        {
            // Arrange
            var pagePath = "";

            // Act & Assert
            var exception = Assert.ThrowsAsync<ArgumentException>(async () =>
                await _preDefinedPageRoleMappingService.GetRolesByPagePathAsync(pagePath));

            Assert.That(exception.Message, Is.EqualTo("Page path is required"));
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Warning,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.IsAny<Exception>(),
                    It.Is<Func<It.IsAnyType, Exception, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }

        [Test]
        public void GetRolesByPagePathAsync_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var pagePath = "/nonexistent/page";

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.NotFound,
                Content = new StringContent("Not found")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.Is<HttpRequestMessage>(req => req.RequestUri.ToString().Contains("/api/PreDefinedPageRoleMapping/roles-by-pagepath")),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _preDefinedPageRoleMappingService.GetRolesByPagePathAsync(pagePath));

            Assert.That(exception.Message, Is.EqualTo("Roles not found"));
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.IsAny<Exception>(),
                    It.Is<Func<It.IsAnyType, Exception, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }
    }
}



