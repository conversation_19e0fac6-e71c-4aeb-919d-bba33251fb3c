﻿@page "/soapnotes/{PatientId:guid}"
@page "/soapnotes"
@using TeyaHealthMobileModel.Model
@using TeyaHealthMobileViewModel.ViewModel
@using System.Text.Json
@using MudBlazor
@using Syncfusion.Blazor.Buttons
@using TeyaMobile.Shared.Components
@inject IProgressNotesService NotesService
@inject NavigationManager Navigation
@inject IJSRuntime JSRuntime
@inject IFormFactor FormFactor
@implements IDisposable

<MudContainer MaxWidth="@(IsTablet? MaxWidth.False: MaxWidth.Medium)" Class="soapnotes-container" Style="@GetContainerStyle()">
    <!-- Provider View - Patient Selection Dropdown -->
    @if (IsProviderView)
    {
        <MudPaper Elevation="@GetPaperElevation()" Class="@GetPatientSelectionClass()" Style="@GetPatientSelectionPaperStyle()">
            <MudStack Row="true" AlignItems="AlignItems.Center" Spacing="@GetPatientSelectionSpacing()">
                <MudIcon Icon="@Icons.Material.Filled.Person" Color="Color.Primary" Size="@GetPatientIconSize()" />
                <MudText Typo="@GetPatientSelectionTypo()" Style="@GetPatientSelectionTextStyle()">Select Patient</MudText>
                <div style="position: relative; flex: 1; min-width: 0; max-width: 100%;">
                    <MudAutocomplete T="PatientInfo"
                                     Label="Search Patient"
                                     @bind-Value="selectedPatient"
                                     SearchFunc="@SearchPatients"
                                     ToStringFunc="@(p => p?.PatientName ?? "")"
                                     Placeholder="Type patient name..."
                                     Variant="Variant.Outlined"
                                     Style="@GetAutocompleteStyle()"
                                     Disabled="@isLoadingPatients"
                                     ResetValueOnEmptyText="true"
                                     CoerceText="true"
                                     CoerceValue="true" />
                    @if (isLoadingPatients)
                    {
                        <div style="position: absolute; right: 6px; top: 50%; transform: translateY(-50%); z-index: 1000;">
                            <MudProgressCircular Size="@GetLoaderSize()" Indeterminate="true" Color="Color.Primary" />
                        </div>
                    }
                </div>
            </MudStack>
        </MudPaper>
    }

    <!-- Main Loading State -->
    @if (isLoading && !isLoadingPatients)
    {
        <MudPaper Elevation="@GetPaperElevation()" Class="@GetLoadingPaperClass()" Style="@GetLoadingStateStyle()">
            <MudStack AlignItems="AlignItems.Center" Spacing="@GetLoadingSpacing()">
                <MudProgressCircular Color="Color.Primary" Size="@GetMainLoaderSize()" Indeterminate="true" />
                <MudText Typo="@GetLoadingTitleTypo()">Loading Notes...</MudText>
                <MudText Typo="@GetLoadingSubtitleTypo()" Color="Color.Secondary">Please wait while we retrieve records</MudText>
            </MudStack>
        </MudPaper>
    }

    <!-- SOAP Notes Content -->
    @if (soapRecords.Any() && !isLoading)
    {
        <MudStack Spacing="@GetRecordStackSpacing()">
            @foreach (var record in soapRecords)
            {
                <MudCard Elevation="@GetCardElevation()" Style="@GetCardStyle()">
                    <MudCardHeader Style="@GetRecordHeaderStyle()">
                        <CardHeaderContent>
                            <MudText Typo="@GetDateTypo()" Style="@GetDateTextStyle()">
                                @record.DateTime.ToString("MMM dd, yyyy")
                            </MudText>
                        </CardHeaderContent>
                    </MudCardHeader>
                    <MudCardContent Class="@GetCardContentClass()" Style="@GetCardContentStyle()">
                        @if (record.ProcessedNotes != null && record.ProcessedNotes.Any())
                        {
                            <MudExpansionPanels MultiExpansion="true" Elevation="0" Style="@GetExpansionPanelStyle()">
                                @foreach (var soapSection in record.ProcessedNotes)
                                {
                                    var hasContent = HasValidContent(soapSection.Value);
                                    var isExpanded = IsSectionExpanded(record.Id, soapSection.Key);
                                    <MudExpansionPanel Expanded="@(hasContent && isExpanded)"
                                                       Gutters="false"
                                                       Class="soap-section"
                                                       Style="@GetExpansionPanelStyle()">
                                        <TitleContent>
                                            <MudStack Row="true" AlignItems="AlignItems.Center" Spacing="@GetSectionTitleSpacing()">
                                                <MudIcon Icon="@GetSectionIcon(soapSection.Key)"
                                                         Color="@GetSectionColor(soapSection.Key)"
                                                         Size="@GetSectionIconSize()" />
                                                <MudText Typo="@GetSectionTitleTypo()"
                                                         Style="@GetSectionTitleStyle()">
                                                    @soapSection.Key
                                                </MudText>
                                            </MudStack>
                                        </TitleContent>
                                        <ChildContent>
                                            <MudDivider />
                                            <div style="@GetSectionContentContainerStyle()">
                                                @if (hasContent)
                                                {
                                                    <MudStack Spacing="@GetContentItemSpacing()">
                                                        @foreach (var item in soapSection.Value)
                                                        {
                                                            @if (!string.IsNullOrEmpty(item.Value.Value) &&
                                                                                                !string.IsNullOrWhiteSpace(item.Value.Value) &&
                                                                                                item.Value.Value != "<p></p>")
                                                            {
                                                                <MudPaper Elevation="0"
                                                                          Class="@GetContentItemClass()"
                                                                          Style="@GetContentItemStyle()">
                                                                    <MudText Typo="@GetItemTitleTypo()"
                                                                             Style="@GetItemTitleStyle()">
                                                                        @item.Key
                                                                    </MudText>
                                                                    <div class="markdown-content"
                                                                         style="@GetMarkdownContentStyle()">
                                                                        @item.Value
                                                                    </div>
                                                                </MudPaper>
                                                            }
                                                        }
                                                    </MudStack>
                                                }
                                                else
                                                {
                                                    <MudAlert Severity="Severity.Info"
                                                              Variant="Variant.Text"
                                                              Class="ma-0"
                                                              Style="@GetAlertStyle()">
                                                        No data available for this section
                                                    </MudAlert>
                                                }
                                            </div>
                                        </ChildContent>
                                    </MudExpansionPanel>
                                }
                            </MudExpansionPanels>
                        }
                        else if (!string.IsNullOrEmpty(record.Notes))
                        {
                            <MudText Typo="@GetRawNotesTypo()" Color="Color.Secondary" Style="text-align: center; width: 100%; padding: 8px 0;">
                                Not available
                            </MudText>
                        }
                    </MudCardContent>
                </MudCard>
            }
        </MudStack>
    }
    else if (!isLoading && !soapRecords.Any() && (IsProviderView ? selectedPatient != null : true))
    {
        <!-- No Data State -->
        <MudPaper Elevation="@GetPaperElevation()" Class="@GetNoDataPaperClass()" Style="@GetNoDataStateStyle()">
            <MudStack AlignItems="AlignItems.Center" Spacing="@GetNoDataSpacing()">
                <MudIcon Icon="@Icons.Material.Outlined.Description"
                         Size="@GetNoDataIconSize()"
                         Style="@GetNoDataIconStyle()" />
                <MudText Typo="@GetNoDataTitleTypo()" Color="Color.Error" Style="font-weight: 600;">
                    No Medical Records Found
                </MudText>
                <MudText Typo="@GetNoDataSubtitleTypo()" Color="Color.Secondary">
                    @if (IsProviderView)
                    {
                        <text>No SOAP notes found for the selected patient.</text>
                    }
                    else
                    {
                        <text>No SOAP notes could be retrieved for this patient at this time.</text>
                    }
                </MudText>
                <MudButton Variant="Variant.Outlined"
                           Color="Color.Primary"
                           Size="@GetButtonSize()"
                           OnClick="@(() => LoadSoapNotes())"
                           StartIcon="@Icons.Material.Filled.Refresh">
                    Retry Loading
                </MudButton>
            </MudStack>
        </MudPaper>
    }
    else if (IsProviderView && selectedPatient == null && !isLoadingPatients)
    {
        <!-- Provider View - No Patient Selected -->
        <MudPaper Elevation="@GetPaperElevation()" Class="@GetNoPatientPaperClass()" Style="@GetNoPatientSelectedStyle()">
            <MudStack AlignItems="AlignItems.Center" Spacing="@GetNoPatientSpacing()">
                <MudIcon Icon="@Icons.Material.Outlined.PersonSearch"
                         Size="@GetNoPatientIconSize()"
                         Style="@GetNoPatientIconStyle()" />
                <MudText Typo="@GetNoPatientTypo()" Style="font-weight: 600;">
                    Select a Patient
                </MudText>
            </MudStack>
        </MudPaper>
    }
</MudContainer>

@code {
    private bool IsTablet => FormFactor.GetFormFactor() == "Tablet";
    private bool IsMobile => FormFactor.GetFormFactor() == "Mobile";
    private bool IsDesktop => FormFactor.GetFormFactor() == "Desktop";

    // Ultra-Compact Mobile Sizing Methods
    private string GetContainerStyle()
    {
        if (IsMobile)
            return "margin: 0; padding: 2px; width: 100%; max-width: 100vw;"; // Ultra-minimal padding
        else if (IsTablet)
            return "margin: 0 auto; padding: 16px; width: 98vw; max-width: 1200px;";
        else
            return "margin: 0 auto; padding: 16px;";
    }

    // Elevation and spacing
    private int GetPaperElevation() => IsMobile ? 0 : 1;

    // Patient Selection Ultra-Compact Styles
    private string GetPatientSelectionClass() => IsMobile ? "pa-1 mb-1" : "pa-3 mb-3";
    private int GetPatientSelectionSpacing() => IsMobile ? 0 : 2;
    private Size GetPatientIconSize() => IsMobile ? Size.Small : Size.Medium;
    private Typo GetPatientSelectionTypo() => IsMobile ? Typo.caption : Typo.subtitle1;

    private string GetPatientSelectionTextStyle()
    {
        if (IsMobile)
            return "font-weight: 600; font-size: 0.7rem;"; // Ultra-small font
        else
            return "font-weight: 600;";
    }

    private string GetPatientSelectionPaperStyle()
    {
        if (IsMobile)
            return "width: 100%; max-width: 100%; background: #f8f9fa; border-radius: 4px; padding: 4px; margin-bottom: 4px; border: 1px solid #e9ecef;"; // Ultra-minimal
        else if (IsTablet)
            return "max-width: 600px; margin: 0 auto 16px auto;";
        else
            return "max-width: 600px; margin: 0 auto 16px auto;";
    }

    private string GetAutocompleteStyle()
    {
        var fontSize = IsMobile ? "0.7rem" : "1.0rem";
        return $"width: 100%; max-width: 100%; font-size: {fontSize};";
    }

    private Size GetLoaderSize() => IsMobile ? Size.Small : Size.Small;

    // Loading State Ultra-Compact Styles
    private string GetLoadingPaperClass() => IsMobile ? "pa-2" : "pa-6";
    private int GetLoadingSpacing() => IsMobile ? 0 : 2;
    private Size GetMainLoaderSize() => IsMobile ? Size.Small : Size.Large;
    private Typo GetLoadingTitleTypo() => IsMobile ? Typo.caption : Typo.subtitle1;
    private Typo GetLoadingSubtitleTypo() => IsMobile ? Typo.overline : Typo.body2;

    private string GetLoadingStateStyle()
    {
        if (IsMobile)
            return "width: 100%; max-width: 100%; text-align: center; background: #ffffff; border-radius: 4px; padding: 8px; border: 1px solid #e9ecef;"; // Ultra-minimal
        else
            return "text-align: center; max-width: 600px; margin: 0 auto;";
    }

    // Record Cards Ultra-Compact Styles
    private int GetRecordStackSpacing() => IsMobile ? 0 : 2;
    private int GetCardElevation() => IsMobile ? 0 : 2;

    private string GetCardStyle()
    {
        if (IsMobile)
            return "border-radius: 4px; overflow: hidden; margin-bottom: 4px; width: 100%; max-width: 100%; border: 1px solid #e9ecef;"; // Ultra-minimal
        else
            return IsTablet ? "border-radius: 10px; overflow: hidden; margin-bottom: 16px; width: 100%; max-width: 100%;" : "border-radius: 10px; overflow: hidden; margin-bottom: 16px; width: 100%; max-width: 900px; margin-left: auto; margin-right: auto;";
    }

    private string GetRecordHeaderStyle()
    {
        if (IsMobile)
            return "background: #f8f9fa; border-bottom: 1px solid #e9ecef; padding: 4px 6px;"; // Ultra-minimal
        else
            return "background: #f8f9fa; border-bottom: 1px solid #e9ecef; padding: 12px 20px;";
    }

    private Typo GetDateTypo() => IsMobile ? Typo.overline : Typo.subtitle2;

    private string GetDateTextStyle()
    {
        if (IsMobile)
            return "font-weight: 600; color: #495057; font-size: 0.7rem;"; // Ultra-small
        else
            return "font-weight: 600; color: #495057;";
    }

    private string GetCardContentClass() => IsMobile ? "pa-0" : "pa-0";

    private string GetCardContentStyle()
    {
        if (IsMobile)
            return "padding: 2px; width: 100%; max-width: 100%;"; // Ultra-minimal
        else
            return IsTablet ? "padding: 12px 20px; width: 100%; max-width: 100%;" : "padding: 12px 20px;";
    }

    // Expansion Panel Ultra-Compact Styles
    private string GetExpansionPanelStyle()
    {
        if (IsMobile)
            return "width: 100% !important; max-width: 100% !important; margin: 0 0 2px 0 !important; border-radius: 4px !important; border: 1px solid #e9ecef !important; overflow: hidden !important;"; // Ultra-minimal
        else
            return "width: 100% !important; max-width: 100% !important; margin: 0 !important; border-radius: 8px !important;";
    }

    private int GetSectionTitleSpacing() => IsMobile ? 0 : 1;
    private Size GetSectionIconSize() => IsMobile ? Size.Small : Size.Medium;

    private Typo GetSectionTitleTypo()
    {
        if (IsMobile) return Typo.overline;
        else return Typo.body2;
    }

    private string GetSectionTitleStyle()
    {
        if (IsMobile)
            return "font-weight: 600; font-size: 0.65rem;"; // Ultra-small
        else
            return "font-weight: 600;";
    }

    private string GetSectionContentContainerStyle()
    {
        if (IsMobile)
            return "width: 100%; padding: 2px 4px;"; // Ultra-minimal
        else
            return "width: 100%;";
    }

    private int GetContentItemSpacing() => IsMobile ? 0 : 1;

    private string GetContentItemClass() => IsMobile ? "pa-0" : "pa-2";

    private string GetContentItemStyle()
    {
        if (IsMobile)
            return "border-left: 2px solid var(--mud-palette-primary); background: #fafafa; width: 100%; margin-bottom: 1px; padding: 2px 4px;"; // Ultra-minimal
        else
            return "border-left: 3px solid var(--mud-palette-primary); background: #fafafa; width: 100%; margin-bottom: 4px;";
    }

    private Typo GetItemTitleTypo()
    {
        if (IsMobile) return Typo.overline;
        else return Typo.caption;
    }

    private string GetItemTitleStyle()
    {
        if (IsMobile)
            return "font-weight: 600; color: var(--mud-palette-primary); margin-bottom: 1px; font-size: 0.6rem;"; // Ultra-small
        else
            return "font-weight: 600; color: var(--mud-palette-primary); margin-bottom: 2px;";
    }

    private string GetMarkdownContentStyle()
    {
        if (IsMobile)
            return "font-size: 0.65rem; line-height: 1.2;"; // Ultra-compact
        else
            return "font-size: 0.98em;";
    }

    private Typo GetRawNotesTypo() => IsMobile ? Typo.overline : Typo.body2;

    private string GetAlertStyle()
    {
        var fontSize = IsMobile ? "0.6rem" : "1em";
        return $"font-size: {fontSize}; width: 100%;";
    }

    // No Data State Ultra-Compact Styles
    private string GetNoDataPaperClass() => IsMobile ? "pa-2" : "pa-6";
    private int GetNoDataSpacing() => IsMobile ? 0 : 2;
    private Size GetNoDataIconSize() => IsMobile ? Size.Small : Size.Large;
    private string GetNoDataIconStyle() => IsMobile ? "font-size: 1.2rem; color: #6c757d; opacity: 0.5;" : "font-size: 2.5rem; color: #6c757d; opacity: 0.5;";
    private Typo GetNoDataTitleTypo() => IsMobile ? Typo.caption : Typo.subtitle2;
    private Typo GetNoDataSubtitleTypo() => IsMobile ? Typo.overline : Typo.body2;
    private Size GetButtonSize() => IsMobile ? Size.Small : Size.Medium;

    private string GetNoDataStateStyle()
    {
        if (IsMobile)
            return "width: 100%; max-width: 100%; text-align: center; border: 1px dashed #dee2e6; border-radius: 4px; background: #ffffff; padding: 8px 4px;"; // Ultra-minimal
        else
            return "text-align: center; border: 2px dashed #dee2e6; font-size: 1em; max-width: 600px; margin: 0 auto;";
    }

    // No Patient Selected Ultra-Compact Styles
    private string GetNoPatientPaperClass() => IsMobile ? "pa-2" : "pa-6";
    private int GetNoPatientSpacing() => IsMobile ? 0 : 2;
    private Size GetNoPatientIconSize() => IsMobile ? Size.Small : Size.Large;
    private string GetNoPatientIconStyle() => IsMobile ? "font-size: 1.2rem; color: #6c757d; opacity: 0.5;" : "font-size: 2.5rem; color: #6c757d; opacity: 0.5;";
    private Typo GetNoPatientTypo() => IsMobile ? Typo.caption : Typo.subtitle2;

    private string GetNoPatientSelectedStyle()
    {
        if (IsMobile)
            return "width: 100%; max-width: 100%; text-align: center; background: #ffffff; border-radius: 4px; padding: 8px 4px; border: 1px solid #e9ecef;"; // Ultra-minimal
        else
            return "text-align: center; max-width: 600px; margin: 0 auto;";
    }

    private string GetSectionIcon(string sectionName)
    {
        return sectionName.ToLower() switch
        {
            var s when s.Contains("subjective") => Icons.Material.Filled.Person,
            var s when s.Contains("objective") => Icons.Material.Filled.Visibility,
            var s when s.Contains("assessment") => Icons.Material.Filled.Assessment,
            var s when s.Contains("plan") => Icons.Material.Filled.Assignment,
            _ => Icons.Material.Filled.Description
        };
    }

    private Color GetSectionColor(string sectionName)
    {
        return sectionName.ToLower() switch
        {
            var s when s.Contains("subjective") => Color.Info,
            var s when s.Contains("objective") => Color.Success,
            var s when s.Contains("assessment") => Color.Warning,
            var s when s.Contains("plan") => Color.Primary,
            _ => Color.Default
        };
    }
}
