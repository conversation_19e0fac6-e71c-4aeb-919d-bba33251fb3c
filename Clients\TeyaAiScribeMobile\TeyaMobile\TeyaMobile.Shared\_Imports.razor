﻿@using static Microsoft.AspNetCore.Components.Web.RenderMode
@using System.Net.Http
@using System.Net.Http.Json
@using Microsoft.AspNetCore.Components.Forms
@using Microsoft.AspNetCore.Components.Routing
@using Microsoft.AspNetCore.Components.Web
@using Microsoft.AspNetCore.Components.Web.Virtualization
@using Microsoft.JSInterop
@using Microsoft.AspNetCore.Components.Authorization
@using Microsoft.AspNetCore.Authorization
@using TeyaMobile.Shared.Layout
@using TeyaMobile.Shared.Pages
@using TeyaMobile.Shared.Services
@using TeyaHealthMobileModel.Model
@using TeyaHealthMobileViewModel.ViewModel
