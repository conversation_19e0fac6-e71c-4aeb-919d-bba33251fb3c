﻿@page "/manageprofile"
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Authorization
@using TeyaWebApp.Authorization
@attribute [Authorize(Policy = "manageprofileAccessPolicy")]
@layout Admin
@using System.Text.Json
@using Microsoft.AspNetCore.Components.Authorization
@using Microsoft.Extensions.Localization
@using TeyaUIViewModels.ViewModel
@using TeyaWebApp.Components.Layout
@using TeyaWebApp.ViewModel
@using TeyaWebApp.Components.Pages
@inject ITokenService TokenService
@inject GraphApiService AuthService
@inject IDialogService DialogService
@using TeyaWebApp.TeyaAIScribeResource
@inject IStringLocalizer<TeyaAIScribeResource> Localizer
@inject NavigationManager Navigation
@inject AuthenticationStateProvider AuthenticationStateProvider


@if (!_isThemeLoaded)
{
    <div style="display: flex; justify-content: center; align-items: center; height: 80vh;">
        <MudProgressCircular Color="Color.Primary" Indeterminate="true" Size="Size.Large" />
    </div>
}
else
{
    <GenericCard Heading="Manage Profile">
        <MudPaper Class="pa-4" Elevation="3">
            <MudIconButton Icon="@Icons.Material.Filled.Close" Color="Color.Primary" OnClick="NavigateToChart" Style="position: absolute; top: 10px; left: 10px; z-index: 2;" />
            <MudForm @ref="form" @bind-IsValid="success">
                <MudGrid>
                    <MudItem xs="12" md="4">
                        <MudTextField Label="@Localizer["DisplayName"]" @bind-Value="userProfile.DisplayName" Variant="Variant.Outlined"
                                      Required="true" Validation="@(new Func<string, IEnumerable<string>>(DisplayNameValidation))" />
                    </MudItem>
                    <MudItem xs="12" md="4">
                        <MudTextField Label="@Localizer["GivenName"]" @bind-Value="userProfile.GivenName" Variant="Variant.Outlined"
                                      Required="true" Validation="@(new Func<string, IEnumerable<string>>(FirstNameValidation))" />
                    </MudItem>
                    <MudItem xs="12" md="4">
                        <MudTextField Label="@Localizer["Surname"]" @bind-Value="userProfile.Surname" Variant="Variant.Outlined"
                                      Required="true" Validation="@(new Func<string, IEnumerable<string>>(LastNameValidation))" />
                    </MudItem>
                    <MudItem xs="12" md="4">
                        <MudTextField Label="@Localizer["Email"]" @bind-Value="userProfile.Mail" Variant="Variant.Outlined" ReadOnly="true"
                                      Required="true" Validation="@(new Func<string, IEnumerable<string>>(EmailValidation))" />
                    </MudItem>
                    <MudItem xs="12" md="4">
                        <MudTextField Label="@Localizer["UserType"]" @bind-Value="userProfile.UserType" Variant="Variant.Outlined"
                                      Required="true" Validation="@(new Func<string, IEnumerable<string>>(AlphabetValidation))" />
                    </MudItem>
                    <MudItem xs="12" md="4">
                        <MudTextField Label="@Localizer["JobTitle"]" @bind-Value="userProfile.JobTitle" Variant="Variant.Outlined"
                                      Required="true" Validation="@(new Func<string, IEnumerable<string>>(AlphabetValidation))" />
                    </MudItem>
                    <MudItem xs="12" md="4">
                        <MudTextField Label="@Localizer["StreetAddress"]" @bind-Value="userProfile.StreetAddress" Variant="Variant.Outlined"
                                      Required="true" RequiredError="Address is required!" />
                    </MudItem>
                    <MudItem xs="12" md="4">
                        <MudTextField Label="@Localizer["City"]" @bind-Value="userProfile.City" Variant="Variant.Outlined"
                                      Required="true" Validation="@(new Func<string, IEnumerable<string>>(AlphabetValidation))" />
                    </MudItem>
                    <MudItem xs="12" md="4">
                        <MudTextField Label="@Localizer["State"]" @bind-Value="userProfile.State" Variant="Variant.Outlined"
                                      Required="true" Validation="@(new Func<string, IEnumerable<string>>(AlphabetValidation))" />
                    </MudItem>
                    <MudItem xs="12" md="4">
                        <MudTextField Label="@Localizer["PostalCode"]" @bind-Value="userProfile.PostalCode" Variant="Variant.Outlined"
                                      Required="true" Validation="@(new Func<string, IEnumerable<string>>(PostalCodeValidation))" />
                    </MudItem>
                    <MudItem xs="12" md="4">
                        <MudTextField Label="@Localizer["Country"]" @bind-Value="userProfile.Country" Variant="Variant.Outlined"
                                      Required="true" Validation="@(new Func<string, IEnumerable<string>>(AlphabetValidation))" />
                    </MudItem>
                    <MudItem xs="12" md="4">
                        <MudSelect T="ThemeItem" Label="Change Theme" Class="mt-2" Value="SelectedTheme" ValueChanged="OnThemeChanged">
                            @foreach (var theme in Themes)
                            {
                                <MudSelectItem Value="@theme">@theme.Name</MudSelectItem>
                            }
                        </MudSelect>

                        <MudThemeProvider Theme="CurrentTheme" />
                    </MudItem>
                </MudGrid>

                <MudDivider Class="my-4" />
                <MudGrid Justify="Justify.FlexEnd">
                    <MudItem>
                        <MudButton Color="Color.Primary" StartIcon="@Icons.Material.Filled.Save" OnClick="Save" Size="Size.Small" Variant="Variant.Filled">
                            @Localizer["SaveChanges"]
                        </MudButton>
                    </MudItem>
                    <MudItem>
                        <MudButton Color="Color.Warning" StartIcon="@Icons.Material.Filled.Close" OnClick="NavigateToChart" Size="Size.Small" Variant="Variant.Filled">
                            @Localizer["Exit"]
                        </MudButton>
                    </MudItem>
                </MudGrid>
            </MudForm>
        </MudPaper>
    </GenericCard>
}