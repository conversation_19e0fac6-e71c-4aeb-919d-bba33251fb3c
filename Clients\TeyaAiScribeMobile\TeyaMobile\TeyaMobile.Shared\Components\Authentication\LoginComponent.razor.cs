using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Logging;
using MudBlazor;

namespace TeyaMobile.Shared.Components.Authentication
{
    public partial class LoginComponent : ComponentBase
    {
        [Inject] private ILogger<LoginComponent>? Logger { get; set; }

        private bool IsLoading = false;
        private bool IsAuthenticated = false;
        private string UserName = string.Empty;
        private string UserEmail = string.Empty;
        private string ErrorMessage = string.Empty;

        protected override async Task OnInitializedAsync()
        {
            await CheckAuthenticationState();
        }

        private async Task CheckAuthenticationState()
        {
            try
            {
                IsAuthenticated = AuthService.IsAuthenticated;

                if (IsAuthenticated)
                {
                    UserName = await AuthService.GetUserNameAsync();
                    UserEmail = await AuthService.GetUserEmailAsync();
                }

                ErrorMessage = string.Empty;
                StateHasChanged();
            }
            catch (Exception ex)
            {
                ErrorMessage = $"Error checking authentication: {ex.Message}";
                Logger?.LogError(ex, "Error checking authentication state");
                StateHasChanged();
            }
        }

        private async Task HandleLogin()
        {
            try
            {
                IsLoading = true;
                ErrorMessage = string.Empty;
                StateHasChanged();

                Logger?.LogInformation("Starting authentication process");

                // Fixed: Handle bool return type from AuthService.LoginAsync()
                bool loginResult = await AuthService.LoginAsync();

                if (loginResult)
                {
                    Logger?.LogInformation("Authentication successful");
                    Snackbar?.Add("Login successful! Welcome back.", Severity.Success);

                    // For Android platform - authentication successful
                    // Navigate to home page where AuthenticationHandler will process user registration
                    Navigation.NavigateTo("/");
                }
                else
                {
                    // For Web platform - login happens via redirect
                    // User registration will be handled by AuthenticationHandler after redirect
                    ErrorMessage = "Authentication failed. Please try again.";
                    Logger?.LogWarning("Authentication failed");
                    Snackbar?.Add("Authentication failed. Please try again.", Severity.Error);
                    IsLoading = false;
                    StateHasChanged();
                }
            }
            catch (Exception ex)
            {
                ErrorMessage = $"Login failed: {ex.Message}";
                Logger?.LogError(ex, "Login error occurred");
                Snackbar?.Add("An unexpected error occurred during login.", Severity.Error);
                IsLoading = false;
                StateHasChanged();
            }
        }

        private async Task HandleLogout()
        {
            try
            {
                IsLoading = true;
                StateHasChanged();

                Logger?.LogInformation("Starting logout process");

                // Fixed: Handle void return type from AuthService.LogoutAsync()
                await AuthService.LogoutAsync();

                Logger?.LogInformation("Logout successful");
                Snackbar?.Add("Logged out successfully. See you next time!", Severity.Info);

                IsAuthenticated = false;
                UserName = string.Empty;
                UserEmail = string.Empty;
                ErrorMessage = string.Empty;
                IsLoading = false;

                StateHasChanged();
            }
            catch (Exception ex)
            {
                ErrorMessage = $"Logout failed: {ex.Message}";
                Logger?.LogError(ex, "Logout error occurred");
                Snackbar?.Add("An error occurred during logout.", Severity.Error);
                IsLoading = false;
                StateHasChanged();
            }
        }

        private void ClearError()
        {
            ErrorMessage = string.Empty;
            StateHasChanged();
        }
    }
}
