using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using Moq;
using Moq.Protected;
using NUnit.Framework;
using TeyaUIModels.Model;
using TeyaUIViewModels.ViewModel;
using System.Net.Http.Json;
using System.Text.Json;
using System.Linq;

namespace TeyaWebClientUIViewModelTestCases
{
    [TestFixture]
    public class PredefinedVisitTypeServiceTests
    {
        private Mock<HttpMessageHandler> _mockHttpMessageHandler;
        private HttpClient _httpClient;
        private Mock<ILogger<PredefinedVisitTypeService>> _mockLogger;
        private Mock<IStringLocalizer<PredefinedVisitTypeService>> _mockLocalizer;
        private Mock<ITokenService> _mockTokenService;
        private PredefinedVisitTypeService _predefinedVisitTypeService;
        private readonly string _baseUrl = "http://test-api.com";
        private readonly string _accessToken = "test-access-token";

        [SetUp]
        public void Setup()
        {
            // Setup environment variable
            Environment.SetEnvironmentVariable("MemberServiceURL", _baseUrl);

            // Setup mock HTTP handler
            _mockHttpMessageHandler = new Mock<HttpMessageHandler>();

            // Setup HttpClient with mock handler
            _httpClient = new HttpClient(_mockHttpMessageHandler.Object)
            {
                BaseAddress = new Uri(_baseUrl)
            };

            // Setup mock logger
            _mockLogger = new Mock<ILogger<PredefinedVisitTypeService>>();

            // Setup mock localizer
            _mockLocalizer = new Mock<IStringLocalizer<PredefinedVisitTypeService>>();
            _mockLocalizer.Setup(l => l["AccessTokenNotFound"])
                .Returns(new LocalizedString("AccessTokenNotFound", "Access token not found"));
            _mockLocalizer.Setup(l => l["AccessTokenMissing"])
                .Returns(new LocalizedString("AccessTokenMissing", "Access token missing"));
            _mockLocalizer.Setup(l => l["ErrorFetchingAllPredefinedVisitTypes"])
                .Returns(new LocalizedString("ErrorFetchingAllPredefinedVisitTypes", "Error fetching all predefined visit types"));

            // Setup mock token service
            _mockTokenService = new Mock<ITokenService>();
            _mockTokenService.Setup(t => t.AccessToken).Returns(_accessToken);
            _mockTokenService.Setup(t => t.GetValidatedAccessTokenAsync()).ReturnsAsync(_accessToken);

            // Create PredefinedVisitTypeService with mocked dependencies
            _predefinedVisitTypeService = new PredefinedVisitTypeService(_httpClient, _mockLocalizer.Object, _mockLogger.Object, _mockTokenService.Object);
        }

        [TearDown]
        public void TearDown()
        {
            _httpClient.Dispose();
            Environment.SetEnvironmentVariable("MemberServiceURL", null);
        }

        [Test]
        public async Task GetAllPredefinedVisitTypesAsync_WhenSuccessful_ReturnsVisitTypes()
        {
            // Arrange
            var expectedVisitTypes = new List<PredefinedVisitTypedata>
            {
                new PredefinedVisitTypedata
                {
                    ID = Guid.NewGuid(),
                    VisitName = "Annual Checkup",
                    CPTCode = "99385"
                },
                new PredefinedVisitTypedata
                {
                    ID = Guid.NewGuid(),
                    VisitName = "Follow-up Visit",
                    CPTCode = "99213"
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedVisitTypes)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _predefinedVisitTypeService.GetAllPredefinedVisitTypesAsync();

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(expectedVisitTypes.Count));
            Assert.That(result[0].ID, Is.EqualTo(expectedVisitTypes[0].ID));
            Assert.That(result[0].VisitName, Is.EqualTo(expectedVisitTypes[0].VisitName));
            Assert.That(result[0].CPTCode, Is.EqualTo(expectedVisitTypes[0].CPTCode));
        }

        [Test]
        public void GetAllPredefinedVisitTypesAsync_WhenAccessTokenIsNull_ThrowsUnauthorizedAccessException()
        {
            // Arrange
            _mockTokenService.Setup(t => t.AccessToken).Returns((string)null);
            _mockTokenService.Setup(t => t.GetValidatedAccessTokenAsync()).ReturnsAsync((string)null);

            // Act & Assert
            var exception = Assert.ThrowsAsync<UnauthorizedAccessException>(async () =>
                await _predefinedVisitTypeService.GetAllPredefinedVisitTypesAsync());

            Assert.That(exception.Message, Is.EqualTo("Access token missing"));
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.IsAny<Exception>(),
                    It.Is<Func<It.IsAnyType, Exception, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }

        [Test]
        public void GetAllPredefinedVisitTypesAsync_WhenAccessTokenIsEmpty_ThrowsUnauthorizedAccessException()
        {
            // Arrange
            _mockTokenService.Setup(t => t.AccessToken).Returns("");
            _mockTokenService.Setup(t => t.GetValidatedAccessTokenAsync()).ReturnsAsync("");

            // Act & Assert
            var exception = Assert.ThrowsAsync<UnauthorizedAccessException>(async () =>
                await _predefinedVisitTypeService.GetAllPredefinedVisitTypesAsync());

            Assert.That(exception.Message, Is.EqualTo("Access token missing"));
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.IsAny<Exception>(),
                    It.Is<Func<It.IsAnyType, Exception, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }

        [Test]
        public void GetAllPredefinedVisitTypesAsync_WhenHttpRequestFails_ThrowsHttpRequestException()
        {
            // Arrange
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.InternalServerError,
                Content = new StringContent("Internal server error")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _predefinedVisitTypeService.GetAllPredefinedVisitTypesAsync());

            Assert.That(exception, Is.Not.Null);
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.IsAny<Exception>(),
                    It.Is<Func<It.IsAnyType, Exception, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }

        [Test]
        public void GetAllPredefinedVisitTypesAsync_WhenExceptionThrown_PropagatesException()
        {
            // Arrange
            var expectedException = new HttpRequestException("Test exception");

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ThrowsAsync(expectedException);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _predefinedVisitTypeService.GetAllPredefinedVisitTypesAsync());

            Assert.That(exception.Message, Is.EqualTo(expectedException.Message));
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.Is<Exception>(ex => ex.Message == expectedException.Message),
                    It.Is<Func<It.IsAnyType, Exception, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }

        [Test]
        public async Task GetAllPredefinedVisitTypesAsync_WhenResponseIsEmpty_ReturnsEmptyList()
        {
            // Arrange
            var expectedVisitTypes = new List<PredefinedVisitTypedata>();

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedVisitTypes)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _predefinedVisitTypeService.GetAllPredefinedVisitTypesAsync();

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(0));
        }

        [Test]
        public async Task GetAllPredefinedVisitTypesAsync_WhenSingleVisitType_ReturnsSingleItem()
        {
            // Arrange
            var expectedVisitTypes = new List<PredefinedVisitTypedata>
            {
                new PredefinedVisitTypedata
                {
                    ID = Guid.NewGuid(),
                    VisitName = "Emergency Visit",
                    CPTCode = "99281"
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedVisitTypes)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _predefinedVisitTypeService.GetAllPredefinedVisitTypesAsync();

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(1));
            Assert.That(result[0].VisitName, Is.EqualTo("Emergency Visit"));
            Assert.That(result[0].CPTCode, Is.EqualTo("99281"));
        }
    }
}



