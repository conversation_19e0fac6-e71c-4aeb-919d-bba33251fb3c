﻿@page "/recorder/{PatientId:guid}"
@page "/audio-recorder"

@using MudBlazor
@using Microsoft.AspNetCore.Components
@using Microsoft.JSInterop
@using TeyaHealthMobileModel.Model
@using TeyaHealthMobileViewModel.ViewModel
@using TeyaMobile.Shared.Components
@implements IDisposable

<MudContainer MaxWidth="MaxWidth.False" Style="height: 65vh; padding: 5px; margin: 0;">
    <MudPaper Elevation="8" 
              Style="border-radius: 20px; background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%); box-shadow: 0 20px 40px rgba(0,0,0,0.1), 0 0 20px rgba(0,0,0,0.05);">
        
        <MudContainer Style="height: 80%; margin-top: 1%; display: flex; justify-content: center; align-items: center; padding: 2px;">
            <MudPaper Elevation="4" 
                      Style=" background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%); border-radius: 25px; width: 100%; max-width: 650px; padding: 30px; backdrop-filter: blur(10px); box-shadow: 0 15px 35px rgba(0,0,0,0.1);">
                
                <MudStack Spacing="20" AlignItems="AlignItems.Center" JustifyContent="JustifyContent.Center">

                    <!-- Status Container -->
                    <MudPaper Elevation="3" 
                              Style="background-color: tomato; color: white; font-size: 1.1rem; padding: 8px 70px; font-weight: 600; border-radius: 30px; min-width: 220px; text-align: center; box-shadow: 0 8px 25px rgba(255, 99, 71, 0.3);">
                        @GetStatusText()
                    </MudPaper>

                    <!-- Wave Animation Container -->
                    <MudPaper Elevation="2" 
                              Style="padding: 20px; border-radius: 20px; background: rgba(255,255,255,0.8); border: 2px solid rgba(0,128,128,0.2); min-height: 90px; width: 100%; max-width: 350px; display: flex; align-items: center; justify-content: center;">
                        @if (ShouldShowWaveGif())
                        {
                            <MudImage Src="_content/TeyaMobile.Shared/images/aiwave.gif" 
                                      Alt="AI Wave Animation"
                                      Style="height:50px; width:300px; object-fit:contain; border-radius: 15px;" />
                        }
                        else
                        {
                            <MudStack AlignItems="AlignItems.Center" Spacing="2" Style="width: 100%;">
                                <div style="width: 80%; height: 6px; background: linear-gradient(90deg, rgba(0,128,128,0.3) 0%, rgba(0,128,128,0.8) 50%, rgba(0,128,128,0.3) 100%); border-radius: 3px; position: relative; overflow: hidden;">
                                    <div style="position: absolute; top: 0; left: -100%; width: 100%; height: 100%; background: linear-gradient(90deg, transparent 0%, rgba(0,128,128,1) 50%, transparent 100%); animation: shimmer 2s infinite;"></div>
                                </div>
                                <MudText Typo="Typo.caption" Style="color: rgba(0,128,128,0.7); font-weight: 500;">
                                    Ready to record
                                </MudText>
                            </MudStack>
                        }
                    </MudPaper>

                    <!-- Timer Container -->
                    <MudPaper Elevation="3" 
                              Style="padding: 15px 30px; border-radius: 25px; background: rgba(255,255,255,0.9); box-shadow: 0 8px 20px rgba(0,0,0,0.08);">
                        <MudStack Row="true" AlignItems="AlignItems.Center" Spacing="3">
                            <MudIcon Icon="@Icons.Material.Filled.Timer" Color="Color.Dark" Size="Size.Medium" />
                            <MudText Typo="Typo.h4" 
                                     Style="font-weight: bold; font-family: 'Arial Black', sans-serif; color: #333;">
                                @FormatDuration(RecordingDuration)
                            </MudText>
                        </MudStack>
                    </MudPaper>

                    <!-- Control Buttons -->
                    <MudStack Row="true" Justify="Justify.Center" Spacing="4" Class="mt-3 mb-3">
                        @if (!IsRecording && !IsPaused && !HasCompletedRecording)
                        {
                            <MudButton Variant="Variant.Filled" 
                                       Color="MudBlazor.Color.Primary"
                                       StartIcon="@Icons.Material.Filled.Mic"
                                       Size="Size.Large"
                                       Style="border-radius:30px; background-color: teal; min-width:120px; height:50px; font-size: 1rem; font-weight: 600; box-shadow: 0 8px 25px rgba(76, 175, 80, 0.3); transition: all 0.3s ease;"
                                       OnClick="StartRecording" 
                                       Disabled="@IsProcessing">
                                Start
                            </MudButton>
                        }
                        else if (IsRecording || IsPaused)
                        {
                            <MudButton Variant="Variant.Filled" 
                                       Color="MudBlazor.Color.Primary"
                                       StartIcon="@(IsPaused ? Icons.Material.Filled.PlayArrow : Icons.Material.Filled.Pause)"
                                       Size="Size.Large"
                                       Style="border-radius:30px; background-color: teal; min-width:120px; height:50px; font-size: 1rem; font-weight: 600; box-shadow: 0 8px 25px rgba(33, 150, 243, 0.3);"
                                       OnClick="PauseResumeRecording" 
                                       Disabled="@IsProcessing">
                                @(IsPaused ? "Resume" : "Pause")
                            </MudButton>
                            
                            <MudButton Variant="Variant.Filled" 
                                       Color="MudBlazor.Color.Primary"
                                       StartIcon="@Icons.Material.Filled.Stop"
                                       Size="Size.Large"
                                       Style="border-radius:30px; background-color: teal; min-width:120px; height:50px; font-size: 1rem; font-weight: 600; box-shadow: 0 8px 25px rgba(244, 67, 54, 0.3);"
                                       OnClick="StopRecording" 
                                       Disabled="@IsProcessing">
                                Stop
                            </MudButton>
                        }
                        else if (HasCompletedRecording)
                        {
                            <MudButton Variant="Variant.Filled" 
                                       Color="MudBlazor.Color.Primary"
                                       StartIcon="@Icons.Material.Filled.Replay"
                                       Size="Size.Large"
                                       Style="border-radius:30px; background-color: teal; min-width:120px; height:50px; font-size: 1rem; font-weight: 600; box-shadow: 0 8px 25px rgba(0, 188, 212, 0.3);"
                                       OnClick="ReRecord" 
                                       Disabled="@IsProcessing">
                                Retake
                            </MudButton>
                            
                            <MudButton Variant="Variant.Filled" 
                                       Color="MudBlazor.Color.Primary"
                                       StartIcon="@Icons.Material.Filled.ArrowForward"
                                       Size="Size.Large"
                                       Style="border-radius:30px; background-color: teal; min-width:120px; height:50px; font-size: 1rem; font-weight: 600; box-shadow: 0 8px 25px rgba(76, 175, 80, 0.3);"
                                       OnClick="ProcessRecording" 
                                       Disabled="@IsProcessing">
                                Process
                            </MudButton>
                        }
                    </MudStack>

                    <!-- Live Transcription Display -->
                    @if ((IsRecording || IsPaused) && !string.IsNullOrWhiteSpace(LiveTranscriptionText))
                    {
                        <MudPaper Elevation="3" 
                                  Class="pa-4" 
                                  Width="100%" 
                                  MaxWidth="600px"
                                  Style="background-color: skyblue; color: black; border-radius: 20px; height: 80px; overflow-y: auto; box-shadow: 0 8px 25px rgba(255, 99, 71, 0.3); position: relative;">
                            <MudStack Row="true" AlignItems="AlignItems.Start" Spacing="2" Style="height: 100%;">
                                <MudIcon Icon="@Icons.Material.Filled.RecordVoiceOver" 
                                         Color="Color.Inherit" 
                                         Style="margin-top: 2px; flex-shrink: 0;" />
                                <div style="flex: 1; height: 100%; overflow-y: auto; line-height: 1.4;">
                                    <MudText Typo="Typo.body1" Style="color: black; word-wrap: break-word;">
                                        "@LiveTranscriptionText"
                                        @if (IsRecording && !IsPaused)
                                        {
                                            <span style="animation: blink 1s infinite; margin-left: 4px;">|</span>
                                        }
                                    </MudText>
                                </div>
                            </MudStack>
                        </MudPaper>
                    }

                </MudStack>
            </MudPaper>
        </MudContainer>
    </MudPaper>

    <!-- Processing Overlay -->
    @if (IsProcessing)
    {
        <div class="custom-loader-container" style="position:fixed;top:0;left:0;right:0;bottom:0;z-index:9999;">
            <div class="custom-loader-logo-wrapper">
                <img src="_content/TeyaMobile.Shared/images/TeyaHealth.png" alt="TeyaHealth Logo" class="custom-loader-logo" />
            </div>
            <div class="custom-loader-message">
                <div class="custom-loader-text">
                    @if (LoaderContext == "start")
                    {
                        <text>You can start your conversation now…<br /><span class="custom-loader-subtext">Speak freely, your AI Scribe is listening!</span></text>
                    }
                    else if (LoaderContext == "process")
                    {
                        <text>We are preparing your data...<br /><span class="custom-loader-subtext">Just a moment while we organize everything for you!</span></text>
                    }
                    else
                    {
                        <text>Loading...<br /><span class="custom-loader-subtext">Please wait</span></text>
                    }
                </div>
            </div>
        </div>
    }
</MudContainer>

<style>
    .custom-loader-container {
        position: fixed;
        top: 0; left: 0; right: 0; bottom: 0;
        width: 100vw;
        height: 100vh;
        background: linear-gradient(135deg, #f8fafc 0%, #e0e7ef 100%);
        display: flex;
        flex-direction: column;
        justify-content: flex-end;
        align-items: center;
        z-index: 9999;
        overflow: hidden;
    }
    .custom-loader-logo-wrapper {
        flex: 1 1 auto;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100vw;
        margin-top: 10vh;
    }
    .custom-loader-logo {
        width: 35px;
        height: 35px;
        object-fit: contain;
        animation: teya-float 1.8s cubic-bezier(.68,-0.55,.27,1.55) infinite;
        filter: drop-shadow(0 8px 24px rgba(16,185,129,0.18));
    }
    @@keyframes teya-float {
        0%, 100% { transform: translateY(0); }
        20% { transform: translateY(-18px) scale(1.08); }
        40% { transform: translateY(-28px) scale(1.12); }
        60% { transform: translateY(-18px) scale(1.08); }
        80% { transform: translateY(0) scale(1); }
    }
    .custom-loader-message {
        width: 90vw;
        padding-bottom: 7vh;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: flex-end;
    }
    .custom-loader-text {
        color: #0e1726;
        font-size: 1.45rem;
        font-weight: 700;
        text-align: center;
        letter-spacing: 0.01em;
        text-shadow: 0 2px 8px rgba(16,185,129,0.08);
        margin-bottom: 0.5rem;
        font-family: 'Roboto', sans-serif;
    }
    .custom-loader-subtext {
        display: block;
        font-size: 1.05rem;
        font-weight: 400;
        color: #10b981;
        margin-top: 0.5rem;
        letter-spacing: 0.01em;
        text-shadow: 0 1px 4px rgba(16,185,129,0.08);
    }
    @@media (max-width: 600px) {
        .custom-loader-logo { width: 35px; height: 35px; }
        .custom-loader-text { font-size: 1.1rem; }
        .custom-loader-message { padding-bottom: 4vh; }
    }
    /* Tablet/iPad Responsive Enhancements */
    @@media (min-width: 768px) and (max-width: 1200px) {
        .mud-stack-row

    {
        flex-direction: row !important;
        gap: 32px !important;
    }

    .mud-button {
        font-size: 1.2rem !important;
        height: 56px !important;
        min-width: 160px !important;
    }

    }
    @@media (min-width: 1200px) {
        .mud-stack-row

    {
        flex-direction: row !important;
        gap: 48px !important;
    }

    .mud-button {
        font-size: 1.4rem !important;
        height: 64px !important;
        min-width: 200px !important;
    }

    }
</style>
