using NUnit.Framework;
using Moq;
using Moq.Protected;
using Microsoft.Extensions.Logging;
using TeyaUIViewModels.ViewModel;
using System;
using System.Net.Http;
using System.Threading.Tasks;
using System.Threading;
using System.Net;
using System.Text.Json;
using System.Text;

namespace TeyaWebClientUIViewModelTestCases
{
    [TestFixture]
    public class GraphAdminServiceTests
    {
        private Mock<HttpMessageHandler> _mockHttpMessageHandler;
        private Mock<ILogger<GraphAdminService>> _mockLogger;
        private HttpClient _httpClient;
        private GraphAdminService _graphAdminService;
        private const string TestTenantId = "test-tenant-id";
        private const string TestClientId = "test-client-id";
        private const string TestClientSecret = "test-client-secret";

        [SetUp]
        public void SetUp()
        {
            _mockHttpMessageHandler = new Mock<HttpMessageHandler>();
            _mockLogger = new Mock<ILogger<GraphAdminService>>();
            _httpClient = new HttpClient(_mockHttpMessageHandler.Object);

            // Set up environment variables
            Environment.SetEnvironmentVariable("ServicePrincipleTenantId", TestTenantId);
            Environment.SetEnvironmentVariable("ServicePrincipleClientId", TestClientId);
            Environment.SetEnvironmentVariable("ServicePrincipleSecret", TestClientSecret);

            _graphAdminService = new GraphAdminService(_httpClient, _mockLogger.Object);
        }

        [TearDown]
        public void TearDown()
        {
            Environment.SetEnvironmentVariable("ServicePrincipleTenantId", null);
            Environment.SetEnvironmentVariable("ServicePrincipleClientId", null);
            Environment.SetEnvironmentVariable("ServicePrincipleSecret", null);
            _httpClient?.Dispose();
        }

        [Test]
        public async Task GetAdminAccessTokenAsync_WhenSuccessful_ReturnsAccessToken()
        {
            // Arrange
            var expectedToken = "test-access-token";
            var expiresIn = 3600;
            var tokenResponse = new
            {
                access_token = expectedToken,
                expires_in = expiresIn,
                token_type = "Bearer"
            };

            var responseContent = JsonSerializer.Serialize(tokenResponse);
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(responseContent, Encoding.UTF8, "application/json")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _graphAdminService.GetAdminAccessTokenAsync();

            // Assert
            Assert.That(result, Is.EqualTo(expectedToken));
        }

        [Test]
        public async Task GetAdminAccessTokenAsync_WhenCachedTokenIsValid_ReturnsCachedToken()
        {
            // Arrange
            var expectedToken = "cached-access-token";
            var expiresIn = 3600;
            var tokenResponse = new
            {
                access_token = expectedToken,
                expires_in = expiresIn,
                token_type = "Bearer"
            };

            var responseContent = JsonSerializer.Serialize(tokenResponse);
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(responseContent, Encoding.UTF8, "application/json")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act - First call to get and cache the token
            var firstResult = await _graphAdminService.GetAdminAccessTokenAsync();
            
            // Act - Second call should return cached token
            var secondResult = await _graphAdminService.GetAdminAccessTokenAsync();

            // Assert
            Assert.That(firstResult, Is.EqualTo(expectedToken));
            Assert.That(secondResult, Is.EqualTo(expectedToken));
            
            // Verify HTTP call was made only once (for caching)
            _mockHttpMessageHandler
                .Protected()
                .Verify(
                    "SendAsync",
                    Times.Once(),
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>());
        }

        [Test]
        public void GetAdminAccessTokenAsync_WhenHttpRequestFails_ThrowsException()
        {
            // Arrange
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad Request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<Exception>(async () =>
                await _graphAdminService.GetAdminAccessTokenAsync());

            Assert.That(exception.Message, Does.Contain("Failed to get admin token"));
            Assert.That(exception.Message, Does.Contain("BadRequest"));
        }

        [Test]
        public void GetAdminAccessTokenAsync_WhenHttpRequestThrowsException_PropagatesException()
        {
            // Arrange
            var expectedException = new HttpRequestException("Network error");

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ThrowsAsync(expectedException);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _graphAdminService.GetAdminAccessTokenAsync());

            Assert.That(exception.Message, Is.EqualTo(expectedException.Message));
        }

        [Test]
        public async Task GetAdminAccessTokenAsync_WhenTokenResponseIsValid_ParsesCorrectly()
        {
            // Arrange
            var expectedToken = "eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9.test.token";
            var expiresIn = 7200;
            var tokenResponse = new
            {
                access_token = expectedToken,
                expires_in = expiresIn,
                token_type = "Bearer",
                scope = "https://graph.microsoft.com/.default"
            };

            var responseContent = JsonSerializer.Serialize(tokenResponse);
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(responseContent, Encoding.UTF8, "application/json")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _graphAdminService.GetAdminAccessTokenAsync();

            // Assert
            Assert.That(result, Is.EqualTo(expectedToken));
        }

        [Test]
        public async Task GetAdminAccessTokenAsync_VerifiesCorrectRequestFormat()
        {
            // Arrange
            var tokenResponse = new
            {
                access_token = "test-token",
                expires_in = 3600
            };

            var responseContent = JsonSerializer.Serialize(tokenResponse);
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(responseContent, Encoding.UTF8, "application/json")
            };

            HttpRequestMessage capturedRequest = null;
            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .Callback<HttpRequestMessage, CancellationToken>((req, ct) => capturedRequest = req)
                .ReturnsAsync(response);

            // Act
            await _graphAdminService.GetAdminAccessTokenAsync();

            // Assert
            Assert.That(capturedRequest, Is.Not.Null);
            Assert.That(capturedRequest.Method, Is.EqualTo(HttpMethod.Post));
            Assert.That(capturedRequest.RequestUri.ToString(), Is.EqualTo($"https://login.microsoftonline.com/{TestTenantId}/oauth2/v2.0/token"));
            Assert.That(capturedRequest.Content, Is.TypeOf<FormUrlEncodedContent>());
        }

        [Test]
        public async Task GetAdminAccessTokenAsync_WhenTokenExpiresSoon_RefreshesToken()
        {
            // Arrange
            var firstToken = "first-token";
            var secondToken = "second-token";
            var expiresIn = 300; // 5 minutes - should trigger refresh due to 5-minute buffer

            var firstTokenResponse = new
            {
                access_token = firstToken,
                expires_in = expiresIn
            };

            var secondTokenResponse = new
            {
                access_token = secondToken,
                expires_in = 3600
            };

            var firstResponseContent = JsonSerializer.Serialize(firstTokenResponse);
            var secondResponseContent = JsonSerializer.Serialize(secondTokenResponse);

            var firstResponse = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(firstResponseContent, Encoding.UTF8, "application/json")
            };

            var secondResponse = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(secondResponseContent, Encoding.UTF8, "application/json")
            };

            _mockHttpMessageHandler
                .Protected()
                .SetupSequence<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(firstResponse)
                .ReturnsAsync(secondResponse);

            // Act
            var firstResult = await _graphAdminService.GetAdminAccessTokenAsync();
            
            // Wait a moment to ensure time passes
            await Task.Delay(100);
            
            var secondResult = await _graphAdminService.GetAdminAccessTokenAsync();

            // Assert
            Assert.That(firstResult, Is.EqualTo(firstToken));
            Assert.That(secondResult, Is.EqualTo(secondToken));
        }
    }
}



