using Microsoft.Extensions.Logging;

namespace TeyaMobile.Shared.Services
{
    public class StorageContainer
    {
        private readonly ILogger<StorageContainer> _logger;
        private Guid? _organizationId;

        public StorageContainer(ILogger<StorageContainer> logger)
        {
            _logger = logger;
        }

        public Guid? OrganizationId
        {
            get => _organizationId;
            set
            {
                if (_organizationId != value)
                {
                    _logger.LogInformation("StorageContainer.OrganizationId changed from {OldValue} to {NewValue}",
                        _organizationId?.ToString() ?? "null",
                        value?.ToString() ?? "null");
                    _organizationId = value;
                }
            }
        }
    }
}