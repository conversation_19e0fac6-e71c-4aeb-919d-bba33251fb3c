<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, maximum-scale=1.0, user-scalable=no, viewport-fit=cover" />
    <title>TeyaMobile</title>
    <base href="/" />
    <link rel="stylesheet" href="_content/TeyaMobile.Shared/bootstrap/bootstrap.min.css" />
    <link rel="stylesheet" href="_content/TeyaMobile.Shared/app.css" />
    <link rel="stylesheet" href="app.css" />
    <link rel="stylesheet" href="TeyaMobile.styles.css" />
    <link rel="icon" href="data:,">
    <link href="_content/Syncfusion.Blazor.Themes/material.css" rel="stylesheet" />
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet" />
    <link href="https://fonts.googleapis.com/css?family=Roboto:300,400,500,700&display=swap" rel="stylesheet" />
    <link href="_content/MudBlazor/MudBlazor.min.css" rel="stylesheet" />
</head>

<body>
    <div class="status-bar-safe-area"></div>

    <div id="app">
        <div class="custom-loader-container">
            <div class="custom-loader-logo-wrapper">
                <img src="_content/TeyaMobile.Shared/images/TeyaHealth.png" alt="TeyaHealth Logo" class="custom-loader-logo" />
            </div>
            <div class="custom-loader-message">
                <div class="custom-loader-text">
                    Getting things ready for you...<br />
                    <span class="custom-loader-subtext">Almost there! Organizing your workspace. Just a moment...</span>
                </div>
            </div>
        </div>
    </div>

    <style>
        .custom-loader-container {
            position: fixed;
            top: 0; left: 0; right: 0; bottom: 0;
            width: 100vw;
            height: 100vh;
            background: linear-gradient(135deg, #f8fafc 0%, #e0e7ef 100%);
            display: flex;
            flex-direction: column;
            justify-content: flex-end;
            align-items: center;
            z-index: 9999;
            overflow: hidden;
        }
        .custom-loader-logo-wrapper {
            flex: 1 1 auto;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100vw;
            margin-top: 10vh;
        }
        .custom-loader-logo {
            width: 35px;
            height: 35px;
            object-fit: contain;
            animation: teya-float 1.8s cubic-bezier(.68,-0.55,.27,1.55) infinite;
            filter: drop-shadow(0 8px 24px rgba(16,185,129,0.18));
        }
        @keyframes teya-float {
            0%, 100% { transform: translateY(0); }
            20% { transform: translateY(-18px) scale(1.08); }
            40% { transform: translateY(-28px) scale(1.12); }
            60% { transform: translateY(-18px) scale(1.08); }
            80% { transform: translateY(0) scale(1); }
        }
        .custom-loader-message {
            width: 90vw;
            padding-bottom: 7vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: flex-end;
        }
        .custom-loader-text {
            color: #0e1726;
            font-size: 1.45rem;
            font-weight: 700;
            text-align: center;
            letter-spacing: 0.01em;
            text-shadow: 0 2px 8px rgba(16,185,129,0.08);
            margin-bottom: 0.5rem;
            font-family: 'Roboto', sans-serif;
        }
        .custom-loader-subtext {
            display: block;
            font-size: 1.05rem;
            font-weight: 400;
            color: #10b981;
            margin-top: 0.5rem;
            letter-spacing: 0.01em;
            text-shadow: 0 1px 4px rgba(16,185,129,0.08);
        }
        @media (max-width: 600px) {
            .custom-loader-logo { width: 35px; height: 35px; }
            .custom-loader-text { font-size: 1.1rem; }
            .custom-loader-message { padding-bottom: 4vh; }
        }
    </style>

    <script src="_content/Syncfusion.Blazor.Core/scripts/syncfusion-blazor.min.js" type="text/javascript"></script>
    <script src="_content/TeyaMobile.Shared/js/AudioRecorder.js"></script>
    <script src="_framework/blazor.webview.js" autostart="false"></script>
    <script src="_content/MudBlazor/MudBlazor.min.js"></script>

    <script>
        if (window.navigator.userAgent.includes('iPhone') || window.navigator.userAgent.includes('iPad')) {
            // iOS-specific initialization
            window.addEventListener('DOMContentLoaded', function () {
                setTimeout(function () {
                    if (typeof Blazor !== 'undefined') {
                        Blazor.start();
                    }
                }, 100);
            });
        }
    </script>
</body>
</html>
