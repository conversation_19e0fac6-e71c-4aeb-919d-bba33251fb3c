﻿using Microsoft.AspNetCore.Components;

namespace TeyaWebApp.Components.GenericFontComponents
{
    // Label Component
    public partial class GenericLabel : ComponentBase
    {
        [Parameter]
        public RenderFragment ChildContent { get; set; }

        [Parameter]
        public string Size { get; set; } = FontSize.Small;

        [Parameter]
        public string Weight { get; set; } = FontWeight.Medium;

        [Parameter]
        public string Color { get; set; } = "inherit";

        [Parameter]
        public bool Uppercase { get; set; } = false;

        [Parameter]
        public string Class { get; set; } = "";

        [Parameter]
        public string For { get; set; }

        [Parameter(CaptureUnmatchedValues = true)]
        public Dictionary<string, object> AdditionalAttributes { get; set; }

        protected override void BuildRenderTree(Microsoft.AspNetCore.Components.Rendering.RenderTreeBuilder builder)
        {
            builder.OpenElement(0, "label");

            if (!string.IsNullOrEmpty(For))
            {
                builder.AddAttribute(1, "for", For);
            }

            builder.AddAttribute(2, "class", GetCssClass());
            builder.AddAttribute(3, "style", GetStyle());

            if (AdditionalAttributes != null)
            {
                foreach (var attribute in AdditionalAttributes)
                {
                    builder.AddAttribute(4, attribute.Key, attribute.Value);
                }
            }

            builder.AddContent(5, ChildContent);
            builder.CloseElement();
        }

        private string GetCssClass()
        {
            var classes = new List<string> { "generic-label" };

            if (!string.IsNullOrEmpty(Class))
            {
                classes.Add(Class);
            }

            if (Uppercase)
            {
                classes.Add("text-uppercase");
            }

            return string.Join(" ", classes);
        }

        private string GetStyle()
        {
            return $"font-family: {FontFamily.Default}; font-size: {Size}; font-weight: {Weight}; color: {Color}; display: inline-block; margin-bottom: 0.25rem;";
        }
    }
}
