﻿@page "/myrequests"
@using TeyaUIModels.Model
@using TeyaUIViewModels.ViewModel
@using Microsoft.Extensions.Localization
@using Microsoft.Extensions.Logging
@using Microsoft.AspNetCore.Authorization
@attribute [Authorize]
@using TeyaWebApp.TeyaAIScribeResource
@using TeyaWebApp.Components.Layout
@layout Admin

<PageTitle>@Localizer["MyRequests"]</PageTitle>

<MudContainer MaxWidth="MaxWidth.False" Class="pa-6">
    <!-- Main Header -->
    <MudStack Row AlignItems="AlignItems.Center" Justify="Justify.SpaceBetween" Class="mb-6">
        <MudStack Row AlignItems="AlignItems.Center" Spacing="3">
            <MudIcon Icon="@Icons.Material.Filled.Send" Color="Color.Primary" Size="Size.Large" />
            <MudStack Spacing="1">
                <MudText Typo="Typo.h4" Style="font-weight: 700; color: #1976d2;">
                    @Localizer["MyRequests"]
                </MudText>
                <MudText Typo="Typo.body2" Style="color: #666;">
                    @Localizer["ManageOutgoingCosigningRequests"]
                </MudText>
            </MudStack>
        </MudStack>

        <!-- Status Summary -->
        <MudStack Row Spacing="2">
            <MudChip T="string" Color="Color.Warning" Size="Size.Medium" Icon="@Icons.Material.Filled.Schedule">
                @_myRequests.Count(r => r.Status == CosigningRequestStatus.Pending) @Localizer["Pending"]
            </MudChip>
            <MudChip T="string" Color="Color.Success" Size="Size.Medium" Icon="@Icons.Material.Filled.CheckCircle">
                @_myRequests.Count(r => r.Status == CosigningRequestStatus.Approved) @Localizer["Approved"]
            </MudChip>
            <MudChip T="string" Color="Color.Error" Size="Size.Medium" Icon="@Icons.Material.Filled.Comment">
                @_myRequests.Count(r => r.Status == CosigningRequestStatus.ChangesRequested) @Localizer["ChangesRequested"]
            </MudChip>
            <MudButton Variant="Variant.Outlined"
                       Color="Color.Primary"
                       StartIcon="@Icons.Material.Filled.Refresh"
                       OnClick="LoadMyRequests"
                       Size="Size.Medium">
                @Localizer["Refresh"]
            </MudButton>
        </MudStack>
    </MudStack>

    <!-- Main Content -->
    @if (_isLoading)
    {
        <MudPaper Class="pa-8" Elevation="2" Style="border-radius: 12px;">
            <MudStack AlignItems="AlignItems.Center" Spacing="3">
                <MudProgressCircular Color="Color.Primary" Size="Size.Large" Indeterminate="true" />
                <MudText Typo="Typo.h6">@Localizer["LoadingRequests"]</MudText>
                <MudText Typo="Typo.body2" Style="color: #666;">@Localizer["PleaseWait"]</MudText>
            </MudStack>
        </MudPaper>
    }
    else if (!_myRequests.Any())
    {
        <!-- Empty State -->
        <MudPaper Class="pa-8" Elevation="2" Style="border-radius: 12px; text-align: center;">
            <MudStack AlignItems="AlignItems.Center" Spacing="4">
                <MudIcon Icon="@Icons.Material.Filled.SendTimeExtension" Size="Size.Large" Style="color: #9e9e9e; font-size: 4rem;" />
                <MudStack Spacing="2" AlignItems="AlignItems.Center">
                    <MudText Typo="Typo.h5" Style="color: #666; font-weight: 600;">
                        @Localizer["NoRequestsFound"]
                    </MudText>
                    <MudText Typo="Typo.body1" Style="color: #999; max-width: 400px;">
                        @Localizer["NoOutgoingRequestsMessage"]
                    </MudText>
                </MudStack>
                <MudButton Variant="Variant.Outlined"
                           Color="Color.Primary"
                           OnClick="LoadMyRequests"
                           StartIcon="@Icons.Material.Filled.Refresh">
                    @Localizer["Refresh"]
                </MudButton>
            </MudStack>
        </MudPaper>
    }
    else
    {
        <!-- GitHub-style Request Cards -->
        <MudStack Spacing="3">
            @foreach (var request in _myRequests.OrderByDescending(r => r.RequestedDate))
            {
                <MudPaper Class="request-card"
                          Elevation="2"
                          Style="@GetRequestCardStyle(request)"
                          @onclick="@(() => ViewRequestDetails(request))">
                    <MudStack Row AlignItems="AlignItems.Center" Justify="Justify.SpaceBetween" Class="pa-4">
                        <!-- Left Section: Request Info -->
                        <MudStack Row AlignItems="AlignItems.Center" Spacing="3">
                            <!-- Status Indicator -->
                            <div class="status-indicator @GetStatusClass(request.Status)"></div>

                            <!-- Request Details -->
                            <MudStack Spacing="1">
                                <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
                                    <MudIcon Icon="@Icons.Material.Filled.Person"
                                             Color="Color.Primary"
                                             Size="Size.Small" />
                                    <MudText Typo="Typo.subtitle1" Style="font-weight: 600;">
                                        @request.PatientName
                                    </MudText>
                                    <MudText Typo="Typo.body2" Style="color: #666;">
                                        (@request.PatientAge, @request.PatientGender)
                                    </MudText>
                                </MudStack>

                                <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
                                    <MudIcon Icon="@Icons.Material.Filled.MedicalServices"
                                             Color="Color.Secondary"
                                             Size="Size.Small" />
                                    <MudText Typo="Typo.body2">
                                        @Localizer["RequestedFrom"]: @request.ReviewerName
                                    </MudText>
                                    <MudText Typo="Typo.caption" Style="color: #999;">
                                        @request.RequestedDate.ToString("MMM dd, yyyy 'at' h:mm tt")
                                    </MudText>
                                </MudStack>

                                <!-- Action Date -->
                                @if (request.ReviewedDate.HasValue)
                                {
                                    <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
                                        <MudIcon Icon="@GetActionIcon(request.Status)"
                                                 Color="@GetStatusColor(request.Status)"
                                                 Size="Size.Small" />
                                        <MudText Typo="Typo.caption" Style="color: #666;">
                                            @GetActionText(request.Status) @request.ReviewedDate.Value.ToString("MMM dd, yyyy 'at' h:mm tt")
                                        </MudText>
                                    </MudStack>
                                }
                            </MudStack>
                        </MudStack>

                        <!-- Right Section: Status & Actions -->
                        <MudStack Row AlignItems="AlignItems.Center" Spacing="3">
                            <!-- Status Badge -->
                            <MudChip T="string"
                                     Color="@GetStatusColor(request.Status)"
                                     Size="Size.Medium"
                                     Icon="@GetStatusIcon(request.Status)"
                                     Variant="Variant.Filled">
                                @GetStatusText(request.Status)
                            </MudChip>

                            <!-- Action Buttons -->
                            <MudStack Row Spacing="2">
                                <MudButton Variant="Variant.Outlined"
                                           Color="Color.Primary"
                                           Size="Size.Medium"
                                           StartIcon="@Icons.Material.Filled.Visibility"
                                           @onclick="@(() => ViewNotes(request.RecordId))"
                                           @onclick:stopPropagation="true"
                                           Class="view-button">
                                    @Localizer["ViewNotes"]
                                </MudButton>

                                @if (request.Status == CosigningRequestStatus.Pending)
                                {
                                    <MudButton Variant="Variant.Text"
                                               Color="Color.Error"
                                               Size="Size.Medium"
                                               StartIcon="@Icons.Material.Filled.Cancel"
                                               @onclick="@(() => CancelRequest(request))"
                                               @onclick:stopPropagation="true"
                                               Class="cancel-button">
                                        @Localizer["Cancel"]
                                    </MudButton>
                                }
                                else if (request.Status == CosigningRequestStatus.ChangesRequested)
                                {
                                    <MudButton Variant="Variant.Filled"
                                               Color="Color.Warning"
                                               Size="Size.Medium"
                                               StartIcon="@Icons.Material.Filled.Edit"
                                               @onclick="@(() => ViewComments(request))"
                                               @onclick:stopPropagation="true"
                                               Class="changes-button">
                                        @Localizer["ViewChanges"]
                                    </MudButton>
                                }
                                else if (request.Status == CosigningRequestStatus.Approved)
                                {
                                    <MudButton Variant="Variant.Text"
                                               Color="Color.Success"
                                               Size="Size.Medium"
                                               StartIcon="@Icons.Material.Filled.CheckCircle"
                                               @onclick="@(() => ViewComments(request))"
                                               @onclick:stopPropagation="true"
                                               Class="approved-button">
                                        @Localizer["ViewDetails"]
                                    </MudButton>
                                }
                            </MudStack>

                            <!-- Expand Arrow -->
                            <MudIconButton Icon="@Icons.Material.Filled.ChevronRight"
                                           Size="Size.Small"
                                           Color="Color.Default"
                                           Style="opacity: 0.6;" />
                        </MudStack>
                    </MudStack>

                    <!-- Comments Preview (if any) -->
                    @if (!string.IsNullOrEmpty(request.CommentsJson) && request.CommentsJson != "[]")
                    {
                        <MudDivider />
                        <div class="pa-3" style="background: #f8f9fa;">
                            <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
                                <MudIcon Icon="@Icons.Material.Filled.Comment"
                                         Size="Size.Small"
                                         Color="@(request.Status == CosigningRequestStatus.ChangesRequested ? Color.Warning : Color.Info)" />
                                <MudText Typo="Typo.caption" Style="color: #666;">
                                    @GetCommentsPreview(request.CommentsJson)
                                </MudText>
                            </MudStack>
                        </div>
                    }
                </MudPaper>
            }
        </MudStack>
    }
</MudContainer>

<!-- Request Details Dialog -->
<MudDialog @bind-IsVisible="_showRequestDetailsDialog" Options="_requestDetailsDialogOptions">
    <TitleContent>
        <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
            <MudIcon Icon="@Icons.Material.Filled.Info" Color="Color.Primary" />
            <MudText Typo="Typo.h6">@Localizer["RequestDetails"]</MudText>
        </MudStack>
    </TitleContent>
    <DialogContent>
        @if (_selectedRequest != null)
        {
            <MudStack Spacing="3">
                <!-- Patient Information -->
                <MudPaper Class="pa-3" Elevation="1" Style="background: #f8f9fa;">
                    <MudGrid>
                        <MudItem xs="6">
                            <MudText Typo="Typo.caption" Style="color: #666; text-transform: uppercase; font-weight: 600;">
                                @Localizer["Patient"]
                            </MudText>
                            <MudText Typo="Typo.body1" Style="font-weight: 600;">
                                @_selectedRequest.PatientName
                            </MudText>
                            <MudText Typo="Typo.body2" Style="color: #666;">
                                @_selectedRequest.PatientAge years old, @_selectedRequest.PatientGender
                            </MudText>
                        </MudItem>
                        <MudItem xs="6">
                            <MudText Typo="Typo.caption" Style="color: #666; text-transform: uppercase; font-weight: 600;">
                                @Localizer["Reviewer"]
                            </MudText>
                            <MudText Typo="Typo.body1" Style="font-weight: 600;">
                                @_selectedRequest.ReviewerName
                            </MudText>
                        </MudItem>
                    </MudGrid>
                </MudPaper>

                <!-- Request Timeline -->
                <MudStack Spacing="2">
                    <MudText Typo="Typo.subtitle1" Style="font-weight: 600;">@Localizer["Timeline"]</MudText>

                    <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
                        <MudIcon Icon="@Icons.Material.Filled.Send" Color="Color.Primary" Size="Size.Small" />
                        <MudText Typo="Typo.body2">
                            @Localizer["RequestSent"]: @_selectedRequest.RequestedDate.ToString("MMM dd, yyyy 'at' h:mm tt")
                        </MudText>
                    </MudStack>

                    @if (_selectedRequest.ReviewedDate.HasValue)
                    {
                        <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
                            <MudIcon Icon="@GetActionIcon(_selectedRequest.Status)"
                                     Color="@GetStatusColor(_selectedRequest.Status)"
                                     Size="Size.Small" />
                            <MudText Typo="Typo.body2">
                                @GetActionText(_selectedRequest.Status): @_selectedRequest.ReviewedDate.Value.ToString("MMM dd, yyyy 'at' h:mm tt")
                            </MudText>
                        </MudStack>
                    }
                </MudStack>

                <!-- Comments Section -->
                @if (!string.IsNullOrEmpty(_selectedRequest.CommentsJson) && _selectedRequest.CommentsJson != "[]")
                {
                    <MudStack Spacing="2">
                        <MudText Typo="Typo.subtitle1" Style="font-weight: 600;">@Localizer["Comments"]</MudText>
                        <MudPaper Class="pa-3" Elevation="1" Style="max-height: 300px; overflow-y: auto;">
                            @foreach (var comment in GetRequestComments(_selectedRequest.CommentsJson))
                            {
                                <MudStack Row AlignItems="AlignItems.Start" Spacing="2" Class="mb-3">
                                    <MudAvatar Color="Color.Secondary" Size="Size.Small">
                                        @comment.CommenterName.Substring(0, 1).ToUpper()
                                    </MudAvatar>
                                    <MudStack Spacing="1" Style="flex: 1;">
                                        <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
                                            <MudText Typo="Typo.subtitle2" Style="font-weight: 600;">
                                                @comment.CommenterName
                                            </MudText>
                                            <MudText Typo="Typo.caption" Style="color: #666;">
                                                @comment.CommentDate.ToString("MMM dd, yyyy 'at' h:mm tt")
                                            </MudText>
                                        </MudStack>
                                        <MudText Typo="Typo.body2" Style="line-height: 1.5;">
                                            @comment.Comment
                                        </MudText>
                                    </MudStack>
                                </MudStack>
                            }
                        </MudPaper>
                    </MudStack>
                }
            </MudStack>
        }
    </DialogContent>
    <DialogActions>
        <MudButton OnClick="CloseRequestDetailsDialog">@Localizer["Close"]</MudButton>
        @if (_selectedRequest?.Status == CosigningRequestStatus.Pending)
        {
            <MudButton Color="Color.Error"
                       Variant="Variant.Outlined"
                       OnClick="@(() => CancelRequest(_selectedRequest))"
                       StartIcon="@Icons.Material.Filled.Cancel">
                @Localizer["CancelRequest"]
            </MudButton>
        }
        <MudButton Color="Color.Primary"
                   Variant="Variant.Filled"
                   OnClick="@(() => ViewNotes(_selectedRequest.RecordId))"
                   StartIcon="@Icons.Material.Filled.Visibility">
            @Localizer["ViewNotes"]
        </MudButton>
    </DialogActions>
</MudDialog>

<style>
    /* GitHub-style Request Cards */
    .request-card {
        border-radius: 12px;
        border: 1px solid #e1e4e8;
        transition: all 0.2s ease;
        cursor: pointer;
        background: white;
    }

    .request-card:hover {
        border-color: #1976d2;
        box-shadow: 0 4px 12px rgba(25, 118, 210, 0.15);
        transform: translateY(-1px);
    }

    .status-indicator {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        flex-shrink: 0;
    }

    .status-indicator.pending {
        background-color: #ff9800;
        box-shadow: 0 0 0 3px rgba(255, 152, 0, 0.2);
        animation: pulse 2s infinite;
    }

    .status-indicator.approved {
        background-color: #4caf50;
        box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.2);
    }

    .status-indicator.changes-requested {
        background-color: #f44336;
        box-shadow: 0 0 0 3px rgba(244, 67, 54, 0.2);
        animation: pulse 2s infinite;
    }

    /* Button Styles */
    .view-button {
        border-color: #1976d2;
        color: #1976d2;
    }

    .view-button:hover {
        background: #1976d2;
        color: white;
    }

    .cancel-button {
        color: #f44336;
    }

    .cancel-button:hover {
        background: rgba(244, 67, 54, 0.1);
    }

    .changes-button {
        background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
        border: none;
        box-shadow: 0 2px 4px rgba(255, 152, 0, 0.3);
    }

    .changes-button:hover {
        background: linear-gradient(135deg, #f57c00 0%, #ef6c00 100%);
        box-shadow: 0 4px 8px rgba(255, 152, 0, 0.4);
    }

    .approved-button {
        color: #4caf50;
    }

    .approved-button:hover {
        background: rgba(76, 175, 80, 0.1);
    }

    /* Animation for status changes */
    @@keyframes pulse {
        0% {
            box-shadow: 0 0 0 0 rgba(25, 118, 210, 0.4);
        }
        70% {
            box-shadow: 0 0 0 6px rgba(25, 118, 210, 0);
        }
        100% {
            box-shadow: 0 0 0 0 rgba(25, 118, 210, 0);
        }
    }

    /* Responsive Design */
    @@media (max-width: 768px) {
        .request-card .pa-4 {
            padding: 16px !important;
        }

        .request-card .mud-stack-row {
            flex-direction: column;
            align-items: flex-start !important;
        }

        .request-card .mud-stack-row > * {
            width: 100%;
        }
    }
</style>