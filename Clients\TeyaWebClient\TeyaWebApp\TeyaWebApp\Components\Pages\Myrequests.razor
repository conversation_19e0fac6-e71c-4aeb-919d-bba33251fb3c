﻿@page "/myrequests"
@using TeyaUIModels.Model
@using TeyaUIViewModels.ViewModel
@using Microsoft.Extensions.Localization
@using Microsoft.Extensions.Logging
@using Microsoft.AspNetCore.Authorization
@attribute [Authorize]
@using TeyaWebApp.TeyaAIScribeResource
@using TeyaWebApp.Components.Layout
@layout Admin

<PageTitle>@Localizer["MyRequests"]</PageTitle>

<MudContainer MaxWidth="MaxWidth.False" Class="pa-6">
    <MudPaper Class="pa-6" Elevation="2" Style="border-radius: 8px;">
        <!-- Header Section -->
        <MudStack Row AlignItems="AlignItems.Center" Justify="Justify.SpaceBetween" Class="mb-4">
            <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
                <MudIcon Icon="@Icons.Material.Filled.Send" Color="Color.Primary" Size="Size.Large" />
                <MudText Typo="Typo.h4" Style="font-weight: 600; color: #1976d2;">
                    @Localizer["MyRequests"]
                </MudText>
            </MudStack>
            <MudChip T="string" Color="Color.Info" Size="Size.Medium" Icon="@Icons.Material.Filled.Info">
                @Localizer["OutgoingRequests"]: @_myRequests.Count
            </MudChip>
        </MudStack>

        <MudDivider Class="mb-4" />

        <!-- Loading State -->
        @if (_isLoading)
        {
            <MudStack AlignItems="AlignItems.Center" Spacing="3" Class="pa-8">
                <MudProgressCircular Color="Color.Primary" Size="Size.Large" Indeterminate="true" />
                <MudText Typo="Typo.body1">@Localizer["LoadingRequests"]</MudText>
            </MudStack>
        }
        else if (!_myRequests.Any())
        {
            <!-- Empty State -->
            <MudStack AlignItems="AlignItems.Center" Spacing="3" Class="pa-8">
                <MudIcon Icon="@Icons.Material.Filled.SendTimeExtension" Size="Size.Large" Style="color: #9e9e9e;" />
                <MudText Typo="Typo.h6" Style="color: #9e9e9e;">@Localizer["NoRequestsFound"]</MudText>
                <MudText Typo="Typo.body2" Style="color: #9e9e9e;">@Localizer["NoOutgoingRequestsMessage"]</MudText>
            </MudStack>
        }
        else
        {
            <!-- Requests Table -->
            <MudTable Items="@_myRequests"
                      Hover="true"
                      Striped="true"
                      Dense="true"
                      FixedHeader="true"
                      Height="600px"
                      Class="requests-table">
                <HeaderContent>
                    <MudTh Style="font-weight: 600;">@Localizer["Patient"]</MudTh>
                    <MudTh Style="font-weight: 600;">@Localizer["Age"]</MudTh>
                    <MudTh Style="font-weight: 600;">@Localizer["Gender"]</MudTh>
                    <MudTh Style="font-weight: 600;">@Localizer["RequestedProvider"]</MudTh>
                    <MudTh Style="font-weight: 600;">@Localizer["Status"]</MudTh>
                    <MudTh Style="font-weight: 600;">@Localizer["RequestDate"]</MudTh>
                    <MudTh Style="font-weight: 600;">@Localizer["ActionDate"]</MudTh>
                    <MudTh Style="font-weight: 600;">@Localizer["Actions"]</MudTh>
                </HeaderContent>
                <RowTemplate>
                    <MudTd DataLabel="Patient">
                        <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
                            <MudIcon Icon="@Icons.Material.Filled.Person" Size="Size.Small" Color="Color.Primary" />
                            <MudText Typo="Typo.body2" Style="font-weight: 500;">@context.PatientName</MudText>
                        </MudStack>
                    </MudTd>
                    <MudTd DataLabel="Age">@context.PatientAge</MudTd>
                    <MudTd DataLabel="Gender">@context.PatientGender</MudTd>
                    <MudTd DataLabel="RequestedProvider">
                        <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
                            <MudIcon Icon="@Icons.Material.Filled.MedicalServices" Size="Size.Small" Color="Color.Secondary" />
                            <MudText Typo="Typo.body2">@context.ReviewerName</MudText>
                        </MudStack>
                    </MudTd>
                    <MudTd DataLabel="Status">
                        <MudChip T="string"
                                 Color="@GetStatusColor(context.Status)"
                                 Size="Size.Small"
                                 Icon="@GetStatusIcon(context.Status)"
                                 Variant="Variant.Filled">
                            @GetStatusText(context.Status)
                        </MudChip>
                    </MudTd>
                    <MudTd DataLabel="RequestDate">
                        <MudText Typo="Typo.body2">@context.RequestedDate.ToString("MM/dd/yyyy")</MudText>
                        <MudText Typo="Typo.caption" Class="text-muted">@context.RequestedDate.ToString("HH:mm")</MudText>
                    </MudTd>
                    <MudTd DataLabel="ActionDate">
                        @if (context.ReviewedDate.HasValue)
                        {
                            <MudText Typo="Typo.body2">@context.ReviewedDate.Value.ToString("MM/dd/yyyy")</MudText>
                            <MudText Typo="Typo.caption" Class="text-muted">@context.ReviewedDate.Value.ToString("HH:mm")</MudText>
                        }
                        else
                        {
                            <MudText Typo="Typo.caption" Class="text-muted">@Localizer["Pending"]</MudText>
                        }
                    </MudTd>
                    <MudTd DataLabel="Actions">
                        <MudStack Row Spacing="1">
                            <MudIconButton Icon="@Icons.Material.Filled.Visibility"
                                           Color="Color.Primary"
                                           Size="Size.Small"
                                           OnClick="@(() => ViewNotes(context.RecordId))"
                                           Title="@Localizer["ViewNotes"]" />
                            
                            @if (context.Status == CosigningRequestStatus.Approved)
                            {
                                <MudIconButton Icon="@Icons.Material.Filled.Cancel"
                                               Color="Color.Error"
                                               Size="Size.Small"
                                               OnClick="@(() => CancelRequest(context))"
                                               Title="@Localizer["CancelRequest"]" />
                            }
                        </MudStack>
                    </MudTd>
                </RowTemplate>
            </MudTable>
        }
    </MudPaper>
</MudContainer>

<style>
    .requests-table {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    .text-muted {
        color: #6c757d;
    }

    .mud-table-row:hover {
        background-color: rgba(25, 118, 210, 0.04) !important;
        cursor: pointer;
    }
</style>