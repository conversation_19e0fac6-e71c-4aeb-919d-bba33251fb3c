name: Azure SQL Database

on:
  workflow_dispatch:
  push:
    branches:
      - Release/TeyaAIScribe.2025.03.17

jobs:
  build:
    runs-on: ubuntu-latest
    
    steps:
      - uses: actions/checkout@v3
      
      - name: Install SQLPackage
        run: |
          curl -L https://aka.ms/sqlpackage-linux -o sqlpackage.zip
          sudo mkdir -p /usr/local/sqlpackage
          sudo unzip sqlpackage.zip -d /usr/local/sqlpackage
          sudo chmod +x /usr/local/sqlpackage/sqlpackage
          echo "/usr/local/sqlpackage" >> $GITHUB_PATH
      
      # Install .NET SDK
      - name: Setup .NET
        uses: actions/setup-dotnet@v3
        with:
          dotnet-version: '6.0.x'


      - uses: azure/login@v1                          
        with:
          creds: ${{ secrets.DEVREPOSECRET }}
      
      - uses: azure/sql-action@v2.3
        with:
          connection-string: 'Server=tcp:teyahealth-server-prod.database.windows.net,1433;Initial Catalog=Teyahealth-prod;Persist Security Info=False;User ID=gaurang;Password=letcheck123@;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;'
          path: 'Database/TeyaHealthSQLDatabase/TeyaHealthSQLDatabase.sqlproj'
          action: 'publish'
          build-arguments: '/p:Configuration=Debug' 
          arguments: '/p:DropObjectsNotInSource=true /p:IgnoreColumnOrder=true /p:BlockOnPossibleDataLoss=false'
          
