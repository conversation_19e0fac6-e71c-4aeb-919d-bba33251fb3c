using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Moq;
using Moq.Protected;
using NUnit.Framework;
using TeyaUIModels.Model;
using TeyaUIViewModels.ViewModel;
using System.Net.Http.Json;
using System.Text;

namespace TeyaWebClientUIViewModelTestCases
{
    [TestFixture]
    public class DiagnosticImagingPageServiceTests
    {
        private Mock<HttpMessageHandler> _mockHttpMessageHandler;
        private HttpClient _httpClient;
        private Mock<IConfiguration> _mockConfiguration;
        private Mock<IStringLocalizer<DiagnosticImagingPageService>> _mockLocalizer;
        private Mock<ILogger<DiagnosticImagingPageService>> _mockLogger;
        private Mock<ITokenService> _mockTokenService;
        private DiagnosticImagingPageService _diagnosticImagingPageService;
        private readonly string _baseUrl = "http://test-api.com";
        private readonly string _accessToken = "test-token";

        [SetUp]
        public void Setup()
        {
            // Setup environment variable
            Environment.SetEnvironmentVariable("EncounterNotesURL", _baseUrl);

            // Setup mock HTTP handler
            _mockHttpMessageHandler = new Mock<HttpMessageHandler>();

            // Setup HttpClient with mock handler
            _httpClient = new HttpClient(_mockHttpMessageHandler.Object)
            {
                BaseAddress = new Uri(_baseUrl)
            };

            // Setup mock configuration
            _mockConfiguration = new Mock<IConfiguration>();

            // Setup mock localizer
            _mockLocalizer = new Mock<IStringLocalizer<DiagnosticImagingPageService>>();
            _mockLocalizer.Setup(l => l["AddressRetrievalFailure"])
                .Returns(new LocalizedString("AddressRetrievalFailure", "Failed to retrieve address"));
            _mockLocalizer.Setup(l => l["MemberRetrievalFailure"])
                .Returns(new LocalizedString("MemberRetrievalFailure", "Failed to retrieve member"));

            // Setup mock logger
            _mockLogger = new Mock<ILogger<DiagnosticImagingPageService>>();

            // Setup mock token service
            _mockTokenService = new Mock<ITokenService>();
            _mockTokenService.Setup(t => t.AccessToken).Returns(_accessToken);

            // Create DiagnosticImagingPageService with mocked dependencies
            _diagnosticImagingPageService = new DiagnosticImagingPageService(_httpClient, _mockConfiguration.Object, _mockLogger.Object, _mockLocalizer.Object, _mockTokenService.Object);
        }

        [TearDown]
        public void TearDown()
        {
            _httpClient.Dispose();
            Environment.SetEnvironmentVariable("EncounterNotesURL", null);
        }

        [Test]
        public async Task GetDiagnosticImagingAsync_WhenSuccessful_ReturnsDiagnosticImagingDTOs()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var expectedImages = new List<DiagnosticImagingDTO>
            {
                new DiagnosticImagingDTO
                {
                    RecordID = Guid.NewGuid(),
                    PatientId = patientId,
                    Status = "Completed",
                    Provider = "Dr. Smith",
                    Facility = "Main Hospital",
                    Procedgure = "Chest X-Ray",
                    IsActive = true
                },
                new DiagnosticImagingDTO
                {
                    RecordID = Guid.NewGuid(),
                    PatientId = patientId,
                    Status = "Pending",
                    Provider = "Dr. Johnson",
                    Facility = "Imaging Center",
                    Procedgure = "MRI Brain",
                    IsActive = false
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedImages)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _diagnosticImagingPageService.GetDiagnosticImagingAsync(patientId, orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(2));
            Assert.That(result[0].Procedgure, Is.EqualTo("Chest X-Ray"));
            Assert.That(result[1].Procedgure, Is.EqualTo("MRI Brain"));
        }

        [Test]
        public async Task GetDiagnosticImagingByIdAsyncAndIsActive_WhenSuccessful_ReturnsActiveDiagnosticImagingDTOs()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var expectedImages = new List<DiagnosticImagingDTO>
            {
                new DiagnosticImagingDTO
                {
                    RecordID = Guid.NewGuid(),
                    PatientId = patientId,
                    Status = "Completed",
                    Provider = "Dr. Smith",
                    Facility = "Main Hospital",
                    Procedgure = "Chest X-Ray",
                    IsActive = true
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedImages)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _diagnosticImagingPageService.GetDiagnosticImagingByIdAsyncAndIsActive(patientId, orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(1));
            Assert.That(result[0].Procedgure, Is.EqualTo("Chest X-Ray"));
            Assert.That(result[0].IsActive, Is.True);
        }
    }
}



