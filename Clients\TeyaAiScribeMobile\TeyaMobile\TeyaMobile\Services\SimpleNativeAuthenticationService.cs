using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Identity.Client;
using System.IdentityModel.Tokens.Jwt;
using TeyaHealthMobileViewModel.ViewModel;

namespace TeyaMobile.Services
{
    /// <summary>
    /// Base authentication service for native platforms (iOS and Android)
    /// </summary>
    public abstract class SimpleNativeAuthenticationService : IAuthenticationService
    {
        protected readonly IPublicClientApplication _publicClientApp;
        protected readonly ILogger _logger;
        protected readonly IConfiguration _configuration;
        protected readonly string[] _scopes;

        protected SimpleNativeAuthenticationService(
            IPublicClientApplication publicClientApp,
            ILogger logger,
            IConfiguration configuration)
        {
            _publicClientApp = publicClientApp;
            _logger = logger;
            _configuration = configuration;

            // Get scopes from configuration
            var azureAdSection = _configuration.GetSection("AzureAd");
            var scopesConfig = azureAdSection.GetSection("Scopes").Get<string[]>();
            _scopes = scopesConfig ?? new[] { "openid" };
        }

        public bool IsAuthenticated
        {
            get
            {
                try
                {
                    var accounts = _publicClientApp.GetAccountsAsync().Result;
                    return accounts.Any();
                }
                catch
                {
                    return false;
                }
            }
        }

        public async Task<string> GetAccessTokenAsync()
        {
            try
            {
                var accounts = await _publicClientApp.GetAccountsAsync();
                if (accounts.Any())
                {
                    var result = await _publicClientApp.AcquireTokenSilent(_scopes, accounts.FirstOrDefault())
                        .ExecuteAsync();
                    return result.AccessToken;
                }
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get access token: {Error}", ex.Message);
                throw;
            }
        }

        public async Task<string> GetUserNameAsync()
        {
            try
            {
                var accounts = await _publicClientApp.GetAccountsAsync();
                if (accounts.Any())
                {
                    return accounts.FirstOrDefault()?.Username;
                }
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get username: {Error}", ex.Message);
                throw;
            }
        }

        public async Task<string> GetUserEmailAsync()
        {
            try
            {
                var accounts = await _publicClientApp.GetAccountsAsync();
                if (accounts.Any())
                {
                    var result = await _publicClientApp.AcquireTokenSilent(_scopes, accounts.FirstOrDefault())
                        .ExecuteAsync();
                    var claims = new JwtSecurityToken(result.AccessToken);
                    return claims.Claims.FirstOrDefault(c => c.Type == "preferred_username")?.Value;
                }
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get user email: {Error}", ex.Message);
                throw;
            }
        }

        public async Task<string> GetGraphApiScopeAsync()
        {
            try
            {
                var accounts = await _publicClientApp.GetAccountsAsync();
                if (accounts.Any())
                {
                    var graphScopes = new[] { "https://graph.microsoft.com/.default" };
                    var result = await _publicClientApp.AcquireTokenSilent(graphScopes, accounts.FirstOrDefault())
                        .ExecuteAsync();
                    return result.AccessToken;
                }
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get Graph API token: {Error}", ex.Message);
                throw;
            }
        }

        public async Task<string> GetServiceSpecificTokenAsync()
        {
            try
            {
                var accounts = await _publicClientApp.GetAccountsAsync();
                if (accounts.Any())
                {
                    var serviceScopes = new[] { "api://your-service-id/.default" };
                    var result = await _publicClientApp.AcquireTokenSilent(serviceScopes, accounts.FirstOrDefault())
                        .ExecuteAsync();
                    return result.AccessToken;
                }
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get service-specific token: {Error}", ex.Message);
                throw;
            }
        }

        public async Task<bool> LoginAsync()
        {
            try
            {
                // Try silent authentication first
                var accounts = await _publicClientApp.GetAccountsAsync();
                if (accounts.Any())
                {
                    try
                    {
                        var result = await _publicClientApp.AcquireTokenSilent(_scopes, accounts.FirstOrDefault())
                            .ExecuteAsync();
                        return true;
                    }
                    catch (MsalUiRequiredException)
                    {
                        // Silent auth failed, need interactive auth
                    }
                }

                // Interactive authentication
                var authResult = await _publicClientApp.AcquireTokenInteractive(_scopes)
                    .WithPrompt(Prompt.SelectAccount)
                    .ExecuteAsync();

                return authResult != null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Login failed: {Error}", ex.Message);
                return false;
            }
        }

        public async Task LogoutAsync()
        {
            try
            {
                var accounts = await _publicClientApp.GetAccountsAsync();
                foreach (var account in accounts)
                {
                    await _publicClientApp.RemoveAsync(account);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Logout failed: {Error}", ex.Message);
                throw;
            }
        }

        protected virtual void ConfigurePlatformSpecificSettings()
        {
            // Base implementation does nothing
            // Platform-specific implementations will override this
        }
    }
}
