using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using Moq;
using Moq.Protected;
using NUnit.Framework;
using TeyaUIModels.Model;
using TeyaWebApp.Services;
using System.Net.Http.Json;
using System.Text.Json;
using TeyaUIViewModels.ViewModel;
using System.Linq;

namespace TeyaWebClientUIViewModelTestCases
{
    [TestFixture]
    public class PatientSpecificAlertServiceTests
    {
        private Mock<HttpMessageHandler> _mockHttpMessageHandler;
        private HttpClient _httpClient;
        private Mock<ILogger<PatientSpecificAlertsService>> _mockLogger;
        private Mock<IStringLocalizer<PatientSpecificAlertsService>> _mockLocalizer;
        private Mock<ITokenService> _mockTokenService;
        private PatientSpecificAlertsService _patientSpecificAlertsService;
        private readonly string _baseUrl = "http://test-api.com";
        private readonly string _accessToken = "test-access-token";

        [SetUp]
        public void Setup()
        {
            // Setup environment variable
            Environment.SetEnvironmentVariable("AlertsServiceURL", _baseUrl);

            // Setup mock HTTP handler
            _mockHttpMessageHandler = new Mock<HttpMessageHandler>();

            // Setup HttpClient with mock handler
            _httpClient = new HttpClient(_mockHttpMessageHandler.Object)
            {
                BaseAddress = new Uri(_baseUrl)
            };

            // Setup mock logger
            _mockLogger = new Mock<ILogger<PatientSpecificAlertsService>>();

            // Setup mock localizer
            _mockLocalizer = new Mock<IStringLocalizer<PatientSpecificAlertsService>>();
            _mockLocalizer.Setup(l => l["ErrorFetchingPatientSpecificAlerts"])
                .Returns(new LocalizedString("ErrorFetchingPatientSpecificAlerts", "Error fetching patient specific alerts"));
            _mockLocalizer.Setup(l => l["ErrorFetchingActivePatientSpecificAlerts"])
                .Returns(new LocalizedString("ErrorFetchingActivePatientSpecificAlerts", "Error fetching active patient specific alerts"));
            _mockLocalizer.Setup(l => l["PatientSpecificAlertsAddedSuccessfully"])
                .Returns(new LocalizedString("PatientSpecificAlertsAddedSuccessfully", "Patient specific alerts added successfully"));
            _mockLocalizer.Setup(l => l["AddPatientSpecificAlertsFailed"])
                .Returns(new LocalizedString("AddPatientSpecificAlertsFailed", "Add patient specific alerts failed"));
            _mockLocalizer.Setup(l => l["ErrorAddingPatientSpecificAlerts"])
                .Returns(new LocalizedString("ErrorAddingPatientSpecificAlerts", "Error adding patient specific alerts"));
            _mockLocalizer.Setup(l => l["PatientSpecificAlertUpdatedSuccessfully"])
                .Returns(new LocalizedString("PatientSpecificAlertUpdatedSuccessfully", "Patient specific alert updated successfully"));
            _mockLocalizer.Setup(l => l["PatientSpecificAlertUpdateFailed"])
                .Returns(new LocalizedString("PatientSpecificAlertUpdateFailed", "Patient specific alert update failed"));
            _mockLocalizer.Setup(l => l["ErrorUpdatingPatientSpecificAlert"])
                .Returns(new LocalizedString("ErrorUpdatingPatientSpecificAlert", "Error updating patient specific alert"));
            _mockLocalizer.Setup(l => l["PatientSpecificAlertsListUpdatedSuccessfully"])
                .Returns(new LocalizedString("PatientSpecificAlertsListUpdatedSuccessfully", "Patient specific alerts list updated successfully"));
            _mockLocalizer.Setup(l => l["PatientSpecificAlertsListUpdateFailed"])
                .Returns(new LocalizedString("PatientSpecificAlertsListUpdateFailed", "Patient specific alerts list update failed"));
            _mockLocalizer.Setup(l => l["ErrorUpdatingPatientSpecificAlertsList"])
                .Returns(new LocalizedString("ErrorUpdatingPatientSpecificAlertsList", "Error updating patient specific alerts list"));
            _mockLocalizer.Setup(l => l["PatientSpecificAlertDeletedSuccessfully"])
                .Returns(new LocalizedString("PatientSpecificAlertDeletedSuccessfully", "Patient specific alert deleted successfully"));
            _mockLocalizer.Setup(l => l["PatientSpecificAlertDeletionFailed"])
                .Returns(new LocalizedString("PatientSpecificAlertDeletionFailed", "Patient specific alert deletion failed"));
            _mockLocalizer.Setup(l => l["ErrorDeletingPatientSpecificAlert"])
                .Returns(new LocalizedString("ErrorDeletingPatientSpecificAlert", "Error deleting patient specific alert"));

            // Setup mock token service
            _mockTokenService = new Mock<ITokenService>();
            _mockTokenService.Setup(t => t.AccessToken).Returns(_accessToken);
            _mockTokenService.Setup(t => t.GetValidatedAccessTokenAsync()).ReturnsAsync(_accessToken);

            // Create PatientSpecificAlertsService with mocked dependencies
            _patientSpecificAlertsService = new PatientSpecificAlertsService(_httpClient, _mockLocalizer.Object, _mockLogger.Object, _mockTokenService.Object);
        }

        [TearDown]
        public void TearDown()
        {
            _httpClient.Dispose();
            Environment.SetEnvironmentVariable("AlertsServiceURL", null);
        }

        [Test]
        public async Task GetAllPatientSpecificAlertsAsync_WhenSuccessful_ReturnsAlerts()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var expectedAlerts = new List<PatientSpecificAlertsData>
            {
                new PatientSpecificAlertsData
                {
                    Id = Guid.NewGuid(),
                    PatientId = patientId,
                    AlertName = "Blood Pressure Check",
                    AlertType = AlertType.Lab,
                    DueDate = DateTime.Now.AddDays(30),
                    IsActive = true,
                    IsRecurring = false
                },
                new PatientSpecificAlertsData
                {
                    Id = Guid.NewGuid(),
                    PatientId = patientId,
                    AlertName = "Annual Physical",
                    AlertType = AlertType.Appointment,
                    DueDate = DateTime.Now.AddDays(365),
                    IsActive = true,
                    IsRecurring = true
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedAlerts)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _patientSpecificAlertsService.GetAllPatientSpecificAlertsAsync(patientId);

            // Assert
            Assert.That(result, Is.Not.Null);
            var resultList = result.ToList();
            Assert.That(resultList.Count, Is.EqualTo(expectedAlerts.Count));
            Assert.That(resultList[0].Id, Is.EqualTo(expectedAlerts[0].Id));
            Assert.That(resultList[0].PatientId, Is.EqualTo(expectedAlerts[0].PatientId));
            Assert.That(resultList[0].AlertName, Is.EqualTo(expectedAlerts[0].AlertName));
            Assert.That(resultList[0].AlertType, Is.EqualTo(expectedAlerts[0].AlertType));
            Assert.That(resultList[0].IsActive, Is.EqualTo(expectedAlerts[0].IsActive));
            Assert.That(resultList[0].IsRecurring, Is.EqualTo(expectedAlerts[0].IsRecurring));
        }

        [Test]
        public void GetAllPatientSpecificAlertsAsync_WhenExceptionThrown_PropagatesException()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var expectedException = new HttpRequestException("Test exception");

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ThrowsAsync(expectedException);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _patientSpecificAlertsService.GetAllPatientSpecificAlertsAsync(patientId));

            Assert.That(exception.Message, Is.EqualTo(expectedException.Message));
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.Is<Exception>(ex => ex.Message == expectedException.Message),
                    It.Is<Func<It.IsAnyType, Exception, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }

        [Test]
        public async Task GetActivePatientSpecificAlertsAsync_WhenSuccessful_ReturnsActiveAlerts()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var expectedAlerts = new List<PatientSpecificAlertsData>
            {
                new PatientSpecificAlertsData
                {
                    Id = Guid.NewGuid(),
                    PatientId = patientId,
                    AlertName = "Blood Pressure Check",
                    AlertType = AlertType.Lab,
                    DueDate = DateTime.Now.AddDays(30),
                    IsActive = true,
                    IsRecurring = false
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedAlerts)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _patientSpecificAlertsService.GetActivePatientSpecificAlertsAsync(patientId);

            // Assert
            Assert.That(result, Is.Not.Null);
            var resultList = result.ToList();
            Assert.That(resultList.Count, Is.EqualTo(expectedAlerts.Count));
            Assert.That(resultList[0].IsActive, Is.True);
        }

        [Test]
        public void GetActivePatientSpecificAlertsAsync_WhenExceptionThrown_PropagatesException()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var expectedException = new HttpRequestException("Test exception");

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ThrowsAsync(expectedException);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _patientSpecificAlertsService.GetActivePatientSpecificAlertsAsync(patientId));

            Assert.That(exception.Message, Is.EqualTo(expectedException.Message));
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.Is<Exception>(ex => ex.Message == expectedException.Message),
                    It.Is<Func<It.IsAnyType, Exception, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }

        [Test]
        public async Task AddPatientSpecificAlertsAsync_WhenSuccessful_CompletesSuccessfully()
        {
            // Arrange
            var alerts = new List<PatientSpecificAlertsData>
            {
                new PatientSpecificAlertsData
                {
                    Id = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    AlertName = "Blood Test",
                    AlertType = AlertType.Lab,
                    DueDate = DateTime.Now.AddDays(30),
                    IsActive = true,
                    IsRecurring = false
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.Created,
                Content = new StringContent("Success")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.DoesNotThrowAsync(async () => await _patientSpecificAlertsService.AddPatientSpecificAlertsAsync(alerts));

            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Information,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.IsAny<Exception>(),
                    It.Is<Func<It.IsAnyType, Exception, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }

        [Test]
        public void AddPatientSpecificAlertsAsync_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var alerts = new List<PatientSpecificAlertsData>
            {
                new PatientSpecificAlertsData
                {
                    Id = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    AlertName = "Blood Test",
                    AlertType = AlertType.Lab,
                    DueDate = DateTime.Now.AddDays(30),
                    IsActive = true,
                    IsRecurring = false
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _patientSpecificAlertsService.AddPatientSpecificAlertsAsync(alerts));

            Assert.That(exception.Message, Is.EqualTo("Add patient specific alerts failed"));
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.IsAny<Exception>(),
                    It.Is<Func<It.IsAnyType, Exception, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }

        [Test]
        public async Task AddPatientSpecificAlertAsync_WhenSuccessful_CompletesSuccessfully()
        {
            // Arrange
            var alert = new PatientSpecificAlertsData
            {
                Id = Guid.NewGuid(),
                PatientId = Guid.NewGuid(),
                AlertName = "Blood Test",
                AlertType = AlertType.Lab,
                DueDate = DateTime.Now.AddDays(30),
                IsActive = true,
                IsRecurring = false
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.Created,
                Content = new StringContent("Success")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.DoesNotThrowAsync(async () => await _patientSpecificAlertsService.AddPatientSpecificAlertAsync(alert));
        }

        [Test]
        public async Task UpdatePatientSpecificAlertAsync_WhenSuccessful_CompletesSuccessfully()
        {
            // Arrange
            var alert = new PatientSpecificAlertsData
            {
                Id = Guid.NewGuid(),
                PatientId = Guid.NewGuid(),
                AlertName = "Blood Test Updated",
                AlertType = AlertType.Lab,
                DueDate = DateTime.Now.AddDays(60),
                IsActive = true,
                IsRecurring = true
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent("Success")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.DoesNotThrowAsync(async () => await _patientSpecificAlertsService.UpdatePatientSpecificAlertAsync(alert));

            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Information,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.IsAny<Exception>(),
                    It.Is<Func<It.IsAnyType, Exception, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }

        [Test]
        public void UpdatePatientSpecificAlertAsync_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var alert = new PatientSpecificAlertsData
            {
                Id = Guid.NewGuid(),
                PatientId = Guid.NewGuid(),
                AlertName = "Blood Test Updated",
                AlertType = AlertType.Lab,
                DueDate = DateTime.Now.AddDays(60),
                IsActive = true,
                IsRecurring = true
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _patientSpecificAlertsService.UpdatePatientSpecificAlertAsync(alert));

            Assert.That(exception.Message, Is.EqualTo("Patient specific alert update failed"));
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.IsAny<Exception>(),
                    It.Is<Func<It.IsAnyType, Exception, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }

        [Test]
        public async Task UpdatePatientSpecificAlertsListAsync_WhenSuccessful_CompletesSuccessfully()
        {
            // Arrange
            var alerts = new List<PatientSpecificAlertsData>
            {
                new PatientSpecificAlertsData
                {
                    Id = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    AlertName = "Blood Test 1",
                    AlertType = AlertType.Lab,
                    DueDate = DateTime.Now.AddDays(30),
                    IsActive = true,
                    IsRecurring = false
                },
                new PatientSpecificAlertsData
                {
                    Id = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    AlertName = "Blood Test 2",
                    AlertType = AlertType.Lab,
                    DueDate = DateTime.Now.AddDays(60),
                    IsActive = true,
                    IsRecurring = true
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent("Success")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.DoesNotThrowAsync(async () => await _patientSpecificAlertsService.UpdatePatientSpecificAlertsListAsync(alerts));

            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Information,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.IsAny<Exception>(),
                    It.Is<Func<It.IsAnyType, Exception, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }

        [Test]
        public async Task DeletePatientSpecificAlertByIdAsync_WhenSuccessful_CompletesSuccessfully()
        {
            // Arrange
            var alertId = Guid.NewGuid();

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.NoContent
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.DoesNotThrowAsync(async () => await _patientSpecificAlertsService.DeletePatientSpecificAlertByIdAsync(alertId));

            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Information,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.IsAny<Exception>(),
                    It.Is<Func<It.IsAnyType, Exception, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }

        [Test]
        public void DeletePatientSpecificAlertByIdAsync_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var alertId = Guid.NewGuid();

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.NotFound,
                Content = new StringContent("Not found")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _patientSpecificAlertsService.DeletePatientSpecificAlertByIdAsync(alertId));

            Assert.That(exception.Message, Is.EqualTo("Patient specific alert deletion failed"));
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.IsAny<Exception>(),
                    It.Is<Func<It.IsAnyType, Exception, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }

        [Test]
        public async Task DeletePatientSpecificAlertByEntityAsync_WhenSuccessful_CompletesSuccessfully()
        {
            // Arrange
            var alert = new PatientSpecificAlertsData
            {
                Id = Guid.NewGuid(),
                PatientId = Guid.NewGuid(),
                AlertName = "Blood Test",
                AlertType = AlertType.Lab,
                DueDate = DateTime.Now.AddDays(30),
                IsActive = true,
                IsRecurring = false
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.NoContent
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.DoesNotThrowAsync(async () => await _patientSpecificAlertsService.DeletePatientSpecificAlertByEntityAsync(alert));

            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Information,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.IsAny<Exception>(),
                    It.Is<Func<It.IsAnyType, Exception, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }

        [Test]
        public void DeletePatientSpecificAlertByEntityAsync_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var alert = new PatientSpecificAlertsData
            {
                Id = Guid.NewGuid(),
                PatientId = Guid.NewGuid(),
                AlertName = "Blood Test",
                AlertType = AlertType.Lab,
                DueDate = DateTime.Now.AddDays(30),
                IsActive = true,
                IsRecurring = false
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _patientSpecificAlertsService.DeletePatientSpecificAlertByEntityAsync(alert));

            Assert.That(exception.Message, Is.EqualTo("Patient specific alert deletion failed"));
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.IsAny<Exception>(),
                    It.Is<Func<It.IsAnyType, Exception, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }
    }
}



