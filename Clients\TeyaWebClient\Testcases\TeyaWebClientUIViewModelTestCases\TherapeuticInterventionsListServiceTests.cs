using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using TeyaUIViewModels.TeyaUIViewModelResources;
using Moq;
using Moq.Protected;
using NUnit.Framework;
using TeyaUIModels.Model;
using TeyaUIViewModels.ViewModel;
using System.Net.Http.Json;
using System.Text.Json;

namespace TeyaWebClientUIViewModelTestCases
{
    [TestFixture]
    public class TherapeuticInterventionsListServiceTests
    {
        private Mock<HttpMessageHandler> _mockHttpMessageHandler;
        private HttpClient _httpClient;
        private Mock<IStringLocalizer<TeyaUIViewModelsStrings>> _mockLocalizer;
        private Mock<ILogger<TherapeuticInterventionsListService>> _mockLogger;
        private Mock<ITokenService> _mockTokenService;
        private Mock<IConfiguration> _mockConfiguration;
        private TherapeuticInterventionsListService _therapeuticInterventionsListService;
        private readonly string _baseUrl = "http://test-api.com";
        private readonly string _accessToken = "test-access-token";

        [SetUp]
        public void Setup()
        {
            // Setup environment variable
            Environment.SetEnvironmentVariable("MemberServiceURL", _baseUrl);

            // Setup mock HTTP handler
            _mockHttpMessageHandler = new Mock<HttpMessageHandler>();

            // Setup HttpClient with mock handler
            _httpClient = new HttpClient(_mockHttpMessageHandler.Object)
            {
                BaseAddress = new Uri(_baseUrl)
            };

            // Setup mock localizer
            _mockLocalizer = new Mock<IStringLocalizer<TeyaUIViewModelsStrings>>();
            _mockLocalizer.Setup(l => l["AccessTokenNotFound"])
                .Returns(new LocalizedString("AccessTokenNotFound", "Access token not found"));
            _mockLocalizer.Setup(l => l["AccessTokenMissing"])
                .Returns(new LocalizedString("AccessTokenMissing", "Access token missing"));
            _mockLocalizer.Setup(l => l["ErrorFetchingAllTherapeuticInterventionsLists"])
                .Returns(new LocalizedString("ErrorFetchingAllTherapeuticInterventionsLists", "Error fetching all therapeutic interventions lists"));

            // Setup mock logger
            _mockLogger = new Mock<ILogger<TherapeuticInterventionsListService>>();

            // Setup mock token service
            _mockTokenService = new Mock<ITokenService>();
            _mockTokenService.Setup(t => t.AccessToken).Returns(_accessToken);

            // Setup mock configuration
            _mockConfiguration = new Mock<IConfiguration>();

            // Create TherapeuticInterventionsListService with mocked dependencies
            _therapeuticInterventionsListService = new TherapeuticInterventionsListService(_httpClient, _mockConfiguration.Object, _mockLocalizer.Object, _mockTokenService.Object);
        }

        [TearDown]
        public void TearDown()
        {
            _httpClient.Dispose();
            Environment.SetEnvironmentVariable("MemberServiceURL", null);
        }

        [Test]
        public async Task GetAllTherapeuticInterventionsListCodesAsync_WhenSuccessful_ReturnsTherapeuticInterventionsListCode()
        {
            // Arrange
            var expectedInterventions = new List<TherapeuticInterventionsListCode>
            {
                new TherapeuticInterventionsListCode
                {
                    ID = Guid.NewGuid(),
                    TherapyType = "Physical Therapy",
                    Description = "Physical therapy for mobility improvement"
                },
                new TherapeuticInterventionsListCode
                {
                    ID = Guid.NewGuid(),
                    TherapyType = "Occupational Therapy",
                    Description = "Occupational therapy for daily living skills"
                },
                new TherapeuticInterventionsListCode
                {
                    ID = Guid.NewGuid(),
                    TherapyType = "Speech Therapy",
                    Description = "Speech therapy for communication improvement"
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(JsonSerializer.Serialize(expectedInterventions))
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _therapeuticInterventionsListService.GetAllTherapeuticInterventionsListCodesAsync();

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(expectedInterventions.Count));
            Assert.That(result[0].ID, Is.EqualTo(expectedInterventions[0].ID));
            Assert.That(result[0].TherapyType, Is.EqualTo(expectedInterventions[0].TherapyType));
            Assert.That(result[0].Description, Is.EqualTo(expectedInterventions[0].Description));

            // Verify all interventions
            Assert.That(result[1].TherapyType, Is.EqualTo("Occupational Therapy"));
            Assert.That(result[2].TherapyType, Is.EqualTo("Speech Therapy"));
        }

        [Test]
        public void GetAllTherapeuticInterventionsListCodesAsync_WhenAccessTokenIsNull_ThrowsUnauthorizedAccessException()
        {
            // Arrange
            _mockTokenService.Setup(t => t.AccessToken).Returns((string)null);

            // Act & Assert
            var exception = Assert.ThrowsAsync<InvalidOperationException>(async () =>
                await _therapeuticInterventionsListService.GetAllTherapeuticInterventionsListCodesAsync());

            Assert.That(exception.Message, Is.EqualTo("Handler did not return a response message."));


        }

        [Test]
        public void GetAllTherapeuticInterventionsListCodesAsync_WhenAccessTokenIsEmpty_ThrowsUnauthorizedAccessException()
        {
            // Arrange
            _mockTokenService.Setup(t => t.AccessToken).Returns(string.Empty);

            // Act & Assert
            var exception = Assert.ThrowsAsync<InvalidOperationException>(async () =>
                await _therapeuticInterventionsListService.GetAllTherapeuticInterventionsListCodesAsync());

            Assert.That(exception.Message, Is.EqualTo("Handler did not return a response message."));


        }

        [Test]
        public void GetAllTherapeuticInterventionsListCodesAsync_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.NotFound,
                Content = new StringContent("Not found")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _therapeuticInterventionsListService.GetAllTherapeuticInterventionsListCodesAsync());

            Assert.That(exception, Is.Not.Null);
        }

        [Test]
        public async Task GetAllTherapeuticInterventionsListCodesAsync_WhenEmptyResponse_ReturnsEmptyList()
        {
            // Arrange
            var expectedInterventions = new List<TherapeuticInterventionsListCode>();

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(JsonSerializer.Serialize(expectedInterventions))
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _therapeuticInterventionsListService.GetAllTherapeuticInterventionsListCodesAsync();

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(0));
        }

        [Test]
        public void GetAllTherapeuticInterventionsListCodesAsync_WhenExceptionThrown_PropagatesException()
        {
            // Arrange
            var expectedException = new HttpRequestException("Test exception");

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ThrowsAsync(expectedException);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _therapeuticInterventionsListService.GetAllTherapeuticInterventionsListCodesAsync());

            Assert.That(exception, Is.Not.Null);
        }

        [Test]
        public async Task GetAllTherapeuticInterventionsListCodesAsync_WhenComplexInterventionData_ReturnsCorrectData()
        {
            // Arrange
            var expectedInterventions = new List<TherapeuticInterventionsListCode>
            {
                new TherapeuticInterventionsListCode
                {
                    ID = Guid.NewGuid(),
                    TherapyType = "Complex intervention with special characters: !@#$%^&*()",
                    Description = "Multi-line description\nwith line breaks\nand special characters: àáâãäåæçèéêë"
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(JsonSerializer.Serialize(expectedInterventions))
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _therapeuticInterventionsListService.GetAllTherapeuticInterventionsListCodesAsync();

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(1));
            var intervention = result[0];
            Assert.That(intervention.TherapyType, Is.EqualTo(expectedInterventions[0].TherapyType));
            Assert.That(intervention.Description, Is.EqualTo(expectedInterventions[0].Description));
        }
    }
}



