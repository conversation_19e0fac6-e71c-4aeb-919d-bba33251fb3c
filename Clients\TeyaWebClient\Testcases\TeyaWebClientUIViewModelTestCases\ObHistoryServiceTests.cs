using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Moq;
using Moq.Protected;
using NUnit.Framework;
using TeyaUIModels.Model;
using TeyaUIViewModels.ViewModel;
using TeyaUIViewModels.TeyaUIViewModelResources;

namespace TeyaWebClientUIViewModelTestCases
{
    [TestFixture]
    public class ObHistoryServiceTests
    {
        private Mock<HttpMessageHandler> _mockHttpMessageHandler;
        private Mock<IConfiguration> _mockConfiguration;
        private Mock<IStringLocalizer<TeyaUIViewModelsStrings>> _mockLocalizer;
        private Mock<ITokenService> _mockTokenService;
        private Mock<ILogger<ObHistoryService>> _mockLogger;
        private HttpClient _httpClient;
        private ObHistoryService _obHistoryService;

        private const string TestEncounterNotesUrl = "https://test-encounternotes.com";
        private const string TestAccessToken = "test-access-token";

        [SetUp]
        public void SetUp()
        {
            _mockHttpMessageHandler = new Mock<HttpMessageHandler>();
            _mockConfiguration = new Mock<IConfiguration>();
            _mockLocalizer = new Mock<IStringLocalizer<TeyaUIViewModelsStrings>>();
            _mockTokenService = new Mock<ITokenService>();
            _mockLogger = new Mock<ILogger<ObHistoryService>>();

            _httpClient = new HttpClient(_mockHttpMessageHandler.Object);

            // Set up environment variable
            Environment.SetEnvironmentVariable("EncounterNotesURL", TestEncounterNotesUrl);

            // Set up mock responses
            _mockTokenService.Setup(t => t.AccessToken).Returns(TestAccessToken);

            _obHistoryService = new ObHistoryService(
                _httpClient,
                _mockConfiguration.Object,
                _mockLocalizer.Object,
                _mockTokenService.Object,
                _mockLogger.Object);
        }

        [TearDown]
        public void TearDown()
        {
            _httpClient?.Dispose();
            Environment.SetEnvironmentVariable("EncounterNotesURL", null);
        }

        private ObHistoryDTO CreateTestObHistory()
        {
            return new ObHistoryDTO
            {
                obId = Guid.NewGuid(),
                PatientId = Guid.NewGuid(),
                OrganizationId = Guid.NewGuid(),
                PcpId = Guid.NewGuid(),
                Symptoms = "Nausea and vomiting",
                Notes = "Patient reports morning sickness symptoms",
                DateOfComplaint = DateTime.UtcNow.AddDays(-7),
                Subscription = false,
                IsDeleted = false
            };
        }

        [Test]
        public async Task GetAllObHistoriesAsync_WhenSuccessful_ReturnsObHistoryList()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = false;
            var expectedObHistories = new List<ObHistoryDTO>
            {
                CreateTestObHistory(),
                CreateTestObHistory()
            };

            var responseContent = JsonSerializer.Serialize(expectedObHistories);
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(responseContent, Encoding.UTF8, "application/json")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _obHistoryService.GetAllObHistoriesAsync(orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(2));
            Assert.That(result[0].Symptoms, Is.EqualTo("Nausea and vomiting"));
        }

        [Test]
        public async Task GetAllObHistoriesAsync_WhenHttpRequestFails_ThrowsException()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = false;
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.InternalServerError,
                Content = new StringContent("Internal Server Error")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<Exception>(async () =>
                await _obHistoryService.GetAllObHistoriesAsync(orgId, subscription));

            Assert.That(exception.Message, Does.Contain("Error retrieving OB histories"));
        }

        [Test]
        public async Task AddAsync_WhenSuccessful_CompletesWithoutException()
        {
            // Arrange
            var obHistory = CreateTestObHistory();
            var orgId = Guid.NewGuid();
            var subscription = false;

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.Created
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            // No exception should be thrown
            await _obHistoryService.AddAsync(obHistory, orgId, subscription);
        }

        [Test]
        public async Task AddAsync_WhenHttpRequestFails_ThrowsException()
        {
            // Arrange
            var obHistory = CreateTestObHistory();
            var orgId = Guid.NewGuid();
            var subscription = false;
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad Request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<Exception>(async () =>
                await _obHistoryService.AddAsync(obHistory, orgId, subscription));

            Assert.That(exception.Message, Does.Contain("Error adding OB history"));
        }

        [Test]
        public async Task UpdateObHistoryAsync_WhenSuccessful_CompletesWithoutException()
        {
            // Arrange
            var obHistoryId = Guid.NewGuid();
            var obHistory = CreateTestObHistory();
            var orgId = Guid.NewGuid();
            var subscription = false;

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            // No exception should be thrown
            await _obHistoryService.UpdateObHistoryAsync(obHistoryId, obHistory, orgId, subscription);
        }

        [Test]
        public async Task DeleteObHistoryAsync_WhenSuccessful_CompletesWithoutException()
        {
            // Arrange
            var obHistoryId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = false;

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.NoContent
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            // No exception should be thrown
            await _obHistoryService.DeleteObHistoryAsync(obHistoryId, orgId, subscription);
        }

        [Test]
        public async Task GetByPatientIdAsync_WhenSuccessful_ReturnsObHistoryList()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = false;
            var expectedObHistories = new List<ObHistoryDTO> { CreateTestObHistory() };

            var responseContent = JsonSerializer.Serialize(expectedObHistories);
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(responseContent, Encoding.UTF8, "application/json")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _obHistoryService.GetByPatientIdAsync(patientId, orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count(), Is.EqualTo(1));
        }

        [Test]
        public async Task UpdateObHistoryListAsync_WhenSuccessful_CompletesWithoutException()
        {
            // Arrange
            var obHistories = new List<ObHistoryDTO>
            {
                CreateTestObHistory(),
                CreateTestObHistory()
            };
            var orgId = Guid.NewGuid();
            var subscription = false;

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            // No exception should be thrown
            await _obHistoryService.UpdateObHistoryListAsync(obHistories, orgId, subscription);
        }

        [Test]
        public async Task UpdateObHistoryListAsync_WhenEmptyList_ThrowsArgumentException()
        {
            // Arrange
            var emptyObHistories = new List<ObHistoryDTO>();
            var orgId = Guid.NewGuid();
            var subscription = false;

            // Act & Assert
            var exception = Assert.ThrowsAsync<Exception>(async () =>
                await _obHistoryService.UpdateObHistoryListAsync(emptyObHistories, orgId, subscription));

            Assert.That(exception.Message, Does.Contain("OB history list is empty"));
        }

        [Test]
        public async Task LoadObHistoriesAsync_WhenSuccessful_ReturnsTransformedObHistoryList()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = false;
            var sourceObHistories = new List<ObHistoryDTO> { CreateTestObHistory() };

            var responseContent = JsonSerializer.Serialize(sourceObHistories);
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(responseContent, Encoding.UTF8, "application/json")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _obHistoryService.LoadObHistoriesAsync(patientId, orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(1));
            Assert.That(result[0].Symptoms, Is.EqualTo("Nausea and vomiting"));
        }
    }
}



