﻿@inherits LayoutComponentBase
@inject INavigationService NavigationService
@inject IJSRuntime JSRuntime
@inject GraphApiService GraphService
@using MudBlazor

<MudThemeProvider />
<MudPopoverProvider />
<MudDialogProvider />
<MudSnackbarProvider />

@if (isLoading)
{
    <div class="custom-loader-container">
        <div class="custom-loader-logo-wrapper">
            <img src="_content/TeyaMobile.Shared/images/TeyaHealth.png" alt="TeyaHealth Logo" class="custom-loader-logo" />
        </div>
        <div class="custom-loader-message">
            <MudText Typo="Typo.h5" Class="custom-loader-text">
                Getting things ready for you...<br />
                <span class="custom-loader-subtext">Almost there! Organizing your workspace. Just a moment...</span>
            </MudText>
        </div>
    </div>
}
else
{
    @if (IsMobile)
    {
        <div class="mobile-layout">
            <MudAppBar Elevation="2" Color="Color.Primary" Fixed="true" Class="mobile-top-bar">
                @if (IsAuthenticated)
                {
                    <MudIconButton Icon="@Icons.Material.Filled.Menu"
                                   Color="Color.Inherit"
                                   Edge="Edge.Start"
                                   OnClick="@((e) => DrawerToggle())" />
                }
                <div style="display: flex; align-items: center; gap: 8px; margin-left: 8px;">
                    <img src="_content/TeyaMobile.Shared/images/TeyaHealth.png" alt="Logo" style="height: 32px; width: 32px;" />
                    <MudText Typo="Typo.h6" Style="font-weight: 600;">
                        TeyaHealth
                    </MudText>
                </div>
                <MudSpacer />
                @if (IsAuthenticated)
                {
                    <MudMenu Icon="@Icons.Material.Filled.AccountCircle"
                             Color="Color.Inherit"
                             AnchorOrigin="Origin.BottomRight"
                             TransformOrigin="Origin.TopRight"
                             Size="Size.Large">
                        <div style="min-width: 280px;">
                            <div style="padding: 20px; background: linear-gradient(135deg, var(--mud-palette-primary), var(--mud-palette-primary-darken)); color: white;">
                                <div style="display: flex; align-items: center; gap: 12px;">
                                    <MudAvatar Color="Color.Secondary" Size="Size.Large">
                                        @if (!string.IsNullOrEmpty(currentUserContext?.UserName))
                                        {
                                            @currentUserContext.UserName.Substring(0, 1).ToUpper()
                                        }
                                        else
                                        {
                                            <text>U</text>
                                        }
                                    </MudAvatar>
                                    <div>
                                        <MudText Typo="Typo.subtitle1" Style="font-weight: 600; margin-bottom: 4px;">
                                            @(currentUserContext?.UserName ?? "User")
                                        </MudText>
                                        <MudText Typo="Typo.caption" Style="opacity: 0.9;">
                                            @(currentUserContext?.Email ?? "")
                                        </MudText>
                                        <div style="display: flex; align-items: center; gap: 4px; margin-top: 4px;">
                                            <div style="width: 8px; height: 8px; background: #10b981; border-radius: 50%; animation: pulse 2s infinite;"></div>
                                            <MudText Typo="Typo.caption" Style="opacity: 0.8;">Online</MudText>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div style="padding: 8px;">
                                <MudMenuItem OnClick="ViewProfile" Icon="@Icons.Material.Filled.Person">
                                    <div style="display: flex; align-items: center; gap: 12px; padding: 4px 0;">
                                        <span>View Profile</span>
                                    </div>
                                </MudMenuItem>
                                <MudMenuItem OnClick="AccountSettings" Icon="@Icons.Material.Filled.Settings">
                                    <div style="display: flex; align-items: center; gap: 12px; padding: 4px 0;">
                                        <span>Account Settings</span>
                                    </div>
                                </MudMenuItem>
                                <MudDivider Style="margin: 8px 0;" />
                                <MudMenuItem OnClick="HandleLogout" Icon="@Icons.Material.Filled.Logout" Style="color: var(--mud-palette-error);">
                                    <div style="display: flex; align-items: center; gap: 12px; padding: 4px 0;">
                                        <span>Sign Out</span>
                                    </div>
                                </MudMenuItem>
                            </div>
                        </div>
                    </MudMenu>
                }
                else
                {
                    <MudButton Href="/login"
                               Variant="Variant.Outlined"
                               Color="Color.Inherit"
                               StartIcon="@Icons.Material.Filled.Login"
                               Style="border-color: rgba(255,255,255,0.5); text-transform: none; font-weight: 500;">
                        Sign In
                    </MudButton>
                }
            </MudAppBar>
            <div class="mobile-main-content">
                <ErrorBoundary>
                    <ChildContent>
                        @Body
                    </ChildContent>
                    <ErrorContent Context="exception">
                        <div class="error-container">
                            <MudAlert Severity="Severity.Error" Class="error-alert">
                                <MudText Typo="Typo.h6" Class="error-title">Something went wrong</MudText>
                                <MudText Typo="Typo.body2" Class="error-message">
                                    An unexpected error occurred. Please try refreshing the page.
                                </MudText>
                                <div class="error-actions">
                                    <MudButton OnClick="@(() => Navigation.NavigateTo(Navigation.Uri))"
                                               Variant="Variant.Filled"
                                               Color="Color.Primary"
                                               StartIcon="@Icons.Material.Filled.Refresh">
                                        Refresh Page
                                    </MudButton>
                                    <MudButton OnClick="@(() => Navigation.NavigateTo("/"))"
                                               Variant="Variant.Text"
                                               Color="Color.Primary">
                                        Go Home
                                    </MudButton>
                                </div>
                            </MudAlert>
                        </div>
                    </ErrorContent>
                </ErrorBoundary>
            </div>
            @if (IsAuthenticated)
            {
                <TabBar />
            }
            @if (IsAuthenticated)
            {
                <MudDrawer @bind-Open="@_drawerOpen"
                           Elevation="2"
                           ClipMode="DrawerClipMode.Always"
                           Anchor="Anchor.Start"
                           Style="top: 64px; height: calc(100vh - 64px);">
                    <MudDrawerHeader Style="padding: 20px;">
                        <div style="display: flex; align-items: center; gap: 12px;">
                            <MudIcon Icon="@Icons.Material.Filled.Layers"
                                     Color="Color.Primary"
                                     Size="Size.Large" />
                            <div>
                                <MudText Typo="Typo.h6" Style="font-weight: 700; margin-bottom: 2px;">
                                    TeyaHealth
                                </MudText>
                                <MudText Typo="Typo.caption" Color="Color.Secondary">
                                    Healthcare Assistant
                                </MudText>
                            </div>
                        </div>
                    </MudDrawerHeader>
                    <MudDrawerContainer>
                        <Nav OnNavigationClick="@(() => _drawerOpen = false)" />
                    </MudDrawerContainer>
                </MudDrawer>
            }
        </div>
    }
    else if (IsTablet)
    {
        <MudLayout>
            @if (IsAuthenticated)
            {
                <MudDrawer @bind-Open="@_drawerOpen"
                           Elevation="2"
                           ClipMode="DrawerClipMode.Always"
                           Anchor="Anchor.Start"
                           Class="web-drawer tablet-drawer"
                           Style="width: 320px;">
                    <MudDrawerHeader Style="padding: 32px;">
                        <div style="display: flex; align-items: center; gap: 20px;">
                            <MudIcon Icon="@Icons.Material.Filled.Layers"
                                     Color="Color.Primary"
                                     Size="Size.Large"
                                     Style="font-size: 2rem;" />
                            <div>
                                <MudText Typo="Typo.h5" Style="font-weight: 700; margin-bottom: 2px; font-size: 1.3rem;">
                                    TeyaHealth
                                </MudText>
                                <MudText Typo="Typo.caption" Color="Color.Secondary" Style="font-size: 1.1rem;">
                                    Healthcare Assistant
                                </MudText>
                            </div>
                        </div>
                    </MudDrawerHeader>
                    <MudDrawerContainer>
                        <Nav />
                    </MudDrawerContainer>
                </MudDrawer>
            }
             <MudMainContent Class="web-main-content tablet-main-content">
                <ErrorBoundary>
                    <ChildContent>
                        @Body
                    </ChildContent>
                    <ErrorContent Context="exception">
                        <div class="error-container">
                            <MudAlert Severity="Severity.Error" Class="error-alert">
                                <MudText Typo="Typo.h6" Class="error-title">Something went wrong</MudText>
                                <MudText Typo="Typo.body2" Class="error-message">
                                    An unexpected error occurred. Please try refreshing the page.
                                </MudText>
                                <div class="error-actions">
                                    <MudButton OnClick="@(() => Navigation.NavigateTo(Navigation.Uri))"
                                               Variant="Variant.Filled"
                                               Color="Color.Primary"
                                               StartIcon="@Icons.Material.Filled.Refresh">
                                        Refresh Page
                                    </MudButton>
                                    <MudButton OnClick="@(() => Navigation.NavigateTo("/"))"
                                               Variant="Variant.Text"
                                               Color="Color.Primary">
                                        Go Home
                                    </MudButton>
                                </div>
                            </MudAlert>
                        </div>
                    </ErrorContent>
                </ErrorBoundary>
            </MudMainContent> 
        </MudLayout>
    }
    else
    {
        <MudLayout>
            <MudAppBar Elevation="2" Color="Color.Primary" Fixed="true" Class="web-app-bar">
                @if (IsAuthenticated)
                {
                    <MudIconButton Icon="@Icons.Material.Filled.Menu"
                                   Color="Color.Inherit"
                                   Edge="Edge.Start"
                                   OnClick="@((e) => DrawerToggle())" />
                }
                <div style="display: flex; align-items: center; gap: 12px; margin-left: 12px;">
                    <img src="_content/TeyaMobile.Shared/images/TeyaHealth.png" alt="Logo" style="height: 40px; width: 40px;" />
                    <MudText Typo="Typo.h6" Style="font-weight: 600;">
                        TeyaHealth
                    </MudText>
                </div>
                <MudSpacer />
                @if (IsAuthenticated)
                {
                    <MudMenu Icon="@Icons.Material.Filled.AccountCircle"
                             Color="Color.Inherit"
                             AnchorOrigin="Origin.BottomRight"
                             TransformOrigin="Origin.TopRight"
                             Size="Size.Large">
                        <div style="min-width: 280px;">
                            <div style="padding: 20px; background: linear-gradient(135deg, var(--mud-palette-primary), var(--mud-palette-primary-darken)); color: white;">
                                <div style="display: flex; align-items: center; gap: 12px;">
                                    <MudAvatar Color="Color.Secondary" Size="Size.Large">
                                        @if (!string.IsNullOrEmpty(currentUserContext?.UserName))
                                        {
                                            @currentUserContext.UserName.Substring(0, 1).ToUpper()
                                        }
                                        else
                                        {
                                            <text>U</text>
                                        }
                                    </MudAvatar>
                                    <div>
                                        <MudText Typo="Typo.subtitle1" Style="font-weight: 600; margin-bottom: 4px;">
                                            @(currentUserContext?.UserName ?? "User")
                                        </MudText>
                                        <MudText Typo="Typo.caption" Style="opacity: 0.9;">
                                            @(currentUserContext?.Email ?? "")
                                        </MudText>
                                        <div style="display: flex; align-items: center; gap: 4px; margin-top: 4px;">
                                            <div style="width: 8px; height: 8px; background: #10b981; border-radius: 50%; animation: pulse 2s infinite;"></div>
                                            <MudText Typo="Typo.caption" Style="opacity: 0.8;">Online</MudText>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div style="padding: 8px;">
                                <MudMenuItem OnClick="ViewProfile" Icon="@Icons.Material.Filled.Person">
                                    <div style="display: flex; align-items: center; gap: 12px; padding: 4px 0;">
                                        <span>View Profile</span>
                                    </div>
                                </MudMenuItem>
                                <MudMenuItem OnClick="AccountSettings" Icon="@Icons.Material.Filled.Settings">
                                    <div style="display: flex; align-items: center; gap: 12px; padding: 4px 0;">
                                        <span>Account Settings</span>
                                    </div>
                                </MudMenuItem>
                                <MudDivider Style="margin: 8px 0;" />
                                <MudMenuItem OnClick="HandleLogout" Icon="@Icons.Material.Filled.Logout" Style="color: var(--mud-palette-error);">
                                    <div style="display: flex; align-items: center; gap: 12px; padding: 4px 0;">
                                        <span>Sign Out</span>
                                    </div>
                                </MudMenuItem>
                            </div>
                        </div>
                    </MudMenu>
                }
                else
                {
                    <MudButton Href="/login"
                               Variant="Variant.Outlined"
                               Color="Color.Inherit"
                               StartIcon="@Icons.Material.Filled.Login"
                               Style="border-color: rgba(255,255,255,0.5); text-transform: none; font-weight: 500;">
                        Sign In
                    </MudButton>
                }
            </MudAppBar>
            @if (IsAuthenticated)
            {
                <MudDrawer @bind-Open="@_drawerOpen"
                           Elevation="2"
                           ClipMode="DrawerClipMode.Always"
                           Anchor="Anchor.Start"
                           Class="web-drawer">
                    <MudDrawerHeader Style="padding: 20px;">
                        <div style="display: flex; align-items: center; gap: 12px;">
                            <MudIcon Icon="@Icons.Material.Filled.Layers"
                                     Color="Color.Primary"
                                     Size="Size.Large" />
                            <div>
                                <MudText Typo="Typo.h6" Style="font-weight: 700; margin-bottom: 2px;">
                                    TeyaHealth
                                </MudText>
                                <MudText Typo="Typo.caption" Color="Color.Secondary">
                                    Healthcare Assistant
                                </MudText>
                            </div>
                        </div>
                    </MudDrawerHeader>
                    <MudDrawerContainer>
                        <Nav />
                    </MudDrawerContainer>
                </MudDrawer>
            }
            <MudMainContent Class="web-main-content">
                <ErrorBoundary>
                    <ChildContent>
                        @Body
                    </ChildContent>
                    <ErrorContent Context="exception">
                        <div class="error-container">
                            <MudAlert Severity="Severity.Error" Class="error-alert">
                                <MudText Typo="Typo.h6" Class="error-title">Something went wrong</MudText>
                                <MudText Typo="Typo.body2" Class="error-message">
                                    An unexpected error occurred. Please try refreshing the page.
                                </MudText>
                                <div class="error-actions">
                                    <MudButton OnClick="@(() => Navigation.NavigateTo(Navigation.Uri))"
                                               Variant="Variant.Filled"
                                               Color="Color.Primary"
                                               StartIcon="@Icons.Material.Filled.Refresh">
                                        Refresh Page
                                    </MudButton>
                                    <MudButton OnClick="@(() => Navigation.NavigateTo("/"))"
                                               Variant="Variant.Text"
                                               Color="Color.Primary">
                                        Go Home
                                    </MudButton>
                                </div>
                            </MudAlert>
                        </div>
                    </ErrorContent>
                </ErrorBoundary>
            </MudMainContent>
        </MudLayout>
    }
}

<MudDialog @bind-IsVisible="showProfileDialog" Options="profileDialogOptions">
    <DialogContent>
        <div style="text-align: center; padding: 24px;">
            <MudAvatar Color="Color.Primary" Size="Size.Large" Style="margin: 0 auto 16px;">
                @if (!string.IsNullOrEmpty(currentUserContext?.UserName))
                {
                    @currentUserContext.UserName.Substring(0, 1).ToUpper()
                }
                else
                {
                    <text>U</text>
                }
            </MudAvatar>
            <MudText Typo="Typo.h5" Style="margin-bottom: 8px; font-weight: 600;">
                @(currentUserContext?.UserName ?? "User")
            </MudText>
            <MudText Typo="Typo.body1" Color="Color.Secondary" Style="margin-bottom: 8px;">
                @(currentUserContext?.Email ?? "")
            </MudText>
            <div style="display: flex; align-items: center; justify-content: center; gap: 6px; margin-bottom: 24px;">
                <div style="width: 10px; height: 10px; background: #10b981; border-radius: 50%; animation: pulse 2s infinite;"></div>
                <MudText Typo="Typo.body2" Color="Color.Secondary">Online</MudText>
            </div>
            <div style="display: flex; flex-direction: column; gap: 12px; max-width: 300px; margin: 0 auto;">
                <MudButton Variant="Variant.Outlined"
                           StartIcon="@Icons.Material.Filled.Person"
                           OnClick="ViewProfile"
                           FullWidth="true"
                           Style="text-transform: none;">
                    View Profile
                </MudButton>
                <MudButton Variant="Variant.Outlined"
                           StartIcon="@Icons.Material.Filled.Settings"
                           OnClick="AccountSettings"
                           FullWidth="true"
                           Style="text-transform: none;">
                    Account Settings
                </MudButton>
                <MudDivider Style="margin: 8px 0;" />
                <MudButton Variant="Variant.Filled"
                           Color="Color.Error"
                           StartIcon="@Icons.Material.Filled.Logout"
                           OnClick="HandleLogout"
                           FullWidth="true"
                           Style="text-transform: none;">
                    Sign Out
                </MudButton>
            </div>
        </div>
    </DialogContent>
</MudDialog>

<style>
    .mobile-layout {
        display: flex;
        flex-direction: column;
        height: 100vh;
        overflow: hidden;
    }

    .mobile-top-bar {
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        right: 0 !important;
        z-index: 1300 !important;
        height: 64px !important;
    }

    .mobile-main-content {
        flex: 1;
        overflow-y: auto;
        overflow-x: hidden;
        margin-top: 64px;
        margin-bottom: 80px;
        padding: 16px;
        -webkit-overflow-scrolling: touch;
    }

    .web-app-bar {
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        right: 0 !important;
        z-index: 1300 !important;
        height: 64px !important;
    }

    .web-drawer {
        margin-top: 64px !important;
        height: calc(100vh - 64px) !important;
    }

    .web-main-content {
        margin-top: 64px !important;
        padding: 24px !important;
        min-height: calc(100vh - 64px) !important;
    }

    .error-container {
        padding: 20px;
        margin: 20px 0;
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 200px;
    }

    .error-alert {
        max-width: 500px;
        width: 100%;
    }

    .error-title {
        margin-bottom: 8px !important;
        font-weight: 600 !important;
    }

    .error-message {
        margin-bottom: 16px !important;
        color: var(--mud-palette-text-secondary) !important;
    }

    .error-actions {
        display: flex;
        gap: 12px;
        flex-wrap: wrap;
        justify-content: center;
        margin-top: 16px;
    }

    @@keyframes pulse {
        0%, 100%

    {
        opacity: 1;
    }

    50% {
        opacity: 0.7;
    }

    }

    .mud-menu .mud-list-item {
        border-radius: 8px;
        margin: 2px 4px;
    }

        .mud-menu .mud-list-item:hover {
            background-color: var(--mud-palette-action-hover);
        }

    @@media (max-width: 600px) {
        .mobile-main-content

    {
        margin-bottom: 70px;
    }

    .error-actions {
        flex-direction: column;
    }

    }

    @@media (min-width: 960px) {
        .web-main-content

    {
        padding: 32px !important;
    }

    }
    @@media (min-width: 1280px) {
        .web-main-content

    {
        padding: 40px !important;
    }

    }

    /* Tablet/iPad Responsive Enhancements */
    @@media (min-width: 768px) and (max-width: 1200px) {
        .web-main-content {
            padding: 48px !important;
            min-height: calc(100vh - 64px) !important;
        }
        .web-app-bar {
            height: 72px !important;
        }
        .web-drawer {
            margin-top: 72px !important;
            height: calc(100vh - 72px) !important;
        }
        .mobile-main-content {
            padding: 32px !important;
            margin-top: 72px;
            margin-bottom: 100px;
        }
        .mobile-top-bar {
            height: 72px !important;
        }
    }
    @@media (min-width: 1200px) {
        .web-main-content {
            padding: 64px !important;
            min-height: calc(100vh - 64px) !important;
        }
        .web-app-bar {
            height: 80px !important;
        }
        .web-drawer {
            margin-top: 80px !important;
            height: calc(100vh - 80px) !important;
        }
        .mobile-main-content {
            padding: 48px !important;
            margin-top: 80px;
            margin-bottom: 120px;
        }
        .mobile-top-bar {
            height: 80px !important;
        }
    }

    .custom-loader-container {
        position: fixed;
        top: 0; left: 0; right: 0; bottom: 0;
        width: 100vw;
        height: 100vh;
        background: linear-gradient(135deg, #f8fafc 0%, #e0e7ef 100%);
        display: flex;
        flex-direction: column;
        justify-content: flex-end;
        align-items: center;
        z-index: 9999;
        overflow: hidden;
    }
    .custom-loader-logo-wrapper {
        flex: 1 1 auto;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100vw;
        margin-top: 10vh;
    }
    .custom-loader-logo {
        width: 35px;
        height: 35px;
        object-fit: contain;
        animation: teya-float 1.8s cubic-bezier(.68,-0.55,.27,1.55) infinite;
        filter: drop-shadow(0 8px 24px rgba(16,185,129,0.18));
    }
    @@keyframes teya-float {
        0%, 100% { transform: translateY(0); }
        20% { transform: translateY(-18px) scale(1.08); }
        40% { transform: translateY(-28px) scale(1.12); }
        60% { transform: translateY(-18px) scale(1.08); }
        80% { transform: translateY(0) scale(1); }
    }
    .custom-loader-message {
        width: 90vw;
        padding-bottom: 7vh;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: flex-end;
    }
    .custom-loader-text {
        color: #0e1726;
        font-size: 1.45rem;
        font-weight: 700;
        text-align: center;
        letter-spacing: 0.01em;
        text-shadow: 0 2px 8px rgba(16,185,129,0.08);
        margin-bottom: 0.5rem;
    }
    .custom-loader-subtext {
        display: block;
        font-size: 1.05rem;
        font-weight: 400;
        color: #10b981;
        margin-top: 0.5rem;
        letter-spacing: 0.01em;
        text-shadow: 0 1px 4px rgba(16,185,129,0.08);
    }
    @@media (max-width: 600px) {
        .custom-loader-logo { width: 35px; height: 35px; }
        .custom-loader-text { font-size: 1.1rem; }
        .custom-loader-message { padding-bottom: 4vh; }
    }
</style>

@code{
    private bool _drawerOpen = false;
    private bool IsAuthenticated = false;
    private bool showProfileDialog = false;
    private bool isLoading = true;
    [Inject] IFormFactor FormFactor { get; set; }
    private bool IsMobile => FormFactor.GetFormFactor() != "Web";
    private UserContext? currentUserContext;
    private bool _disposed = false;
    [Inject] StorageContainer storageContainer { get; set; }
    [Inject] private NavigationManager Navigation { get; set; }
    [Inject] private IAuthenticationService AuthService { get; set; }
    [Inject] ActiveUser user { get; set; }
    [Inject] private ISnackbar Snackbar { get; set; }
    [Inject] private IOrganizationService OrganizationService { get; set; } 

    private DialogOptions profileDialogOptions = new()
    {
        MaxWidth = MaxWidth.Small,
        FullWidth = true,
        CloseButton = true,
        CloseOnEscapeKey = true
    };

    private bool IsTablet => FormFactor.GetFormFactor() == "Tablet";

    protected override async Task OnInitializedAsync()
    {
        try
        {
            NavigationService.Initialize(Navigation);
            await CheckAuthenticationState();
            Navigation.LocationChanged += OnLocationChanged;

            // Loader and organization id population logic
            if (IsAuthenticated && (!storageContainer.OrganizationId.HasValue || storageContainer.OrganizationId == Guid.Empty))
            {
                isLoading = true;
                var cts = new CancellationTokenSource();
                cts.CancelAfter(TimeSpan.FromSeconds(30));
                bool orgIdSet = false;
                try
                {
                    await GraphService.GetLoggedInUserDetailsAsync();
                    if (!string.IsNullOrWhiteSpace(user.OrganizationName))
                    {
                        var orgIdTask = OrganizationService.GetOrganizationIdByNameAsync(user.OrganizationName);
                        var completedTask = await Task.WhenAny(orgIdTask, Task.Delay(Timeout.Infinite, cts.Token));
                        if (completedTask == orgIdTask)
                        {
                            var orgId = await orgIdTask;
                            storageContainer.OrganizationId = orgId;
                            orgIdSet = true;
                        }
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error getting organization id: {ex.Message}");
                }
                finally
                {
                    isLoading = false;
                    if (!orgIdSet)
                    {
                        Snackbar.Add("Organization not found at the moment.", Severity.Warning);
                    }
                }
            }
            else
            {
                isLoading = false;
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error in OnInitializedAsync: {ex.Message}");
            isLoading = false;
        }
    }

    private void OnUserContextChanged(UserContext userContext)
    {
        if (!_disposed)
        {
            currentUserContext = userContext;
            InvokeAsync(StateHasChanged);
        }
    }

    private async void OnLocationChanged(object sender, LocationChangedEventArgs e)
    {
        if (!_disposed)
        {
            await CheckAuthenticationState();
            await InvokeAsync(StateHasChanged);
        }
    }

    private async Task CheckAuthenticationState()
    {
        try
        {
            IsAuthenticated = AuthService.IsAuthenticated;
            _drawerOpen = false;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Auth state check error: {ex.Message}");
            IsAuthenticated = false;
            _drawerOpen = false;
        }
    }

    private void DrawerToggle()
    {
        if (IsAuthenticated)
        {
            _drawerOpen = !_drawerOpen;
        }
    }

    private void ViewProfile()
    {
        showProfileDialog = false;
        Navigation.NavigateTo("/profile");
    }

    private void AccountSettings()
    {
        showProfileDialog = false;
        Navigation.NavigateTo("/settings");
    }

    private async Task HandleLogout()
    {
        try
        {
            showProfileDialog = false;
            await AuthService.LogoutAsync();
            Navigation.NavigateTo("/");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Logout error: {ex.Message}");
            Snackbar.Add("Error during logout. Please try again.", Severity.Error);
        }
    }

    public void Dispose()
    {
        if (!_disposed)
        {
            _disposed = true;
            Navigation.LocationChanged -= OnLocationChanged;
        }
    }
}