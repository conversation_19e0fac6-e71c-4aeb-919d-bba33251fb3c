using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using Moq;
using Moq.Protected;
using NUnit.Framework;
using TeyaUIModels.Model;
using TeyaUIViewModels.ViewModel;
using System.Net.Http.Json;
using System.Text.Json;

namespace TeyaWebClientUIViewModelTestCases
{
    [TestFixture]
    public class RelationServiceTests
    {
        private Mock<HttpMessageHandler> _mockHttpMessageHandler;
        private HttpClient _httpClient;
        private Mock<IConfiguration> _mockConfiguration;
        private Mock<IStringLocalizer<RelationService>> _mockLocalizer;
        private Mock<ITokenService> _mockTokenService;
        private RelationService _relationService;
        private readonly string _baseUrl = "http://test-api.com";
        private readonly string _accessToken = "test-access-token";

        [SetUp]
        public void Setup()
        {
            // Setup environment variable
            Environment.SetEnvironmentVariable("EncounterNotesURL", _baseUrl);

            // Setup mock HTTP handler
            _mockHttpMessageHandler = new Mock<HttpMessageHandler>();

            // Setup HttpClient with mock handler
            _httpClient = new HttpClient(_mockHttpMessageHandler.Object)
            {
                BaseAddress = new Uri(_baseUrl)
            };

            // Setup mock configuration
            _mockConfiguration = new Mock<IConfiguration>();

            // Setup mock localizer
            _mockLocalizer = new Mock<IStringLocalizer<RelationService>>();
            _mockLocalizer.Setup(l => l["RelationRetrievalFailure"])
                .Returns(new LocalizedString("RelationRetrievalFailure", "Relation retrieval failure"));

            // Setup mock token service
            _mockTokenService = new Mock<ITokenService>();
            _mockTokenService.Setup(t => t.AccessToken).Returns(_accessToken);

            // Create RelationService with mocked dependencies
            _relationService = new RelationService(_httpClient, _mockConfiguration.Object, _mockLocalizer.Object, _mockTokenService.Object);
        }

        [TearDown]
        public void TearDown()
        {
            _httpClient.Dispose();
            Environment.SetEnvironmentVariable("EncounterNotesURL", null);
        }

        [Test]
        public async Task GetRelationsAsync_WhenSuccessful_ReturnsRelations()
        {
            // Arrange
            var expectedRelations = new List<Relations>
            {
                new Relations
                {
                    RecordID = Guid.NewGuid(),
                    Relation = "Father"
                },
                new Relations
                {
                    RecordID = Guid.NewGuid(),
                    Relation = "Mother"
                },
                new Relations
                {
                    RecordID = Guid.NewGuid(),
                    Relation = "Spouse"
                },
                new Relations
                {
                    RecordID = Guid.NewGuid(),
                    Relation = "Child"
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedRelations)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _relationService.GetRelationsAsync();

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(expectedRelations.Count));
            Assert.That(result[0].RecordID, Is.EqualTo(expectedRelations[0].RecordID));
            Assert.That(result[0].Relation, Is.EqualTo(expectedRelations[0].Relation));
            Assert.That(result[1].Relation, Is.EqualTo(expectedRelations[1].Relation));
            Assert.That(result[2].Relation, Is.EqualTo(expectedRelations[2].Relation));
            Assert.That(result[3].Relation, Is.EqualTo(expectedRelations[3].Relation));
        }

        [Test]
        public void GetRelationsAsync_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.NotFound,
                Content = new StringContent("Not found")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _relationService.GetRelationsAsync());

            Assert.That(exception.Message, Is.EqualTo("Relation retrieval failure"));
        }

        [Test]
        public async Task GetRelationsAsync_WhenEmptyResponse_ReturnsEmptyList()
        {
            // Arrange
            var expectedRelations = new List<Relations>();

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedRelations)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _relationService.GetRelationsAsync();

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(0));
        }

        [Test]
        public void GetRelationsAsync_WhenExceptionThrown_PropagatesException()
        {
            // Arrange
            var expectedException = new HttpRequestException("Test exception");

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ThrowsAsync(expectedException);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _relationService.GetRelationsAsync());

            Assert.That(exception, Is.Not.Null);
            Assert.That(exception.Message, Is.EqualTo("Test exception"));
        }

        [Test]
        public async Task GetRelationsAsync_WhenSingleRelation_ReturnsSingleItem()
        {
            // Arrange
            var expectedRelations = new List<Relations>
            {
                new Relations
                {
                    RecordID = Guid.NewGuid(),
                    Relation = "Guardian"
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedRelations)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _relationService.GetRelationsAsync();

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(1));
            Assert.That(result[0].Relation, Is.EqualTo("Guardian"));
        }

        [Test]
        public void GetRelationsAsync_WhenInternalServerError_ThrowsHttpRequestException()
        {
            // Arrange
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.InternalServerError,
                Content = new StringContent("Internal server error")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _relationService.GetRelationsAsync());

            Assert.That(exception.Message, Is.EqualTo("Relation retrieval failure"));
        }

        [Test]
        public void GetRelationsAsync_WhenBadRequest_ThrowsHttpRequestException()
        {
            // Arrange
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _relationService.GetRelationsAsync());

            Assert.That(exception.Message, Is.EqualTo("Relation retrieval failure"));
        }

        [Test]
        public async Task GetRelationsAsync_WhenComplexRelationData_ReturnsCorrectData()
        {
            // Arrange
            var expectedRelations = new List<Relations>
            {
                new Relations
                {
                    RecordID = Guid.NewGuid(),
                    Relation = "Step-Father"
                },
                new Relations
                {
                    RecordID = Guid.NewGuid(),
                    Relation = "Step-Mother"
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedRelations)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _relationService.GetRelationsAsync();

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(2));
            Assert.That(result[0].Relation, Is.EqualTo("Step-Father"));
            Assert.That(result[1].Relation, Is.EqualTo("Step-Mother"));
        }
    }
}



