using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Moq;
using Moq.Protected;
using NUnit.Framework;
using TeyaUIModels.Model;
using TeyaUIViewModels.ViewModel;
using System.Net.Http.Json;
using System.Text.Json;

namespace TeyaWebClientUIViewModelTestCases
{
    [TestFixture]
    public class RoleslistServiceTests
    {
        private Mock<HttpMessageHandler> _mockHttpMessageHandler;
        private HttpClient _httpClient;
        private Mock<IStringLocalizer<RoleslistService>> _mockLocalizer;
        private Mock<ILogger<RoleslistService>> _mockLogger;
        private Mock<ITokenService> _mockTokenService;
        private RoleslistService _roleslistService;
        private readonly string _baseUrl = "http://test-api.com";
        private readonly string _accessToken = "test-access-token";

        [SetUp]
        public void Setup()
        {
            // Setup environment variable
            Environment.SetEnvironmentVariable("MemberServiceURL", _baseUrl);

            // Setup mock HTTP handler
            _mockHttpMessageHandler = new Mock<HttpMessageHandler>();

            // Setup HttpClient with mock handler
            _httpClient = new HttpClient(_mockHttpMessageHandler.Object)
            {
                BaseAddress = new Uri(_baseUrl)
            };

            // Setup mock localizer
            _mockLocalizer = new Mock<IStringLocalizer<RoleslistService>>();
            _mockLocalizer.Setup(l => l["AccessTokenNotFound"])
                .Returns(new LocalizedString("AccessTokenNotFound", "Access token not found"));
            _mockLocalizer.Setup(l => l["AccessTokenMissing"])
                .Returns(new LocalizedString("AccessTokenMissing", "Access token missing"));
            _mockLocalizer.Setup(l => l["ErrorFetchingAllRoleslists"])
                .Returns(new LocalizedString("ErrorFetchingAllRoleslists", "Error fetching all roles lists"));
            _mockLocalizer.Setup(l => l["ErrorFetchingRoleNames"])
                .Returns(new LocalizedString("ErrorFetchingRoleNames", "Error fetching role names"));

            // Setup mock logger
            _mockLogger = new Mock<ILogger<RoleslistService>>();

            // Setup mock token service
            _mockTokenService = new Mock<ITokenService>();
            _mockTokenService.Setup(t => t.AccessToken).Returns(_accessToken);
            _mockTokenService.Setup(t => t.GetValidatedAccessTokenAsync()).ReturnsAsync(_accessToken);

            // Create RoleslistService with mocked dependencies
            _roleslistService = new RoleslistService(_httpClient, _mockLocalizer.Object, _mockLogger.Object, _mockTokenService.Object);
        }

        [TearDown]
        public void TearDown()
        {
            _httpClient.Dispose();
            Environment.SetEnvironmentVariable("MemberServiceURL", null);
        }

        [Test]
        public async Task GetAllRoleslistsAsync_WhenSuccessful_ReturnsRolesdata()
        {
            // Arrange
            var expectedRolesdata = new List<Rolesdata>
            {
                new Rolesdata
                {
                    ID = Guid.NewGuid(),
                    Rolename = "Administrator"
                },
                new Rolesdata
                {
                    ID = Guid.NewGuid(),
                    Rolename = "User"
                },
                new Rolesdata
                {
                    ID = Guid.NewGuid(),
                    Rolename = "Manager"
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(JsonSerializer.Serialize(expectedRolesdata))
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _roleslistService.GetAllRoleslistsAsync();

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(expectedRolesdata.Count));
            Assert.That(result[0].ID, Is.EqualTo(expectedRolesdata[0].ID));
            Assert.That(result[0].Rolename, Is.EqualTo(expectedRolesdata[0].Rolename));
            Assert.That(result[1].Rolename, Is.EqualTo(expectedRolesdata[1].Rolename));
            Assert.That(result[2].Rolename, Is.EqualTo(expectedRolesdata[2].Rolename));
        }

        [Test]
        public void GetAllRoleslistsAsync_WhenAccessTokenIsNull_ThrowsUnauthorizedAccessException()
        {
            // Arrange
            _mockTokenService.Setup(t => t.GetValidatedAccessTokenAsync()).ReturnsAsync((string)null);

            // Act & Assert
            var exception = Assert.ThrowsAsync<UnauthorizedAccessException>(async () =>
                await _roleslistService.GetAllRoleslistsAsync());

            Assert.That(exception.Message, Is.EqualTo("Access token missing"));

            // Verify that error was logged
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.IsAny<Exception>(),
                    It.Is<Func<It.IsAnyType, Exception, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }

        [Test]
        public void GetAllRoleslistsAsync_WhenAccessTokenIsEmpty_ThrowsUnauthorizedAccessException()
        {
            // Arrange
            _mockTokenService.Setup(t => t.GetValidatedAccessTokenAsync()).ReturnsAsync(string.Empty);

            // Act & Assert
            var exception = Assert.ThrowsAsync<UnauthorizedAccessException>(async () =>
                await _roleslistService.GetAllRoleslistsAsync());

            Assert.That(exception.Message, Is.EqualTo("Access token missing"));

            // Verify that error was logged
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.IsAny<Exception>(),
                    It.Is<Func<It.IsAnyType, Exception, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }

        [Test]
        public void GetAllRoleslistsAsync_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.NotFound,
                Content = new StringContent("Not found")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _roleslistService.GetAllRoleslistsAsync());

            Assert.That(exception, Is.Not.Null);

            // Verify that error was logged
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.IsAny<Exception>(),
                    It.Is<Func<It.IsAnyType, Exception, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }

        [Test]
        public async Task GetAllRoleslistsAsync_WhenEmptyResponse_ReturnsEmptyList()
        {
            // Arrange
            var expectedRolesdata = new List<Rolesdata>();

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(JsonSerializer.Serialize(expectedRolesdata))
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _roleslistService.GetAllRoleslistsAsync();

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(0));
        }

        [Test]
        public async Task GetAllRoleNamesAsync_WhenSuccessful_ReturnsRoleNames()
        {
            // Arrange
            var rolesdata = new List<Rolesdata>
            {
                new Rolesdata
                {
                    ID = Guid.NewGuid(),
                    Rolename = "Administrator"
                },
                new Rolesdata
                {
                    ID = Guid.NewGuid(),
                    Rolename = "User"
                },
                new Rolesdata
                {
                    ID = Guid.NewGuid(),
                    Rolename = "Manager"
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(JsonSerializer.Serialize(rolesdata))
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _roleslistService.GetAllRoleNamesAsync();

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(3));
            Assert.That(result[0], Is.EqualTo("Administrator"));
            Assert.That(result[1], Is.EqualTo("User"));
            Assert.That(result[2], Is.EqualTo("Manager"));
        }

        [Test]
        public async Task GetAllRoleNamesAsync_WhenEmptyRolesList_ReturnsEmptyList()
        {
            // Arrange
            var rolesdata = new List<Rolesdata>();

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(JsonSerializer.Serialize(rolesdata))
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _roleslistService.GetAllRoleNamesAsync();

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(0));
        }

        [Test]
        public void GetAllRoleNamesAsync_WhenGetAllRoleslistsAsyncFails_ThrowsException()
        {
            // Arrange
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.InternalServerError,
                Content = new StringContent("Internal server error")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _roleslistService.GetAllRoleNamesAsync());

            Assert.That(exception, Is.Not.Null);

            // Verify that error was logged
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.IsAny<Exception>(),
                    It.Is<Func<It.IsAnyType, Exception, string>>((v, t) => true)),
                Times.AtLeastOnce);
        }
    }
}



