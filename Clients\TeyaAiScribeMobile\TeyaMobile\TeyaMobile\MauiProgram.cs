﻿using CommunityToolkit.Maui;
using Syncfusion.Maui.Core.Hosting;
using CommunityToolkit.Maui.Core;
using Microsoft.Extensions.Configuration;
using TeyaHealthMobileModel.Model;
using TeyaHealthMobileViewModel.ViewModel;
using Microsoft.Identity.Client;
using Syncfusion.Blazor;
using System.Text.Json;
using System.Text.Json.Serialization;
using MudBlazor.Services;
using TeyaMobile.Services;
using Microsoft.Extensions.Logging;
using TeyaMobile.Shared.Services;
using Syncfusion.Licensing;
using MudBlazor;






#if ANDROID
using TeyaMobile.Platforms;
#elif IOS
using TeyaMobile.Platforms;
using WebKit;
using Microsoft.Maui.Handlers;
using Microsoft.Maui.Platform;
using CoreGraphics;
#endif

namespace TeyaMobile
{
    public static class MauiProgram
    {
        public static MauiApp CreateMauiApp()
        { 
            SyncfusionLicenseProvider.RegisterLicense("Ngo9BigBOggjHTQxAR8/V1JEaF5cXmRCeUxxWmFZfVtgcV9HaFZTQmYuP1ZhSXxWdkNgWX9bcXxXQGhaVUF9XEI=");
            //SyncfusionLicenseProvider.RegisterLicense("Mzg4OTIzM0AzMjM5MmUzMDJlMzAzYjMyMzkzYlJwa09TaVM0bUVZdnhmOGFWR0plYWd0TFFTQzFSNjRmV0JSTkpJTlBQMlk9;Mgo+DSMBMAY9C3t2XFhhQlJHfVldX2pWfFN0QHNYf1R0dV9EYUwgOX1dQl9mSXhTdEVgWH5beX1dQ2VXU00=;Mgo+DSMBPh8sVXJ9S0d+X1JPcEBAVHxLflFzVWJZdVpxfldHcC0sT3RfQFhjT35RdkZiW39ecHdXRWteWA==;Mzg4OTIzNkAzMjM5MmUzMDJlMzAzYjMyMzkzYlMvRHlQMUFCOVV6VU1BVE0rMjdIUjFYallkQXhoY1lVQmVVNVpHNUZXblU9;NRAiBiAaIQQuGjN/V09+XU9HdVREQmFAYVF2R2ZJfl56cFRMYl1BNQtUQF1hTH5VdkdjWn5ccHRURGdaWkZ/;ORg4AjUWIQA/Gnt2XFhhQlJHfVldX2pWfFN0QHNYf1R0dV9EYUwgOX1dQl9mSXhTdEVgWH1ecHVRRWJXU00=;Mzg4OTIzOUAzMjM5MmUzMDJlMzAzYjMyMzkzYlJwa09TaVM0bUVZdnhmOGFWR0plYWd0TFFTQzFSNjRmV0JSTkpJTlBQMlk9");
            var builder = MauiApp.CreateBuilder();

            builder
                .UseMauiApp<App>()
                .ConfigureSyncfusionCore()
                .UseMauiCommunityToolkit()
                .UseMauiCommunityToolkitMediaElement()
                .UseMauiCommunityToolkitCore()
                .ConfigureFonts(fonts =>
                {
                    fonts.AddFont("OpenSans-Regular.ttf", "OpenSans");
                    fonts.AddFont("OpenSans-Semibold.ttf", "OpenSansSemibold");
                    fonts.AddFont("FluentSystemIcons-Filled.ttf", "FluentIcons");
                    fonts.AddFont("MaterialIcons-Regular.ttf", "MaterialIcons");
                    fonts.AddFont("MauiMaterialAssets.ttf", "MauiMaterialAssets");
                })
                .UseMauiCommunityToolkit();

            builder.Logging.ClearProviders();
            builder.Logging.AddDebug();

            builder.Services.AddSyncfusionBlazor();
// Fix for CS0117 and CS0144 errors
// The issue arises because 'Palette' is not a concrete class and cannot be instantiated directly.
// Instead, use 'PaletteLight' or 'PaletteDark' which are concrete implementations of 'Palette'.

// Fix for CS0747: Invalid initializer member declarator
// The issue arises because the `Palette` property is being initialized incorrectly.
// Instead, use `PaletteLight` or `PaletteDark` which are concrete implementations of `Palette`.


        builder.Services.AddMudServices(config =>
        {
            config.SnackbarConfiguration = new SnackbarConfiguration()
            {
                PositionClass = Defaults.Classes.Position.BottomRight,
                PreventDuplicates = false,
                NewestOnTop = false,
                ShowCloseIcon = true,
                VisibleStateDuration = 5000,
                HideTransitionDuration = 500,
                ShowTransitionDuration = 500,
                SnackbarVariant = Variant.Filled
            };

            // Configure the theme using a new instance of MudTheme
            var theme = new MudTheme()
            {
                PaletteLight = new PaletteLight()
                {
                    Primary = "#512BD4",
                    Secondary = "#DFD8F7",
                    Background = "#ffffff",
                    Surface = "#f7f8fa",
                    AppbarBackground = "rgba(255,255,255,0.85)",
                    DrawerBackground = "rgba(255,255,255,0.92)",
                    DrawerText = "#242424",
                    DrawerIcon = "#512BD4",
                    TextPrimary = "#242424",
                    TextSecondary = "#6E6E6E",
                    ActionDefault = "#512BD4",
                    ActionDisabled = "#C8C8C8",
                    ActionDisabledBackground = "#E1E1E1",
                    Divider = "rgba(80,80,80,0.12)",
                    TableLines = "rgba(80,80,80,0.08)",
                    LinesDefault = "rgba(80,80,80,0.08)",
                    OverlayDark = "rgba(80,80,80,0.32)",
                    Success = "#10b981",
                    Warning = "#f59e42",
                    Error = "#e53935",
                    Info = "#2196f3"
                },
                LayoutProperties = new LayoutProperties()
                {
                    DefaultBorderRadius = "18px"
                },
                Typography = new Typography()
                {
                    Default = new Default() { FontFamily = new[] { "Open Sans", "Roboto", "Arial", "sans-serif" }, FontSize = "1.05rem" },
                    H1 = new H1() { FontFamily = new[] { "Open Sans", "Roboto", "Arial", "sans-serif" }, FontWeight = 700, FontSize = "2.8rem" },
                    H2 = new H2() { FontFamily = new[] { "Open Sans", "Roboto", "Arial", "sans-serif" }, FontWeight = 700, FontSize = "2.2rem" },
                    H3 = new H3() { FontFamily = new[] { "Open Sans", "Roboto", "Arial", "sans-serif" }, FontWeight = 700, FontSize = "1.8rem" },
                    H4 = new H4() { FontFamily = new[] { "Open Sans", "Roboto", "Arial", "sans-serif" }, FontWeight = 700, FontSize = "1.4rem" },
                    H5 = new H5() { FontFamily = new[] { "Open Sans", "Roboto", "Arial", "sans-serif" }, FontWeight = 700, FontSize = "1.2rem" },
                    H6 = new H6() { FontFamily = new[] { "Open Sans", "Roboto", "Arial", "sans-serif" }, FontWeight = 700, FontSize = "1.1rem" },
                    Button = new MudBlazor.Button()
                    {
                        FontFamily = new[] { "Open Sans", "Roboto", "Arial", "sans-serif" },
                        FontWeight = 600,
                        FontSize = "1.1rem"
                    },
                    Caption = new Caption() { FontFamily = new[] { "Open Sans", "Roboto", "Arial", "sans-serif" }, FontWeight = 400, FontSize = "0.95rem" },
                    Overline = new Overline() { FontFamily = new[] { "Open Sans", "Roboto", "Arial", "sans-serif" }, FontWeight = 400, FontSize = "0.85rem" }
                },
                Shadows = new MudBlazor.Shadow()
                {
                    Elevation = new[]
                    {
                                    "none",
                                    "0px 2px 8px 0px rgba(80,80,80,0.08)",
                                    "0px 4px 16px 0px rgba(80,80,80,0.10)",
                                    "0px 8px 32px 0px rgba(80,80,80,0.12)",
                                    "0px 16px 48px 0px rgba(80,80,80,0.14)"
                    }
                },
                ZIndex = new ZIndex()
                {
                    Drawer = 1300,
                    AppBar = 1400,
                    Dialog = 1500,
                    Snackbar = 1600,
                    Tooltip = 1700
                }
            };
        });
        
            using var stream = FileSystem.OpenAppPackageFileAsync("appsettings.json").Result;
            builder.Configuration.AddJsonStream(stream);

            foreach (var setting in builder.Configuration.AsEnumerable())
            {
                if (!string.IsNullOrEmpty(setting.Key) && setting.Value != null)
                {
                    Environment.SetEnvironmentVariable(setting.Key, setting.Value);
                }
            }

            builder.Services.AddLocalization();

            // MSAL Configuration
            builder.Services.AddSingleton<IPublicClientApplication>(provider =>
            {
                var configuration = provider.GetRequiredService<IConfiguration>();
                var azureAdSection = configuration.GetSection("AzureAd");

                var clientId = azureAdSection["ClientId"];
                var instance = azureAdSection["Instance"];
                var tenantId = azureAdSection["TenantId"];

                var appBuilder = PublicClientApplicationBuilder
                    .Create(clientId)
                    .WithAuthority($"{instance}{tenantId}")
                    .WithRedirectUri($"msal{clientId}://auth");

#if ANDROID
                appBuilder = appBuilder.WithParentActivityOrWindow(() => Microsoft.Maui.ApplicationModel.Platform.CurrentActivity);
#elif IOS
                appBuilder = appBuilder.WithIosKeychainSecurityGroup("com.teyahealth.teyamobile");
#endif

                return appBuilder.Build();
            });

            // Authentication Services
            builder.Services.AddSingleton<TeyaHealthMobileViewModel.ViewModel.IAuthenticationService, SimpleAndroidAuthenticationService>();
            builder.Services.AddAuthorizationCore();

            builder.Services.AddTransient<MainPage>();
            //builder.Services.AddTransient<AudioRecorderComponent>();

            builder.Services.AddScoped<NavigationManager>();
            builder.Services.AddSingleton<IConfiguration>(builder.Configuration);
            builder.Services.AddScoped<ISpeechService, SpeechService>();
            builder.Services.AddScoped<IProgressNotesService, ProgressNotesService>();
            builder.Services.AddScoped<IAppointmentService, AppointmentService>();
            builder.Services.AddScoped<INavigationHistoryService, NavigationHistoryService>();
            builder.Services.AddScoped<INavigationService, NavigationService>();

            // Configure JsonSerializerOptions with ActiveUserConverter for proper extension attribute handling
            builder.Services.Configure<JsonSerializerOptions>(options =>
            {
                options.PropertyNamingPolicy = JsonNamingPolicy.CamelCase;
                options.DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull;
                options.Converters.Add(new ActiveUserConverter());
            });


            builder.Services.AddScoped<HttpClient>();
            builder.Services.AddScoped<ActiveUser>();
            builder.Services.AddScoped<IOrganizationService, OrganizationService>();
            builder.Services.AddScoped<IRoleslistService, RoleslistService>();
            builder.Services.AddScoped<IRoleService, RoleService>();
            builder.Services.AddScoped<GraphApiService>();
            builder.Services.AddScoped<IMemberService, MemberService>();
            builder.Services.AddScoped<IPredefinedTemplateService, PredefinedTemplateService>();
            builder.Services.AddScoped<IGraphAdminService, GraphAdminService>();
            builder.Services.AddSingleton<IFormFactor, FormFactor>();

            builder.Services.AddScoped<StorageContainer>();

            AppContext.SetSwitch("BlazorWebView.AppHostAddressAlways0000", false);

#if ANDROID
            builder.Services.AddScoped<IAudioRecorder, TeyaMobile.Platforms.Android.Services.AndroidAudioRecorderService>();
#elif IOS
            builder.Services.AddScoped<IAudioRecorder, TeyaMobile.Platforms.iOS.Services.IOSAudioRecorderService>();
#endif

            builder.Services.AddMauiBlazorWebView();

#if DEBUG
            builder.Services.AddBlazorWebViewDeveloperTools();
            builder.Logging.AddDebug();
#endif


            return builder.Build();
        }
    }
}
