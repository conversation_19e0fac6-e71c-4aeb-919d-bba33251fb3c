﻿namespace TeyaWebApp.Components.GenericFontComponents
{
    /// <summary>
    /// Color palette for the typography system
    /// </summary>
    public static class TextColor
    {
        public const string Primary = "var(--text-color-primary, #333333)";
        public const string Secondary = "var(--text-color-secondary, #666666)";
        public const string Muted = "var(--text-color-muted, #888888)";
        public const string Accent = "var(--text-color-accent, #0077cc)";
        public const string Error = "var(--text-color-error, #d32f2f)";
        public const string Success = "var(--text-color-success, #2e7d32)";
        public const string Warning = "var(--text-color-warning, #ed6c02)";
        public const string Info = "var(--text-color-info, #0288d1)";
    }
}
