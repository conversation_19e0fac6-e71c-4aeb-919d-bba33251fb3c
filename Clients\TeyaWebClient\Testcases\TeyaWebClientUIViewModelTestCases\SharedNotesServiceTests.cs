using System;
using System.Collections.Generic;
using NUnit.Framework;
using TeyaUIModels.Model;
using TeyaUIViewModels.ViewModel;

namespace TeyaWebClientUIViewModelTestCases
{
    [TestFixture]
    public class SharedNotesServiceTests
    {
        private SharedNotesService _sharedNotesService;
        private bool _onChangeEventFired;

        [SetUp]
        public void Setup()
        {
            _sharedNotesService = new SharedNotesService();
            _onChangeEventFired = false;

            // Subscribe to the OnChange event
            _sharedNotesService.OnChange += () => _onChangeEventFired = true;
        }

        [TearDown]
        public void TearDown()
        {
            // Unsubscribe from events to prevent memory leaks
            _sharedNotesService.OnChange -= () => _onChangeEventFired = true;
        }

        [Test]
        public void GetChiefComplaints_WhenInitialized_ReturnsEmptyList()
        {
            // Act
            var result = _sharedNotesService.GetChiefComplaints();

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(0));
        }

        [Test]
        public void GetChiefComplaints_ReturnsNewListInstance()
        {
            // Arrange
            var complaints = new List<ChiefComplaintDTO>
            {
                new ChiefComplaintDTO
                {
                    Id = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    Description = "Severe headache for 2 days - High severity",
                    DateOfComplaint = DateTime.Now,
                    OrganizationId = Guid.NewGuid(),
                    PcpId = Guid.NewGuid(),
                    IsDeleted = false,
                    Subscription = false
                }
            };

            _sharedNotesService.AddChiefComplaints(complaints);

            // Act
            var result1 = _sharedNotesService.GetChiefComplaints();
            var result2 = _sharedNotesService.GetChiefComplaints();

            // Assert
            Assert.That(result1, Is.Not.SameAs(result2));
            Assert.That(result1.Count, Is.EqualTo(result2.Count));
            Assert.That(result1[0].Id, Is.EqualTo(result2[0].Id));
        }

        [Test]
        public void AddChiefComplaints_WhenValidComplaints_AddsToCollection()
        {
            // Arrange
            var complaints = new List<ChiefComplaintDTO>
            {
                new ChiefComplaintDTO
                {
                    Id = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    Description = "Sharp chest pain on left side - High severity, 1 hour duration",
                    DateOfComplaint = DateTime.Now,
                    OrganizationId = Guid.NewGuid(),
                    PcpId = Guid.NewGuid(),
                    IsDeleted = false,
                    Subscription = false
                },
                new ChiefComplaintDTO
                {
                    Id = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    Description = "Difficulty breathing during exercise - Medium severity, 3 days duration",
                    DateOfComplaint = DateTime.Now,
                    OrganizationId = Guid.NewGuid(),
                    PcpId = Guid.NewGuid(),
                    IsDeleted = false,
                    Subscription = false
                }
            };

            // Act
            _sharedNotesService.AddChiefComplaints(complaints);
            var result = _sharedNotesService.GetChiefComplaints();

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(2));
            Assert.That(result[0].Description, Does.Contain("Sharp chest pain on left side"));
            Assert.That(result[0].Description, Does.Contain("High severity"));
            Assert.That(result[0].Description, Does.Contain("1 hour duration"));
            Assert.That(result[0].IsDeleted, Is.False);
            Assert.That(result[1].Description, Does.Contain("Difficulty breathing during exercise"));
            Assert.That(result[1].Description, Does.Contain("Medium severity"));
        }

        [Test]
        public void AddChiefComplaints_WhenValidComplaints_FiresOnChangeEvent()
        {
            // Arrange
            var complaints = new List<ChiefComplaintDTO>
            {
                new ChiefComplaintDTO
                {
                    Id = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    Description = "High fever with chills - High severity, 2 days duration",
                    DateOfComplaint = DateTime.Now,
                    OrganizationId = Guid.NewGuid(),
                    PcpId = Guid.NewGuid(),
                    IsDeleted = false,
                    Subscription = false
                }
            };

            // Act
            _sharedNotesService.AddChiefComplaints(complaints);

            // Assert
            Assert.That(_onChangeEventFired, Is.True);
        }

        [Test]
        public void AddChiefComplaints_WhenNullComplaints_DoesNotAddAndDoesNotFireEvent()
        {
            // Arrange
            List<ChiefComplaintDTO> complaints = null;

            // Act
            _sharedNotesService.AddChiefComplaints(complaints);
            var result = _sharedNotesService.GetChiefComplaints();

            // Assert
            Assert.That(result.Count, Is.EqualTo(0));
            Assert.That(_onChangeEventFired, Is.False);
        }

        [Test]
        public void AddChiefComplaints_WhenEmptyComplaints_DoesNotAddAndDoesNotFireEvent()
        {
            // Arrange
            var complaints = new List<ChiefComplaintDTO>();

            // Act
            _sharedNotesService.AddChiefComplaints(complaints);
            var result = _sharedNotesService.GetChiefComplaints();

            // Assert
            Assert.That(result.Count, Is.EqualTo(0));
            Assert.That(_onChangeEventFired, Is.False);
        }

        [Test]
        public void AddChiefComplaints_WhenCalledMultipleTimes_AccumulatesComplaints()
        {
            // Arrange
            var firstBatch = new List<ChiefComplaintDTO>
            {
                new ChiefComplaintDTO
                {
                    Id = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    Description = "Feeling sick to stomach - Medium severity, 1 day duration",
                    DateOfComplaint = DateTime.Now,
                    OrganizationId = Guid.NewGuid(),
                    PcpId = Guid.NewGuid(),
                    IsDeleted = false,
                    Subscription = false
                }
            };

            var secondBatch = new List<ChiefComplaintDTO>
            {
                new ChiefComplaintDTO
                {
                    Id = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    Description = "Feeling lightheaded - Low severity, 30 minutes duration",
                    DateOfComplaint = DateTime.Now,
                    OrganizationId = Guid.NewGuid(),
                    PcpId = Guid.NewGuid(),
                    IsDeleted = false,
                    Subscription = false
                },
                new ChiefComplaintDTO
                {
                    Id = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    Description = "Extreme tiredness - Medium severity, 1 week duration",
                    DateOfComplaint = DateTime.Now,
                    OrganizationId = Guid.NewGuid(),
                    PcpId = Guid.NewGuid(),
                    IsDeleted = false,
                    Subscription = false
                }
            };

            // Act
            _sharedNotesService.AddChiefComplaints(firstBatch);
            _onChangeEventFired = false; // Reset for second test
            _sharedNotesService.AddChiefComplaints(secondBatch);
            var result = _sharedNotesService.GetChiefComplaints();

            // Assert
            Assert.That(result.Count, Is.EqualTo(3));
            Assert.That(result[0].Description, Does.Contain("Feeling sick to stomach"));
            Assert.That(result[1].Description, Does.Contain("Feeling lightheaded"));
            Assert.That(result[2].Description, Does.Contain("Extreme tiredness"));
            Assert.That(_onChangeEventFired, Is.True);
        }

        [Test]
        public void AssessmentsChanged_WhenCalled_FiresOnChangeEvent()
        {
            // Act
            _sharedNotesService.AssessmentsChanged();

            // Assert
            Assert.That(_onChangeEventFired, Is.True);
        }

        [Test]
        public void OnChangeEvent_WhenMultipleSubscribers_NotifiesAllSubscribers()
        {
            // Arrange
            bool firstSubscriberNotified = false;
            bool secondSubscriberNotified = false;

            _sharedNotesService.OnChange += () => firstSubscriberNotified = true;
            _sharedNotesService.OnChange += () => secondSubscriberNotified = true;

            var complaints = new List<ChiefComplaintDTO>
            {
                new ChiefComplaintDTO
                {
                    Id = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    Description = "Test description - Low severity, 1 hour duration",
                    DateOfComplaint = DateTime.Now,
                    OrganizationId = Guid.NewGuid(),
                    PcpId = Guid.NewGuid(),
                    IsDeleted = false,
                    Subscription = false
                }
            };

            // Act
            _sharedNotesService.AddChiefComplaints(complaints);

            // Assert
            Assert.That(firstSubscriberNotified, Is.True);
            Assert.That(secondSubscriberNotified, Is.True);
        }

        [Test]
        public void OnChangeEvent_WhenNoSubscribers_DoesNotThrowException()
        {
            // Arrange
            var serviceWithoutSubscribers = new SharedNotesService();
            var complaints = new List<ChiefComplaintDTO>
            {
                new ChiefComplaintDTO
                {
                    Id = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    Description = "Test description - Low severity, 1 hour duration",
                    DateOfComplaint = DateTime.Now,
                    OrganizationId = Guid.NewGuid(),
                    PcpId = Guid.NewGuid(),
                    IsDeleted = false,
                    Subscription = false
                }
            };

            // Act & Assert
            Assert.DoesNotThrow(() => serviceWithoutSubscribers.AddChiefComplaints(complaints));
            Assert.DoesNotThrow(() => serviceWithoutSubscribers.AssessmentsChanged());
        }

        [Test]
        public void AddChiefComplaints_WhenComplaintsHaveComplexData_PreservesAllProperties()
        {
            // Arrange
            var complexComplaint = new ChiefComplaintDTO
            {
                Id = Guid.NewGuid(),
                PatientId = Guid.NewGuid(),
                Description = "Multi-line description\nwith line breaks\nand special characters: àáâãäåæçèéêë - Critical severity, 2 weeks and 3 days duration",
                DateOfComplaint = DateTime.Now.AddDays(-5),
                OrganizationId = Guid.NewGuid(),
                PcpId = Guid.NewGuid(),
                IsDeleted = true,
                Subscription = false
            };

            var complaints = new List<ChiefComplaintDTO> { complexComplaint };

            // Act
            _sharedNotesService.AddChiefComplaints(complaints);
            var result = _sharedNotesService.GetChiefComplaints();

            // Assert
            Assert.That(result.Count, Is.EqualTo(1));
            var retrievedComplaint = result[0];
            Assert.That(retrievedComplaint.Id, Is.EqualTo(complexComplaint.Id));
            Assert.That(retrievedComplaint.PatientId, Is.EqualTo(complexComplaint.PatientId));
            Assert.That(retrievedComplaint.Description, Is.EqualTo(complexComplaint.Description));
            Assert.That(retrievedComplaint.DateOfComplaint, Is.EqualTo(complexComplaint.DateOfComplaint));
            Assert.That(retrievedComplaint.IsDeleted, Is.EqualTo(complexComplaint.IsDeleted));
            Assert.That(retrievedComplaint.Subscription, Is.EqualTo(complexComplaint.Subscription));
        }
    }
}


