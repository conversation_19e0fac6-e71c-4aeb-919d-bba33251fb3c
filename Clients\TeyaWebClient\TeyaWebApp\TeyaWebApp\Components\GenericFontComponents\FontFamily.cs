﻿namespace TeyaWebApp.Components.GenericFontComponents
{
    /// <summary>
    /// Font family constants for the typography system
    /// </summary>
    public static class FontFamily
    {
        public const string Default = "var(--font-family-default, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif)";
        public const string Monospace = "var(--font-family-monospace, SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace)";
        public const string Heading = "var(--font-family-heading, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif)";
    }
}
