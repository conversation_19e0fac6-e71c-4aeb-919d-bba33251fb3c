using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using Moq;
using Moq.Protected;
using NUnit.Framework;
using TeyaUIModels.Model;
using TeyaUIViewModels.ViewModel;
using TeyaUIViewModels.TeyaUIViewModelResources;
using System.Net.Http.Json;
using System.Text;
using System.Text.Json;

namespace TeyaWebClientUIViewModelTestCases
{
    [TestFixture]
    public class AllergyServiceTests
    {
        private Mock<HttpMessageHandler> _mockHttpMessageHandler;
        private HttpClient _httpClient;
        private Mock<IConfiguration> _mockConfiguration;
        private Mock<IStringLocalizer<TeyaUIViewModelsStrings>> _mockLocalizer;
        private Mock<ITokenService> _mockTokenService;
        private AllergyService _allergyService;
        private readonly string _baseUrl = "http://test-api.com";
        private readonly string _accessToken = "test-token";

        [SetUp]
        public void Setup()
        {
            // Setup environment variable
            Environment.SetEnvironmentVariable("EncounterNotesURL", _baseUrl);

            // Setup mock HTTP handler
            _mockHttpMessageHandler = new Mock<HttpMessageHandler>();

            // Setup HttpClient with mock handler
            _httpClient = new HttpClient(_mockHttpMessageHandler.Object)
            {
                BaseAddress = new Uri(_baseUrl)
            };

            // Setup mock configuration
            _mockConfiguration = new Mock<IConfiguration>();

            // Setup mock localizer
            _mockLocalizer = new Mock<IStringLocalizer<TeyaUIViewModelsStrings>>();
            _mockLocalizer.Setup(l => l["AllergyServiceError1"])
                .Returns(new LocalizedString("AllergyServiceError1", "Error in allergy service"));
            _mockLocalizer.Setup(l => l["AllergyServiceError2"])
                .Returns(new LocalizedString("AllergyServiceError2", "Error fetching allergies"));

            // Setup mock token service
            _mockTokenService = new Mock<ITokenService>();
            _mockTokenService.Setup(t => t.AccessToken).Returns(_accessToken);

            // Create AllergyService with mocked dependencies
            _allergyService = new AllergyService(_httpClient, _mockConfiguration.Object, _mockLocalizer.Object, _mockTokenService.Object);
        }

        [TearDown]
        public void TearDown()
        {
            _httpClient.Dispose();
            Environment.SetEnvironmentVariable("EncounterNotesURL", null);
        }

        [Test]
        public async Task GetAllergyByIdAsync_WhenSuccessful_ReturnsAllergies()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var expectedAllergies = new List<Allergy>
            {
                new Allergy
                {
                    PatientId = patientId,
                    MedicineId = Guid.NewGuid(),
                    DrugName = "Test Drug",
                    Classification = "Test Classification",
                    Agent = "Test Agent",
                    Reaction = "Test Reaction",
                    Type = "Test Type",
                    IsActive = true,
                    OrganizationId = orgId,
                    Subscription = subscription
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedAllergies)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _allergyService.GetAllergyByIdAsync(patientId, orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(1));
            Assert.That(result[0].PatientId, Is.EqualTo(expectedAllergies[0].PatientId));
            Assert.That(result[0].DrugName, Is.EqualTo(expectedAllergies[0].DrugName));
            Assert.That(result[0].Classification, Is.EqualTo(expectedAllergies[0].Classification));
            Assert.That(result[0].Agent, Is.EqualTo(expectedAllergies[0].Agent));
            Assert.That(result[0].Reaction, Is.EqualTo(expectedAllergies[0].Reaction));
            Assert.That(result[0].Type, Is.EqualTo(expectedAllergies[0].Type));
        }

        [Test]
        public void GetAllergyByIdAsync_WhenHttpRequestFails_ThrowsException()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var expectedException = new HttpRequestException("Test exception");

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ThrowsAsync(expectedException);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _allergyService.GetAllergyByIdAsync(patientId, orgId, subscription));

            Assert.That(exception.Message, Is.EqualTo(expectedException.Message));
        }

        [Test]
        public async Task GetAllergyByIdAsyncAndIsActive_WhenSuccessful_ReturnsActiveAllergies()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var expectedAllergies = new List<Allergy>
            {
                new Allergy
                {
                    PatientId = patientId,
                    MedicineId = Guid.NewGuid(),
                    DrugName = "Test Drug",
                    Classification = "Test Classification",
                    Agent = "Test Agent",
                    Reaction = "Test Reaction",
                    Type = "Test Type",
                    IsActive = true,
                    OrganizationId = orgId,
                    Subscription = subscription
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedAllergies)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _allergyService.GetAllergyByIdAsyncAndIsActive(patientId, orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(1));
            Assert.That(result[0].PatientId, Is.EqualTo(expectedAllergies[0].PatientId));
            Assert.That(result[0].IsActive, Is.True);
        }

        [Test]
        public void GetAllergyByIdAsyncAndIsActive_WhenHttpRequestFails_ThrowsException()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var expectedException = new HttpRequestException("Test exception");

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ThrowsAsync(expectedException);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _allergyService.GetAllergyByIdAsyncAndIsActive(patientId, orgId, subscription));

            Assert.That(exception.Message, Is.EqualTo(expectedException.Message));
        }

        [Test]
        public async Task AddAllergyAsync_WhenSuccessful_CompletesWithoutException()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var allergies = new List<Allergy>
            {
                new Allergy
                {
                    PatientId = patientId,
                    MedicineId = Guid.NewGuid(),
                    DrugName = "Test Drug",
                    Classification = "Test Classification",
                    Agent = "Test Agent",
                    Reaction = "Test Reaction",
                    Type = "Test Type",
                    IsActive = true,
                    OrganizationId = orgId,
                    Subscription = subscription
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent("Success")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.DoesNotThrowAsync(async () => await _allergyService.AddAllergyAsync(allergies, orgId, subscription));
        }

        [Test]
        public void AddAllergyAsync_WhenHttpRequestFails_ThrowsException()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var allergies = new List<Allergy>
            {
                new Allergy
                {
                    PatientId = patientId,
                    MedicineId = Guid.NewGuid(),
                    DrugName = "Test Drug",
                    Classification = "Test Classification",
                    Agent = "Test Agent",
                    Reaction = "Test Reaction",
                    Type = "Test Type",
                    IsActive = true,
                    OrganizationId = orgId,
                    Subscription = subscription
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _allergyService.AddAllergyAsync(allergies, orgId, subscription));

            Assert.That(exception.Message, Is.EqualTo("Error in allergy service"));
        }

        [Test]
        public async Task UpdateAllergyAsync_WhenSuccessful_CompletesWithoutException()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var allergy = new Allergy
            {
                PatientId = patientId,
                MedicineId = Guid.NewGuid(),
                DrugName = "Test Drug",
                Classification = "Test Classification",
                Agent = "Test Agent",
                Reaction = "Test Reaction",
                Type = "Test Type",
                IsActive = true,
                OrganizationId = orgId,
                Subscription = subscription
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent("Success")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.DoesNotThrowAsync(async () => await _allergyService.UpdateAllergyAsync(allergy, orgId, subscription));
        }

        [Test]
        public void UpdateAllergyAsync_WhenHttpRequestFails_ThrowsException()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var allergy = new Allergy
            {
                PatientId = patientId,
                MedicineId = Guid.NewGuid(),
                DrugName = "Test Drug",
                Classification = "Test Classification",
                Agent = "Test Agent",
                Reaction = "Test Reaction",
                Type = "Test Type",
                IsActive = true,
                OrganizationId = orgId,
                Subscription = subscription
            };

            var expectedException = new HttpRequestException("Test exception");

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ThrowsAsync(expectedException);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _allergyService.UpdateAllergyAsync(allergy, orgId, subscription));

            Assert.That(exception.Message, Is.EqualTo(expectedException.Message));
        }

        [Test]
        public async Task DeleteAllergyAsync_WhenSuccessful_CompletesWithoutException()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var allergy = new Allergy
            {
                PatientId = patientId,
                MedicineId = Guid.NewGuid(),
                DrugName = "Test Drug",
                Classification = "Test Classification",
                Agent = "Test Agent",
                Reaction = "Test Reaction",
                Type = "Test Type",
                IsActive = true,
                OrganizationId = orgId,
                Subscription = subscription
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent("Success")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.DoesNotThrowAsync(async () => await _allergyService.DeleteAllergyAsync(allergy, orgId, subscription));
        }

        [Test]
        public void DeleteAllergyAsync_WhenHttpRequestFails_ThrowsException()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var allergy = new Allergy
            {
                PatientId = patientId,
                MedicineId = Guid.NewGuid(),
                DrugName = "Test Drug",
                Classification = "Test Classification",
                Agent = "Test Agent",
                Reaction = "Test Reaction",
                Type = "Test Type",
                IsActive = true,
                OrganizationId = orgId,
                Subscription = subscription
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _allergyService.DeleteAllergyAsync(allergy, orgId, subscription));

            Assert.That(exception.Message, Is.EqualTo("Error in allergy service"));
        }
    }
}



