using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using Moq;
using Moq.Protected;
using NUnit.Framework;
using TeyaUIModels.Model;
using TeyaUIViewModels.ViewModel;
using TeyaUIViewModels.TeyaUIViewModelResources;
using System.Net.Http.Json;
using System.Text.Json;
using System.Linq;

namespace TeyaWebClientUIViewModelTestCases
{
    [TestFixture]
    public class ProcedureServiceTests
    {
        private Mock<HttpMessageHandler> _mockHttpMessageHandler;
        private HttpClient _httpClient;
        private Mock<IConfiguration> _mockConfiguration;
        private Mock<IStringLocalizer<TeyaUIViewModelsStrings>> _mockLocalizer;
        private Mock<ITokenService> _mockTokenService;
        private ProcedureService _procedureService;
        private readonly string _baseUrl = "http://test-api.com";
        private readonly string _accessToken = "test-access-token";

        [SetUp]
        public void Setup()
        {
            // Setup environment variable
            Environment.SetEnvironmentVariable("EncounterNotesURL", _baseUrl);

            // Setup mock HTTP handler
            _mockHttpMessageHandler = new Mock<HttpMessageHandler>();

            // Setup HttpClient with mock handler
            _httpClient = new HttpClient(_mockHttpMessageHandler.Object)
            {
                BaseAddress = new Uri(_baseUrl)
            };

            // Setup mock configuration
            _mockConfiguration = new Mock<IConfiguration>();

            // Setup mock localizer
            _mockLocalizer = new Mock<IStringLocalizer<TeyaUIViewModelsStrings>>();
            _mockLocalizer.Setup(l => l["TasksRetrievalFailure"])
                .Returns(new LocalizedString("TasksRetrievalFailure", "Tasks retrieval failure"));
            _mockLocalizer.Setup(l => l["AddressRetrievalFailure"])
                .Returns(new LocalizedString("AddressRetrievalFailure", "Address retrieval failure"));

            // Setup mock token service
            _mockTokenService = new Mock<ITokenService>();
            _mockTokenService.Setup(t => t.AccessToken).Returns(_accessToken);

            // Create ProcedureService with mocked dependencies
            _procedureService = new ProcedureService(_httpClient, _mockConfiguration.Object, _mockLocalizer.Object, _mockTokenService.Object);
        }

        [TearDown]
        public void TearDown()
        {
            _httpClient.Dispose();
            Environment.SetEnvironmentVariable("EncounterNotesURL", null);
        }

        [Test]
        public async Task GetProcedureByPatientId_WhenSuccessful_ReturnsProcedures()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var expectedProcedures = new List<Procedures>
            {
                new Procedures
                {
                    Id = Guid.NewGuid(),
                    PatientId = patientId,
                    CPTCode = "99213",
                    Description = "Office visit",
                    Notes = "Routine checkup",
                    OrderedBy = "Dr. Smith",
                    OrderDate = DateTime.Now.AddDays(-7),
                    IsDeleted = false
                },
                new Procedures
                {
                    Id = Guid.NewGuid(),
                    PatientId = patientId,
                    CPTCode = "80053",
                    Description = "Comprehensive metabolic panel",
                    Notes = "Lab work ordered",
                    OrderedBy = "Dr. Johnson",
                    OrderDate = DateTime.Now.AddDays(-3),
                    IsDeleted = false
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedProcedures)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _procedureService.GetProcedureByPatientId(patientId, orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(expectedProcedures.Count));
            Assert.That(result[0].Id, Is.EqualTo(expectedProcedures[0].Id));
            Assert.That(result[0].PatientId, Is.EqualTo(expectedProcedures[0].PatientId));
            Assert.That(result[0].CPTCode, Is.EqualTo(expectedProcedures[0].CPTCode));
            Assert.That(result[0].Description, Is.EqualTo(expectedProcedures[0].Description));
            Assert.That(result[0].Notes, Is.EqualTo(expectedProcedures[0].Notes));
            Assert.That(result[0].OrderedBy, Is.EqualTo(expectedProcedures[0].OrderedBy));
            Assert.That(result[0].IsDeleted, Is.EqualTo(expectedProcedures[0].IsDeleted));
        }

        [Test]
        public void GetProcedureByPatientId_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.NotFound,
                Content = new StringContent("Not found")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _procedureService.GetProcedureByPatientId(patientId, orgId, subscription));

            Assert.That(exception.Message, Is.EqualTo("Address retrieval failure"));
        }

        [Test]
        public async Task AddProcedureAsync_WhenSuccessful_CompletesSuccessfully()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;
            var procedures = new List<Procedures>
            {
                new Procedures
                {
                    Id = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    CPTCode = "99214",
                    Description = "Office visit - detailed",
                    Notes = "Follow-up appointment",
                    OrderedBy = "Dr. Brown",
                    OrderDate = DateTime.Now,
                    IsDeleted = false
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.Created,
                Content = new StringContent("Success")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.DoesNotThrowAsync(async () => await _procedureService.AddProcedureAsync(procedures, orgId, subscription));
        }

        [Test]
        public void AddProcedureAsync_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;
            var procedures = new List<Procedures>
            {
                new Procedures
                {
                    Id = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    CPTCode = "99214",
                    Description = "Office visit - detailed",
                    Notes = "Follow-up appointment",
                    OrderedBy = "Dr. Brown",
                    OrderDate = DateTime.Now,
                    IsDeleted = false
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _procedureService.AddProcedureAsync(procedures, orgId, subscription));

            Assert.That(exception.Message, Is.EqualTo("Tasks retrieval failure"));
        }

        [Test]
        public async Task UpdateProcedureListAsync_WhenSuccessful_CompletesSuccessfully()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;
            var procedures = new List<Procedures>
            {
                new Procedures
                {
                    Id = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    CPTCode = "99215",
                    Description = "Office visit - comprehensive",
                    Notes = "Updated procedure notes",
                    OrderedBy = "Dr. Wilson",
                    OrderDate = DateTime.Now.AddDays(-1),
                    IsDeleted = false
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent("Success")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            Assert.DoesNotThrowAsync(async () => await _procedureService.UpdateProcedureListAsync(procedures, orgId, subscription));
        }

        [Test]
        public void UpdateProcedureListAsync_WhenNotSuccessful_ThrowsHttpRequestException()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var subscription = true;
            var procedures = new List<Procedures>
            {
                new Procedures
                {
                    Id = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    CPTCode = "99215",
                    Description = "Office visit - comprehensive",
                    Notes = "Updated procedure notes",
                    OrderedBy = "Dr. Wilson",
                    OrderDate = DateTime.Now.AddDays(-1),
                    IsDeleted = false
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Bad request")
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act & Assert
            var exception = Assert.ThrowsAsync<HttpRequestException>(async () =>
                await _procedureService.UpdateProcedureListAsync(procedures, orgId, subscription));

            Assert.That(exception, Is.Not.Null);
        }

        [Test]
        public async Task LoadProcedureAsync_WhenSuccessful_ReturnsTransformedProcedures()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var expectedProcedures = new List<Procedures>
            {
                new Procedures
                {
                    Id = Guid.NewGuid(),
                    PatientId = patientId,
                    CPTCode = "99213",
                    Description = "Office visit",
                    Notes = "Routine checkup",
                    OrderedBy = "Dr. Smith",
                    OrderDate = DateTime.Now.AddDays(-7),
                    LastUpdatedDate = DateTime.Now.AddDays(-6),
                    CreatedByUserId = Guid.NewGuid(),
                    UpdatedByUserId = Guid.NewGuid(),
                    OrganizationId = orgId,
                    PcpId = Guid.NewGuid(),
                    IsDeleted = false,
                    AssessmentData = "Assessment data",
                    AssessmentId = Guid.NewGuid(),
                    ChiefComplaint = "Chief complaint",
                    ChiefComplaintId = Guid.NewGuid()
                }
            };

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedProcedures)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _procedureService.LoadProcedureAsync(patientId, orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(expectedProcedures.Count));
            Assert.That(result[0].Id, Is.EqualTo(expectedProcedures[0].Id));
            Assert.That(result[0].PatientId, Is.EqualTo(expectedProcedures[0].PatientId));
            Assert.That(result[0].CPTCode, Is.EqualTo(expectedProcedures[0].CPTCode));
            Assert.That(result[0].Description, Is.EqualTo(expectedProcedures[0].Description));
            Assert.That(result[0].Notes, Is.EqualTo(expectedProcedures[0].Notes));
            Assert.That(result[0].OrderedBy, Is.EqualTo(expectedProcedures[0].OrderedBy));
            Assert.That(result[0].OrderDate, Is.EqualTo(expectedProcedures[0].OrderDate));
            Assert.That(result[0].LastUpdatedDate, Is.EqualTo(expectedProcedures[0].LastUpdatedDate));
            Assert.That(result[0].CreatedByUserId, Is.EqualTo(expectedProcedures[0].CreatedByUserId));
            Assert.That(result[0].UpdatedByUserId, Is.EqualTo(expectedProcedures[0].UpdatedByUserId));
            Assert.That(result[0].OrganizationId, Is.EqualTo(expectedProcedures[0].OrganizationId));
            Assert.That(result[0].PcpId, Is.EqualTo(expectedProcedures[0].PcpId));
            Assert.That(result[0].IsDeleted, Is.EqualTo(expectedProcedures[0].IsDeleted));
            Assert.That(result[0].AssessmentData, Is.EqualTo(expectedProcedures[0].AssessmentData));
            Assert.That(result[0].AssessmentId, Is.EqualTo(expectedProcedures[0].AssessmentId));
            Assert.That(result[0].ChiefComplaint, Is.EqualTo(expectedProcedures[0].ChiefComplaint));
            Assert.That(result[0].ChiefComplaintId, Is.EqualTo(expectedProcedures[0].ChiefComplaintId));
        }

        [Test]
        public void LoadProcedureAsync_WhenExceptionThrown_ThrowsExceptionWithMessage()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var expectedException = new HttpRequestException("Test exception");

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ThrowsAsync(expectedException);

            // Act & Assert
            var exception = Assert.ThrowsAsync<Exception>(async () =>
                await _procedureService.LoadProcedureAsync(patientId, orgId, subscription));

            Assert.That(exception.Message, Does.Contain("Error loading OB histories"));
            Assert.That(exception.Message, Does.Contain(expectedException.Message));
        }

        [Test]
        public async Task LoadProcedureAsync_WhenEmptyResult_ReturnsEmptyList()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = true;
            var expectedProcedures = new List<Procedures>();

            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = JsonContent.Create(expectedProcedures)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);

            // Act
            var result = await _procedureService.LoadProcedureAsync(patientId, orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(0));
        }
    }
}



