﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <Version>1.0.50702.3</Version>
    <TargetFrameworks>net9.0</TargetFrameworks>
	<ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <Compile Update="TeyaUIViewModelResources\TeyaUIViewModelsStrings.Designer.cs">
      <DependentUpon>TeyaUIViewModelsStrings.resx</DependentUpon>
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
    </Compile>
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Update="TeyaUIViewModelResources\TeyaUIViewModelsStrings.resx">
      <SubType>Designer</SubType>
      <LastGenOutput>TeyaUIViewModelsStrings.Designer.cs</LastGenOutput>
      <Generator>PublicResXFileCodeGenerator</Generator>
    </EmbeddedResource>
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Components" Version="9.0.0" />
    <PackageReference Include="Microsoft.AspNetCore.Http.Abstractions" Version="2.3.0" />
    <PackageReference Include="Microsoft.CognitiveServices.Speech" Version="1.44.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="9.0.0" />
    <PackageReference Include="Microsoft.Extensions.Localization.Abstractions" Version="9.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="9.0.0" />
    <PackageReference Include="Microsoft.Extensions.Options" Version="9.0.0" />
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="8.2.1" />
	 <PackageReference Include="Xamarin.Essentials" Version="1.8.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\TeyaHealthMobileModel\TeyaHealthMobileModel.csproj" />
  </ItemGroup>

</Project>
