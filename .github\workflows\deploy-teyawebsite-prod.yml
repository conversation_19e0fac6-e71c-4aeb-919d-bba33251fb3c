name: Te<PERSON>-Appservice-Website-Prod

on:
  workflow_dispatch:
  push:
    branches:
      - Release/TeyaAIScribe.2025.03.17

env:
  APP_SERVICE_NAME: Teya-Website-Prod
  RESOURCE_GROUP: teyahealth-rg-prod-eastus-001
  CONTAINER_REGISTRY_LOGIN_SERVER: teyahealthprod.azurecr.io
  DOCKER_FILE_PATH: Clients/TeyaWebsite/TeyaWebsite/Dockerfile
  PROJECT_NAME_FOR_DOCKER: teyawebsite

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to container registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.CONTAINER_REGISTRY_LOGIN_SERVER }}
          username: TeyaHealthProd
          password: ****************************************************

      - name: Build and push container image
        uses: docker/build-push-action@v5
        with:
          push: true
          tags: ${{ env.CONTAINER_REGISTRY_LOGIN_SERVER }}/${{ env.PROJECT_NAME_FOR_DOCKER }}:${{ github.sha }}
          file: ${{ env.DOCKER_FILE_PATH }}

      - name: Azure Login
        uses: azure/login@v1
        with:
          creds: ${{ secrets.DEVREPOSECRET }}

      - name: Set App Settings with Key Vault References
        run: |
          az webapp config appsettings set \
            --name ${{ env.APP_SERVICE_NAME }} \
            --resource-group ${{ env.RESOURCE_GROUP }} \
            --settings \
              KEYVAULT__URLS='@Microsoft.KeyVault(SecretUri=https://teyawebappvault-prod.vault.azure.net/secrets/keyvault-urls/a98bf06de50945db9e0d37ab6271270c)' \
              KEYVAULT__TENANTID='@Microsoft.KeyVault(SecretUri=https://commonvault-prod.vault.azure.net/secrets/keyvault-tenantid/566dd56e9aba4f1f925add2176c4f90c)' \
              KEYVAULT__CLIENTID='@Microsoft.KeyVault(SecretUri=https://commonvault-prod.vault.azure.net/secrets/keyvault-clientid/88a86ced41e94e00aef115db2a4dc44c)' \
              KEYVAULT__CLIENTSECRET='@Microsoft.KeyVault(SecretUri=https://commonvault-prod.vault.azure.net/secrets/keyvault-clientsecret/bba83b8321b241689b13ddebad98d4d3)' \
              ENVIRONMENT_KEY='@Microsoft.KeyVault(SecretUri=https://teyawebappvault-prod.vault.azure.net/secrets/environment-key/fc626eabd0ac4157815996691ce5e816)'
              
      - name: Deploy Docker image to Azure App Service
        run: |
          az webapp config container set \
            --name ${{ env.APP_SERVICE_NAME }} \
            --resource-group ${{ env.RESOURCE_GROUP }} \
            --docker-custom-image-name ${{ env.CONTAINER_REGISTRY_LOGIN_SERVER }}/${{ env.PROJECT_NAME_FOR_DOCKER }}:${{ github.sha }} \
            --docker-registry-server-url https://${{ env.CONTAINER_REGISTRY_LOGIN_SERVER }}

      - name: Logout
        run: az logout
