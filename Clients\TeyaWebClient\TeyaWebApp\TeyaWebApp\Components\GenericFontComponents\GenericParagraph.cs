﻿using Microsoft.AspNetCore.Components;

namespace TeyaWebApp.Components.GenericFontComponents
{
    // Paragraph Component
    public partial class GenericParagraph : ComponentBase
    {
        [Parameter]
        public RenderFragment ChildContent { get; set; }

        [Parameter]
        public string Size { get; set; } = FontSize.Medium;

        [Parameter]
        public string Weight { get; set; } = FontWeight.Regular;

        [Parameter]
        public string LineHeight { get; set; } = "1.5";

        [Parameter]
        public string Color { get; set; } = "inherit";

        [Parameter]
        public string MarginBottom { get; set; } = "1rem";

        [Parameter]
        public string Class { get; set; } = "";

        [Parameter(CaptureUnmatchedValues = true)]
        public Dictionary<string, object> AdditionalAttributes { get; set; }

        protected override void BuildRenderTree(Microsoft.AspNetCore.Components.Rendering.RenderTreeBuilder builder)
        {
            builder.OpenElement(0, "p");
            builder.AddAttribute(1, "class", $"generic-paragraph {Class}");
            builder.AddAttribute(2, "style", GetStyle());

            if (AdditionalAttributes != null)
            {
                foreach (var attribute in AdditionalAttributes)
                {
                    builder.AddAttribute(3, attribute.Key, attribute.Value);
                }
            }

            builder.AddContent(4, ChildContent);
            builder.CloseElement();
        }

        private string GetStyle()
        {
            return $"font-family: {FontFamily.Default}; font-size: {Size}; font-weight: {Weight}; line-height: {LineHeight}; color: {Color}; margin-bottom: {MarginBottom}; margin-top: 0;";
        }
    }
}
