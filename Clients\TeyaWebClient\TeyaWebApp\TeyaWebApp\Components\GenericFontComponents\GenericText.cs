﻿using Microsoft.AspNetCore.Components;

namespace TeyaWebApp.Components.GenericFontComponents
{
    // Generic Text Component
    public partial class GenericText : ComponentBase
    {
        [Parameter]
        public RenderFragment ChildContent { get; set; }

        [Parameter]
        public string Size { get; set; } = FontSize.Medium;

        [Parameter]
        public string Weight { get; set; } = FontWeight.Regular;

        [Parameter]
        public string Family { get; set; } = FontFamily.Default;

        [Parameter]
        public string Color { get; set; } = "inherit";

        [Parameter]
        public bool Truncate { get; set; } = false;

        [Parameter]
        public bool Italic { get; set; } = false;

        [Parameter]
        public bool Uppercase { get; set; } = false;

        [Parameter]
        public string LineHeight { get; set; } = "normal";

        [Parameter]
        public string Align { get; set; } = "left";

        [Parameter]
        public string Class { get; set; } = "";

        [Parameter(CaptureUnmatchedValues = true)]
        public Dictionary<string, object> AdditionalAttributes { get; set; }

        protected override void BuildRenderTree(Microsoft.AspNetCore.Components.Rendering.RenderTreeBuilder builder)
        {
            builder.OpenElement(0, "span");
            builder.AddAttribute(1, "class", GetCssClass());
            builder.AddAttribute(2, "style", GetStyle());

            if (AdditionalAttributes != null)
            {
                foreach (var attribute in AdditionalAttributes)
                {
                    builder.AddAttribute(3, attribute.Key, attribute.Value);
                }
            }

            builder.AddContent(4, ChildContent);
            builder.CloseElement();
        }

        private string GetCssClass()
        {
            var classes = new List<string> { "generic-text" };

            if (!string.IsNullOrEmpty(Class))
            {
                classes.Add(Class);
            }

            if (Truncate)
            {
                classes.Add("text-truncate");
            }

            if (Uppercase)
            {
                classes.Add("text-uppercase");
            }

            if (Italic)
            {
                classes.Add("text-italic");
            }

            if (Align != "left")
            {
                classes.Add($"text-{Align}");
            }

            return string.Join(" ", classes);
        }

        private string GetStyle()
        {
            var styleList = new List<string>();

            if (!string.IsNullOrEmpty(Size))
            {
                styleList.Add($"font-size: {Size}");
            }

            if (!string.IsNullOrEmpty(Weight))
            {
                styleList.Add($"font-weight: {Weight}");
            }

            if (!string.IsNullOrEmpty(Family))
            {
                styleList.Add($"font-family: {Family}");
            }

            if (!string.IsNullOrEmpty(Color) && Color != "inherit")
            {
                styleList.Add($"color: {Color}");
            }

            if (!string.IsNullOrEmpty(LineHeight) && LineHeight != "normal")
            {
                styleList.Add($"line-height: {LineHeight}");
            }

            return string.Join("; ", styleList);
        }
    }
}
