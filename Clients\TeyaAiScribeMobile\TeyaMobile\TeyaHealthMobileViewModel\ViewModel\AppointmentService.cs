﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using System.ComponentModel;
using System.Net.Http.Json;
using System.Text;
using TeyaHealthMobileModel.Model;
using TeyaHealthMobileViewModel.TeyaUIViewModelResources;

namespace TeyaHealthMobileViewModel.ViewModel
{
    public class AppointmentService : IAppointmentService
    {
        private readonly HttpClient _httpClient;
        private readonly IStringLocalizer<TeyaUIViewModelsStrings> _localizer;
        private readonly string _AppointmentsUrl;
        private readonly IConfiguration _configuration;
        private readonly IAuthenticationService _authenticationService;

        public AppointmentService(HttpClient httpClient, IConfiguration configuration, IStringLocalizer<TeyaUIViewModelsStrings> localizer, IAuthenticationService authService)
        {
            _httpClient = httpClient;
            _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
            _authenticationService = authService ?? throw new ArgumentNullException(nameof(authService));
            _AppointmentsUrl = _configuration["AppointmentsUrl"];
            //"http://10.0.2.2/Appointments";
        }

        public async Task<List<Appointment>> GetAllAppointmentsAsync()
        {
            try
            {
                //var token = await SecureStorage.GetAsync("auth_token");

                //if (string.IsNullOrEmpty(token))
                //{
                //    throw new InvalidOperationException("Authentication token not found");
                //}

                //var request = new HttpRequestMessage(HttpMethod.Get, _AppointmentsUrl);
                //request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", token);

                var request = new HttpRequestMessage(HttpMethod.Get, _AppointmentsUrl);

                var response = await _httpClient.SendAsync(request);

                response.EnsureSuccessStatusCode(); // Throws exception for non-success codes

                var appointments = await response.Content.ReadFromJsonAsync<List<Appointment>>();
                return appointments ?? new List<Appointment>();
            }
            catch (HttpRequestException ex)
            {
                // Log the error if needed
                Console.WriteLine($"HTTP request failed: {ex.Message}");
                throw new HttpRequestException(_localizer["AppointmentRetrievalFailure"], ex);
            }
            catch (Exception ex)
            {
                // Handle other exceptions
                Console.WriteLine($"Error retrieving appointments: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Retrieves a list of appointments for a specific date.
        /// </summary>
        /// <param name="date">The date for which appointments are to be retrieved.</param>
        /// <returns>A list of appointments.</returns>
        public async Task<List<Appointment>> GetAppointmentsAsync(
            [Description("Appointment date (yyyy-MM-dd)")] DateTime date,
            [Description("Organization ID (GUID)")] Guid? OrgID,
            [Description("Subscription flag")] bool Subscription)
        {
            var accessToken = await _authenticationService.GetServiceSpecificTokenAsync();
            var dateUrl = $"{_AppointmentsUrl}/api/Appointments/{date:yyyy-MM-dd}/{OrgID}/{Subscription}";

            var requestMessage = new HttpRequestMessage(HttpMethod.Get, dateUrl);
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

            var response = await _httpClient.SendAsync(requestMessage);

            if (response.IsSuccessStatusCode)
            {
                return await response.Content.ReadFromJsonAsync<List<Appointment>>();
            }
            else
            {
                throw new HttpRequestException(_localizer["AppointmentRetrievalFailure"]);
            }
        }

        /// <summary>
        /// Creates new appointments by sending a request to the appointment registration API.
        /// </summary>
        /// <param name="appointments">A list of appointments to be created.</param>
        public async Task CreateAppointmentsAsync([Description("Appointment data")] List<Appointment> appointments)
        {
            var accessToken = await _authenticationService.GetServiceSpecificTokenAsync();
            var apiUrl = $"{_AppointmentsUrl}/api/Appointments/appointmentRecord";
            var bodyContent = System.Text.Json.JsonSerializer.Serialize(appointments);
            var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");

            var requestMessage = new HttpRequestMessage(HttpMethod.Post, apiUrl)
            {
                Content = content
            };
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
            var response = await _httpClient.SendAsync(requestMessage);

            if (response.IsSuccessStatusCode)
            {
                var responseBody = await response.Content.ReadAsStringAsync();
            }
            else
            {
                throw new HttpRequestException(_localizer["AppointmentRetrievalFailure"]);
            }
        }

        /// <summary>
        /// Retrieves a list of appointments for a specific patientId.
        /// </summary>
        /// <param id="patientId">The Id for which appointments are to be retrieved.</param>
        /// <returns>A list of appointments.</returns>
        public async Task<List<Appointment>> GetAppointmentsByIdAsync(Guid id)
        {
            var apiUrl = $"{_AppointmentsUrl}/api/Appointments/user/{id}";
            var accessToken = await _authenticationService.GetServiceSpecificTokenAsync();
            var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

            var response = await _httpClient.SendAsync(requestMessage);

            if (response.IsSuccessStatusCode)
            {
                return await response.Content.ReadFromJsonAsync<List<Appointment>>();
            }
            else
            {
                throw new HttpRequestException(_localizer["AppointmentRetrievalFailure"]);
            }
        }

        /// <summary>
        /// Updates an existing appointment.
        /// </summary>
        /// <param name="appointment">The appointment object containing updated details.</param>
        public async Task UpdateAppointmentAsync(
            [Description("Appointment ID (GUID)")] Appointment appointment)
        {
            var accessToken = await _authenticationService.GetServiceSpecificTokenAsync();
            var apiUrl = $"{_AppointmentsUrl}/api/Appointments/{appointment.Id}";
            var bodyContent = System.Text.Json.JsonSerializer.Serialize(appointment);
            var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");

            var requestMessage = new HttpRequestMessage(HttpMethod.Put, apiUrl);
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
            requestMessage.Content = content;

            var response = await _httpClient.SendAsync(requestMessage);
            response.EnsureSuccessStatusCode();
        }

        /// <summary>
        /// Deletes an appointment by its unique identifier.
        /// </summary>
        /// <param name="appointmentId">The unique identifier of the appointment to be deleted.</param>
        public async Task DeleteAppointmentAsync(
            [Description("Appointment ID (GUID)")] Guid appointmentId,
            [Description("Organization ID (GUID)")] Guid? OrgID,
            [Description("Subscription flag")] bool Subscription)
        {
            try
            {
                var accessToken = await _authenticationService.GetServiceSpecificTokenAsync();
                var apiUrl = $"{_AppointmentsUrl}/api/Appointments/{appointmentId}/{OrgID}/{Subscription}";
                var requestMessage = new HttpRequestMessage(HttpMethod.Delete, apiUrl);
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);
                response.EnsureSuccessStatusCode();
            }
            catch (Exception ex)
            {
                throw new Exception(_localizer["Error"], ex);
            }
        }
    }
}